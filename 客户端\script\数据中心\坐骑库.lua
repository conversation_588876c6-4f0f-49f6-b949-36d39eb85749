--======================================================================--
-- @作者: www.wnfj.com，联系QQ：79550111
-- @创建时间:   2018-03-03 02:34:19
-- @Last Modified time: 2024-12-05 17:26:07
--======================================================================--
-- function 引擎.补差(zq,角色)--,方向,帧数
-- 	local zqmx
-- 	if type(zq) ~= "table" then
-- 		zqmx = zq
-- 	else
-- 		zqmx = zq.模型
-- 	end
-- 	local zqs={}
-- 	zqs.x,zqs.y=0,0
-- 	if zq == nil then
-- 		return zqs.x,zqs.y
-- 	end
	-- 3092329135 北斗 shape.wdb  694933611    492047308  shape.wd7
	-- if zqmx=="神气小龟" and (角色=="鬼潇潇" or 角色=="桃夭夭" or 角色=="偃无师" or 角色=="骨精灵"or 角色=="龙太子") then
	-- 	zqs.x,zqs.y=-10,45
	-- elseif zqmx=="汗血宝马" and ( 角色=="偃无师") then
	--  	zqs.x,zqs.y=0,40
	-- elseif zqmx=="欢喜羊羊" and (角色=="偃无师") then
	--  	zqs.x,zqs.y=0,40
	-- elseif zqmx=="魔力斗兽" and (角色=="鬼潇潇" or 角色=="骨精灵") then
	--  	zqs.x,zqs.y=0,40
	-- elseif zqmx=="披甲战狼" and (角色=="鬼潇潇" or 角色=="骨精灵") then
	--  	zqs.x,zqs.y=0,40
	-- elseif zqmx=="闲云野鹤" and ( 角色=="桃夭夭" or 角色=="龙太子") then
	--  	zqs.x,zqs.y=0,-10
	-- elseif zqmx=="云魅仙鹿" and (角色=="桃夭夭" or 角色=="龙太子") then
	--  	zqs.x,zqs.y=-10,40
	-- elseif zqmx=="青霄天麟" then
	--  	zqs.x,zqs.y=0,0
	-- elseif zqmx=="玄冰灵虎" then
	--  	zqs.x,zqs.y=0,-10
	-- elseif zqmx=="七彩小驴" or zqmx=="粉红小驴" then
	--  	zqs.x,zqs.y=0,-10
	-- elseif zqmx=="玄火神驹" then
	--  	zqs.x,zqs.y=0,-15
	-- elseif zqmx=="紫霞云麒" then
	--  	zqs.x,zqs.y=0,-10
 	-- if zqmx=="九尾冰狐" then
 -- 		print(帧数)
 		-- if 帧数<=3 then
 		--     zqs.y = 55
 		-- else
 		-- 	zqs.y = -帧数
 		-- end
 -- 		zqs.y = -帧数*2
	 	-- zqs.x,zqs.y=0,0
	 	-- if 方向==5 then --左
	 	--     zqs.x,zqs.y=-20,0
	 	-- elseif 方向==7 then --右
	 	-- 	zqs.x,zqs.y=20,0
 		-- elseif 方向==0 then --右下
	 	-- 	zqs.x,zqs.y=10,10
 		-- elseif 方向==1 then --左下
	 	-- 	zqs.x,zqs.y=-10,10
	 	-- end
	--  	if 角色=="骨精灵" or 角色=="鬼潇潇" or 角色=="桃夭夭" or 角色=="偃无师" then
	--  	    zqs.x,zqs.y=0,50
 -- 	    elseif 角色=="巨魔王" or 角色=="虎头怪" then
 -- 	    	if 方向==6 then --上
	-- 	 	    zqs.x,zqs.y=0,10
	-- 	 	elseif 方向==4 then --下
	-- 	 		zqs.x,zqs.y=0,10
	-- 	 	elseif 方向==5 then --左
	-- 	 		zqs.x,zqs.y=5,10
	-- 	 	elseif 方向==7 then --右
	-- 	 		zqs.x,zqs.y=-5,10
	--  		elseif 方向==0 then --右下
	-- 	 		zqs.x,zqs.y=-10,10
	--  		elseif 方向==1 then --左下
	-- 	 		zqs.x,zqs.y=10,10
	--  		elseif 方向==2 then --右上
	-- 	 		zqs.x,zqs.y=0,20
	-- 	 	elseif 方向==3 then --左下
	-- 	 		zqs.x,zqs.y=0,20
	-- 	 	end
	--  	end
	-- end
-- 	return zqs.x,zqs.y
-- end

function 引擎.补差(zq,角色)
	local zqs={}
	zqs.x,zqs.y=0,0
	if zq=="汗血宝马" and (角色=="桃夭夭" or 角色=="神天兵" ) then
		zqs.x,zqs.y=0,0
	    return zqs.x,zqs.y




	end
	return zqs.x,zqs.y
end




function 引擎.新增坐骑(人物,坐骑,动作)

	local 名称 = 人物.."_"..坐骑.."_"..动作
	if 名称 == "剑侠客_蓝色狐狸_站立"	 then
		return 0x00100020
	elseif 名称 == "剑侠客_蓝色狐狸_奔跑"	 then
		return 0x00100021
	elseif 名称 == "飞燕女_蓝色狐狸_站立"	 then
		return 0x00100008
	elseif 名称 == "飞燕女_蓝色狐狸_奔跑"	 then
		return 0x00100009
	elseif 名称 == "骨精灵_蓝色狐狸_站立"  	 then
		return 0x00100008
	elseif 名称 == "骨精灵_蓝色狐狸_奔跑"  	 then
		return 0x00100009
	elseif 名称 == "影精灵_蓝色狐狸_站立"  	 then
		return 0x00100008
	elseif 名称 == "影精灵_蓝色狐狸_奔跑"  	 then
		return 0x00100009
	elseif 名称 == "英女侠_蓝色狐狸_站立"	 then
		return 0x00100008
	elseif 名称 == "英女侠_蓝色狐狸_奔跑"	 then
		return 0x00100009
	elseif 名称 == "狐美人_蓝色狐狸_站立"	 then
		return 0x00100008
	elseif 名称 == "狐美人_蓝色狐狸_奔跑"	 then
		return 0x00100009
	elseif 名称 == "虎头怪_蓝色狐狸_站立"	 then
		return 0x00100018
	elseif 名称 == "虎头怪_蓝色狐狸_奔跑"	 then
		return 0x00100019
	elseif 名称 == "巨魔王_蓝色狐狸_站立"	 then
		return 0x00100022
	elseif 名称 == "巨魔王_蓝色狐狸_奔跑"	 then
		return 0x00100023
	elseif 名称 == "龙太子_蓝色狐狸_站立"	 then
		return 0x00100020
	elseif 名称 == "龙太子_蓝色狐狸_奔跑"	 then
		return 0x00100021
	elseif 名称 == "神天兵_蓝色狐狸_站立"	 then
		return 0x00100020
	elseif 名称 == "神天兵_蓝色狐狸_奔跑"	 then
		return 0x00100021
	elseif 名称 == "舞天姬_蓝色狐狸_站立"	 then
		return 0x00100008
	elseif 名称 == "舞天姬_蓝色狐狸_奔跑"	 then
		return 0x00100009
	elseif 名称 == "逍遥生_蓝色狐狸_站立"	 then
		return 0x00100020
	elseif 名称 == "逍遥生_蓝色狐狸_奔跑"   then
		return 0x00100021
	elseif 名称 == "玄彩娥_蓝色狐狸_站立"	 then
		return 0x00100008
	elseif 名称 == "玄彩娥_蓝色狐狸_奔跑"	 then
		return 0x00100009
	elseif 名称 == "巫蛮儿_蓝色狐狸_站立"	 then
		return 0x00100008
	elseif 名称 == "巫蛮儿_蓝色狐狸_奔跑"	 then
		return 0x00100009
	elseif 名称 == "羽灵神_蓝色狐狸_站立"	 then
		return 0x00100020
	elseif 名称 == "羽灵神_蓝色狐狸_奔跑"	 then
		return 0x00100021
	elseif 名称 == "杀破狼_蓝色狐狸_站立"	 then
		return 0x00100020
	elseif 名称 == "杀破狼_蓝色狐狸_奔跑"	 then
		return 0x00100021
	elseif 名称 == "鬼潇潇_蓝色狐狸_站立"	 then
		return 0x00100008
	elseif 名称 == "鬼潇潇_蓝色狐狸_奔跑"	 then
		return 0x00100009
	elseif 名称 == "偃无师_蓝色狐狸_站立"	 then
		return 0x00100020
	elseif 名称 == "偃无师_蓝色狐狸_奔跑"	 then
		return 0x00100021
	elseif 名称 == "桃夭夭_蓝色狐狸_站立"	 then
		return 0x00100008
	elseif 名称 == "桃夭夭_蓝色狐狸_奔跑"	 then
		return 0x00100009

	elseif 名称 == "剑侠客_黄金狮子_站立"	 then
		return 0x00100002
	elseif 名称 == "剑侠客_黄金狮子_奔跑"	 then
		return 0x00100003
	elseif 名称 == "飞燕女_黄金狮子_站立"	 then
		return 0x00100000
	elseif 名称 == "飞燕女_黄金狮子_奔跑"	 then
		return 0x00100001
	elseif 名称 == "骨精灵_黄金狮子_站立"  	 then
		return 0x00100000
	elseif 名称 == "骨精灵_黄金狮子_奔跑"  	 then
		return 0x00100001
	elseif 名称 == "影精灵_黄金狮子_站立"  	 then
		return 0x00100000
	elseif 名称 == "影精灵_黄金狮子_奔跑"  	 then
		return 0x00100001
	elseif 名称 == "英女侠_黄金狮子_站立"	 then
		return 0x00100000
	elseif 名称 == "英女侠_黄金狮子_奔跑"	 then
		return 0x00100001
	elseif 名称 == "狐美人_黄金狮子_站立"	 then
		return 0x00100000
	elseif 名称 == "狐美人_黄金狮子_奔跑"	 then
		return 0x00100001
	elseif 名称 == "虎头怪_黄金狮子_站立"	 then
		return 0x00100004
	elseif 名称 == "虎头怪_黄金狮子_奔跑"	 then
		return 0x00100005
	elseif 名称 == "巨魔王_黄金狮子_站立"	 then
		return 0x00100006
	elseif 名称 == "巨魔王_黄金狮子_奔跑"	 then
		return 0x00100007
	elseif 名称 == "龙太子_黄金狮子_站立"	 then
		return 0x00100002
	elseif 名称 == "龙太子_黄金狮子_奔跑"	 then
		return 0x00100003
	elseif 名称 == "神天兵_黄金狮子_站立"	 then
		return 0x00100002
	elseif 名称 == "神天兵_黄金狮子_奔跑"	 then
		return 0x00100003
	elseif 名称 == "舞天姬_黄金狮子_站立"	 then
		return 0x00100000
	elseif 名称 == "舞天姬_黄金狮子_奔跑"	 then
		return 0x00100001
	elseif 名称 == "逍遥生_黄金狮子_站立"	 then
		return 0x00100002
	elseif 名称 == "逍遥生_黄金狮子_奔跑"   then
		return 0x00100003
	elseif 名称 == "玄彩娥_黄金狮子_站立"	 then
		return 0x00100000
	elseif 名称 == "玄彩娥_黄金狮子_奔跑"	 then
		return 0x00100001
	elseif 名称 == "巫蛮儿_黄金狮子_站立"	 then
		return 0x00100000
	elseif 名称 == "巫蛮儿_黄金狮子_奔跑"	 then
		return 0x00100001
	elseif 名称 == "羽灵神_黄金狮子_站立"	 then
		return 0x00100002
	elseif 名称 == "羽灵神_黄金狮子_奔跑"	 then
		return 0x00100003
	elseif 名称 == "杀破狼_黄金狮子_站立"	 then
		return 0x00100002
	elseif 名称 == "杀破狼_黄金狮子_奔跑"	 then
		return 0x00100003
	elseif 名称 == "鬼潇潇_黄金狮子_站立"	 then
		return 0x00100000
	elseif 名称 == "鬼潇潇_黄金狮子_奔跑"	 then
		return 0x00100001
	elseif 名称 == "偃无师_黄金狮子_站立"	 then
		return 0x00100002
	elseif 名称 == "偃无师_黄金狮子_奔跑"	 then
		return 0x00100003
	elseif 名称 == "桃夭夭_黄金狮子_站立"	 then
		return 0x00100000
	elseif 名称 == "桃夭夭_黄金狮子_奔跑"	 then
		return 0x00100001

	elseif 名称 == "剑侠客_七彩祥云_站立"	 then
		return 0x00100016
	elseif 名称 == "剑侠客_七彩祥云_奔跑"	 then
		return 0x00100017
	elseif 名称 == "飞燕女_七彩祥云_站立"	 then
		return 0x00100012
	elseif 名称 == "飞燕女_七彩祥云_奔跑"	 then
		return 0x00100013
	elseif 名称 == "骨精灵_七彩祥云_站立"  	 then
		return 0x00100012
	elseif 名称 == "骨精灵_七彩祥云_奔跑"  	 then
		return 0x00100013
	elseif 名称 == "影精灵_七彩祥云_站立"  	 then
		return 0x00100012
	elseif 名称 == "影精灵_七彩祥云_奔跑"  	 then
		return 0x00100013
	elseif 名称 == "英女侠_七彩祥云_站立"	 then
		return 0x00100012
	elseif 名称 == "英女侠_七彩祥云_奔跑"	 then
		return 0x00100013
	elseif 名称 == "狐美人_七彩祥云_站立"	 then
		return 0x00100012
	elseif 名称 == "狐美人_七彩祥云_奔跑"	 then
		return 0x00100013
	elseif 名称 == "虎头怪_七彩祥云_站立"	 then
		return 0x00100014
	elseif 名称 == "虎头怪_七彩祥云_奔跑"	 then
		return 0x00100015
	elseif 名称 == "巨魔王_七彩祥云_站立"	 then
		return 0x00100010
	elseif 名称 == "巨魔王_七彩祥云_奔跑"	 then
		return 0x00100011
	elseif 名称 == "龙太子_七彩祥云_站立"	 then
		return 0x00100016
	elseif 名称 == "龙太子_七彩祥云_奔跑"	 then
		return 0x00100017
	elseif 名称 == "神天兵_七彩祥云_站立"	 then
		return 0x00100016
	elseif 名称 == "神天兵_七彩祥云_奔跑"	 then
		return 0x00100017
	elseif 名称 == "舞天姬_七彩祥云_站立"	 then
		return 0x00100012
	elseif 名称 == "舞天姬_七彩祥云_奔跑"	 then
		return 0x00100013
	elseif 名称 == "逍遥生_七彩祥云_站立"	 then
		return 0x00100016
	elseif 名称 == "逍遥生_七彩祥云_奔跑"   then
		return 0x00100017
	elseif 名称 == "玄彩娥_七彩祥云_站立"	 then
		return 0x00100012
	elseif 名称 == "玄彩娥_七彩祥云_奔跑"	 then
		return 0x00100013
	elseif 名称 == "巫蛮儿_七彩祥云_站立"	 then
		return 0x00100012
	elseif 名称 == "巫蛮儿_七彩祥云_奔跑"	 then
		return 0x00100013
	elseif 名称 == "羽灵神_七彩祥云_站立"	 then
		return 0x00100016
	elseif 名称 == "羽灵神_七彩祥云_奔跑"	 then
		return 0x00100017
	elseif 名称 == "杀破狼_七彩祥云_站立"	 then
		return 0x00100016
	elseif 名称 == "杀破狼_七彩祥云_奔跑"	 then
		return 0x00100017
	elseif 名称 == "鬼潇潇_七彩祥云_站立"	 then
		return 0x00100012
	elseif 名称 == "鬼潇潇_七彩祥云_奔跑"	 then
		return 0x00100013
	elseif 名称 == "偃无师_七彩祥云_站立"	 then
		return 0x00100016
	elseif 名称 == "偃无师_七彩祥云_奔跑"	 then
		return 0x00100017
	elseif 名称 == "桃夭夭_七彩祥云_站立"	 then
		return 0x00100012
	elseif 名称 == "桃夭夭_七彩祥云_奔跑"	 then
		return 0x00100013

	elseif 名称 == "剑侠客_粉红火鸡_站立"	 then
		return 0x00100028
	elseif 名称 == "剑侠客_粉红火鸡_奔跑"	 then
		return 0x00100029
	elseif 名称 == "飞燕女_粉红火鸡_站立"	 then
		return 0x00100030
	elseif 名称 == "飞燕女_粉红火鸡_奔跑"	 then
		return 0x00100031
	elseif 名称 == "骨精灵_粉红火鸡_站立"  	 then
		return 0x00100030
	elseif 名称 == "骨精灵_粉红火鸡_奔跑"  	 then
		return 0x00100031
	elseif 名称 == "影精灵_粉红火鸡_站立"  	 then
		return 0x00100030
	elseif 名称 == "影精灵_粉红火鸡_奔跑"  	 then
		return 0x00100031
	elseif 名称 == "英女侠_粉红火鸡_站立"	 then
		return 0x00100030
	elseif 名称 == "英女侠_粉红火鸡_奔跑"	 then
		return 0x00100031
	elseif 名称 == "狐美人_粉红火鸡_站立"	 then
		return 0x00100030
	elseif 名称 == "狐美人_粉红火鸡_奔跑"	 then
		return 0x00100031
	elseif 名称 == "虎头怪_粉红火鸡_站立"	 then
		return 0x00100026
	elseif 名称 == "虎头怪_粉红火鸡_奔跑"	 then
		return 0x00100027
	elseif 名称 == "巨魔王_粉红火鸡_站立"	 then
		return 0x00100024
	elseif 名称 == "巨魔王_粉红火鸡_奔跑"	 then
		return 0x00100025
	elseif 名称 == "龙太子_粉红火鸡_站立"	 then
		return 0x00100028
	elseif 名称 == "龙太子_粉红火鸡_奔跑"	 then
		return 0x00100029
	elseif 名称 == "神天兵_粉红火鸡_站立"	 then
		return 0x00100028
	elseif 名称 == "神天兵_粉红火鸡_奔跑"	 then
		return 0x00100029
	elseif 名称 == "舞天姬_粉红火鸡_站立"	 then
		return 0x00100030
	elseif 名称 == "舞天姬_粉红火鸡_奔跑"	 then
		return 0x00100031
	elseif 名称 == "逍遥生_粉红火鸡_站立"	 then
		return 0x00100028
	elseif 名称 == "逍遥生_粉红火鸡_奔跑"   then
		return 0x00100029
	elseif 名称 == "玄彩娥_粉红火鸡_站立"	 then
		return 0x00100030
	elseif 名称 == "玄彩娥_粉红火鸡_奔跑"	 then
		return 0x00100031
	elseif 名称 == "巫蛮儿_粉红火鸡_站立"	 then
		return 0x00100030
	elseif 名称 == "巫蛮儿_粉红火鸡_奔跑"	 then
		return 0x00100031
	elseif 名称 == "羽灵神_粉红火鸡_站立"	 then
		return 0x00100028
	elseif 名称 == "羽灵神_粉红火鸡_奔跑"	 then
		return 0x00100029
	elseif 名称 == "杀破狼_粉红火鸡_站立"	 then
		return 0x00100028
	elseif 名称 == "杀破狼_粉红火鸡_奔跑"	 then
		return 0x00100029
	elseif 名称 == "鬼潇潇_粉红火鸡_站立"	 then
		return 0x00100030
	elseif 名称 == "鬼潇潇_粉红火鸡_奔跑"	 then
		return 0x00100031
	elseif 名称 == "偃无师_粉红火鸡_站立"	 then
		return 0x00100028
	elseif 名称 == "偃无师_粉红火鸡_奔跑"	 then
		return 0x00100029
	elseif 名称 == "桃夭夭_粉红火鸡_站立"	 then
		return 0x00100030
	elseif 名称 == "桃夭夭_粉红火鸡_奔跑"	 then
		return 0x00100031

	elseif 名称 == "剑侠客_彩虹毛驴_站立" or 名称 == "龙太子_彩虹毛驴_站立" or 名称 == "神天兵_彩虹毛驴_站立" or 名称 == "逍遥生_彩虹毛驴_站立"
		or 名称 == "羽灵神_彩虹毛驴_站立" or 名称 == "杀破狼_彩虹毛驴_站立" or 名称 == "偃无师_彩虹毛驴_站立" then
		return 0x00100038
	elseif 名称 == "剑侠客_彩虹毛驴_奔跑" or 名称 == "龙太子_彩虹毛驴_奔跑" or 名称 == "神天兵_彩虹毛驴_奔跑" or 名称 == "逍遥生_彩虹毛驴_奔跑"
	    or 名称 == "羽灵神_彩虹毛驴_奔跑" or 名称 == "杀破狼_彩虹毛驴_奔跑" or 名称 == "偃无师_彩虹毛驴_奔跑" then
		return 0x00100039
	elseif 名称 == "飞燕女_彩虹毛驴_站立" or 名称 == "骨精灵_彩虹毛驴_站立" or 名称 == "影精灵_彩虹毛驴_站立" or 名称 == "英女侠_彩虹毛驴_站立" or 名称 == "狐美人_彩虹毛驴_站立"
		or 名称 == "舞天姬_彩虹毛驴_站立" or 名称 == "玄彩娥_彩虹毛驴_站立" or 名称 == "巫蛮儿_彩虹毛驴_站立" or 名称 == "鬼潇潇_彩虹毛驴_站立"
		or 名称 == "桃夭夭_彩虹毛驴_站立" then
		return 0x00100032
	elseif 名称 == "飞燕女_彩虹毛驴_奔跑" or 名称 == "骨精灵_彩虹毛驴_奔跑" or 名称 == "影精灵_彩虹毛驴_奔跑"  or 名称 == "英女侠_彩虹毛驴_奔跑" or 名称 == "狐美人_彩虹毛驴_奔跑"
	    or 名称 == "舞天姬_彩虹毛驴_奔跑" or 名称 == "玄彩娥_彩虹毛驴_奔跑" or 名称 == "巫蛮儿_彩虹毛驴_奔跑" or 名称 == "鬼潇潇_彩虹毛驴_奔跑"
	    or 名称 == "桃夭夭_彩虹毛驴_奔跑" then
		return 0x00100033
	elseif 名称 == "虎头怪_彩虹毛驴_站立"	 then
		return 0x00100034
	elseif 名称 == "虎头怪_彩虹毛驴_奔跑"	 then
		return 0x00100035
	elseif 名称 == "巨魔王_彩虹毛驴_站立"	 then
		return 0x00100036
	elseif 名称 == "巨魔王_彩虹毛驴_奔跑"	 then
		return 0x00100037

	elseif 名称 == "剑侠客_粉嫩小猪_站立" or 名称 == "龙太子_粉嫩小猪_站立" or 名称 == "神天兵_粉嫩小猪_站立" or 名称 == "逍遥生_粉嫩小猪_站立"
		or 名称 == "羽灵神_粉嫩小猪_站立" or 名称 == "杀破狼_粉嫩小猪_站立" or 名称 == "偃无师_粉嫩小猪_站立" then
		return 0x00100046
	elseif 名称 == "剑侠客_粉嫩小猪_奔跑" or 名称 == "龙太子_粉嫩小猪_奔跑" or 名称 == "神天兵_粉嫩小猪_奔跑" or 名称 == "逍遥生_粉嫩小猪_奔跑"
	    or 名称 == "羽灵神_粉嫩小猪_奔跑" or 名称 == "杀破狼_粉嫩小猪_奔跑" or 名称 == "偃无师_粉嫩小猪_奔跑" then
		return 0x00100047
	elseif 名称 == "飞燕女_粉嫩小猪_站立" or 名称 == "骨精灵_粉嫩小猪_站立" or 名称 == "影精灵_粉嫩小猪_站立" or 名称 == "英女侠_粉嫩小猪_站立" or 名称 == "狐美人_粉嫩小猪_站立"
		or 名称 == "舞天姬_粉嫩小猪_站立" or 名称 == "玄彩娥_粉嫩小猪_站立" or 名称 == "巫蛮儿_粉嫩小猪_站立" or 名称 == "鬼潇潇_粉嫩小猪_站立"
		or 名称 == "桃夭夭_粉嫩小猪_站立" then
		return 0x00100040
	elseif 名称 == "飞燕女_粉嫩小猪_奔跑" or 名称 == "骨精灵_粉嫩小猪_奔跑"  or 名称 == "影精灵_粉嫩小猪_奔跑" or 名称 == "英女侠_粉嫩小猪_奔跑" or 名称 == "狐美人_粉嫩小猪_奔跑"
	    or 名称 == "舞天姬_粉嫩小猪_奔跑" or 名称 == "玄彩娥_粉嫩小猪_奔跑" or 名称 == "巫蛮儿_粉嫩小猪_奔跑" or 名称 == "鬼潇潇_粉嫩小猪_奔跑"
	    or 名称 == "桃夭夭_粉嫩小猪_奔跑" then
		return 0x00100041
	elseif 名称 == "虎头怪_粉嫩小猪_站立"	 then
		return 0x00100042
	elseif 名称 == "虎头怪_粉嫩小猪_奔跑"	 then
		return 0x00100043
	elseif 名称 == "巨魔王_粉嫩小猪_站立"	 then
		return 0x00100044
	elseif 名称 == "巨魔王_粉嫩小猪_奔跑"	 then
		return 0x00100045

	elseif 名称 == "剑侠客_水晶芭蕉_站立" or 名称 == "龙太子_水晶芭蕉_站立" or 名称 == "神天兵_水晶芭蕉_站立" or 名称 == "逍遥生_水晶芭蕉_站立"
		or 名称 == "羽灵神_水晶芭蕉_站立" or 名称 == "杀破狼_水晶芭蕉_站立" or 名称 == "偃无师_水晶芭蕉_站立" then
		return 0x30303090
	elseif 名称 == "剑侠客_水晶芭蕉_奔跑" or 名称 == "龙太子_水晶芭蕉_奔跑" or 名称 == "神天兵_水晶芭蕉_奔跑" or 名称 == "逍遥生_水晶芭蕉_奔跑"
	    or 名称 == "羽灵神_水晶芭蕉_奔跑" or 名称 == "杀破狼_水晶芭蕉_奔跑" or 名称 == "偃无师_水晶芭蕉_奔跑" then
		return 0x30303091
	elseif 名称 == "飞燕女_水晶芭蕉_站立" or 名称 == "骨精灵_水晶芭蕉_站立"  or 名称 == "影精灵_水晶芭蕉_站立" or 名称 == "英女侠_水晶芭蕉_站立" or 名称 == "狐美人_水晶芭蕉_站立"
		or 名称 == "舞天姬_水晶芭蕉_站立" or 名称 == "玄彩娥_水晶芭蕉_站立" or 名称 == "巫蛮儿_水晶芭蕉_站立" or 名称 == "鬼潇潇_水晶芭蕉_站立"
		or 名称 == "桃夭夭_水晶芭蕉_站立" then
		return 0x30303088
	elseif 名称 == "飞燕女_水晶芭蕉_奔跑" or 名称 == "骨精灵_水晶芭蕉_奔跑"  or 名称 == "影精灵_水晶芭蕉_奔跑"  or 名称 == "英女侠_水晶芭蕉_奔跑" or 名称 == "狐美人_水晶芭蕉_奔跑"
	    or 名称 == "舞天姬_水晶芭蕉_奔跑" or 名称 == "玄彩娥_水晶芭蕉_奔跑" or 名称 == "巫蛮儿_水晶芭蕉_奔跑" or 名称 == "鬼潇潇_水晶芭蕉_奔跑"
	    or 名称 == "桃夭夭_水晶芭蕉_奔跑" then
		return 0x30303089
	elseif 名称 == "虎头怪_水晶芭蕉_站立"	 then
		return 0x30303084
	elseif 名称 == "虎头怪_水晶芭蕉_奔跑"	 then
		return 0x30303085
	elseif 名称 == "巨魔王_水晶芭蕉_站立"	 then
		return 0x30303086
	elseif 名称 == "巨魔王_水晶芭蕉_奔跑"	 then
		return 0x30303087

	elseif 名称 == "剑侠客_深海狂鲨_站立" or 名称 == "龙太子_深海狂鲨_站立" or 名称 == "神天兵_深海狂鲨_站立" or 名称 == "逍遥生_深海狂鲨_站立"
		or 名称 == "羽灵神_深海狂鲨_站立" or 名称 == "杀破狼_深海狂鲨_站立" or 名称 == "偃无师_深海狂鲨_站立" then
		return 0xAABBCC06
	elseif 名称 == "剑侠客_深海狂鲨_奔跑" or 名称 == "龙太子_深海狂鲨_奔跑" or 名称 == "神天兵_深海狂鲨_奔跑" or 名称 == "逍遥生_深海狂鲨_奔跑"
	    or 名称 == "羽灵神_深海狂鲨_奔跑" or 名称 == "杀破狼_深海狂鲨_奔跑" or 名称 == "偃无师_深海狂鲨_奔跑" then
		return 0xAABBCC07
	elseif 名称 == "飞燕女_深海狂鲨_站立" or 名称 == "骨精灵_深海狂鲨_站立" or 名称 == "影精灵_深海狂鲨_站立" or 名称 == "英女侠_深海狂鲨_站立" or 名称 == "狐美人_深海狂鲨_站立"
		or 名称 == "舞天姬_深海狂鲨_站立" or 名称 == "玄彩娥_深海狂鲨_站立" or 名称 == "巫蛮儿_深海狂鲨_站立" or 名称 == "鬼潇潇_深海狂鲨_站立"
		or 名称 == "桃夭夭_深海狂鲨_站立" then
		return 0xAABBCC00
	elseif 名称 == "飞燕女_深海狂鲨_奔跑" or 名称 == "骨精灵_深海狂鲨_奔跑"  or 名称 == "影精灵_深海狂鲨_奔跑"  or 名称 == "英女侠_深海狂鲨_奔跑" or 名称 == "狐美人_深海狂鲨_奔跑"
	    or 名称 == "舞天姬_深海狂鲨_奔跑" or 名称 == "玄彩娥_深海狂鲨_奔跑" or 名称 == "巫蛮儿_深海狂鲨_奔跑" or 名称 == "鬼潇潇_深海狂鲨_奔跑"
	    or 名称 == "桃夭夭_深海狂鲨_奔跑" then
		return 0xAABBCC01
	elseif 名称 == "虎头怪_深海狂鲨_站立"	 then
		return 0xAABBCC02
	elseif 名称 == "虎头怪_深海狂鲨_奔跑"	 then
		return 0xAABBCC03
	elseif 名称 == "巨魔王_深海狂鲨_站立"	 then
		return 0xAABBCC04
	elseif 名称 == "巨魔王_深海狂鲨_奔跑"	 then
		return 0xAABBCC05

    elseif 名称 == "剑侠客_雷霆黑豹_站立" or 名称 == "龙太子_雷霆黑豹_站立" or 名称 == "神天兵_雷霆黑豹_站立" or 名称 == "逍遥生_雷霆黑豹_站立"
		or 名称 == "羽灵神_雷霆黑豹_站立" or 名称 == "杀破狼_雷霆黑豹_站立" or 名称 == "偃无师_雷霆黑豹_站立" then
		return 0xAABBEE06
	elseif 名称 == "剑侠客_雷霆黑豹_奔跑" or 名称 == "龙太子_雷霆黑豹_奔跑" or 名称 == "神天兵_雷霆黑豹_奔跑" or 名称 == "逍遥生_雷霆黑豹_奔跑"
	    or 名称 == "羽灵神_雷霆黑豹_奔跑" or 名称 == "杀破狼_雷霆黑豹_奔跑" or 名称 == "偃无师_雷霆黑豹_奔跑" then
		return 0xAABBEE07
	elseif 名称 == "飞燕女_雷霆黑豹_站立" or 名称 == "骨精灵_雷霆黑豹_站立"  or 名称 == "影精灵_雷霆黑豹_站立" or 名称 == "英女侠_雷霆黑豹_站立" or 名称 == "狐美人_雷霆黑豹_站立"
		or 名称 == "舞天姬_雷霆黑豹_站立" or 名称 == "玄彩娥_雷霆黑豹_站立" or 名称 == "巫蛮儿_雷霆黑豹_站立" or 名称 == "鬼潇潇_雷霆黑豹_站立"
		or 名称 == "桃夭夭_雷霆黑豹_站立" then
		return 0xAABBEE00
	elseif 名称 == "飞燕女_雷霆黑豹_奔跑" or 名称 == "骨精灵_雷霆黑豹_奔跑" or 名称 == "影精灵_雷霆黑豹_奔跑"  or 名称 == "英女侠_雷霆黑豹_奔跑" or 名称 == "狐美人_雷霆黑豹_奔跑"
	    or 名称 == "舞天姬_雷霆黑豹_奔跑" or 名称 == "玄彩娥_雷霆黑豹_奔跑" or 名称 == "巫蛮儿_雷霆黑豹_奔跑" or 名称 == "鬼潇潇_雷霆黑豹_奔跑"
	    or 名称 == "桃夭夭_雷霆黑豹_奔跑" then
		return 0xAABBEE01
	elseif 名称 == "虎头怪_雷霆黑豹_站立"	 then
		return 0xAABBEE02
	elseif 名称 == "虎头怪_雷霆黑豹_奔跑"	 then
		return 0xAABBEE03
	elseif 名称 == "巨魔王_雷霆黑豹_站立"	 then
		return 0xAABBEE04
	elseif 名称 == "巨魔王_雷霆黑豹_奔跑"	 then
		return 0xAABBEE05

	elseif 名称 == "剑侠客_九尾妖狐_站立" or 名称 == "龙太子_九尾妖狐_站立" or 名称 == "神天兵_九尾妖狐_站立" or 名称 == "逍遥生_九尾妖狐_站立"
		or 名称 == "羽灵神_九尾妖狐_站立" or 名称 == "杀破狼_九尾妖狐_站立" or 名称 == "偃无师_九尾妖狐_站立" then
		return 0xAABBFF06
	elseif 名称 == "剑侠客_九尾妖狐_奔跑" or 名称 == "龙太子_九尾妖狐_奔跑" or 名称 == "神天兵_九尾妖狐_奔跑" or 名称 == "逍遥生_九尾妖狐_奔跑"
	    or 名称 == "羽灵神_九尾妖狐_奔跑" or 名称 == "杀破狼_九尾妖狐_奔跑" or 名称 == "偃无师_九尾妖狐_奔跑" then
		return 0xAABBFF07
	elseif 名称 == "飞燕女_九尾妖狐_站立" or 名称 == "骨精灵_九尾妖狐_站立" or 名称 == "影精灵_九尾妖狐_站立" or 名称 == "英女侠_九尾妖狐_站立" or 名称 == "狐美人_九尾妖狐_站立"
		or 名称 == "舞天姬_九尾妖狐_站立" or 名称 == "玄彩娥_九尾妖狐_站立" or 名称 == "巫蛮儿_九尾妖狐_站立" or 名称 == "鬼潇潇_九尾妖狐_站立"
		or 名称 == "桃夭夭_九尾妖狐_站立" then
		return 0xAABBFF00
	elseif 名称 == "飞燕女_九尾妖狐_奔跑" or 名称 == "骨精灵_九尾妖狐_奔跑"  or 名称 == "影精灵_九尾妖狐_奔跑"  or 名称 == "英女侠_九尾妖狐_奔跑" or 名称 == "狐美人_九尾妖狐_奔跑"
	    or 名称 == "舞天姬_九尾妖狐_奔跑" or 名称 == "玄彩娥_九尾妖狐_奔跑" or 名称 == "巫蛮儿_九尾妖狐_奔跑" or 名称 == "鬼潇潇_九尾妖狐_奔跑"
	    or 名称 == "桃夭夭_九尾妖狐_奔跑" then
		return 0xAABBFF01
	elseif 名称 == "虎头怪_九尾妖狐_站立"	 then
		return 0xAABBFF02
	elseif 名称 == "虎头怪_九尾妖狐_奔跑"	 then
		return 0xAABBFF03
	elseif 名称 == "巨魔王_九尾妖狐_站立"	 then
		return 0xAABBFF04
	elseif 名称 == "巨魔王_九尾妖狐_奔跑"	 then
		return 0xAABBFF05

	elseif 名称 == "剑侠客_砖石小马_站立" or 名称 == "龙太子_砖石小马_站立" or 名称 == "神天兵_砖石小马_站立" or 名称 == "逍遥生_砖石小马_站立"
		or 名称 == "羽灵神_砖石小马_站立" or 名称 == "杀破狼_砖石小马_站立" or 名称 == "偃无师_砖石小马_站立" then
		return 0xAABBDD06
	elseif 名称 == "剑侠客_砖石小马_奔跑" or 名称 == "龙太子_砖石小马_奔跑" or 名称 == "神天兵_砖石小马_奔跑" or 名称 == "逍遥生_砖石小马_奔跑"
	    or 名称 == "羽灵神_砖石小马_奔跑" or 名称 == "杀破狼_砖石小马_奔跑" or 名称 == "偃无师_砖石小马_奔跑" then
		return 0xAABBDD07
	elseif 名称 == "飞燕女_砖石小马_站立" or 名称 == "骨精灵_砖石小马_站立" or 名称 == "影精灵_砖石小马_站立" or 名称 == "英女侠_砖石小马_站立" or 名称 == "狐美人_砖石小马_站立"
		or 名称 == "舞天姬_砖石小马_站立" or 名称 == "玄彩娥_砖石小马_站立" or 名称 == "巫蛮儿_砖石小马_站立" or 名称 == "鬼潇潇_砖石小马_站立"
		or 名称 == "桃夭夭_砖石小马_站立" then
		return 0xAABBDD00
	elseif 名称 == "飞燕女_砖石小马_奔跑" or 名称 == "骨精灵_砖石小马_奔跑" or 名称 == "影精灵_砖石小马_奔跑"  or 名称 == "英女侠_砖石小马_奔跑" or 名称 == "狐美人_砖石小马_奔跑"
	    or 名称 == "舞天姬_砖石小马_奔跑" or 名称 == "玄彩娥_砖石小马_奔跑" or 名称 == "巫蛮儿_砖石小马_奔跑" or 名称 == "鬼潇潇_砖石小马_奔跑"
	    or 名称 == "桃夭夭_砖石小马_奔跑" then
		return 0xAABBDD01
	elseif 名称 == "虎头怪_砖石小马_站立"	 then
		return 0xAABBDD02
	elseif 名称 == "虎头怪_砖石小马_奔跑"	 then
		return 0xAABBDD03
	elseif 名称 == "巨魔王_砖石小马_站立"	 then
		return 0xAABBDD04
	elseif 名称 == "巨魔王_砖石小马_奔跑"	 then
		return 0xAABBDD05
     end
    return ""
end




function 引擎.坐骑库(id,zq,sp)
	if zq==nil then
	    return
	end
	local zqs = {
		宝贝葫芦 = {0x63C1AA04,0x939B6AA2,1,"vvxxzcom/祥瑞坐骑.wdf"},
		神气小龟 = {0xE88353,0x702610D3,2,"vvxxzcom/祥瑞坐骑.wdf"},
		汗血宝马 = {0x7B49FA9A,0x3F76F5B2,3,"vvxxzcom/祥瑞坐骑.wdf"},
		欢喜羊羊 = {0x8D4DBAAE,0x2636063C,4,"vvxxzcom/祥瑞坐骑.wdf"},
		魔力斗兽 = {0x4B0E16F1,0x28F7499E,3,"vvxxzcom/祥瑞坐骑.wdf"},
		披甲战狼 = {0x3B0CC9,0xF6B76F79,4,"vvxxzcom/祥瑞坐骑.wdf"},
		闲云野鹤 = {0x49CAB729,0x1544FBAD,3,"vvxxzcom/祥瑞坐骑.wdf"},
		云魅仙鹿 = {0x621ECF47,0x98D7DB2,4,"vvxxzcom/祥瑞坐骑.wdf"},
		七彩神驴 = {0x03633E07,0x01040DCE,4,"vvxxzcom/祥瑞坐骑.wdf"},
	}
	local sps = {
		展翅高飞 = {0x2DC16EF4,0x47A59E6C,"vvxxzcom/祥瑞坐骑.wdf"},
		旗开得胜 = {0x4FB7A645,0xC89B8D7B,"vvxxzcom/祥瑞坐骑.wdf"},
		霸王雄风 = {0x8AC5514E,0xD30116BE,"vvxxzcom/祥瑞坐骑.wdf"},
		独眼观天 = {0xCB41BF07,0x6D415352,"vvxxzcom/祥瑞坐骑.wdf"},
		威武不屈 = {0xE385373B,0x71FE0155,"vvxxzcom/祥瑞坐骑.wdf"},
		深藏不露 = {0x2529E5A5,0x51C03CD4,"vvxxzcom/祥瑞坐骑.wdf"},
		异域浓情 = {0xE8B35E96,0x3949C769,"vvxxzcom/祥瑞坐骑.wdf"},
		流星天马 = {0x72489CFD,0x4D136355,"vvxxzcom/祥瑞坐骑.wdf"},
		威猛将军 = {0x5BDBA7CB,0x5CDC5A5E,"vvxxzcom/祥瑞坐骑.wdf"},
		知情达理 = {0xCCBF24B8,0xFE4B37F2,"vvxxzcom/祥瑞坐骑.wdf"},
		气宇轩昂 = {0xEC4C09DF,0x57B096DF,"vvxxzcom/祥瑞坐骑.wdf"},
		如花似玉 = {0xA6966FD2,0xCA8864D1,"vvxxzcom/祥瑞坐骑.wdf"},
		傲视天下 = {0xBB906984,0x2549904, "vvxxzcom/祥瑞坐骑.wdf"},
		铁血豪情 = {0x742FBF19,0x103FFB93,"vvxxzcom/祥瑞坐骑.wdf"},
		唯我独尊 = {0x7F6FFC35,0x716B5DC1,"vvxxzcom/祥瑞坐骑.wdf"},
		异域风情 = {0x1FED0CD8,0xD8EB6880,"vvxxzcom/祥瑞坐骑.wdf"},
		叱咤风云 = {0xAB007164,0x2E177381,"vvxxzcom/祥瑞坐骑.wdf"},
		假面勇者 = {0xE7CB8205,0xE615404,"vvxxzcom/祥瑞坐骑.wdf"},
		霓裳魅影 = {0xE0CB07C8,0xD9D958E6,"vvxxzcom/祥瑞坐骑.wdf"},
		披星戴月 = {0x8ED6D8CC,0xC5D8F53D,"vvxxzcom/祥瑞坐骑.wdf"},
		烈焰燃情 = {0x8C575D26,0x7B15590A,"vvxxzcom/祥瑞坐骑.wdf"},
		天雨流芳 = {0x503F394B,0x23BF657B,"vvxxzcom/祥瑞坐骑.wdf"},
		灵光再现 = {0xC4D118C5,0xCBC6930A,"vvxxzcom/祥瑞坐骑.wdf"},
		倾国倾城 = {0x1F01B8BE,0xBDA4DDAB,"vvxxzcom/祥瑞坐骑.wdf"},
		空 = {}
	}
	local scs
	if id == "飞燕女" then
		scs = {{0x4492502E,0xF6D6D5E6,"vvxxzcom/祥瑞坐骑.wdf"},{0xDCB946EC,0xDCC84D4E,"vvxxzcom/祥瑞坐骑.wdf"},{0xDF01F29D,0xA77B55E4,"vvxxzcom/祥瑞坐骑.wdf"},{0x6E0AD379,0x87C7A650,"vvxxzcom/祥瑞坐骑.wdf"}}
	elseif id == "英女侠" then
		scs = {{0xD43912A9,0xD2D4CAD3,"vvxxzcom/祥瑞坐骑.wdf"},{0x70291C50,0x30CABF19,"vvxxzcom/祥瑞坐骑.wdf"},{0x726C392E,0x68FB1969,"vvxxzcom/祥瑞坐骑.wdf"},{0x2474769B,0xACD868DE,"vvxxzcom/祥瑞坐骑.wdf"}}
	elseif id == "巫蛮儿" then
		scs = {{0xF2BC9369,0xB11F6642,"vvxxzcom/祥瑞坐骑.wdf"},{0x9B73C75F,0xDA0A8B06,"vvxxzcom/祥瑞坐骑.wdf"},{0x2F5EAD3F,0x499F9D37,"vvxxzcom/祥瑞坐骑.wdf"},{0xCCC0985C,0xC8F56BA3,"vvxxzcom/祥瑞坐骑.wdf"}}
	elseif id == "偃无师" then
		scs = {{0x00000110,0x00000111,"vvxxzcom/祥瑞坐骑.wdf"},{0x00000110,0x00000111,"vvxxzcom/祥瑞坐骑.wdf"},{0x00000110,0x00000111,"vvxxzcom/祥瑞坐骑.wdf"},{0x00000110,0x00000111,"vvxxzcom/祥瑞坐骑.wdf"}}
	elseif id == "逍遥生" then
		scs = {{0xA35491C9,0x49D7C76E,"vvxxzcom/祥瑞坐骑.wdf"},{0xB770EAD4,0x9A1479D8,"vvxxzcom/祥瑞坐骑.wdf"},{0x76D629EA,0xFB50C58F,"vvxxzcom/祥瑞坐骑.wdf"},{0x3D392EF4,0xA5E02A65,"vvxxzcom/祥瑞坐骑.wdf"}}
	elseif id == "剑侠客" then
		scs = {{0x67101CB7,0x9C8790BA,"vvxxzcom/祥瑞坐骑.wdf"},{0x32DA9583,0xEC9AC961,"vvxxzcom/祥瑞坐骑.wdf"},{0x766731D,0x8C50358A,"vvxxzcom/祥瑞坐骑.wdf"},{0xA95A126D,0x513DDE6C,"vvxxzcom/祥瑞坐骑.wdf"}}
	elseif id == "狐美人" then
		scs = {{0xE3123BDA,0x956305B5,"vvxxzcom/祥瑞坐骑.wdf"},{0xFB798485,0xD1997415,"vvxxzcom/祥瑞坐骑.wdf"},{0x64C21A63,0xD5D2FA14,"vvxxzcom/祥瑞坐骑.wdf"},{0xBCD86DDA,0xF0062006,"vvxxzcom/祥瑞坐骑.wdf"}}
	elseif id == "骨精灵" then
		scs = {{0xBEEF3795,0x3C6BF98F,"vvxxzcom/祥瑞坐骑.wdf"},{0xE2C1CDE4,0xBAD0F711,"vvxxzcom/祥瑞坐骑.wdf"},{0x75B09FA1,0x5E5736EE,"vvxxzcom/祥瑞坐骑.wdf"},{0x83DD50D3,0xB84C7C38,"vvxxzcom/祥瑞坐骑.wdf"}}
	elseif id == "影精灵" then
		scs = {{0xBEEF3795,0x3C6BF98F,"vvxxzcom/祥瑞坐骑.wdf"},{0xE2C1CDE4,0xBAD0F711,"vvxxzcom/祥瑞坐骑.wdf"},{0x75B09FA1,0x5E5736EE,"vvxxzcom/祥瑞坐骑.wdf"},{0x83DD50D3,0xB84C7C38,"vvxxzcom/祥瑞坐骑.wdf"}}
	elseif id == "鬼潇潇" then
		scs = {{0x00000118,0x00000119,"vvxxzcom/祥瑞坐骑.wdf"},{0x00000118,0x00000119,"vvxxzcom/祥瑞坐骑.wdf"},{0x00000118,0x00000119,"vvxxzcom/祥瑞坐骑.wdf"},{0x00000118,0x00000119,"vvxxzcom/祥瑞坐骑.wdf"}}
	elseif id == "杀破狼" then
		scs = {{0xE137A55D,0xDB553291,"vvxxzcom/祥瑞坐骑.wdf"},{0x8BEA762D,0x14EE7109,"vvxxzcom/祥瑞坐骑.wdf"},{0x46A79E5,0x3CD5444,"vvxxzcom/祥瑞坐骑.wdf"},{0xF974CEB,0x54A8F096,"vvxxzcom/祥瑞坐骑.wdf"}}
	elseif id == "巨魔王" then
		scs = {{0x21ED721D,0x5A05E1C0,"vvxxzcom/祥瑞坐骑.wdf"},{0x9DFEB143,0x77C20678,"vvxxzcom/祥瑞坐骑.wdf"},{0x1AF61311,0x6E370D46,"vvxxzcom/祥瑞坐骑.wdf"},{0xCC1426ED,0x39FE09DB,"vvxxzcom/祥瑞坐骑.wdf"}}
	elseif id == "虎头怪" then
		scs = {{0x99AD84CD,0x9FA6D533,"vvxxzcom/祥瑞坐骑.wdf"},{0xF56603D1,0x83DBBA94,"vvxxzcom/祥瑞坐骑.wdf"},{0x37FFB9DF,0x64426F93,"vvxxzcom/祥瑞坐骑.wdf"},{0x95BC0425,0xC6053278,"vvxxzcom/祥瑞坐骑.wdf"}}
	elseif id == "舞天姬" then
		scs = {{0x54DB4F4D,0xCB722714,"vvxxzcom/祥瑞坐骑.wdf"},{0xD92FC3DE,0x809F42FE,"vvxxzcom/祥瑞坐骑.wdf"},{0x212848A1,0xAAD7CB93,"vvxxzcom/祥瑞坐骑.wdf"},{0xB44DF735,0xECA5DB49,"vvxxzcom/祥瑞坐骑.wdf"}}
	elseif id == "玄彩娥" then
		scs = {{0x861EE4D9,0x9F2F9C11,"vvxxzcom/祥瑞坐骑.wdf"},{0x3316877C,0x31F77503,"vvxxzcom/祥瑞坐骑.wdf"},{0x779A3DF,0x622664DC,"vvxxzcom/祥瑞坐骑.wdf"},{0xA6FD7850,0xB9FD9DBD,"vvxxzcom/祥瑞坐骑.wdf"}}
	elseif id == "桃夭夭" then
		scs = {{0x00000109,0x00000112,"vvxxzcom/祥瑞坐骑.wdf"},{0x00000109,0x00000112,"vvxxzcom/祥瑞坐骑.wdf"},{0x00000109,0x00000112,"vvxxzcom/祥瑞坐骑.wdf"},{0x00000109,0x00000112,"vvxxzcom/祥瑞坐骑.wdf"}}
	elseif id == "羽灵神" then
		scs = {{0x7D31F43E,0x76E4E3D6,"vvxxzcom/祥瑞坐骑.wdf"},{0x7B86A5F4,0xE496A2D7,"vvxxzcom/祥瑞坐骑.wdf"},{0x8072202A,0x6528F013,"vvxxzcom/祥瑞坐骑.wdf"},{0x4BBD02E6,0x694A236B,"vvxxzcom/祥瑞坐骑.wdf"}}
	elseif id == "神天兵" then
		scs = {{0x77104303,0xBC38000F,"vvxxzcom/祥瑞坐骑.wdf"},{0xF4EF98B5,0x7C731501,"vvxxzcom/祥瑞坐骑.wdf"},{0x7F6D09AB,0x13AD1C23,"vvxxzcom/祥瑞坐骑.wdf"},{0x60A47C21,0x63930A54,"vvxxzcom/祥瑞坐骑.wdf"}}
	elseif id == "龙太子" then
		scs = {{0x4F27A59F,0x801F438D,"vvxxzcom/祥瑞坐骑.wdf"},{0x46F4FDF6,0xC9EF2751,"vvxxzcom/祥瑞坐骑.wdf"},{0x5B0EDDAD,0xB227D39F,"vvxxzcom/祥瑞坐骑.wdf"},{0x643F7DDE,0xCC8E0921,"vvxxzcom/祥瑞坐骑.wdf"}}
	end

	if zq==nil then
		tp.窗口.消息框:添加文本("坐骑zp1为空"..zq,"xt")
	elseif  zq~=nil and zqs[zq]==nil and 引擎.新增坐骑(sp,zq,"奔跑")==nil then
		tp.窗口.消息框:添加文本("坐骑zp2为空"..zq,"xt")
	elseif  zq~=nil and zqs[zq]~=nil and zqs[zq][3]==nil and 引擎.新增坐骑(sp,zq,"奔跑")==nil  then
		tp.窗口.消息框:添加文本("坐骑zp3为空"..zq,"xt")
	end
	if zqs[zq]==nil then
		return
	end
	local bh = zqs[zq][3]
	-- table.print(zqs[zq])
	return {坐骑资源=zqs[zq][4],坐骑站立=zqs[zq][1],坐骑行走=zqs[zq][2],人物资源=scs[bh][3],人物站立=scs[bh][1],人物行走=scs[bh][2],坐骑饰品站立=sps[sp][1],坐骑饰品行走=sps[sp][2],坐骑饰品资源=sps[sp][3]}
end

