--======================================================================--
-- @作者: www.wnfj.com，联系QQ：79550111
-- @创建时间:   2018-03-03 02:34:19
-- @Last Modified time: 2024-12-05 17:26:07
--======================================================================--
function 引擎.取头像(jp)
	if jp == "超级红孩儿" then
		jp="进阶小魔头"
	elseif  jp == "魔化毗舍童子" then
		jp="毗舍童子"
	end
	local jps = {}
	if jp == "虎头怪" then
		-- jps[1] = 4
		jps[2] = 1845064969
		jps[3] = 2571786186
		jps[4] = 1584078605
		jps[5] = 3359055550
		jps[6] = 865988562
		jps[7] = "wzife.wdf"
		jps[8] = 682815547
		jps[9] = "wzife.wd3"



	elseif jp == "影精灵" then
		jps[2] = 0x00000913 	--小头像
		jps[3] = 0x00000917 	---道具头像99*13
		jps[4] = 0x00000908 	--对话头像大
		jps[5] = 0x00000917  	--65*65
		jps[6] = 0X00000908		--可无	 --小小
		jps[7] = "vvxxzcom/yjl/cwmx.wdf"
		jps[8] = 0x00000909
		jps[9] = "wzife.wd3"
	elseif jp == "影精灵1" then
		jps[2] = 0x00000914	--小头像
		jps[3] = 0x00000918 	---道具头像99*13
		jps[4] = 0x00000908 	--对话头像大
		jps[5] = 0x00000918   	--65*65
		jps[6] = 0X00000908		--可无
		jps[8] = 0x00000910  	 --小小
		jps[7] = "vvxxzcom/yjl/cwmx.wdf"
		jps[9] = "wzife.wd3"
	elseif jp == "影精灵2" then
		jps[2] = 0x00000915 	--小头像
		jps[3] = 0x00000919 	---道具头像99*13
		jps[4] = 0x00000908 	--对话头像大
		jps[5] = 0x00000919  	--65*65
		jps[6] = 0x00000908		--可无
		jps[8] = 0x00000911  	 --小小
		jps[7] = "vvxxzcom/yjl/cwmx.wdf"
		jps[9] = "wzife.wd3"
	elseif jp == "影精灵3" then
		jps[2] = 0x00000916 	--小头像
		jps[3] = 0x00000920 	---道具头像99*13
		jps[4] = 0x00000908 	--对话头像大
		jps[5] = 0x00000920   	--65*65
		jps[6] = 0x00000908		--可无
		jps[8] = 0x00000912  	 --小小
		jps[7] = "vvxxzcom/yjl/cwmx.wdf"
		jps[9] = "wzife.wd3"



	elseif jp == "舞天姬" then
		-- jps[1] = 9 --1610184778
		jps[2] = 4183923439
		jps[3] = 837105923
		jps[4] = 3547289847
		jps[5] = 2410388328
		jps[6] = 783365583
		jps[7] = "wzife.wdf"
		jps[8] = 729585859
		jps[9] = "wzife.wd3"
	elseif jp == "龙太子" then
		-- jps[1] = 7
		jps[2] = 2968964991
		jps[3] = 3724855951
		jps[4] = 269238848
		jps[5] = 1570362097
		jps[6] = 2585947038
		jps[7] = "wzife.wdf"
		jps[8] = 268541173
		jps[9] = "wzife.wd3"
	elseif jp == "神天兵" then
		-- jps[1] = 3805405383
		jps[2] = 3633356312
		jps[3] = 2078571550
		jps[4] = 419903813
		jps[5] = 3805405383
		jps[6] = 2041810656
		jps[7] = "wzife.wdf"
		jps[8] = 1709491641
		jps[9] = "wzife.wd3"
	elseif jp == "逍遥生" then
		-- jps[1] = 16
		jps[2] = 3988784113
		jps[3] = 2557559806
		jps[4] = 4177070114
		jps[5] = 3996010872
		jps[6] = 92987728

		jps[7] = "wzife.wdf"
		jps[8] = 2295255222
		jps[9] = "wzife.wd3"
	elseif jp == "偃无师" then
		-- jps[1] = 21
		jps[2] = 10559816
		jps[3] = 10559817
		jps[4] = 10559815
		jps[5] = 10559824
		jps[6] = 10559814
		jps[7] = "wzife.wd5"
		jps[8] = 1674272704
		jps[9] = "common/wzife.wdf"
	elseif jp == "玄彩娥" then
		-- jps[1] = 17
		jps[2] = 1823274963
		jps[3] = 1560323955
		jps[4] = 2423018618
		jps[5] = 947503475
		jps[6] = 838654255
		jps[7] = "wzife.wdf"
		jps[8] = 1897575802 --1897575802
		jps[9] = "wzife.wd3"
	elseif jp == "杀破狼" then
		-- jps[1] = 23
		jps[2] = 2873683137
		jps[3] = 2518794069
		jps[4] = 3753428933
		jps[5] = 1033988096
		jps[6] = 1978306148
		jps[7] = "wzife.wdf"
		jps[8] = 2493964791
		jps[9] = "wzife.wd3"
	elseif jp == "鬼潇潇" then
		-- jps[1] = 19
		jps[2] = 10559800
		jps[3] = 10559809
		jps[4] = 10559801
		jps[5] = 10559826
		jps[6] = 10559808
		jps[7] = "wzife.wd5"
		jps[8] = 2577690333
		jps[9] = "common/wzife.wdf"
	elseif jp == "桃夭夭" then
		-- jps[1] = 20
		jps[2] = 10559810
		jps[3] = 10559813
		jps[4] = 10559811
		jps[5] = 10559825
		jps[6] = 10559812
		jps[7] = "wzife.wd5"
		jps[8] = 240545518
		jps[9] = "common/wzife.wdf"
	elseif jp == "巫蛮儿" then
		-- jps[1] = 22
		jps[2] = 1969001626
		jps[3] = 4127390861
		jps[4] = 1288146214
		jps[5] = 2569948308
		jps[6] = 2921462013
		jps[7] = "wzife.wdf"
		jps[8] = 350754450
		jps[9] = "wzife.wd3"
	elseif jp == "英女侠" then
		-- jps[1] = 18
		jps[2] = 4082176052
		jps[3] = 1714055813
		jps[4] = 112453972
		jps[5] = 1619905405
		jps[6] = 3703514110
		jps[7] = "wzife.wdf"
		jps[8] = 2816469774
		jps[9] = "wzife.wd3"
	elseif jp == "狐美人" then
		-- jps[1] = 3
		jps[2] = 193125810
		jps[3] = 1337913854
		jps[4] = 3274417234
		jps[5] = 764631086
		jps[6] = 507487855
		jps[7] = "wzife.wdf"
		jps[8] = 2398918162 --3745008152
		jps[9] = "wzife.wd3"
	elseif jp == "剑侠客" then
		-- jps[1] = 5
		jps[2] = 3615876352
		jps[3] = 3402595886
		jps[4] = 1410056737
		jps[5] = 366914688
		jps[6] = 485626985
		jps[7] = "wzife.wdf"
		jps[8] = 2029853147
		jps[9] = "wzife.wd3"
	elseif jp == "羽灵神" then
		-- jps[1] = 24
		jps[2] = 4194685178
		jps[3] = 983360139
		jps[4] = 1939929797
		jps[5] = 311711491
		jps[6] = 415704253
		jps[7] = "wzife.wdf"
		jps[8] = 2376797415 --492527331
		jps[9] = "wzife.wd3"
	elseif jp == "飞燕女" then
		-- jps[1] = 1
		jps[2] = 343987807
		jps[3] = 2821539099
		jps[4] = 1784035300
		jps[5] = 819471807
		jps[6] = 3149034421
		jps[7] = "wzife.wdf"
		jps[8] = 1797451587
		jps[9] = "wzife.wd3"
	elseif jp == "巨魔王" then
		-- jps[1] = 6
		jps[2] = 1285321634
		jps[3] = 2476120802
		jps[4] = 3683691832
		jps[5] = 4077790929
		jps[6] = 3295538630
		jps[7] = "wzife.wdf"
		jps[8] = 868047251 --3026821695  "wzife.wdf"
		jps[9] = "wzife.wd3"
	elseif jp == "骨精灵" then
		-- jps[1] = 2
		jps[2] = 1580183394
		jps[3] = 2126719765
		jps[4] = 3992137269
		jps[5] = 1433642861
		jps[6] = 2072196139
		jps[7] = "wzife.wdf"
		jps[8] = 4126416397
		jps[9] = "wzife.wd3"

	--########################################################?自己修改?##########################################
	--########################################################?自己修改?##########################################
	elseif jp == "新二大王" then
		jps[4] = "0x2478FFCE"
		jps[7] = ""
	elseif jp == "新孙悟空" then
		jps[4] = 0x3FA3472C
		jps[7] = "wzife.wdf"
	elseif jp == "新猪八戒" then --jp == "猪八戒" or
		jps[4] = 0x93557CA2
		jps[7] = "wzife.wdf"
	-- elseif jp == "巫奎虎_助战" then
	-- 	jps[1] = 16777298
	-- 	jps[7] = "zdy.rpk"
	-- elseif jp == "二郎神_助战" then
	-- 	jps[1] = 16777237
	-- 	jps[7] = "zdy.rpk"
	-- elseif jp == "地涌夫人_助战" then
	-- 	jps[1] = 16777232
	-- 	jps[7] = "zdy.rpk"
	-- elseif jp == "晶晶姑娘_助战" then
	-- 	jps[1] = 16777249
	-- 	jps[7] = "zdy.rpk"
	-- elseif jp == "九灵元圣_助战" then
	-- 	jps[1] = 16777283
	-- 	jps[7] = "zdy.rpk"
	-- elseif jp == "程咬金_助战" then
	-- 	jps[1] = 16777220
	-- 	jps[7] = "zdy.rpk"
	-- elseif jp == "青狮精_助战" then
	-- 	jps[1] = 16777252
	-- 	jps[7] = "zdy.rpk"
	-- elseif jp == "地藏菩萨_助战" then
	-- 	jps[1] = 16777223
	-- 	jps[7] = "zdy.rpk"
	-- elseif jp == "观音菩萨_助战" then
	-- 	jps[1] = 16777240
	-- 	jps[7] = "zdy.rpk"
	-- elseif jp == "李天王_助战" then
	-- 	jps[1] = 16777264
	-- 	jps[7] = "zdy.rpk"
	-- elseif jp == "东海龙王_助战" then
	-- 	jps[1] = 16777267
	-- 	jps[7] = "zdy.rpk"
	-- elseif jp == "牛魔王_助战" then
	-- 	jps[1] = 16777268
	-- 	jps[7] = "zdy.rpk"
	-- elseif jp == "菩提老祖_助战" then
	-- 	jps[1] = 16777271
	-- 	jps[7] = "zdy.rpk"
	-- elseif jp == "沙和尚_助战" then
	-- 	jps[1] = 16777286
	-- 	jps[7] = "zdy.rpk"
	-- elseif jp == "孙婆婆_助战" then
	-- 	jps[1] = 16777289
	-- 	jps[7] = "zdy.rpk"
	-- elseif jp == "齐天大圣_助战" then
	-- 	jps[1] = 16777280
	-- 	jps[7] = "zdy.rpk"
	-- elseif jp == "阎罗王_助战" then
	-- 	jps[1] = 16777301
	-- 	jps[7] = "zdy.rpk"
	-- elseif jp == "镇元子_助战" then
	-- 	jps[1] = 16777304
	-- 	jps[7] = "zdy.rpk"
	-- elseif jp == "成周妖皇_助战" then
	-- 	jps[1] = 16777217
	-- 	jps[7] = "zdy.rpk"
	-- elseif jp == "猪八戒_助战" then
	-- 	jps[1] = 16777313
	-- 	jps[7] = "zdy.rpk"
	-- elseif jp == "九头虫_助战" then
	-- 	jps[1] = 16777255
	-- 	jps[7] = "zdy.rpk"
	elseif jp == "虎头怪新全身" then
		jps[2] = 0x1000034
		jps[3] = 0x1000052
		jps[4] ="zdy3.rpk"
	elseif jp == "舞天姬新全身" then
		jps[2] = 0x1000026
		jps[3] = 0x1000051
		jps[4] ="zdy3.rpk"
	elseif jp == "龙太子新全身" then
		jps[2] = 0x1000031
		jps[3] = 0x1000050
		jps[4] ="zdy3.rpk"
	elseif jp == "神天兵新全身" then
		jps[2] = 0x1000029
		jps[3] = 0x1000053
		jps[4] ="zdy3.rpk"
	elseif jp == "逍遥生新全身" then
		jps[2] = 0x1000025
		jps[3] = 0x1000054
		jps[4] ="zdy3.rpk"
	elseif jp == "偃无师新全身" then
		jps[2] = 0x1000023
		jps[3] = 0x1000049
		jps[4] ="zdy3.rpk"
	elseif jp == "玄彩娥新全身" then
		jps[2] = 0x1000024
		jps[3] = 0x1000048
		jps[4] ="zdy3.rpk"
	elseif jp == "杀破狼新全身" then
		jps[2] = 0x1000030
		jps[3] = 0x1000047
		jps[4] ="zdy3.rpk"
	elseif jp == "鬼潇潇新全身" then
		jps[2] = 0x1000036
		jps[3] = 0x1000046
		jps[4] ="zdy3.rpk"
	elseif jp == "桃夭夭新全身" then
		jps[2] = 0x1000028
		jps[3] = 0x1000045
		jps[4] ="zdy3.rpk"
	elseif jp == "巫蛮儿新全身" then
		jps[2] = 0x1000027
		jps[3] = 0x1000044
		jps[4] ="zdy3.rpk"
	elseif jp == "英女侠新全身" then
		jps[2] = 0x1000022
		jps[3] = 0x1000043
		jps[4] ="zdy3.rpk"
	elseif jp == "狐美人新全身" then
		jps[2] = 0x1000035
		jps[3] = 0x1000042
		jps[4] ="zdy3.rpk"
	elseif jp == "剑侠客新全身" then
		jps[2] = 0x1000033
		jps[3] = 0x1000076
		jps[4] ="zdy3.rpk"
	elseif jp == "羽灵神新全身" then
		jps[2] = 0x1000021
		jps[3] = 0x1000057
		jps[4] ="zdy3.rpk"
	elseif jp == "飞燕女新全身" then
		jps[2] = 0x1000038
		jps[3] = 0x1000041
		jps[4] ="zdy3.rpk"
	elseif jp == "巨魔王新全身" then
		jps[2] = 0x1000032
		jps[3] = 0x1000058
		jps[4] ="zdy3.rpk"
	elseif jp == "骨精灵新全身" then
		jps[2] = 0x1000037
		jps[3] = 0x1000040
		jps[4] ="zdy3.rpk"
	elseif jp == "梦幻精灵" then
		jps[2] = 0x4f8bd202
		jps[7] ="wzife.wdf"
	elseif jp == "梦幻精灵小" then
		jps[2] = 899185117
		jps[7] ="common/wzife.wdf"
	elseif jp == "系统" then
		jps[2] = 0x28a47088
		jps[7] ="common/wzife.wdf"
	---------------------
	elseif jp == "涂山雪" or jp == "进阶涂山雪"then
		jps[1] = 0x1000132
		jps[2] = 0x1000133
		jps[7] = "zdy3.rpk"
		jps[8] = "zdy3.rpk"
	elseif jp == "狐不归" or jp == "进阶狐不归"then
		jps[1] = 0x1000136
		jps[2] = 0x1000137
		jps[7] = "zdy3.rpk"
		jps[8] = "zdy3.rpk"
	elseif jp == "花铃"or jp == "进阶花铃" then
		jps[1] = 0x1000134
		jps[2] = 0x1000135
		jps[7] = "zdy3.rpk"
		jps[8] = "zdy3.rpk"
	elseif jp == "月魅"or jp == "进阶月魅" then
		jps[1] = 0x1000141
		jps[2] = 0x1000142
		jps[7] = "zdy3.rpk"
		jps[8] = "zdy3.rpk"
	elseif jp == "月影仙"or jp == "进阶月影仙" then
		jps[1] = 0x1000143
		jps[2] = 0x1000144
		jps[7] = "zdy3.rpk"
		jps[8] = "zdy3.rpk"
	elseif jp == "云游火"or jp == "进阶云游火" then
		jps[1] = 0x1000138
		jps[2] = 0x1000139
		jps[7] = "zdy3.rpk"
		jps[8] = "zdy3.rpk"
	elseif jp == "超级神鼠"or jp == "进阶超级神鼠" then
		jps[1] = 0x1000127
		jps[2] = 0x1000126
		jps[7] = "zdy3.rpk"
		jps[8] = "zdy3.rpk"
	elseif jp == "超级神狗" or jp == "进阶超级神狗"then
		jps[1] = 0x1000122
		jps[2] = 0x1000145
		jps[7] = "zdy3.rpk"
		jps[8] = "zdy3.rpk"
	elseif jp == "超级神猪"or jp == "进阶超级神猪" then
		jps[1] = 0x1000120
		jps[2] = 0x1000121
		jps[7] = "zdy3.rpk"
		jps[8] = "zdy3.rpk"
	elseif jp == "超级猪小戒" or jp == "进阶超级猪小戒" then
		jps[1] = 0x1000128
		jps[2] = 0x1000129
		jps[7] = "zdy3.rpk"
		jps[8] = "zdy3.rpk"
	elseif jp == "超级飞天" or jp == "进阶超级飞天" then
		jps[1] = 0x1000140
		jps[2] = 0x1000123
		jps[7] = "zdy3.rpk"
		jps[8] = "zdy3.rpk"
	elseif jp == "谛听" then
		jps[1] = 0x1000146
		jps[2] = 0x1000146
		jps[7] = "zdy3.rpk"
		jps[8] = "zdy3.rpk"
	elseif jp == "恶魔泡泡" then
		jps[1] = 0x08000038
		jps[2] = 0x08000037
		jps[4] = 0x08000037
		jps[7] = "vvxxzcom/monster.wdf"
		jps[8] = "vvxxzcom/monster.wdf"
	elseif jp == "自在心猿" then
		jps[1] = 0x08000053
		jps[2] = 0x08000046
		jps[4] = 0x08000045
		jps[7] = "vvxxzcom/monster.wdf"
		jps[8] = "vvxxzcom/monster.wdf"
	elseif jp == "超级飞廉" then
		jps[1] = 0x08000078
		jps[2] = 0x08000079
		jps[4] = 0x08000079
		jps[7] = "vvxxzcom/monster.wdf"
		jps[8] = "vvxxzcom/monster.wdf"
	elseif jp == "沙暴" then
		jps[1] = 0x08000065
		jps[2] = 0x08000064
		jps[4] = 0x08000064
		jps[7] = "vvxxzcom/monster.wdf"
		jps[8] = "vvxxzcom/monster.wdf"
	elseif jp == "雷龙" then
		jps[1] = 0x08000056
		jps[2] = 0x08000057
		jps[4] = 0x08000057
		jps[7] = "vvxxzcom/monster.wdf"
		jps[8] = "vvxxzcom/monster.wdf"
	elseif jp == "进阶谛听" then
		jps[1] = 0x1000131
		jps[2] = 0x1000130
		jps[7] = "zdy3.rpk"
		jps[8] = "zdy3.rpk"
	elseif jp == "超级鲲鹏" or jp == "进阶超级鲲鹏" then
		jps[1] = 0x1000354
		jps[2] = 0x1000351
		jps[7] = "zdy.rpk"
		jps[8] = "zdy.rpk"
	elseif jp == "超级神虎（壬寅）" or jp == "进阶超级神虎（壬寅）" then
		jps[1] = "ABAB0003"
		jps[2] = "ABAB0003"
		jps[7] = "tfg.rpk"
		jps[8] = "tfg.rpk"
	elseif jp == "广目巡守" or jp == "进阶广目巡守" then
		jps[1] = "广目巡守小头像"
		jps[2] = "广目巡守小头像"
		jps[7] = "tfg.rpk"
		jps[8] = "tfg.rpk"
	elseif jp == "妙华天女" or jp == "进阶妙华天女" then
		jps[1] = "妙华天女头像小"
		jps[2] = "妙华天女头像小"
		jps[7] = "tfg.rpk"
		jps[8] = "tfg.rpk"
	elseif jp == "龙鲤" or jp == "进阶龙鲤" then
		jps[1] = "龙鲤头像"
		jps[2] = "龙鲤头像"
		jps[7] = "tfg.rpk"
		jps[8] = "tfg.rpk"
	elseif jp == "执音" or jp == "进阶执音" then
		jps[1] = "进阶执音头像"
		jps[2] = "进阶执音头像"
		jps[7] = "tfg.rpk"
		jps[8] = "tfg.rpk"
	end
	------------梦战头像结束
	--########################################################?自己修改?##########################################
	if jps[7] ==nil and jps[4] ==nil then
	  	local jps0 = 引擎.取所有模型(jp)
		if jps0~= nil then
			if jps0.大图标~=nil and jps0.大图标 ~= "" then
				jps[1] = jps0.小图标
			end
			if jps0.小图标~=nil and jps0.小图标 ~= "" then
				jps[2] = jps0.大图标
			end
			if jps0.对话头像~=nil and jps0.对话头像 ~= "" then
				jps[4] = jps0.对话头像
			end
			jps[7] = ""
			jps[8] = ""
			jps[10] = ""
		end
	end
	if jps[1] ==nil then
		jps[1] = 0xB56ECBA9
		jps[7] = ""
	end
	if jps[2] ==nil then
	 	jps[2] = 0x338A1A07
	 	jps[8] = ""
	end
	if jps[4] ==nil then
	 	jps[4] = 0xF14B1666
	 	jps[10] = ""
	end
	return jps
end

--火星人 善财童子 哮天犬 泪妖 镜妖 进饶僧 灵猫兽  狂豹兽 灵灯 般若天女 155  165

--超级神羊  超级六耳 超级土地 超级神猴 超级神鸡 超级玉兔