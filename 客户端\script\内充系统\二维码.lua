--======================================================================--

--======================================================================--
local 二维码 = class()
local floor = math.floor
local tp,zts,zt
local format = string.format
local insert = table.insert



function 二维码:初始化(根)
	self.ID = 20172
	self.x = 0
	self.y = 0
	self.xx = 0
	self.yy = 0
	self.微信支付 = "微信支付"
	self.支付宝支付 = "支付宝支付"
	self.可视 = false
	self.鼠标 = false
	self.焦点 = false
	self.可移动 = true
	tp = 根
	self.窗口时间 = 0
	zts = tp.字体表.道具字体
	zts1= tp.字体表.描边字体
	zt = tp.字体表.描边字体
	self.控件类 = require("ggeui/加载类")()
	local 总控件 = self.控件类:创建控件('序号控件')
	总控件:置可视(true,true)


end

function 二维码:打开(x,y,内容)
	if self.可视 then
		self.可视 = false
		return
	else
		self.x = x
		self.y = y
		insert(tp.窗口_,self)
		local 资源 = tp.资源
		local 按钮 = tp._按钮
		local 自适应 = tp._自适应
		self.资源组 = {
			[1] =资源:载入('wdf/vvxxzcom/VIP底板1.png',"图片"),-- 自适应.创建(0,1,400,540,3,9),
			[2] = 资源:载入(程序目录..'wdf/vvxxzcom/pic/pay.png',"图片"),

			[10]=资源:载入(程序目录..'wdf/vvxxzcom/pic/code.png',"图片"),
			--[11]=资源:载入(程序目录..'wdf/vvxxzcom/pic/weixin.png',"图片"),
			--[12]=资源:载入(程序目录..'wdf/vvxxzcom/pic/alipay.png',"图片")
			}

		self.线 = tp.资源:载入("wzife.wd1","网易WDF动画",999600305)
		self.线:置区域(0,0,350,2)
		self.内容=内容
		tp.运行时间 = tp.运行时间 + 1
	    	self.窗口时间 = tp.运行时间
	    	self.可视 = true
	    	self.物品组={}
	    	self.首冲金额=内容[1]
	    	self.支付渠道=内容[2]
	    	self.订单号=内容[3]
	    end
end



function 二维码:显示(dt,x,y)
	self.焦点 = false
	local 偏移x,偏移y = 等比例缩放公式(280,400,self.资源组[1].宽度,self.资源组[1].高度)



	self.资源组[1]:显示(self.x+70,self.y,偏移x,偏移y)
	self.资源组[10]:显示(self.x+121 ,self.y+110)

	zts:置颜色(黑色)
	zts:显示(self.x+210-180+12+25,self.y+48,"  请扫码支付以下金额\n        "..self.首冲金额.."元")
	zts:置颜色(红色):显示(self.x+80,self.y+310,"请务必按照上方金额付款")
	zts:置颜色(红色):显示(self.x+80,self.y+350," 请勿主动关闭该界面")


	if self.支付渠道 == "wxpay" then
		zts:显示(self.x+173,self.y+15,self.微信支付)
	end
	if self.支付渠道 == "alipay" then
		zts:显示(self.x+173,self.y+15,self.支付宝支付)
	end
	    self.计时器 = (self.计时器 or 0) + dt

    -- 按秒执行其他操作
	    if self.计时器 > 1 then  -- 在这里设置执行操作的时间间隔，例如1秒
	        发送数据(94.4,{内容=self.订单号})
	        self.计时器 = 0
	    end
end



function 二维码:刷新(内容)
	self.内容=内容
end


function 二维码:检查点(x,y)
	if self.资源组 ~= nil and self.资源组[1]:是否选中(x,y)  then
		return true
	end
end

function 二维码:初始移动(x,y)
	tp.运行时间 = tp.运行时间 + 1
	if not tp.消息栏焦点 then
  		self.窗口时间 = tp.运行时间
 	end
	if not self.焦点 then
		tp.移动窗口 = true
	end
	if self.鼠标 and  not self.焦点 then
		self.xx = x - self.x
		self.yy = y - self.y
	end
end

function 二维码:开始移动(x,y)
	if self.鼠标 then
		self.x = x - self.xx
		self.y = y - self.yy
	end
end

return 二维码