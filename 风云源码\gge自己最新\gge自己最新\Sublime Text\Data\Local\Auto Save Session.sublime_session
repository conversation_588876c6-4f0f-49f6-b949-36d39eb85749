{
	"folder_history":
	[
	],
	"last_version": 3103,
	"last_window_id": 2,
	"log_indexing": false,
	"settings":
	{
		"new_window_height": 480.0,
		"new_window_settings":
		{
			"auto_complete":
			{
				"selected_items":
				[
					[
						"九黎",
						"九黎返回值"
					],
					[
						"五开宠物",
						"五开宠物礼包"
					],
					[
						"els",
						"else	else end"
					],
					[
						"for",
						"for	for i=1,10"
					],
					[
						"初始化",
						"初始化装备属性"
					],
					[
						"else",
						"elseif"
					],
					[
						"print",
						"print	(...)"
					],
					[
						"ret",
						"return"
					],
					[
						"el",
						"else	else end"
					],
					[
						"func",
						"function"
					],
					[
						"战斗准备",
						"战斗准备类:创建战斗	(1001.lua)"
					],
					[
						"自动",
						"自动捉鬼数据	(初始化脚本.lua)"
					],
					[
						"table",
						"table.print	(gge)"
					],
					[
						"嗜血",
						"嗜血恶兽	(双城记.lua)"
					],
					[
						"凌",
						"凌将军你先行"
					],
					[
						"双城记",
						"双城记玄机之数"
					],
					[
						"尝试",
						"尝试触碰"
					],
					[
						"犀牛",
						"犀牛将军人形"
					],
					[
						"锦衣",
						"锦衣=\"青花瓷	(怪物属性.lua)"
					],
					[
						"机关",
						"机关人人形	(怪物属性.lua)"
					],
					[
						"re",
						"return	return 返回"
					],
					[
						"talbp",
						"table.print	(gge)"
					],
					[
						"fo",
						"fori	for i,v in ipairs()"
					],
					[
						"talb",
						"table.print	(gge)"
					],
					[
						"我要挑战",
						"我要挑战你的心魔"
					],
					[
						"tabl",
						"table.print	(gge)"
					],
					[
						"R",
						"ReadExcel"
					],
					[
						"紫电",
						"紫电青霜"
					],
					[
						"开场",
						"开场发言=\"这群人竟然能封住真火，大哥你先撤，我砍死他们！"
					],
					[
						"灵感",
						"灵感元神"
					],
					[
						"金鱼",
						"金鱼右将军"
					],
					[
						"主动",
						"主动技能={\"破血狂攻"
					],
					[
						"nil",
						"nil,\"物理"
					],
					[
						"男人_",
						"男人_老伯"
					],
					[
						"tablep",
						"table.print	(gge)"
					],
					[
						"tal",
						"table.print	(gge)"
					],
					[
						"召唤",
						"召唤数量"
					],
					[
						"主怪",
						"主怪编号"
					],
					[
						"额外",
						"额外数据"
					],
					[
						"ro",
						"random"
					],
					[
						"进阶百足",
						"进阶百足将军	(Task670.lua)"
					],
					[
						"max",
						"math.max	(x, ...)"
					],
					[
						"取随机数",
						"取随机数()<=10	(AI战斗.lua)"
					],
					[
						"风云",
						"风云战将	(取师门.lua)"
					],
					[
						"ta",
						"table.print	(gge)"
					],
					[
						"雷霆",
						"雷霆万钧	(战斗技能.lua)"
					],
					[
						"小",
						"小还丹"
					],
					[
						"飞",
						"飞砂走石"
					],
					[
						"ma",
						"math.ceil	(x)"
					],
					[
						"sj",
						"sj()<=gl"
					],
					[
						"高级进击",
						"高级进击法暴"
					],
					[
						"剑",
						"剑荡四方"
					],
					[
						"指定",
						"指定对象=\"自己"
					],
					[
						"req",
						"require	require \"\""
					],
					[
						"cl",
						"clock"
					],
					[
						"取玩家",
						"取玩家战斗"
					],
					[
						"月",
						"月影"
					],
					[
						"e",
						"else	else end"
					],
					[
						"成员",
						"成员数据"
					],
					[
						"lo",
						"local	local x = 1"
					],
					[
						"法术",
						"法术状态"
					],
					[
						"数字",
						"数字id"
					],
					[
						"高级",
						"高级防御"
					],
					[
						"高级法术",
						"高级法术连击"
					],
					[
						"主人",
						"主人序号"
					],
					[
						"全场",
						"全场属性"
					],
					[
						"物理",
						"物理伤害结果"
					],
					[
						"增加",
						"增加战意"
					],
					[
						"气血",
						"气血上限"
					],
					[
						"暴",
						"暴怒"
					],
					[
						"玩家",
						"玩家id"
					],
					[
						"攻击",
						"攻击编号"
					],
					[
						"死亡",
						"死亡计算"
					],
					[
						"给予",
						"给予道具"
					],
					[
						"更新",
						"更新参战宝宝"
					],
					[
						"fun",
						"function	function"
					],
					[
						"技能",
						"技能回合CD"
					],
					[
						"dh",
						"dh-jr	dh-jr"
					],
					[
						"餐风",
						"餐风饮露"
					],
					[
						"dhwb",
						"dhwb-wj	dhwb-wj"
					],
					[
						"dhw",
						"dhwb-jr	dhwb-jr"
					],
					[
						"任务",
						"任务包裹"
					],
					[
						"天蚕",
						"天蚕丝"
					],
					[
						"补天",
						"补天石"
					],
					[
						"麒麟",
						"麒麟血火"
					],
					[
						"七宝",
						"七宝玲珑灯"
					],
					[
						"玄龟",
						"玄龟板水"
					],
					[
						"龙之",
						"龙之筋金"
					],
					[
						"清心",
						"清心咒"
					],
					[
						"金钱",
						"金钱镖"
					],
					[
						"金凤",
						"金凤羽金"
					],
					[
						"飞剑",
						"飞剑金"
					],
					[
						"神兵",
						"神兵鉴赏"
					],
					[
						"打造",
						"打造技巧"
					],
					[
						"级别",
						"级别限制"
					],
					[
						"扣除",
						"扣除银子"
					],
					[
						"os",
						"os.time	([table])"
					],
					[
						"in",
						"insert"
					],
					[
						"取",
						"取随机数"
					]
				]
			},
			"build_system_choices":
			[
				[
					[
						[
							"Packages/Lua/ggebc.sublime-build",
							"RunInCommand"
						],
						[
							"Packages/Lua/ggegame.sublime-build",
							"RunInCommand"
						],
						[
							"Packages/Lua/ggeserver.sublime-build",
							"RunInCommand"
						]
					],
					[
						"Packages/Lua/ggeserver.sublime-build",
						"RunInCommand"
					]
				],
				[
					[
						[
							"Packages/Lua/ggebc.sublime-build",
							"Stop"
						],
						[
							"Packages/Lua/ggegame.sublime-build",
							"Stop"
						],
						[
							"Packages/Lua/ggeserver.sublime-build",
							"Stop"
						]
					],
					[
						"Packages/Lua/ggeserver.sublime-build",
						"Stop"
					]
				]
			],
			"build_varint": "",
			"command_palette":
			{
				"height": 57.0,
				"last_filter": "",
				"selected_items":
				[
					[
						"Snippet: ",
						"Snippet: table.sort"
					],
					[
						"Snippet: cas",
						"Snippet: class模版"
					],
					[
						"remove",
						"Package Control: Remove Package"
					]
				],
				"width": 696.0
			},
			"console":
			{
				"height": 228.0,
				"history":
				[
				]
			},
			"distraction_free":
			{
				"menu_visible": true,
				"show_minimap": false,
				"show_open_files": false,
				"show_tabs": false,
				"side_bar_visible": false,
				"status_bar_visible": false
			},
			"file_history":
			[
				"/D/MyGame/防官复古三经脉/服务端/Script/系统处理类/系统处理类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/角色处理类/道具仓库.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/角色处理类/道具处理类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/初始化脚本.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/全局函数类/全局循环类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/地煞星.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/角色处理类/角色处理类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/活动.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/战斗处理类/怪物调用/结算处理.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/对话处理类/NPC对话处理.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/角色处理类/帮派处理.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/对话处理类/NPC对话内容.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/助战处理类/加载数据.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/对话处理类/对话调用/1854.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/物品数据.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/全局函数类/全局函数.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/对话处理类/初始.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/对话处理类/对话调用/1514.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/对话处理类/对话调用/1114.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/助战处理类/MateControl.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/战斗处理类/战斗处理.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/对话处理类/对话调用/1001.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/明暗雷怪.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/野怪.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/商店处理类/商城处理类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/系统处理类/假人事件类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/角色处理类/召唤兽处理类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/宝宝.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/任务处理类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task135.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/HPClient类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/ggeclient.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/战斗处理类/战斗计算/战斗技能.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/系统处理类/内充系统pay8.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/系统处理类/小巷子独家.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/全局函数类/方法函数类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/角色处理类/礼包奖励类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/系统处理类/内充系统.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/后台网络处理.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/协程通信.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/战斗处理类/创建战斗.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/对话处理类/对话调用/1844.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/帮派青龙玄武.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/系统处理类/GM工具类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/系统处理类/聊天处理类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/战斗处理类/战斗计算/战斗计算.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/战斗处理类/战斗计算/状态处理.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/助战处理类/基础信息.lua",
				"/D/MyGame/防官复古三经脉/风云源码/gge自己最新/gge自己最新/Core/Game/gge引擎.lua",
				"/D/MyGame/防官复古三经脉/版本更新/250122更新五福系统 - 副本/gge自己最新/Core/Game/副本/gge自己最新/Core/Game/gge引擎.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/战斗处理类/战斗计算/战斗固伤计算.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/对话处理类/商业对话.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/技能数据库.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/系统处理类/网络处理类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/系统处理类/首席弟子类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/战斗处理类/战斗计算/战斗执行类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/取师门.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/角色处理类/召唤兽进化.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/对话处理类/对话调用/1825.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/角色处理类/行囊出售处理.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/角色处理类/装备处理类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task5.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/投放怪.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/战斗处理类/战斗计算/物理技能计算.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/剑会.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task137.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/ggeserver.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/ggedebug.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/ggebuf.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/封包加密.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/安卓通信.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/商店处理类/商店处理类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/商店处理类/师门商店类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/地图处理类/地图坐标类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/地图处理类/地图处理类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/地图处理类/路径类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/地图处理类/MAP.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/属性控制/子女.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/属性控制/宠物.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/传送位置.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/传送圈位置.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/取经验.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/变身卡.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/场景等级.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/场景NPC.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/染色.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/法术技能特效.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/装备特技.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/角色.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/系统处理类/游戏活动类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/角色处理类/内存类_物品.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/角色处理类/技能类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/角色处理类/队伍处理类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task10.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task102.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task103.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task106.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task11.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task110.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task111.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task12.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task127.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task129.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task13.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task132.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task133.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task134.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task136.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task138.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task14.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task15.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task16.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task17.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task18.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task2.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task201.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task203.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task205.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task206.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task209.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task210.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task211.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task212.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task213.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task214.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task240.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task250.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task3.lua"
			],
			"find":
			{
				"height": 33.0
			},
			"find_in_files":
			{
				"height": 138.0,
				"where_history":
				[
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\角色处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\maplj",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\全局函数类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\助战处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\系统处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类\\战斗处理.lua",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\角色处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\角色处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\角色处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类\\战斗处理.lua",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类\\战斗处理.lua",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\系统处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类\\战斗处理.lua",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\角色处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类\\战斗计算",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\角色处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\角色处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\角色处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\角色处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\角色处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\角色处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类\\战斗计算",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类\\战斗计算",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类\\战斗计算",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\数据中心",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\全局函数类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\系统处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\角色处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\对话处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\数据中心",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\线程",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类"
				]
			},
			"find_state":
			{
				"case_sensitive": false,
				"find_history":
				[
					"3832",
					"索要其他角色临时行囊",
					"整理其他角色临时行囊啊",
					"整理临时行囊啊",
					"索要其他角色临时行囊",
					"索要临时行囊",
					"整理临时行囊啊",
					"玩家数据[数字id].角色",
					"索要临时行囊",
					"整理临时行囊啊",
					"self.数据",
					"索要其他角色临时行囊",
					"玩家数据[id].角色",
					"索要其他角色临时行囊",
					"满足条件",
					"索要临时行囊",
					"临时行囊",
					"道具仓库.txt",
					"索要临时行囊",
					"3832",
					"超级兽决",
					"重置数据",
					"副本数据",
					"副本",
					"重置数据",
					"车迟",
					"车迟国",
					"整理临时行囊啊",
					"索要临时行囊",
					"3832",
					"熟练度",
					"添加银子",
					"掉落列表",
					"给予道具",
					"给于",
					"掉落数据",
					"仙玉点卡是否",
					"地煞",
					"奇经八脉",
					"四打",
					"取帮派建筑达标情况",
					"取升级建筑对话",
					"取帮派建筑达标情况",
					"取升级建筑对话",
					"1级建筑",
					"封禁",
					"锦囊",
					"年兽",
					"更新玩家每日",
					"叛逆",
					"年兽",
					"剧情直飞特权",
					"小巷子",
					"你账号下没有其他角色，无法使用！",
					"你账号下没有角色，无法使用！",
					"飞升凭证",
					"服饰.数据",
					"确认激活",
					"恭喜少侠通过考验，现在我可以为你激活渡劫专用锦衣",
					"主线=14",
					"渡劫锦衣",
					"生死劫",
					"飞升",
					"生死劫",
					"历劫.渡劫",
					"取任务",
					"获取任务信息",
					"飞升",
					"月卡",
					"获取任务信息",
					"飞升凭证",
					"进入游戏",
					"增加潜力",
					"升级技能",
					"升级",
					"洗点操作",
					"重置人物",
					"名称数据",
					"玩家数据",
					"获取数据",
					"助战处理类:数据处理",
					"助战处理类",
					"677",
					"分角色",
					"粉橘色",
					"bb修炼",
					"飞升降修",
					"飞升",
					"取修炼",
					"开始渡劫",
					"渡劫",
					"加人物修炼经验",
					"修炼",
					"飞升",
					"月卡",
					"飞升凭证",
					"癫散戏票·双城记",
					"谛听",
					"赐福列表",
					"修罗傀儡鬼",
					"1920",
					"30010",
					"持国",
					"谛听",
					"月华露品质",
					"魔兽要诀",
					"商城",
					"化圣",
					"仙露",
					"42",
					"52",
					"设置任务135",
					"gxsc",
					"剧情直飞",
					"暂未开放",
					"内政",
					"金银",
					"127.64.247.33",
					"127.0.0.1",
					"原本42秒",
					"等待起始",
					"等待计时",
					"等待起始",
					"计时",
					"50",
					"60",
					"元身",
					"设置玄武任务"
				],
				"highlight": true,
				"in_selection": false,
				"preserve_case": false,
				"regex": false,
				"replace_history":
				[
					"127.0.0.1",
					"127.64.247.33",
					"@作者: www.wnfj.com",
					"www.wnfj.com",
					"万能飞机：www.wnfj.com",
					"\"九黎城\"",
					"发送信息[1][i]",
					"影精灵.dll",
					"共享仓库类",
					"",
					"签到奖品.txt",
					"",
					"小巷子定制\\签到配置",
					"一颗",
					"",
					"程序目录..[[小巷子定制\\掉落配置\\]]..序号..\".ini\"",
					"程序目录..[[小巷子定制\\掉落配置\\]]..\"..序号..\".ini\"\"",
					"程序目录..[[小巷子定制\\掉落配置\\]]..\"..序号..[[.]]ini\"",
					"程序目录..[[小巷子定制\\掉落配置\\]]..\"..序号..[.]ini\"",
					"奖励物品",
					"更多游戏请访问小巷子资源网：WWW.vvxxz.com,版本定制授权联系QQ：79550111,36537044",
					"小巷子资源网，联系QQ：79550111,36537044",
					"通天河",
					"角色",
					"帮派青龙玄武:",
					"帮派青龙:",
					"青龙:",
					"完成任务_",
					"设置任务",
					"完成任务_",
					"设置任务",
					",6557,",
					"如梦奇谭之五更寒",
					"NPCcl_",
					"NPCdhcl_",
					"NPCdh_",
					"胜利MOB_",
					"~= 99999999",
					"~= 1314520",
					"怪物对话解析",
					"怪物对话内容",
					"任务说明",
					"rwgx",
					"奖励",
					"驱鬼",
					"不可封印=10",
					"不可封印=100"
				],
				"reverse": false,
				"show_context": true,
				"use_buffer2": true,
				"whole_word": false,
				"wrap": true
			},
			"incremental_find":
			{
				"height": 33.0
			},
			"input":
			{
				"height": 43.0
			},
			"menu_visible": true,
			"output.SublimeLinter":
			{
				"height": 0.0
			},
			"output.exec":
			{
				"height": 192.0
			},
			"output.find_results":
			{
				"height": 0.0
			},
			"output.mdpopups":
			{
				"height": 0.0
			},
			"pinned_build_system": "Packages/Lua/ggeserver.sublime-build",
			"replace":
			{
				"height": 62.0
			},
			"save_all_on_build": true,
			"select_file":
			{
				"height": 0.0,
				"last_filter": "",
				"selected_items":
				[
					[
						"给予",
						"111111服务端\\给予道具.txt"
					],
					[
						"",
						"111111服务端\\Script\\数据中心\\宝宝.lua"
					]
				],
				"width": 0.0
			},
			"select_project":
			{
				"height": 0.0,
				"last_filter": "",
				"selected_items":
				[
				],
				"width": 0.0
			},
			"select_symbol":
			{
				"height": 0.0,
				"last_filter": "",
				"selected_items":
				[
				],
				"width": 0.0
			},
			"show_minimap": true,
			"show_open_files": false,
			"show_tabs": true,
			"side_bar_visible": true,
			"side_bar_width": 223.0,
			"status_bar_visible": true,
			"template_settings":
			{
			}
		},
		"new_window_width": 640.0
	},
	"windows":
	[
		{
			"auto_complete":
			{
				"selected_items":
				[
					[
						"九黎",
						"九黎返回值"
					],
					[
						"五开宠物",
						"五开宠物礼包"
					],
					[
						"els",
						"else	else end"
					],
					[
						"for",
						"for	for i=1,10"
					],
					[
						"初始化",
						"初始化装备属性"
					],
					[
						"else",
						"elseif"
					],
					[
						"print",
						"print	(...)"
					],
					[
						"ret",
						"return"
					],
					[
						"el",
						"else	else end"
					],
					[
						"func",
						"function"
					],
					[
						"战斗准备",
						"战斗准备类:创建战斗	(1001.lua)"
					],
					[
						"自动",
						"自动捉鬼数据	(初始化脚本.lua)"
					],
					[
						"table",
						"table.print	(gge)"
					],
					[
						"嗜血",
						"嗜血恶兽	(双城记.lua)"
					],
					[
						"凌",
						"凌将军你先行"
					],
					[
						"双城记",
						"双城记玄机之数"
					],
					[
						"尝试",
						"尝试触碰"
					],
					[
						"犀牛",
						"犀牛将军人形"
					],
					[
						"锦衣",
						"锦衣=\"青花瓷	(怪物属性.lua)"
					],
					[
						"机关",
						"机关人人形	(怪物属性.lua)"
					],
					[
						"re",
						"return	return 返回"
					],
					[
						"talbp",
						"table.print	(gge)"
					],
					[
						"fo",
						"fori	for i,v in ipairs()"
					],
					[
						"talb",
						"table.print	(gge)"
					],
					[
						"我要挑战",
						"我要挑战你的心魔"
					],
					[
						"tabl",
						"table.print	(gge)"
					],
					[
						"R",
						"ReadExcel"
					],
					[
						"紫电",
						"紫电青霜"
					],
					[
						"开场",
						"开场发言=\"这群人竟然能封住真火，大哥你先撤，我砍死他们！"
					],
					[
						"灵感",
						"灵感元神"
					],
					[
						"金鱼",
						"金鱼右将军"
					],
					[
						"主动",
						"主动技能={\"破血狂攻"
					],
					[
						"nil",
						"nil,\"物理"
					],
					[
						"男人_",
						"男人_老伯"
					],
					[
						"tablep",
						"table.print	(gge)"
					],
					[
						"tal",
						"table.print	(gge)"
					],
					[
						"召唤",
						"召唤数量"
					],
					[
						"主怪",
						"主怪编号"
					],
					[
						"额外",
						"额外数据"
					],
					[
						"ro",
						"random"
					],
					[
						"进阶百足",
						"进阶百足将军	(Task670.lua)"
					],
					[
						"max",
						"math.max	(x, ...)"
					],
					[
						"取随机数",
						"取随机数()<=10	(AI战斗.lua)"
					],
					[
						"风云",
						"风云战将	(取师门.lua)"
					],
					[
						"ta",
						"table.print	(gge)"
					],
					[
						"雷霆",
						"雷霆万钧	(战斗技能.lua)"
					],
					[
						"小",
						"小还丹"
					],
					[
						"飞",
						"飞砂走石"
					],
					[
						"ma",
						"math.ceil	(x)"
					],
					[
						"sj",
						"sj()<=gl"
					],
					[
						"高级进击",
						"高级进击法暴"
					],
					[
						"剑",
						"剑荡四方"
					],
					[
						"指定",
						"指定对象=\"自己"
					],
					[
						"req",
						"require	require \"\""
					],
					[
						"cl",
						"clock"
					],
					[
						"取玩家",
						"取玩家战斗"
					],
					[
						"月",
						"月影"
					],
					[
						"e",
						"else	else end"
					],
					[
						"成员",
						"成员数据"
					],
					[
						"lo",
						"local	local x = 1"
					],
					[
						"法术",
						"法术状态"
					],
					[
						"数字",
						"数字id"
					],
					[
						"高级",
						"高级防御"
					],
					[
						"高级法术",
						"高级法术连击"
					],
					[
						"主人",
						"主人序号"
					],
					[
						"全场",
						"全场属性"
					],
					[
						"物理",
						"物理伤害结果"
					],
					[
						"增加",
						"增加战意"
					],
					[
						"气血",
						"气血上限"
					],
					[
						"暴",
						"暴怒"
					],
					[
						"玩家",
						"玩家id"
					],
					[
						"攻击",
						"攻击编号"
					],
					[
						"死亡",
						"死亡计算"
					],
					[
						"给予",
						"给予道具"
					],
					[
						"更新",
						"更新参战宝宝"
					],
					[
						"fun",
						"function	function"
					],
					[
						"技能",
						"技能回合CD"
					],
					[
						"dh",
						"dh-jr	dh-jr"
					],
					[
						"餐风",
						"餐风饮露"
					],
					[
						"dhwb",
						"dhwb-wj	dhwb-wj"
					],
					[
						"dhw",
						"dhwb-jr	dhwb-jr"
					],
					[
						"任务",
						"任务包裹"
					],
					[
						"天蚕",
						"天蚕丝"
					],
					[
						"补天",
						"补天石"
					],
					[
						"麒麟",
						"麒麟血火"
					],
					[
						"七宝",
						"七宝玲珑灯"
					],
					[
						"玄龟",
						"玄龟板水"
					],
					[
						"龙之",
						"龙之筋金"
					],
					[
						"清心",
						"清心咒"
					],
					[
						"金钱",
						"金钱镖"
					],
					[
						"金凤",
						"金凤羽金"
					],
					[
						"飞剑",
						"飞剑金"
					],
					[
						"神兵",
						"神兵鉴赏"
					],
					[
						"打造",
						"打造技巧"
					],
					[
						"级别",
						"级别限制"
					],
					[
						"扣除",
						"扣除银子"
					],
					[
						"os",
						"os.time	([table])"
					],
					[
						"in",
						"insert"
					],
					[
						"取",
						"取随机数"
					]
				]
			},
			"buffers":
			[
				{
					"file": "main.lua",
					"settings":
					{
						"buffer_size": 22742,
						"line_ending": "Windows"
					}
				},
				{
					"file": "Script/角色处理类/道具处理类.lua",
					"settings":
					{
						"buffer_size": 390352,
						"line_ending": "Windows"
					}
				}
			],
			"build_system": "Packages/Lua/ggeserver.sublime-build",
			"build_system_choices":
			[
				[
					[
						[
							"Packages/Lua/ggebc.sublime-build",
							"RunInCommand"
						],
						[
							"Packages/Lua/ggegame.sublime-build",
							"RunInCommand"
						],
						[
							"Packages/Lua/ggeserver.sublime-build",
							"RunInCommand"
						]
					],
					[
						"Packages/Lua/ggeserver.sublime-build",
						"RunInCommand"
					]
				],
				[
					[
						[
							"Packages/Lua/ggebc.sublime-build",
							"Stop"
						],
						[
							"Packages/Lua/ggegame.sublime-build",
							"Stop"
						],
						[
							"Packages/Lua/ggeserver.sublime-build",
							"Stop"
						]
					],
					[
						"Packages/Lua/ggeserver.sublime-build",
						"Stop"
					]
				]
			],
			"build_varint": "",
			"command_palette":
			{
				"height": 57.0,
				"last_filter": "",
				"selected_items":
				[
					[
						"Snippet: ",
						"Snippet: table.sort"
					],
					[
						"Snippet: cas",
						"Snippet: class模版"
					],
					[
						"remove",
						"Package Control: Remove Package"
					]
				],
				"width": 696.0
			},
			"console":
			{
				"height": 228.0,
				"history":
				[
				]
			},
			"distraction_free":
			{
				"menu_visible": true,
				"show_minimap": false,
				"show_open_files": false,
				"show_tabs": false,
				"side_bar_visible": false,
				"status_bar_visible": false
			},
			"expanded_folders":
			[
				"/D/MyGame/防官复古三经脉/服务端",
				"/D/MyGame/防官复古三经脉/服务端/Script",
				"/D/MyGame/防官复古三经脉/服务端/Script/角色处理类"
			],
			"file_history":
			[
				"/D/MyGame/防官复古三经脉/服务端/Script/系统处理类/系统处理类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/角色处理类/道具仓库.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/角色处理类/道具处理类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/初始化脚本.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/全局函数类/全局循环类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/地煞星.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/角色处理类/角色处理类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/活动.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/战斗处理类/怪物调用/结算处理.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/对话处理类/NPC对话处理.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/角色处理类/帮派处理.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/对话处理类/NPC对话内容.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/助战处理类/加载数据.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/对话处理类/对话调用/1854.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/物品数据.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/全局函数类/全局函数.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/对话处理类/初始.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/对话处理类/对话调用/1514.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/对话处理类/对话调用/1114.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/助战处理类/MateControl.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/战斗处理类/战斗处理.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/对话处理类/对话调用/1001.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/明暗雷怪.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/野怪.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/商店处理类/商城处理类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/系统处理类/假人事件类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/角色处理类/召唤兽处理类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/宝宝.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/任务处理类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task135.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/HPClient类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/ggeclient.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/战斗处理类/战斗计算/战斗技能.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/系统处理类/内充系统pay8.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/系统处理类/小巷子独家.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/全局函数类/方法函数类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/角色处理类/礼包奖励类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/系统处理类/内充系统.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/后台网络处理.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/协程通信.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/战斗处理类/创建战斗.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/对话处理类/对话调用/1844.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/帮派青龙玄武.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/系统处理类/GM工具类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/系统处理类/聊天处理类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/战斗处理类/战斗计算/战斗计算.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/战斗处理类/战斗计算/状态处理.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/助战处理类/基础信息.lua",
				"/D/MyGame/防官复古三经脉/风云源码/gge自己最新/gge自己最新/Core/Game/gge引擎.lua",
				"/D/MyGame/防官复古三经脉/版本更新/250122更新五福系统 - 副本/gge自己最新/Core/Game/副本/gge自己最新/Core/Game/gge引擎.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/战斗处理类/战斗计算/战斗固伤计算.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/对话处理类/商业对话.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/技能数据库.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/系统处理类/网络处理类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/系统处理类/首席弟子类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/战斗处理类/战斗计算/战斗执行类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/取师门.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/角色处理类/召唤兽进化.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/对话处理类/对话调用/1825.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/角色处理类/行囊出售处理.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/角色处理类/装备处理类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task5.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/投放怪.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/战斗处理类/战斗计算/物理技能计算.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/剑会.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task137.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/ggeserver.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/ggedebug.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/ggebuf.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/封包加密.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/安卓通信.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/商店处理类/商店处理类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/商店处理类/师门商店类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/地图处理类/地图坐标类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/地图处理类/地图处理类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/地图处理类/路径类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/地图处理类/MAP.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/属性控制/子女.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/属性控制/宠物.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/传送位置.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/传送圈位置.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/取经验.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/变身卡.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/场景等级.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/场景NPC.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/染色.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/法术技能特效.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/装备特技.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/数据中心/角色.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/系统处理类/游戏活动类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/角色处理类/内存类_物品.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/角色处理类/技能类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/角色处理类/队伍处理类.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task10.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task102.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task103.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task106.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task11.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task110.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task111.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task12.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task127.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task129.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task13.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task132.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task133.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task134.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task136.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task138.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task14.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task15.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task16.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task17.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task18.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task2.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task201.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task203.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task205.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task206.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task209.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task210.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task211.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task212.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task213.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task214.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task240.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task250.lua",
				"/D/MyGame/防官复古三经脉/服务端/Script/任务处理类/活动调用/Task3.lua"
			],
			"find":
			{
				"height": 33.0
			},
			"find_in_files":
			{
				"height": 138.0,
				"where_history":
				[
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\角色处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\maplj",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\全局函数类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\助战处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\系统处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类\\战斗处理.lua",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\角色处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\角色处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\角色处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类\\战斗处理.lua",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类\\战斗处理.lua",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\系统处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类\\战斗处理.lua",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\角色处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类\\战斗计算",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\角色处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\角色处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\角色处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\角色处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\角色处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\角色处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类\\战斗计算",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类\\战斗计算",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类\\战斗计算",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\数据中心",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\全局函数类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\系统处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\角色处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\对话处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\数据中心",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\线程",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script",
					"D:\\MyGame\\防官复古三经脉\\服务端\\Script\\战斗处理类"
				]
			},
			"find_state":
			{
				"case_sensitive": false,
				"find_history":
				[
					"3832",
					"索要其他角色临时行囊",
					"整理其他角色临时行囊啊",
					"整理临时行囊啊",
					"索要其他角色临时行囊",
					"索要临时行囊",
					"整理临时行囊啊",
					"玩家数据[数字id].角色",
					"索要临时行囊",
					"整理临时行囊啊",
					"self.数据",
					"索要其他角色临时行囊",
					"玩家数据[id].角色",
					"索要其他角色临时行囊",
					"满足条件",
					"索要临时行囊",
					"临时行囊",
					"道具仓库.txt",
					"索要临时行囊",
					"3832",
					"超级兽决",
					"重置数据",
					"副本数据",
					"副本",
					"重置数据",
					"车迟",
					"车迟国",
					"整理临时行囊啊",
					"索要临时行囊",
					"3832",
					"熟练度",
					"添加银子",
					"掉落列表",
					"给予道具",
					"给于",
					"掉落数据",
					"仙玉点卡是否",
					"地煞",
					"奇经八脉",
					"四打",
					"取帮派建筑达标情况",
					"取升级建筑对话",
					"取帮派建筑达标情况",
					"取升级建筑对话",
					"1级建筑",
					"封禁",
					"锦囊",
					"年兽",
					"更新玩家每日",
					"叛逆",
					"年兽",
					"剧情直飞特权",
					"小巷子",
					"你账号下没有其他角色，无法使用！",
					"你账号下没有角色，无法使用！",
					"飞升凭证",
					"服饰.数据",
					"确认激活",
					"恭喜少侠通过考验，现在我可以为你激活渡劫专用锦衣",
					"主线=14",
					"渡劫锦衣",
					"生死劫",
					"飞升",
					"生死劫",
					"历劫.渡劫",
					"取任务",
					"获取任务信息",
					"飞升",
					"月卡",
					"获取任务信息",
					"飞升凭证",
					"进入游戏",
					"增加潜力",
					"升级技能",
					"升级",
					"洗点操作",
					"重置人物",
					"名称数据",
					"玩家数据",
					"获取数据",
					"助战处理类:数据处理",
					"助战处理类",
					"677",
					"分角色",
					"粉橘色",
					"bb修炼",
					"飞升降修",
					"飞升",
					"取修炼",
					"开始渡劫",
					"渡劫",
					"加人物修炼经验",
					"修炼",
					"飞升",
					"月卡",
					"飞升凭证",
					"癫散戏票·双城记",
					"谛听",
					"赐福列表",
					"修罗傀儡鬼",
					"1920",
					"30010",
					"持国",
					"谛听",
					"月华露品质",
					"魔兽要诀",
					"商城",
					"化圣",
					"仙露",
					"42",
					"52",
					"设置任务135",
					"gxsc",
					"剧情直飞",
					"暂未开放",
					"内政",
					"金银",
					"127.64.247.33",
					"127.0.0.1",
					"原本42秒",
					"等待起始",
					"等待计时",
					"等待起始",
					"计时",
					"50",
					"60",
					"元身",
					"设置玄武任务"
				],
				"highlight": true,
				"in_selection": false,
				"preserve_case": false,
				"regex": false,
				"replace_history":
				[
					"127.0.0.1",
					"127.64.247.33",
					"@作者: www.wnfj.com",
					"www.wnfj.com",
					"万能飞机：www.wnfj.com",
					"\"九黎城\"",
					"发送信息[1][i]",
					"影精灵.dll",
					"共享仓库类",
					"",
					"签到奖品.txt",
					"",
					"小巷子定制\\签到配置",
					"一颗",
					"",
					"程序目录..[[小巷子定制\\掉落配置\\]]..序号..\".ini\"",
					"程序目录..[[小巷子定制\\掉落配置\\]]..\"..序号..\".ini\"\"",
					"程序目录..[[小巷子定制\\掉落配置\\]]..\"..序号..[[.]]ini\"",
					"程序目录..[[小巷子定制\\掉落配置\\]]..\"..序号..[.]ini\"",
					"奖励物品",
					"更多游戏请访问小巷子资源网：WWW.vvxxz.com,版本定制授权联系QQ：79550111,36537044",
					"小巷子资源网，联系QQ：79550111,36537044",
					"通天河",
					"角色",
					"帮派青龙玄武:",
					"帮派青龙:",
					"青龙:",
					"完成任务_",
					"设置任务",
					"完成任务_",
					"设置任务",
					",6557,",
					"如梦奇谭之五更寒",
					"NPCcl_",
					"NPCdhcl_",
					"NPCdh_",
					"胜利MOB_",
					"~= 99999999",
					"~= 1314520",
					"怪物对话解析",
					"怪物对话内容",
					"任务说明",
					"rwgx",
					"奖励",
					"驱鬼",
					"不可封印=10",
					"不可封印=100"
				],
				"reverse": false,
				"show_context": true,
				"use_buffer2": true,
				"whole_word": false,
				"wrap": true
			},
			"groups":
			[
				{
					"selected": 1,
					"sheets":
					[
						{
							"buffer": 0,
							"file": "main.lua",
							"semi_transient": false,
							"settings":
							{
								"buffer_size": 22742,
								"regions":
								{
								},
								"selection":
								[
									[
										9168,
										9168
									]
								],
								"settings":
								{
									"BracketHighlighterBusy": false,
									"bh_regions":
									[
										"bh_c_define",
										"bh_c_define_center",
										"bh_c_define_open",
										"bh_c_define_close",
										"bh_c_define_content",
										"bh_default",
										"bh_default_center",
										"bh_default_open",
										"bh_default_close",
										"bh_default_content",
										"bh_round",
										"bh_round_center",
										"bh_round_open",
										"bh_round_close",
										"bh_round_content",
										"bh_curly",
										"bh_curly_center",
										"bh_curly_open",
										"bh_curly_close",
										"bh_curly_content",
										"bh_double_quote",
										"bh_double_quote_center",
										"bh_double_quote_open",
										"bh_double_quote_close",
										"bh_double_quote_content",
										"bh_angle",
										"bh_angle_center",
										"bh_angle_open",
										"bh_angle_close",
										"bh_angle_content",
										"bh_tag",
										"bh_tag_center",
										"bh_tag_open",
										"bh_tag_close",
										"bh_tag_content",
										"bh_regex",
										"bh_regex_center",
										"bh_regex_open",
										"bh_regex_close",
										"bh_regex_content",
										"bh_square",
										"bh_square_center",
										"bh_square_open",
										"bh_square_close",
										"bh_square_content",
										"bh_unmatched",
										"bh_unmatched_center",
										"bh_unmatched_open",
										"bh_unmatched_close",
										"bh_unmatched_content",
										"bh_single_quote",
										"bh_single_quote_center",
										"bh_single_quote_open",
										"bh_single_quote_close",
										"bh_single_quote_content"
									],
									"bracket_highlighter.busy": false,
									"bracket_highlighter.locations":
									{
										"close":
										{
											"1":
											[
												6882,
												6883
											]
										},
										"icon":
										{
											"1":
											[
												"Packages/BracketHighlighter/icons/double_quote.png",
												"region.greenish"
											]
										},
										"open":
										{
											"1":
											[
												6868,
												6869
											]
										},
										"unmatched":
										{
										}
									},
									"bracket_highlighter.regions":
									[
										"bh_tag",
										"bh_tag_center",
										"bh_tag_open",
										"bh_tag_close",
										"bh_tag_content",
										"bh_double_quote",
										"bh_double_quote_center",
										"bh_double_quote_open",
										"bh_double_quote_close",
										"bh_double_quote_content",
										"bh_curly",
										"bh_curly_center",
										"bh_curly_open",
										"bh_curly_close",
										"bh_curly_content",
										"bh_default",
										"bh_default_center",
										"bh_default_open",
										"bh_default_close",
										"bh_default_content",
										"bh_angle",
										"bh_angle_center",
										"bh_angle_open",
										"bh_angle_close",
										"bh_angle_content",
										"bh_unmatched",
										"bh_unmatched_center",
										"bh_unmatched_open",
										"bh_unmatched_close",
										"bh_unmatched_content",
										"bh_regex",
										"bh_regex_center",
										"bh_regex_open",
										"bh_regex_close",
										"bh_regex_content",
										"bh_square",
										"bh_square_center",
										"bh_square_open",
										"bh_square_close",
										"bh_square_content",
										"bh_single_quote",
										"bh_single_quote_center",
										"bh_single_quote_open",
										"bh_single_quote_close",
										"bh_single_quote_content",
										"bh_c_define",
										"bh_c_define_center",
										"bh_c_define_open",
										"bh_c_define_close",
										"bh_c_define_content",
										"bh_round",
										"bh_round_center",
										"bh_round_open",
										"bh_round_close",
										"bh_round_content"
									],
									"c_time":
									[
										128,
										3,
										99,
										100,
										97,
										116,
										101,
										116,
										105,
										109,
										101,
										10,
										100,
										97,
										116,
										101,
										116,
										105,
										109,
										101,
										10,
										113,
										0,
										67,
										10,
										7,
										232,
										3,
										27,
										17,
										24,
										45,
										0,
										0,
										0,
										113,
										1,
										133,
										113,
										2,
										82,
										113,
										3,
										46
									],
									"color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme",
									"function_name_status_row": 277,
									"is_init_dirty_state": false,
									"origin_encoding": "UTF-8",
									"syntax": "Packages/Lua/Lua.sublime-syntax",
									"tab_size": 4,
									"translate_tabs_to_spaces": false
								},
								"translation.x": 0.0,
								"translation.y": 6750.0,
								"zoom_level": 1.0
							},
							"stack_index": 1,
							"type": "text"
						},
						{
							"buffer": 1,
							"file": "Script/角色处理类/道具处理类.lua",
							"semi_transient": false,
							"settings":
							{
								"buffer_size": 390352,
								"regions":
								{
								},
								"selection":
								[
									[
										12541,
										12541
									]
								],
								"settings":
								{
									"BracketHighlighterBusy": false,
									"bh_regions":
									[
										"bh_angle",
										"bh_angle_center",
										"bh_angle_open",
										"bh_angle_close",
										"bh_angle_content",
										"bh_single_quote",
										"bh_single_quote_center",
										"bh_single_quote_open",
										"bh_single_quote_close",
										"bh_single_quote_content",
										"bh_square",
										"bh_square_center",
										"bh_square_open",
										"bh_square_close",
										"bh_square_content",
										"bh_regex",
										"bh_regex_center",
										"bh_regex_open",
										"bh_regex_close",
										"bh_regex_content",
										"bh_double_quote",
										"bh_double_quote_center",
										"bh_double_quote_open",
										"bh_double_quote_close",
										"bh_double_quote_content",
										"bh_curly",
										"bh_curly_center",
										"bh_curly_open",
										"bh_curly_close",
										"bh_curly_content",
										"bh_tag",
										"bh_tag_center",
										"bh_tag_open",
										"bh_tag_close",
										"bh_tag_content",
										"bh_c_define",
										"bh_c_define_center",
										"bh_c_define_open",
										"bh_c_define_close",
										"bh_c_define_content",
										"bh_default",
										"bh_default_center",
										"bh_default_open",
										"bh_default_close",
										"bh_default_content",
										"bh_round",
										"bh_round_center",
										"bh_round_open",
										"bh_round_close",
										"bh_round_content",
										"bh_unmatched",
										"bh_unmatched_center",
										"bh_unmatched_open",
										"bh_unmatched_close",
										"bh_unmatched_content"
									],
									"c_time":
									[
										128,
										3,
										99,
										100,
										97,
										116,
										101,
										116,
										105,
										109,
										101,
										10,
										100,
										97,
										116,
										101,
										116,
										105,
										109,
										101,
										10,
										113,
										0,
										67,
										10,
										7,
										232,
										9,
										20,
										17,
										41,
										31,
										0,
										0,
										0,
										113,
										1,
										133,
										113,
										2,
										82,
										113,
										3,
										46
									],
									"color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme",
									"function_name_status_row": 547,
									"origin_encoding": "UTF-8",
									"syntax": "Packages/Lua/Lua.sublime-syntax",
									"translate_tabs_to_spaces": false
								},
								"translation.x": 0.0,
								"translation.y": 13215.0,
								"zoom_level": 1.0
							},
							"stack_index": 0,
							"type": "text"
						}
					]
				}
			],
			"incremental_find":
			{
				"height": 33.0
			},
			"input":
			{
				"height": 43.0
			},
			"layout":
			{
				"cells":
				[
					[
						0,
						0,
						1,
						1
					]
				],
				"cols":
				[
					0.0,
					1.0
				],
				"rows":
				[
					0.0,
					1.0
				]
			},
			"menu_visible": true,
			"output.SublimeLinter":
			{
				"height": 0.0
			},
			"output.exec":
			{
				"height": 192.0
			},
			"output.find_results":
			{
				"height": 0.0
			},
			"output.mdpopups":
			{
				"height": 0.0
			},
			"pinned_build_system": "Packages/Lua/ggeserver.sublime-build",
			"position": "0,2,3,-32000,-32000,-1,-1,591,52,52,708",
			"project": "开发服务端.sublime-project",
			"replace":
			{
				"height": 62.0
			},
			"save_all_on_build": true,
			"select_file":
			{
				"height": 0.0,
				"last_filter": "",
				"selected_items":
				[
					[
						"给予",
						"111111服务端\\给予道具.txt"
					],
					[
						"",
						"111111服务端\\Script\\数据中心\\宝宝.lua"
					]
				],
				"width": 0.0
			},
			"select_project":
			{
				"height": 0.0,
				"last_filter": "",
				"selected_items":
				[
				],
				"width": 0.0
			},
			"select_symbol":
			{
				"height": 0.0,
				"last_filter": "",
				"selected_items":
				[
				],
				"width": 0.0
			},
			"selected_group": 0,
			"settings":
			{
			},
			"show_minimap": true,
			"show_open_files": false,
			"show_tabs": true,
			"side_bar_visible": true,
			"side_bar_width": 223.0,
			"status_bar_visible": true,
			"template_settings":
			{
			},
			"window_id": 1,
			"workspace_name": "/D/MyGame/防官复古三经脉/服务端/开发服务端.sublime-workspace"
		},
		{
			"auto_complete":
			{
				"selected_items":
				[
					[
						"枫",
						"枫影二刃1	(战斗类.lua)"
					],
					[
						"超级技能",
						"超级技能替换	(超级技能替换.lua)"
					],
					[
						"门派",
						"门派转换费用"
					],
					[
						"常规",
						"常规提示:打开(\"#Y你发起了对	(玩家.lua)"
					],
					[
						"武神坛",
						"武神坛模式"
					],
					[
						"换",
						"换抽奖"
					],
					[
						"sel",
						"self"
					],
					[
						"else",
						"elseif"
					],
					[
						"co",
						"copy"
					],
					[
						"tl",
						"table"
					],
					[
						"se",
						"self:打开"
					],
					[
						"置",
						"置物品"
					],
					[
						"t",
						"table"
					],
					[
						"fs",
						"fsb"
					],
					[
						"锦衣数据",
						"锦衣数据SYS	(main.lua)"
					],
					[
						"talb",
						"table.print	(gge)"
					],
					[
						"ta",
						"table.print	(gge)"
					],
					[
						"tal",
						"table.print	(gge)"
					],
					[
						"tab",
						"table.print	(gge)"
					],
					[
						"tablep",
						"table.print	(gge)"
					],
					[
						"to",
						"tostring	(e)"
					],
					[
						"for",
						"for	for i=1,10"
					],
					[
						"蛛丝",
						"蛛丝阵法	(技能库.lua)"
					],
					[
						"日光",
						"日光耀	(技能库.lua)"
					],
					[
						"落叶",
						"落叶萧萧	(技能库.lua)"
					],
					[
						"f",
						"function	function"
					],
					[
						"fa",
						"false"
					],
					[
						"re",
						"return	return 返回"
					],
					[
						"table",
						"table.print	(gge)"
					],
					[
						"te",
						"true"
					],
					[
						"e",
						"else	else end"
					],
					[
						"w",
						"while	while cond"
					],
					[
						"大唐",
						"大唐官府"
					],
					[
						"全屏",
						"全屏法术"
					],
					[
						"小",
						"小桃红"
					],
					[
						"袁",
						"袁天罡"
					],
					[
						"小白",
						"小白龙"
					],
					[
						"普陀",
						"普陀_接引仙女"
					],
					[
						"烹饪",
						"烹饪技巧"
					],
					[
						"王",
						"女人_王大嫂"
					],
					[
						"PR",
						"print"
					],
					[
						"fh",
						"fhz2"
					],
					[
						"fhz",
						"fhz1"
					],
					[
						"裁缝",
						"裁缝技巧"
					],
					[
						"打造",
						"打造技巧"
					],
					[
						"分解",
						"分解装备"
					],
					[
						"人物",
						"人物装备"
					],
					[
						"召唤兽",
						"召唤兽装备"
					],
					[
						"灵",
						"灵石"
					],
					[
						"普通",
						"普通人物装备"
					],
					[
						"制造",
						"制造类"
					],
					[
						"资源",
						"资源组"
					],
					[
						"lo",
						"local	local x = 1"
					],
					[
						"五",
						"五味露"
					],
					[
						"os",
						"os.time	([table])"
					],
					[
						"状态",
						"状态特效"
					],
					[
						"npc",
						"npc查询"
					],
					[
						"当前",
						"当前地图"
					],
					[
						"A",
						"ARGB	(主控.lua)"
					],
					[
						"n",
						"not"
					],
					[
						"rn",
						"return	return 返回"
					],
					[
						"cjs",
						"cjsr	创建输入"
					],
					[
						"gge",
						"ggewzl	 ggewzl "
					],
					[
						"ggeu",
						"ggeui	 ggeui "
					],
					[
						"str",
						"string"
					],
					[
						"fun",
						"function	function"
					],
					[
						"ca",
						"class"
					],
					[
						"净瓶",
						"净瓶女娲_普通	(普通模型库.lua)"
					],
					[
						"千年",
						"千年蛇魅_普通	(普通模型库.lua)"
					],
					[
						"巡游",
						"巡游天神_普通	(普通模型库.lua)"
					],
					[
						"c",
						"class	class模版"
					],
					[
						"run",
						"return	return 返回"
					]
				]
			},
			"buffers":
			[
				{
					"file": "main.lua",
					"settings":
					{
						"buffer_size": 24795,
						"line_ending": "Windows"
					}
				},
				{
					"file": "script/功能界面/底图框.lua",
					"settings":
					{
						"buffer_size": 16275,
						"line_ending": "Windows"
					}
				},
				{
					"file": "script/更新类/临时行囊.lua",
					"settings":
					{
						"buffer_size": 9698,
						"line_ending": "Windows"
					}
				}
			],
			"build_system": "Packages/Lua/ggegame.sublime-build",
			"build_system_choices":
			[
				[
					[
						[
							"Packages/Lua/ggegame.sublime-build",
							"RunInCommand"
						],
						[
							"Packages/Lua/ggeobj.sublime-build",
							"RunInCommand"
						],
						[
							"Packages/Lua/ggeserver.sublime-build",
							"RunInCommand"
						]
					],
					[
						"Packages/Lua/ggeserver.sublime-build",
						"RunInCommand"
					]
				],
				[
					[
						[
							"Packages/Lua/ggegame.sublime-build",
							"Stop"
						],
						[
							"Packages/Lua/ggeobj.sublime-build",
							"Stop"
						],
						[
							"Packages/Lua/ggeserver.sublime-build",
							"Stop"
						]
					],
					[
						"Packages/Lua/ggegame.sublime-build",
						"Stop"
					]
				]
			],
			"build_varint": "",
			"command_palette":
			{
				"height": 357.0,
				"last_filter": "",
				"selected_items":
				[
					[
						"remove",
						"Package Control: Remove Package"
					],
					[
						"Snippet: ",
						"Snippet: #!/usr/bin/env"
					]
				],
				"width": 446.0
			},
			"console":
			{
				"height": 0.0,
				"history":
				[
				]
			},
			"distraction_free":
			{
				"menu_visible": true,
				"show_minimap": false,
				"show_open_files": false,
				"show_tabs": false,
				"side_bar_visible": false,
				"status_bar_visible": false
			},
			"expanded_folders":
			[
				"/D/MyGame/防官复古三经脉/客户端",
				"/D/MyGame/防官复古三经脉/客户端/script",
				"/D/MyGame/防官复古三经脉/客户端/script/更新类"
			],
			"file_history":
			[
				"/D/MyGame/防官复古三经脉/客户端/script/网络/hp.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/更新类/经脉流派.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/显示类/道具详情.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/宠物打书内丹栏.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/数据中心/物品库.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/功能界面/底图框.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/战斗类/战斗类.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/内充系统/内充系统pay8.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/全局/主控.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/全局/变量2.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/更新类/累充礼包.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/网络/数据交换.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/内充系统/内充系统.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/战斗类/战斗命令类.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/数据中心/技能库.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/资源类/加载类.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/召唤兽资质栏.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/数据中心/自定义库.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/初系统/选择角色动画.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/属性控制/队伍.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/数据中心/特效库.lua",
				"/D/MyGame/防官复古三经脉/风云源码/gge自己最新/gge自己最新/Core/Game/gge引擎.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/战斗类/战斗单位类.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/更新类/帮派技能学习.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/更新类/临时行囊.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/战斗类/战斗自动栏.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/数据中心/特效.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/全局/主显.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/更新类/角色转换.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/战斗类/战斗技能栏.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/功能界面/坐骑属性栏.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/新道具行囊.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/助战类/助战道具行囊.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/剑会/剑会匹配.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/初系统/标题.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/全局/人物.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/全局/假人控制.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/全局/假人.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/全局/玩家.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/初系统/登陆.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/功能界面/交易类.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/功能界面/召唤兽驯养.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/功能界面/合成旗类.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/功能界面/坐骑修炼.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/功能界面/坐骑炫彩.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/功能界面/坐骑统御.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/功能界面/好友列表.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/功能界面/好友查看类.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/功能界面/好友消息类.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/功能界面/小地图.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/功能界面/时辰.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/功能界面/更多属性.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/功能界面/管理界面.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/功能界面/给予类.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/功能界面/给予NPC.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/功能界面/聊天框.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/功能界面/进化宝宝.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/功能界面/首席投票榜.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/一键附魔.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/临时背包.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/事件.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/人物炫彩.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/人物状态栏.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/人物称谓栏.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/任务栏.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/任务追踪栏.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/传送点.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/修炼升级.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/共享仓库.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/出售.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/创建帮派.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/剧情处理器.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/加入帮派.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/召唤兽仓库.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/召唤兽查看栏.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/召唤兽炫彩.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/商会宠物上架.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/商会宠物界面.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/商会物品上架.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/商会物品界面.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/商店.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/子女养育.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/宠物炼妖栏.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/宠物状态栏.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/宠物领养栏.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/帮派界面.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/幻化.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/强化技能学习.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/快捷技能选择栏.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/打造.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/技能学习.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/新宠物炼妖栏.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/染色.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/梦幻指引.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/法宝锻造.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/法宝.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/灵饰.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/生死劫.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/第二场景.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/组合输入框.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/自由事件.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/自由技能栏.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/道具仓库.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/道具行囊.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/钓鱼.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/锦衣.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/长安保卫战.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/队伍栏.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/队伍阵型栏.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/场景类/飞行符.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/多重对话类/任务事件.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/多重对话类/对话栏.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/战斗类/战斗状态栏.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/战斗类/战斗观战.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/战斗类/战斗道具栏.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/数据中心/场景NPC.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/数据中心/坐骑库.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/数据中心/头像库.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/数据中心/战斗模型库.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/数据中心/技能库22.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/数据中心/明雷库.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/数据中心/点库.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/数据中心/音效库.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/显示类/喊话.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/显示类/常规提示.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/显示类/技能.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/显示类/技能_格子.lua",
				"/D/MyGame/防官复古三经脉/客户端/script/显示类/文本栏.lua"
			],
			"find":
			{
				"height": 41.0
			},
			"find_in_files":
			{
				"height": 138.0,
				"where_history":
				[
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\全局",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类\\战斗类.lua",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类\\战斗类.lua",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\全局",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\初系统",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\资源类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\Log",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\显示类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\全局",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类\\战斗单位类.lua",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\资源类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\全局",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script",
					"D:\\MyGame\\防官复古三经脉\\客户端\\script\\战斗类"
				]
			},
			"find_state":
			{
				"case_sensitive": true,
				"find_history":
				[
					"[15]",
					"战利品行囊",
					"排列",
					"[11]",
					"超级行囊",
					"维护",
					"接服务失败",
					"111111111",
					"jn",
					"self.资源组[12]",
					"[12]",
					"门派闯关活动使者",
					"UI_任务",
					"ALT+Q",
					"发送数据2",
					"排列",
					"[8]",
					" [8]",
					"角色多开",
					"证",
					"凭证",
					"器",
					"仙露丸",
					"宠物打书内丹",
					"仙露丸",
					"梦幻西游",
					"全局游戏标题",
					"群",
					"红色",
					"描边文字20",
					"换抓鬼",
					"CDK充值",
					"命令数据",
					"秒显示",
					"命令数据",
					"暂未开放",
					"首冲",
					"授权码==\"suvvv.com\"",
					"CDK",
					"命令数据",
					"等待时间",
					"命令数据",
					"等待时间",
					"计时",
					"等待",
					"战斗",
					"时间",
					"命令数据",
					"计时",
					"42",
					"50",
					"时间",
					"http",
					"www.",
					"确认充值",
					"支付宝",
					"您的账号已经",
					"每日可用次数",
					"内置加速",
					"内充系统",
					"qq1369639077",
					"9xRVCmGmz33nf5dRwnhfYregfrrBz5",
					"CDK",
					"授权码",
					"力辟苍穹",
					"全局ip",
					"梦幻西游",
					"env",
					"dll",
					"9xRVCmGmz33nf5dRwnhfYregfrrBz5",
					"授权码 ~=\"9xRVCmGmz33nf5dRwnhfYregfrrBz5\"",
					"扫码充值",
					"mysql",
					"独行",
					"高级独行",
					"高级毒性",
					"高级进击必杀",
					"独行",
					"zdy",
					"\"zdy.rpk\"",
					"独行",
					"空白",
					"超级赐福",
					"超级技能",
					"赐福",
					"赐福、",
					"否定",
					"空白",
					"否定信仰",
					"超信仰",
					"903",
					"空白",
					"超级否定",
					"skill法术特效后置",
					"状态_璞玉灵钵",
					"dll",
					"vvxx",
					"资源类_加载:初始化()",
					"风沙之盾",
					"变身",
					"资源类_加载:初始化()",
					"变身",
					"独行",
					"超级否定",
					"5505.5",
					"设置指令1",
					"self.人物资源组[6]",
					"人物资源组",
					"[6] ",
					"操作类型",
					"参数=",
					"暂未参战",
					"5503",
					"参数=",
					"登陆清空",
					"标题",
					"同步气血",
					"流程==100",
					"加血",
					"[3]",
					"[3] ",
					"辅助技能",
					"[14]",
					"一键出售",
					"战斗连击显示",
					"连击",
					"315",
					"[12]"
				],
				"highlight": true,
				"in_selection": false,
				"preserve_case": false,
				"regex": false,
				"replace_history":
				[
					"授权码==\"suvvv.com\"  or 授权码==\"mkB67RrAFIj1F1KwGo\"",
					"授权码==\"suvvv.com\"  or 授权码==\"suvvv.com\"",
					"suvvv.com",
					"授权码 ~=\"9xRVCmGmz33nf5dRwnhfYregfrrBz5\" or 授权码 ~=\"qq1369639077\"",
					"@作者: www.wnfj.com",
					"更多游戏请访问万能飞机：www.wnfj.com",
					"影精灵.dll",
					"vvxxzcom/yjl/jmtb.wdf",
					"vvxxzcom/yjl/影精灵.dll",
					"vvxxzcom/yjl/影精灵.wdf",
					"战斗超级技能",
					"超级技能战斗显示",
					",jns_8",
					",jns_7",
					",jns_6",
					",jns_3",
					"jns_1",
					",jns_8",
					",jns_7",
					",jns_6",
					",jns_3",
					"jns_1",
					"共享仓库类",
					"召唤兽驯养",
					"self.战斗单位[ljcs]",
					"self.角色",
					"tp.队伍[1].强化技能",
					"场景类_强化技能学习",
					"黄色",
					"孤儿手册",
					"孩子兑换",
					"vvxxzcom/pic/",
					"\"vvxxzcom/pic/",
					"self.y+158",
					"\"副本积分\"",
					"",
					"self.VIP经验",
					"[15]",
					"\"vvxxzcom/vvxxzitem.wdf\"",
					"self.图鉴",
					"wp",
					"物品组",
					"vvxxzcom/登陆资源.wdf",
					"vvxxzcom/祥瑞坐骑.wdf",
					"3740",
					"",
					"场景类_道具行囊",
					"\"主人公\"",
					"人物",
					"'aaa.wdf',\"网易WDF动画\",0x1343E14",
					"临时序列",
					"common/mapani.wdf",
					"common/item.wdf",
					"common/wzife.wdf",
					"wzife.wd4",
					"common/wzife.wdf",
					"",
					"战斗单位[ljcs]",
					"战斗单位..ljcs",
					"引擎.场景",
					"zts",
					"模型 == \"进阶",
					"zdy3",
					"wzife.wdf",
					"zdy3.rpk",
					"dy3.rpk",
					"600",
					"800",
					"820",
					"600",
					"800"
				],
				"reverse": false,
				"show_context": true,
				"use_buffer2": true,
				"whole_word": false,
				"wrap": true
			},
			"groups":
			[
				{
					"selected": 2,
					"sheets":
					[
						{
							"buffer": 0,
							"file": "main.lua",
							"semi_transient": false,
							"settings":
							{
								"buffer_size": 24795,
								"regions":
								{
								},
								"selection":
								[
									[
										8256,
										8256
									]
								],
								"settings":
								{
									"BracketHighlighterBusy": false,
									"bh_regions":
									[
										"bh_c_define",
										"bh_c_define_center",
										"bh_c_define_open",
										"bh_c_define_close",
										"bh_c_define_content",
										"bh_square",
										"bh_square_center",
										"bh_square_open",
										"bh_square_close",
										"bh_square_content",
										"bh_single_quote",
										"bh_single_quote_center",
										"bh_single_quote_open",
										"bh_single_quote_close",
										"bh_single_quote_content",
										"bh_curly",
										"bh_curly_center",
										"bh_curly_open",
										"bh_curly_close",
										"bh_curly_content",
										"bh_double_quote",
										"bh_double_quote_center",
										"bh_double_quote_open",
										"bh_double_quote_close",
										"bh_double_quote_content",
										"bh_tag",
										"bh_tag_center",
										"bh_tag_open",
										"bh_tag_close",
										"bh_tag_content",
										"bh_default",
										"bh_default_center",
										"bh_default_open",
										"bh_default_close",
										"bh_default_content",
										"bh_round",
										"bh_round_center",
										"bh_round_open",
										"bh_round_close",
										"bh_round_content",
										"bh_regex",
										"bh_regex_center",
										"bh_regex_open",
										"bh_regex_close",
										"bh_regex_content",
										"bh_unmatched",
										"bh_unmatched_center",
										"bh_unmatched_open",
										"bh_unmatched_close",
										"bh_unmatched_content",
										"bh_angle",
										"bh_angle_center",
										"bh_angle_open",
										"bh_angle_close",
										"bh_angle_content"
									],
									"bracket_highlighter.busy": false,
									"bracket_highlighter.locations":
									{
										"close":
										{
										},
										"icon":
										{
										},
										"open":
										{
										},
										"unmatched":
										{
										}
									},
									"bracket_highlighter.regions":
									[
										"bh_angle",
										"bh_angle_center",
										"bh_angle_open",
										"bh_angle_close",
										"bh_angle_content",
										"bh_c_define",
										"bh_c_define_center",
										"bh_c_define_open",
										"bh_c_define_close",
										"bh_c_define_content",
										"bh_curly",
										"bh_curly_center",
										"bh_curly_open",
										"bh_curly_close",
										"bh_curly_content",
										"bh_default",
										"bh_default_center",
										"bh_default_open",
										"bh_default_close",
										"bh_default_content",
										"bh_double_quote",
										"bh_double_quote_center",
										"bh_double_quote_open",
										"bh_double_quote_close",
										"bh_double_quote_content",
										"bh_regex",
										"bh_regex_center",
										"bh_regex_open",
										"bh_regex_close",
										"bh_regex_content",
										"bh_round",
										"bh_round_center",
										"bh_round_open",
										"bh_round_close",
										"bh_round_content",
										"bh_single_quote",
										"bh_single_quote_center",
										"bh_single_quote_open",
										"bh_single_quote_close",
										"bh_single_quote_content",
										"bh_square",
										"bh_square_center",
										"bh_square_open",
										"bh_square_close",
										"bh_square_content",
										"bh_tag",
										"bh_tag_center",
										"bh_tag_open",
										"bh_tag_close",
										"bh_tag_content",
										"bh_unmatched",
										"bh_unmatched_center",
										"bh_unmatched_open",
										"bh_unmatched_close",
										"bh_unmatched_content"
									],
									"c_time":
									[
										128,
										3,
										99,
										100,
										97,
										116,
										101,
										116,
										105,
										109,
										101,
										10,
										100,
										97,
										116,
										101,
										116,
										105,
										109,
										101,
										10,
										113,
										0,
										67,
										10,
										7,
										232,
										3,
										27,
										17,
										24,
										59,
										0,
										0,
										0,
										113,
										1,
										133,
										113,
										2,
										82,
										113,
										3,
										46
									],
									"color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme",
									"function_name_status_row": 289,
									"is_init_dirty_state": false,
									"origin_encoding": "UTF-8",
									"syntax": "Packages/Lua/Lua.sublime-syntax",
									"tab_size": 4,
									"translate_tabs_to_spaces": false
								},
								"translation.x": 0.0,
								"translation.y": 6870.0,
								"zoom_level": 1.0
							},
							"stack_index": 2,
							"type": "text"
						},
						{
							"buffer": 1,
							"file": "script/功能界面/底图框.lua",
							"semi_transient": false,
							"settings":
							{
								"buffer_size": 16275,
								"regions":
								{
								},
								"selection":
								[
									[
										13975,
										13958
									]
								],
								"settings":
								{
									"BracketHighlighterBusy": false,
									"bh_regions":
									[
										"bh_c_define",
										"bh_c_define_center",
										"bh_c_define_open",
										"bh_c_define_close",
										"bh_c_define_content",
										"bh_default",
										"bh_default_center",
										"bh_default_open",
										"bh_default_close",
										"bh_default_content",
										"bh_round",
										"bh_round_center",
										"bh_round_open",
										"bh_round_close",
										"bh_round_content",
										"bh_curly",
										"bh_curly_center",
										"bh_curly_open",
										"bh_curly_close",
										"bh_curly_content",
										"bh_double_quote",
										"bh_double_quote_center",
										"bh_double_quote_open",
										"bh_double_quote_close",
										"bh_double_quote_content",
										"bh_angle",
										"bh_angle_center",
										"bh_angle_open",
										"bh_angle_close",
										"bh_angle_content",
										"bh_tag",
										"bh_tag_center",
										"bh_tag_open",
										"bh_tag_close",
										"bh_tag_content",
										"bh_regex",
										"bh_regex_center",
										"bh_regex_open",
										"bh_regex_close",
										"bh_regex_content",
										"bh_square",
										"bh_square_center",
										"bh_square_open",
										"bh_square_close",
										"bh_square_content",
										"bh_unmatched",
										"bh_unmatched_center",
										"bh_unmatched_open",
										"bh_unmatched_close",
										"bh_unmatched_content",
										"bh_single_quote",
										"bh_single_quote_center",
										"bh_single_quote_open",
										"bh_single_quote_close",
										"bh_single_quote_content"
									],
									"c_time":
									[
										128,
										3,
										99,
										100,
										97,
										116,
										101,
										116,
										105,
										109,
										101,
										10,
										100,
										97,
										116,
										101,
										116,
										105,
										109,
										101,
										10,
										113,
										0,
										67,
										10,
										7,
										232,
										9,
										20,
										17,
										52,
										18,
										0,
										0,
										0,
										113,
										1,
										133,
										113,
										2,
										82,
										113,
										3,
										46
									],
									"color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme",
									"function_name_status_row": 396,
									"origin_encoding": "UTF-8",
									"syntax": "Packages/Lua/Lua.sublime-syntax",
									"translate_tabs_to_spaces": false
								},
								"translation.x": 0.0,
								"translation.y": 9107.0,
								"zoom_level": 1.0
							},
							"stack_index": 1,
							"type": "text"
						},
						{
							"buffer": 2,
							"file": "script/更新类/临时行囊.lua",
							"semi_transient": false,
							"settings":
							{
								"buffer_size": 9698,
								"regions":
								{
								},
								"selection":
								[
									[
										1113,
										1113
									]
								],
								"settings":
								{
									"BracketHighlighterBusy": false,
									"bh_regions":
									[
										"bh_angle",
										"bh_angle_center",
										"bh_angle_open",
										"bh_angle_close",
										"bh_angle_content",
										"bh_single_quote",
										"bh_single_quote_center",
										"bh_single_quote_open",
										"bh_single_quote_close",
										"bh_single_quote_content",
										"bh_square",
										"bh_square_center",
										"bh_square_open",
										"bh_square_close",
										"bh_square_content",
										"bh_regex",
										"bh_regex_center",
										"bh_regex_open",
										"bh_regex_close",
										"bh_regex_content",
										"bh_double_quote",
										"bh_double_quote_center",
										"bh_double_quote_open",
										"bh_double_quote_close",
										"bh_double_quote_content",
										"bh_curly",
										"bh_curly_center",
										"bh_curly_open",
										"bh_curly_close",
										"bh_curly_content",
										"bh_tag",
										"bh_tag_center",
										"bh_tag_open",
										"bh_tag_close",
										"bh_tag_content",
										"bh_c_define",
										"bh_c_define_center",
										"bh_c_define_open",
										"bh_c_define_close",
										"bh_c_define_content",
										"bh_default",
										"bh_default_center",
										"bh_default_open",
										"bh_default_close",
										"bh_default_content",
										"bh_round",
										"bh_round_center",
										"bh_round_open",
										"bh_round_close",
										"bh_round_content",
										"bh_unmatched",
										"bh_unmatched_center",
										"bh_unmatched_open",
										"bh_unmatched_close",
										"bh_unmatched_content"
									],
									"c_time":
									[
										128,
										3,
										99,
										100,
										97,
										116,
										101,
										116,
										105,
										109,
										101,
										10,
										100,
										97,
										116,
										101,
										116,
										105,
										109,
										101,
										10,
										113,
										0,
										67,
										10,
										7,
										232,
										9,
										20,
										17,
										52,
										18,
										0,
										0,
										0,
										113,
										1,
										133,
										113,
										2,
										82,
										113,
										3,
										46
									],
									"color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme",
									"function_name_status_row": 47,
									"origin_encoding": "UTF-8",
									"syntax": "Packages/Lua/Lua.sublime-syntax"
								},
								"translation.x": 0.0,
								"translation.y": 825.0,
								"zoom_level": 1.0
							},
							"stack_index": 0,
							"type": "text"
						}
					]
				}
			],
			"incremental_find":
			{
				"height": 33.0
			},
			"input":
			{
				"height": 33.0
			},
			"layout":
			{
				"cells":
				[
					[
						0,
						0,
						1,
						1
					]
				],
				"cols":
				[
					0.0,
					1.0
				],
				"rows":
				[
					0.0,
					1.0
				]
			},
			"menu_visible": true,
			"output.SublimeLinter":
			{
				"height": 124.0
			},
			"output.exec":
			{
				"height": 435.0
			},
			"output.find_results":
			{
				"height": 0.0
			},
			"output.mdpopups":
			{
				"height": 0.0
			},
			"pinned_build_system": "Packages/Lua/ggegame.sublime-build",
			"position": "0,2,3,-32000,-32000,-1,-1,643,2664,104,3320",
			"project": "开发梦幻西游.sublime-project",
			"replace":
			{
				"height": 62.0
			},
			"save_all_on_build": true,
			"select_file":
			{
				"height": 0.0,
				"last_filter": "",
				"selected_items":
				[
					[
						"染色",
						"客户端源码\\script\\场景类\\染色.lua"
					]
				],
				"width": 0.0
			},
			"select_project":
			{
				"height": 0.0,
				"last_filter": "",
				"selected_items":
				[
				],
				"width": 0.0
			},
			"select_symbol":
			{
				"height": 0.0,
				"last_filter": "",
				"selected_items":
				[
				],
				"width": 0.0
			},
			"selected_group": 0,
			"settings":
			{
			},
			"show_minimap": true,
			"show_open_files": false,
			"show_tabs": true,
			"side_bar_visible": true,
			"side_bar_width": 236.0,
			"status_bar_visible": true,
			"template_settings":
			{
			},
			"window_id": 2,
			"workspace_name": "/D/MyGame/防官复古三经脉/客户端/开发梦幻西游.sublime-workspace"
		}
	],
	"workspaces":
	{
		"recent_workspaces":
		[
			"/D/MyGame/防官复古三经脉/客户端/开发梦幻西游.sublime-workspace",
			"/D/MyGame/防官复古三经脉/服务端/开发服务端.sublime-workspace"
		]
	}
}
