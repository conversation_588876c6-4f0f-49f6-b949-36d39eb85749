local 系统类_每日签到 = class()

local floor = math.floor
local min = math.min
local tp,zts1,zts2
local xxx = 0
local yyy = 0
local max = 1
local insert = table.insert
local mouseb = 引擎.鼠标弹起


-- local wp={
--          [1]={名称="40装备礼包"},--技能书
--          [2]={名称="50W储备金"}, --特技书 新手宝宝
--          [3]={名称="新手宝宝"},
--          [4]={名称="地载阵"},-- 金砖 回梦丹
--          [5]={名称="红色合成旗"},

--          [6]={名称="种族坐骑"},
--          [7]={名称="1级宝石礼包"},
--          [8]={名称="100W储备金"},
--          [9]={名称="高品九转*2"},
--          [10]={名称="修炼果*2"},

--          [11]={名称="90装备礼包"},
--          [12]={名称="2级宝石礼包"}, --2级宝石礼包
--          [13]={名称="新手宝宝"},
--          [14]={名称="高品九转*4"},
--          [15]={名称="测试经验"},

--          [16]={名称="回梦丹"},
--          [17]={名称="3级宝石礼包"},
--          [18]={名称="黄色合成旗"},
--          [19]={名称="高品九转*6"},
--          [20]={名称="修炼果*6"},

--          [21]={名称="100装备礼包"},
--          [22]={名称="4级宝石礼包"},
--          [23]={名称="新手宝宝"},
--          [24]={名称="秘宝宝箱"},
--          [25]={名称="机缘宝箱"},
--         }



function 系统类_每日签到:初始化(根)
	self.ID = 41
	--宽高 549 431
	self.x = 全局游戏宽度/2-600/2
	self.y = 全局游戏高度/2-432/2
	self.xx = 0
	self.yy = 0
	self.注释 = "每日签到"
	self.可视 = false
	self.鼠标 = false
	self.焦点 = false
	self.可移动 = true
	self.状态 = 1
	self.窗口时间 = 0
	tp = 根
	zts = tp.字体表.普通字体
	zta = tp.字体表.普通字体
	zts1 = tp.字体表.描边字体
	zts2 = tp.字体表.普通字体__
	self.分类选中=""
	self.子类选中=""
	self.玩法介绍内容=""
	self.丰富文本说明 = 根._丰富文本(476,265)

end

function 系统类_每日签到:打开(数据)
	--table.print(数据)
	if self.可视 then
		self.可视 = false
		self.资源组 = nil
		self.物品组={}
		return
	else
		insert(tp.窗口_,self)
		local 资源 = tp.资源
		local 自适应 = tp._自适应
		local 按钮 = tp._按钮
		self.资源组 = {
			[1] = 资源:载入('wdf/vvxxzcom/签到.png',"图片"),
			[2] = 按钮.创建(pwd("14"),0,0,4,true,true),
			[3] = 按钮.创建(pwd("7"),0,0,4,true,true),
			[4] = pwd("32"),
			[5] = pwd("31"),
			[6] = pwd("33"),
			[7] = pwd("34"),
			[8] = 资源:载入('nice.wdf',"网易WDF动画",0x0683C414), --已领取

		}
		self.状态 = 1
		self.加入 = 0
		self.数据=数据.签到数据
		--table.print(数据.物品组)
 		self.几号 = self.数据.几号
 		self.月份 = self.数据.月份
 		self.当月天数=self.数据.当月天数
 		self.累计签到=self.数据.累计签到
 		local 当前时间 = os.time()
 		self.周几 = os.date("%w",os.time()-(os.date("%d", os.time())-1)*86400)
		tp.运行时间 = tp.运行时间 + 1
		self.窗口时间 = tp.运行时间
		self.物品组={}
	    	self:加载物品(数据)
		self.可视 = true
		self.起始位置 = self.周几 + 1
		self.上月天数 = os.date("%d",os.time({year=os.date("%Y"),month=os.date("%m"),day=0}))
	end
end

function 系统类_每日签到:刷新(数据)
	--table.print(数据)
	self.数据=数据.签到数据
	--table.print(self.数据)
	self.几号 = self.数据.几号
	self.月份 = self.数据.月份
	self.当月天数=self.数据.当月天数
	self.累计签到=self.数据.累计签到
	--print(self.数据.累计签到)
end


function 系统类_每日签到:显示(dt,x,y)
	self.资源组[8]:更新(x,y)
	self.资源组[2]:更新(x,y)
	self.资源组[3]:更新(x,y)

	self.丰富文本说明:更新(dt,x,y)
	if self.资源组[2]:事件判断() then
		self:打开()
		return false
	elseif self.资源组[3]:事件判断() then
		发送数据(46.6,{月份=self.月份,几号=self.几号})
		--print(self.月份)
	end
	self.焦点 = false
	self.资源组[1]:显示(self.x,self.y)
	self.资源组[2]:显示(self.x+480,self.y+2)
	self.资源组[3]:显示(self.x+382+27-2,self.y+105-1)
	local 奖励组 = 0
	local xx = 0
	local yy = 0
	-- for i=1,42 do
	-- 	if xx>=7 then
	-- 	    yy=yy+1
	-- 	    xx=0
	-- 	end
	-- 	if i < self.起始位置+0 then
	-- 		self.资源组[4]:显示(self.x+16+xx*62,self.y+52+yy*45)
	-- 		zts1:显示(self.x+15+xx*62,self.y+52+yy*45,self.上月天数-self.周几+i)
	-- 	elseif i >= self.起始位置+0 and i<=self.当月天数+self.周几 then
	-- 		self.资源组[5]:显示(self.x+16+xx*62,self.y+52+yy*45)
	-- 		zts1:显示(self.x+15+xx*62,self.y+52+yy*45,i-self.周几)
	-- 		if self.数据[i-self.周几] then
	-- 		    self.资源组[7]:显示(self.x+14+xx*62,self.y+75+yy*45)
	-- 		else
	-- 			self.资源组[6]:显示(self.x+14+xx*62,self.y+75+yy*45)
	-- 		end
	-- 	else
	-- 		self.资源组[4]:显示(self.x+16+xx*62,self.y+52+yy*45)
	-- 		zts1:显示(self.x+15+xx*62,self.y+52+yy*45,i-self.当月天数-self.周几)
	-- 	end
	-- 	xx=xx+1
	-- end

	for i=1,28 do

		local 偏移x,偏移y = 等比例缩放公式(45,45,tp.物品格子背景_.宽度,tp.物品格子背景_.高度)
		tp.物品格子背景_:显示(self.x+68+57+25+(xx-1)*49-82,self.y+35+yy*67+8,偏移x,偏移y)

		xx = xx + 1

		if xx==7 then
		    xx=0
		    yy=yy+1
		end
		-- if i == 7 then
		-- 	self.资源组[9]:显示(self.x+68+57+25+(xx-1)*49-82,self.y+35+yy*67+8,偏移x,偏移y)
		-- end
--print (self.数据[7])
		-- if self.数据[i] == false then
		-- 	print("开始进入")
		-- 	self.资源组[8]:显示(self.x+68+57+25+(xx-1)*49-82,self.y+35+yy*67+8,偏移x,偏移y)
		-- end
	end


	for k,v in pairs(self.物品组) do

    	if k<=7 then
            local wx = k*49
			v.名称.小动画:显示(self.x+61+wx-95,self.y+40,偏移x,偏移y)
			local 偏移x,偏移y = 等比例缩放公式(43,43,v.名称.小动画.宽度,v.名称.小动画.高度)
			for i=1,28 do
				if self.数据[k] then
					奖励组 = 奖励组+1
				end
			end
			--print(self.累计签到)
			for i=1,self.累计签到  do
				if i ==k then
					v.名称.小动画:灰度级()
					self.资源组[8]:显示(self.x-125+wx-95+40,self.y-340)
				end
			end
			if v.名称.名称=="九转金丹" then
			   v.名称.品质=300
			end
			if v.名称.小动画:是否选中(x,y,偏移x,偏移y) then
		    	tp.提示:商城提示(self.x-150+wx,self.y-90,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
		    end

    	end

    	if k<=14 and k>7 then
            local wx =(k-7)*49
			v.名称.小动画:显示(self.x+61+wx-95,self.y+43+1*66,偏移x,偏移y)
			if v.名称.小动画:是否选中(x,y) then
		    	tp.提示:商城提示(self.x-150+wx,self.y-10,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)

		    end
			for i=1,self.数据.累计签到  do
				if i ==k then
					v.名称.小动画:灰度级()
					self.资源组[8]:显示(self.x-125+wx-95+40,self.y-270)
				end
			end
		 --    	if self.数据[k] ~=nil and self.数据[k] then
			-- 	v.名称.小动画:灰度级()
			-- 	self.资源组[8]:显示(self.x-125+wx-95+40,self.y-270)
			-- end
    	end
    	if k<=21 and k>14 then
            local wx =(k-14)*49
			v.名称.小动画:显示(self.x+61+wx-95,self.y+43+2*66,偏移x,偏移y)
			if v.名称.小动画:是否选中(x,y) then
		    	tp.提示:商城提示(self.x-150+wx,self.y+90,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
		   	 end
			for i=1,self.数据.累计签到  do
				if i ==k then
					v.名称.小动画:灰度级()
					self.资源组[8]:显示(self.x-125+wx-95+40,self.y-200)
				end
			end
			 --   	 if self.数据[k] ~=nil and self.数据[k] then
			-- 	v.名称.小动画:灰度级()
			-- 	self.资源组[8]:显示(self.x-125+wx-95+40,self.y-200)
			-- end
    	end
    	if k<=28 and k>21 then
            local wx =(k-21)*49
			v.名称.小动画:显示(self.x+61+wx-95,self.y+43+3*66,偏移x,偏移y)
			if v.名称.小动画:是否选中(x,y) then
		    	tp.提示:商城提示(self.x-150+wx,self.y+170,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
		   	 end
		   	 for i=1,28 do
				if self.数据[k] then
					奖励组 = 奖励组+1
				end
			end
			for i=1,self.数据.累计签到  do
				if i ==k then
					v.名称.小动画:灰度级()
					self.资源组[8]:显示(self.x-125+wx-95+40,self.y-140)
				end

			end

		 --   	 if self.数据[k] ~=nil and self.数据[k] then
			-- 	v.名称.小动画:灰度级()
			-- 	self.资源组[8]:显示(self.x-125+wx-95+40,self.y-140)
			-- end
    	end
   --  	if k<=30 and k>24 then
   --          local wx =(k-24)*49
			-- v.名称.小动画:显示(self.x+61+wx-95,self.y+43+4*66,偏移x,偏移y)


			-- if v.名称.小动画:是否选中(x,y) then
		 --    	tp.提示:商城提示(self.x-150+wx,self.y+250,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
		 --    end
   --  	end
	end


	zts1:置颜色(白色)
	zts1:显示(self.x+369+27,self.y+30,os.date("%Y", os.time()).."年"..os.date("%m", os.time()).."月"..os.date("%d", os.time()).."日")
	zts1:显示(self.x+389+27,self.y+45,os.date("%X", os.time()))
	zta:置颜色(白色)
	-- zta:显示(self.x+10,self.y+326,"请打开梦幻指引在副本任务中查看签到奖励")
	-- zta:显示(self.x+10,self.y+343,"请打开梦幻指引在副本任务中查看签到奖励")
	-- zta:显示(self.x+10,self.y+360,"请打开梦幻指引在副本任务中查看签到奖励")
	--self.资源组[8]:显示(self.x+0-125,self.y-330)
	zts2:置颜色(红色):显示(self.x + xx * 150 + 71-5,self.y + yy * 60 +94,"每天都可领取相对于的奖励，每7日为丰厚奖励")

end


function 系统类_每日签到:加载物品(数据)

	local  wp = 数据.物品组

	for k,v in pairs(wp) do
		if self.物品组[k]==nil then
		   self.物品组[k]={}
		end
		for i,n in pairs(v) do
			-- print(n)
			if self.物品组[k][i]== nil then
				self.物品组[k][i]={}
			end
			local 资源=引擎.取物品(n)
			self.物品组[k][i].小动画=tp.资源:载入(资源[11],"网易WDF动画",资源[12])
			self.物品组[k][i].大动画=tp.资源:载入(资源[11],"网易WDF动画",资源[13])
			self.物品组[k][i].名称=n
			self.物品组[k][i].说明=资源[1]
		end
	end
end


function 系统类_每日签到:检查点(x,y)
	if self.资源组[1] ~= nil and self.资源组[1]:是否选中(x,y)  then
		return true
	end
end

function 系统类_每日签到:初始移动(x,y)
	tp.运行时间 = tp.运行时间 + 1
	if not tp.消息栏焦点 then
		self.窗口时间 = tp.运行时间
	end
	if not self.焦点 then
		tp.移动窗口 = true
	end
	if self.鼠标 and  not self.焦点 then
		self.xx = x - self.x
		self.yy = y - self.y
	end
end

function 系统类_每日签到:开始移动(x,y)
	if self.鼠标 then
		self.x = x - self.xx
		self.y = y - self.yy
	end
end

return 系统类_每日签到