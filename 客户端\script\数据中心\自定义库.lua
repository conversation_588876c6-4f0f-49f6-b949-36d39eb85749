
Fight特效加速={}--越小越慢
Fight特效加速["生命之泉"]={附加y=-75}
Fight特效加速["炼气化神"]={附加y=-75}
Fight特效加速["紧箍咒"]={附加y=-35}
Fight特效加速["龙腾"]={加速=0.03}
Fight特效加速["月光"]={加速=0.03}
Fight特效加速["唧唧歪歪"]={加速=0.015}
Fight特效加速["新_唧唧歪歪"]={加速=0.02}
Fight特效加速["牵魂蛛丝"]={加速=0.04}
Fight特效加速["血雨"]={加速=0.04}
Fight特效加速["碎星诀"]={附加y=-50,加速=0.03}
Fight特效加速["追魂刺"]={加速=0.04}
Fight特效加速["新_金刚护法"]={加速=0.04}
Fight特效加速["新_金刚护体"]={加速=0.02}
Fight特效加速["新_失心符"]={加速=0.04}
Fight特效加速["新_日月乾坤"]={加速=0.04}
Fight特效加速["百爪狂杀"]={加速=1.5}
Fight特效加速["法术暴击"]={加速=3}

skill无需状态={
    ["高级进击必杀"]=1,
    ["超级进击必杀"]=1,
    ["超级盾气"]=1,
    ["超级法术抵抗"]=1,
    ["超级遗志"]=1,
    ["进击必杀"]=1,
    ["发瘟匣"]=1,
    ["修罗隐身"]=1,
    ["满天花雨"]=1,
    ["其疾如风"]=1,
    ["其徐如林"]=1,
    ["侵掠如火"]=1,
    ["岿然如山"]=1,
    ["无间地狱"]=1,
    ["清静菩提"]=1,
    ["媚眼如丝"]=1,
    ["晶清诀"]=1,
    ["铜头铁臂"]=1,
    ["水清诀"]=1,
    ["玉清诀"]=1,
    ["反间之计"]=1,
    ["北冥之渊"]=1,
    ["天煞"]=1,
    ["苍鸾怒击"]=1,
    ["狂怒"]=1,
    ["九梵清莲"]=1,
    ["傲视"]=1,
    ["舍生取义"]=1,
    ["无需特效"]=1,
    ["河东狮吼"]=1,
    ["光辉之甲"]=1,
    ["圣灵之甲"]=1,
    ["流云诀"]=1,
    ["复活"]=1,
    ["破甲术"]=1,
    ["啸风诀"]=1,
    ["野兽之力"]=1,
    ["碎甲术"]=1,
    ["魂飞魄散"]=1,
    ["汲魂"]=1,
    ["绝殇"]=1,
    ["宁心"]=1,
    ["知己知彼"]=1,
    ["翩鸿一击"]=1,
    ["天魔解体"]=1,
    ["分身术"]=1,
    ["照妖镜"]=1,
    ["魔兽之印"]=1,
    ["诸天看护"]=1,
    ["无畏布施（减）"]=1,
    ["狂袭"]=1,
    ["感念"]=1,
    ["顾盼生姿"]=1,
    ["八戒上身"]=1,
    ["杀威铁棒"]=1,
    ["龙骇"]=1,
    ["破浪"]=1,
    ["盘龙"]=1,
    ["龙啸"]=1,
    ["清吟"]=1,
    ["潜龙在渊"]=1,
    ["鹰啸"]=1,
    ["肝胆"]=1,
    ["长啸"]=1,
    ["练魂"]=1,
    ["天雷斩"]=1,
    ["雷怒霆激"]=1,
    ["沙威铁棒"]=1,
    ["威震凌霄"]=1,
    ["天地洞明"]=1,
    ["灵刃"]=1,
    ["灵法"]=1,
    ["灵断"]=1,
    ["御风"]=1,
    ["怒吼"]=1,
    ["阳护"]=1,
    ["护佑"]=1,
    ["疯狂"]=1,
    ["魔息术"]=1,
    ["重创"]=1,
    ["攻伐"]=1,
    ["蔓延"]=1,
    ["还元"]=1,
    ["怒霆"]=1,
    ["魔冥"]=1,
    ["六道无量"]=1,
    ["愈勇"]=1,
    ["放下屠刀"]=1,
    ["诵经"]=1,
    ["凭虚御风"]=1,
    ["惊锋"]=1,
    ["魂魇"]=1,
    ["狂战"]=1,
    ["酣战"]=1,
    ["开辟"]=1,
    ["清吟"]=1,
    ["柳暗花明"]=1,
    ["清吟"]=1,
    ["清吟"]=1,
    ["清吟"]=1,
    ["清吟"]=1,
    ["清吟"]=1,
    ["清吟"]=1,
    ["清吟"]=1,
    ["清吟"]=1,
    ["超级赐福·元吉"]=1,
    ["超级赐福·双喜"]=1,
    ["超级赐福·三和"]=1,
    ["超级赐福·四季"]=1,
    ["超级赐福·五福"]=1,

}
				---  空白新增   超级赐福

skill法术特效后置={
	["阎罗令"]=1,
    -- ["清吟"]=1,
    -- ["清吟"]=1,
    -- ["清吟"]=1,
}


skill恢复={
	["无穷妙道"]=1,
	["地涌金莲"]=1,
	["星月之惠"]=1,
	["玉清诀"]=1,
	["晶清诀"]=1,
	["冰清诀"]=1,
	["水清诀"]=1,
	["四海升平"]=1,
	["命归术"]=1,
	["气归术"]=1,
	["凝神诀"]=1,
	["凝气诀"]=1,
	["命疗术"]=1,
	["心疗术"]=1,
	["气疗术"]=1,
	["归元咒"]=1,
	["乾天罡气"]=1,
	["我佛慈悲"]=1,
	["杨柳甘露"]=1,
	["推拿"]=1,
	["推气过宫"]=1,
	["活血"]=1,
	["妙手回春"]=1,
	["救死扶伤"]=1,
	["解毒"]=1,
	["百毒不侵"]=1,
	["宁心"]=1,
	["解封"]=1,
	["清心"]=1,
	["驱魔"]=1,
	["驱尸"]=1,
	["寡欲令"]=1,
	["复苏"]=1,
	["普渡众生"]=1,
	["慈航普度"]=1,
	["起死回生"]=1,
	["回魂咒"]=1,
	["舍生取义"]=1,
	["自在心法"]=1,
	["还阳术"]=1,
	["醍醐灌顶"]=1,
	["净土灵华"]=1,
	["重生"]=1,
	["莲花心音"]=1,
	["由己渡人"]=1,
	["妙悟"]=1,
	["六尘不染"]=1,
	["电光火石"]=1,
	["魍魉追魂"]=1,
	["清风望月"]=1,
	["绝处逢生"]=1,
	["金刚怒目"]=1,
}

skill物攻={
	["牛刀小试"]=1,
	["剑荡四方"]=1,
	["翻江搅海"]=1,
	["满天花雨"]=1,
	["破血狂攻"]=1,
	["弱点击破"]=1,
	["善恶有报"]=1,
	["迅风出击"]=1,
	["惊心一剑"]=1,
	["壁垒击破"]=1,
	["超级壁垒击破"]=1,
	["横扫千军"]=1,
	["破碎无双"]=1,
	["狮搏"]=1,
	["象形"]=1,
	["连环击"]=1,
	["鹰击"]=1,
	["烟雨剑法"]=1,
	["飘渺式"]=1,
	["天雷斩"]=1,
	["裂石"]=1,
	["断岳势"]=1,
	["天崩地裂"]=1,
	["浪涌"]=1,
	["惊涛怒"]=1,
	["力劈华山"]=1,
	["破釜沉舟"]=1,
	["死亡召唤"]=1,
	["疯狂鹰击"]=1,
	["同伤式"]=1,
	["六道无量"]=1,
	["百爪狂杀"]=1,
	["翩鸿一击"]=1,
	["长驱直入"]=1,
	["腾雷"]=1,
	["天神怒斩"]=1,
	["水击三千"]=1,
	["惊天动地"]=1,
	["摧枯拉朽"]=1,
	["披挂上阵"]=1,
	["葬玉焚花"]=1,
	["风雷斩"]=1,
	["日光耀"]=1,
	["靛沧啸"]=1,
	["巨岩击"]=1,
	["苍茫刺"]=1,
	["地裂焚"]=1,
	["威仪九霄"]=1,
	["千蛛噬魂"]=1,
	["蛛丝缠绕"]=1,
	["绝命毒牙"]=1,
	["无赦咒令"]=1,
	["百鬼噬魂"]=1,
	["生杀予夺"]=1,
	["血影蚀心"]=1,
	["困兽之斗"]=1,
	["敲金击玉"]=1,
	["金击式"]=1,
	["天命剑法"]=1,
	["落土止息"]=1,
	["威震凌霄"]=1,
	["当头一棒"]=1,
	["神针撼海"]=1,
	["杀威铁棒"]=1,
	["泼天乱棒"]=1,
	["棒打雄风"]=1,
	["花谢花飞"]=1,

	["枫影二刃"] = 1,
	["三荒尽灭"] = 1,
	["力辟苍穹"] = 1,
	["铁血生风"] = 1,
	["一斧开天"] = 1,
	["魔神之刃"] = 1,

}
skill封印={
	["摧心术"]=1,
	["反间之计"]=1,
	["催眠符"]=1,
	["失心符"]=1,
	["落魄符"]=1,
	["失忆符"]=1,
	["追魂符"]=1,
	["离魂符"]=1,
	["失魂符"]=1,
	["定身符"]=1,
	["莲步轻舞"]=1,
	["如花解语"]=1,
	["似玉生香"]=1,
	["碎玉弄影"]=1,
	["娉婷袅娜"]=1,
	["错乱"]=1,
	["百万神兵"]=1,
	["日月乾坤"]=1,
	["威慑"]=1,
	["含情脉脉"]=1,
	["魔音摄魂"]=1,
	["夺魄令"]=1,
	["惊魂掌"]=1,
	["煞气诀"]=1,
	["秘传封印"]=1,
	["顺势而为"]=1,
	["妖风四起"]=1,
	["金刚镯"]=1,
	["偷龙转凤"]=1,
	["毁灭之光"]=1,
	["一笑倾城"]=1,
	["镇妖"]=1,
	["掌心雷"]=1,
	["八戒上身"]=1,
}
skill减益={
	["尸腐毒"]=1,
	["紧箍咒"]=1,
	["放下屠刀"]=1,
	["碎甲术"]=1,
	["破甲术"]=1,
	["河东狮吼"]=1,
	["雾杀"]=1,
	["笑里藏刀"]=1,
	["锢魂术"]=1,
	["月下霓裳"]=1,
	["否极泰来"]=1,
	["尸腐无常"]=1,
	["碎甲符"]=1,
	["凋零之歌"]=1,
	["魂飞魄散"]=1,
	["落花成泥"]=1,
	["雷浪穿云"]=1,
	["知己知彼"]=1,
	["画地为牢"]=1,
	["牵魂蛛丝"]=1,
	["噬毒"]=1,
	["凝滞术"]=1,
	["停陷术"]=1,
	["死亡之音"]=1,
}
skill增益={
	["变身"]=1,
	["超级永恒"]=1,
	["移魂化骨"]=1,
	["狂怒"]=1,
	["后发制人"]=1,
	["杀气诀"]=1,
	["安神诀"]=1,
	["分身术"]=1,
	["达摩护体"]=1,
	["金刚护法"]=1,
	["潜龙在渊"]=1,
	["天雷灌注"]=1,
	["雷怒霆激"]=1,
	["霹雳弦惊"]=1,
	["金刚护体"]=1,
	["韦陀护法"]=1,
	["风沙之盾"]=1,
	["一苇渡江"]=1,
	["佛法无边"]=1,
	["楚楚可怜"]=1,
	-- ["天神护法"]=1,
	["乘风破浪"]=1,
	["神龙摆尾"]=1,
	["生命之泉"]=1,
	["炼气化神"]=1,
	["天地同寿"]=1,
	["乾坤妙法"]=1,
	["普渡众生"]=1,
	["灵动九天"]=1,
	["幽冥鬼眼"]=1,
	["修罗隐身"]=1,
	["火甲术"]=1,
	["魔王回首"]=1,
	["定心术"]=1,
	["极度疯狂"]=1,
	["魔息术"]=1,
	["天魔解体"]=1,
	["盘丝阵"]=1,
	["不动如山"]=1,
	["碎星诀"]=1,
	["镇魂诀"]=1,
	["明光宝烛"]=1,
	["金身舍利"]=1,
	["炎护"]=1,
	["蜜润"]=1,
	["法术防御"]=1,
	["太极护法"]=1,
	["罗汉金钟"]=1,
	["流云诀"]=1,
	["啸风诀"]=1,
	["野兽之力"]=1,
	["魔兽之印"]=1,
	["光辉之甲"]=1,
	["圣灵之甲"]=1,
	["牛劲"]=1,
	["毒萃"]=1,
	["波澜不惊"]=1,
	["其疾如风"]=1,
	["其徐如林"]=1,
	["侵掠如火"]=1,
	["岿然如山"]=1,
	["龙战于野"]=1,
	["裂魂"]=1,
	["花语歌谣"]=1,
	["无双战魂"]=1,
	["无间地狱"]=1,
	["媚眼如丝"]=1,
	["清静菩提"]=1,
	["鸣雷诀"]=1,
	["逆鳞"]=1,
	["钟馗论道"]=1,
	["诸天看护"]=1,
	["渡劫金身"]=1,
	["天神护体"]=1,
	["凝神术"]=1,
	["颠倒五行"]=1,
	["莲心剑意"]=1,
	["剑意莲心"]=1,
	["幻镜术"]=1,
	["同舟共济"]=1,
	["真君显灵"]=1,
	["无畏布施"]=1,
	["北冥之渊"]=1,
	["功德无量"]=1,
	["森罗迷瘴"]=1,
	["心随意动"]=1,
	["燃血术"]=1,
	["化羽为血"]=1,
	["气慑天军"]=1,
	["九幽除名"]=1,
	["铜头铁臂"]=1,
	["无所遁形"]=1,
	["呼子唤孙"]=1,
	["齐天神通"]=1,
	["金刚不坏"]=1,
	["灵能激发"]=1,
	["修罗咒"]=1,
	["身似菩提"]=1,
	["菩提心佑"]=1,
	["心如明镜"]=1,
	["法力陷阱"]=1,

	["怒哮"]=1,
	["炎魂"]=1,
}

skill无需物理={
    ["高级连击"]=1,
    ["超级连击"]=1,
    ["理直气壮"]=1,
    ["乘胜追击"]=1,
    ["无需特效"]=1,
    ["武神之怒"]=1,
    ["力劈华山"]=1,
    ["千蛛噬魂"]=1,
    ["蛛丝缠绕"]=1,
    ["六道无情"]=1,
    ["破釜沉舟"]=1,
    ["满天花雨"]=1,
    ["惊涛怒"]=1,
    ["翻江搅海"]=1,
    ["神针撼海"]=1,
    ["神牛攻击"]=1,
    ["葬玉焚花"]=1,
    ["自矜"]=1,
    ["威震凌霄"]=1,
    ["腾雷"]=1,
    ["同伤式"]=1,
    ["死亡召唤"]=1,
 --    ["日光耀"]=1,
	-- ["靛沧啸"]=1,
	-- ["巨岩击"]=1,
	-- ["苍茫刺"]=1,
	-- ["地裂焚"]=1,
}

skill无需法术={
    ["高级连击"]=1,
    ["超级连击"]=1,
    ["其疾如风"]=1,
    ["其徐如林"]=1,
    ["侵掠如火"]=1,
    ["无间地狱"]=1,
    ["媚眼如丝"]=1,
    ["修罗隐身"]=1,
    ["法术防御"]=1,
    ["无需特效"]=1,
    ["傲视"]=1,
    ["无敌牛虱"]=1,
    ["无敌牛妖"]=1,
    ["无畏布施"]=1,
    ["高级进击必杀"]=1,
    ["超级进击必杀"]=1,
    ["进击必杀"]=1,
    ["高级进击法爆"]=1,
    ["进击法爆"]=1,
    ["雷怒霆激"]=1,
    ["气慑天军"]=1,
    ["赤焰"]=1,
    ["阳护"]=1,
    ["盾气"]=1,
    ["高级盾气"]=1,
    ["超级盾气"]=1,
    ["超级法术抵抗"]=1,
    ["遗志"]=1,
    ["高级遗志"]=1,
    ["超级遗志"]=1,
    ["金刚不坏"]=1,
    ["北冥之渊"]=50,
    -- ["鲲鹏出场"]=1,
}


skill法攻={
    ["水攻"]=10,
	["烈火"]=20,
	["落岩"]=10,
	["雷击"]=10,
	["尸腐毒"]=20,
	["勾魂"]=20,
	["摄魄"]=20,
	["瘴气"]=20,
	["雨落寒沙"]=40,
	["夺命咒"]=30,

	["落叶萧萧"]=50,
	["蛊木迷瘴"]=50,
	["荆棘舞"]=40,
	["尘土刃"]=50,
	["冰川怒"]=30,
	["唧唧歪歪"]=50,
	["谆谆教诲"]=40,
	["五雷咒"]=50,
	["落雷符"]=50,
	["雷霆万钧"]=30,
	["五雷正法"]=50,
	["雷法·崩裂"]=50,
	["雷法·震煞"]=50,
	["雷法·翻天"]=50,
	["雷法·坤伏"]=50,
	["雷法·倒海"]=50,
	["雷法·轰天"]=50,
	["龙卷雨击"]=50,
	["二龙戏珠"]=50,
	["龙腾"]=40,
	["三昧真火"]=20,
	["超级三昧真火"]=20,
	["飞砂走石"]=50,
	["泰山压顶"]=50,
	["水漫金山"]=50,
	["亢龙归海"]=50,
	["风雷韵动"]=30,
	["地狱烈火"]=50,
	["奔雷咒"]=40,
	["超级泰山压顶"]=50,
	["超级水漫金山"]=50,
	["超级地狱烈火"]=50,
	["超级奔雷咒"]=40,
	["月光"]=50,
	["上古灵符"]=50,
	["血雨"]=50,
	["魔火焚世"]=50,
	["八凶法阵"]=30,
	["流沙轻音"]=50,
	["叱咤风云"]=50,
	["食指大动"]=50,
	["风卷残云"]=50,

	["天降灵葫"]=50,
	["摇头摆尾"]=50,
	["风云变色"]=50,
	["魔焰滔天"]=50,
	["扶摇万里"]=50,
	["五蕴神焰"]=50,
	["漫卷狂沙"]=50,
	["古藤秘咒"]=40,
	["疾风秋叶"]=50,
	["枯木逢春"]=50,
	["棒掀北斗"]=50,
	["灵彻太虚"]=50,
	["兴风作浪"]=50,
	["棍打诸神"]=50,
	["意马心猿"]=50,
	["天罗地网"]=50,
	["子母神针"]=50,
	["飞花摘叶"]=30,
	["姐妹同心"]=50,
	["鸿渐于陆"]=50,

	["自爆"]=50,

	["五雷轰顶"]=50,
	["龙吟"]=50,
	["苍茫树"]=30,
	["靛沧海"]=30,
	["日光华"]=30,
	["地裂火"]=30,
	["巨岩破"]=30,
	["判官令"]=10,
	["阎罗令"]=10,
	["黄泉之息"]=40,
	["追魂刺"]=50,
	["云暗天昏"]=50,
	["诅咒之伤"]=50,
	["吸血特技"]=10,
	["夜舞倾城"]=50,
	["琴音三叠"]=50,
}

skill无需抖动={
    ["地狱烈火"]=1,
    ["泰山压顶"]=1,
    ["龙吟"]=1,
    ["五雷咒"]=1,
    ["龙卷雨击"]=1,
    ["飞砂走石"]=1,
    ["扶摇万里"]=1,
    ["月光"]=1,
    ["唧唧歪歪"]=1,
    ["谆谆教诲"]=1,
    ["风云变色"]=50,
    ["天降灵葫"]=50,
    ["五蕴神焰"]=50,
    ["夜舞倾城"]=50,
    ["诅咒之伤"]=50,
    ["追魂刺"]=50,
    ["扶摇万里"]=50,
    ["流沙轻音"]=50,

}

skill战斗道具={
	["醉仙果"]=5,
    ["七珍丸"]=5,
    ["九转续命丹"]=5,
    ["十全大补丸"]=5,
    ["固本培元丹"]=5,
    ["舒筋活络丸"]=5,
    ["凝气丸"]=5,
	["金创药"]=5,
    ["小还丹"]=5,
    ["千年保心丹"]=5,
    ["金香玉"]=5,
    ["五龙丹"]=5,
    ["翡翠豆腐"]=5,
    ["佛跳墙"]=5,
    ["蛇蝎美人"]=5,
    ["风水混元丹"]=5,
    ["定神香"]=5,
    ["十香返生丸"]=5,
    ["佛光舍利子"]=5,
    ["九转回魂丹"]=5,
    ["珍露酒"]=5,
    ["虎骨酒"]=5,
    ["女儿红"]=5,
    ["蛇胆酒"]=5,
    ["醉生梦死"]=5,
    ["梅花酒"]=5,
    ["百味酒"]=5,
    ["天不老"]=5,
    ["紫石英"]=5,
    ["血色茶花"]=5,
    ["熊胆"]=5,
    ["鹿茸"]=5,
    ["六道轮回"]=5,
    ["凤凰尾"]=5,
    ["硫磺草"]=5,
    ["龙之心屑"]=5,
    ["火凤之睛"]=5,
    ["四叶花"]=5,
    ["天青地白"]=5,
    ["七叶莲"]=5,
    ["丁香水"]=5,
    ["月星子"]=5,
    ["仙狐涎"]=5,
    ["地狱灵芝"]=5,
    ["麝香"]=5,
    ["血珊瑚"]=5,
    ["餐风饮露"]=5,
    ["白露为霜"]=5,
    ["天龙水"]=5,
    ["孔雀红"]=5,
    ["紫丹罗"]=5,
    ["佛手"]=5,
    ["旋复花"]=5,
    ["龙须草"]=5,
    ["百色花"]=5,
    ["香叶"]=5,
    ["白玉骨头"]=5,
    ["鬼切草"]=5,
    ["灵脂"]=5,
    ["曼陀罗花"]=5,
    ["飞刀"]=4,
    ["飞蝗石"]=4,
    ["铁蒺黎"]=4,
    ["无影神针"]=4,
    ["孔雀翎"]=4,
    ["含沙射影"]=4,
    ["回龙镊魂镖"]=4,
    ["寸阴若梦"]=4,
    ["魔睛子"]=4,
    ["顺逆神针"]=4,
    ["乾坤袋"]=4,
    ["惊魂铃"]=4,
    ["鬼泣"]=4,
    ["发瘟匣"]=4,
    ["断线木偶"]=4,
    ["摄魂"]=4,
    ["无魂傀儡"]=4,
    ["无尘扇"]=4,
    ["缚妖索"]=4,
    ["捆仙绳"]=4,
    ["缚龙索"]=4,
    ["现形符"]=4,
    ["番天印"]=4,
    ["落雨金钱"]=4,
    ["照妖镜"]=4,
    ["落宝金钱"]=4,
    ["无字经"]=4,
    ["舞雪冰蝶"]=4,
    ["紫火如意"]=4,
    ["金钱镖"]=4,
    ["乾坤玄火塔"]=2,
    ["铸兵锤"]=2,
    ["混元伞"]=2,
    ["五彩娃娃"]=2,
    ["万鬼幡"]=2,
    ["聚妖铃"]=2,
    ["苍白纸人"]=2,
    ["干将莫邪"]=2,
    ["分水"]=2,
    ["缩地尺"]=2,
    ["赤焰"]=2,
    ["天煞"]=2,
    ["神木宝鼎"]=2,
    ["金蟾"]=2,
    ["九梵清莲"]=2,
    ["苍灵雪羽"]=2,
    ["璞玉灵钵"]=2,
    ["烽火狼烟"]=2,
    ["清心咒"]=5,
    ["罗汉珠"]=5,
}
Fightdiwo={}
Fightdiwo["干将莫邪"]=1
Fightdiwo["苍白纸人"]=1
Fightdiwo["铸兵锤"]=1
Fightdiwo["混元伞"]=1
Fightdiwo["乾坤玄火塔"]=1
Fightdiwo["护盾"]=1
Fightdiwo["无畏布施"]=1
Fightdiwo["盾气"]=1
Fightdiwo["高级盾气"]=1
--Fightdiwo["超级盾气"]=1

Fightztqz={}
Fightztqz["寡欲令"]={py={0,25},cp = true}
Fightztqz["驱魔"]={py={0,25},cp = true}
Fightztqz["韦陀护法"]={py={0,-15},cp = true}
Fightztqz["风沙之盾"]={py={0,0},cp = false}
Fightztqz["红袖添香"]={py={10,0},cp = true}
Fightztqz["天罗地网"]={py={1,3},cp = false}
Fightztqz["乾坤玄火塔"]={py={1,3},cp = false}
Fightztqz["干将莫邪"]={py={1,3},cp = false}
Fightztqz["铸兵锤"]={py={1,3},cp = false}
Fightztqz["莲步轻舞"]={py={1,3},cp = false}
Fightztqz["如花解语"]={py={1,3},cp = false}
Fightztqz["定身符"]={py={5,2} ,cp = false}
Fightztqz["镇妖"]={py={0,0},cp = false}
Fightztqz["失忆符"]={py={0,0},cp = false}
Fightztqz["催眠符"]={py={0,0},cp = false}
Fightztqz["落魄符"]={py={0,0},cp = false}
Fightztqz["追魂符"]={py={0,0},cp = false}
Fightztqz["含情脉脉"]={py={0,0},cp = false}
Fightztqz["血雨"]={py={0,0},cp = false}
Fightztqz["离魂符"]={py={0,0},cp = false}
Fightztqz["失心符"]={py={0,0},cp = false}
Fightztqz["失魂符"]={py={0,0},cp = false}
Fightztqz["碎甲符"]={py={0,0},cp = false}
Fightztqz["象形"]={py={0,0},cp = false}
Fightztqz["似玉生香"]={py={0,0},cp = false}
Fightztqz["横扫千军"]={py={0,0},cp = false}
Fightztqz["后发制人"]={py={0,0},cp = false}
Fightztqz["苍白纸人"]={py={0,0},cp = false}
Fightztqz["混元伞"]={py={0,0},cp = false}
Fightztqz["乘风破浪"]={py={0,0},cp = false}
Fightztqz["一苇渡江"]={py={0,0},cp = false}
Fightztqz["逆鳞"]={py={0,0},cp = false}
Fightztqz["夺魄令"]={py={0,0},cp = false}
Fightztqz["百万神兵"]={py={0,0},cp = false}
Fightztqz["护法紫丝"]={py={0,0},cp = false}
Fightztqz["死亡召唤"]={py={0,0},cp = false}
Fightztqz["法术防御"]={py={0,0},cp = false}
Fightztqz["魔音摄魂"]={py={0,0},cp = false}
Fightztqz["炎护"]={py={0,0},cp = false}
Fightztqz["妖风四起"]={py={0,0},cp = false}
Fightztqz["娉婷袅娜"]={py={0,0},cp = false}
Fightztqz["月下霓裳"]={py={0,0},cp = false}
Fightztqz["颠倒五行"]={py={2,-31},cp = true}
Fightztqz["脱壳"]={py={2,-31},cp = true}
Fightztqz["极度疯狂"]={py={2,-31},cp = true}
Fightztqz["鸣雷诀"]={py={2,-31},cp = true}
Fightztqz["变身"]={py={2,-31},cp = true}
Fightztqz["九幽除名"]={py={2,-31},cp = true}
Fightztqz["肝胆相照"]={py={2,-31},cp = true}
Fightztqz["雾杀"]={py={2,-31},cp = true}
Fightztqz["惊魂掌"]={py={2,-31},cp = true}
Fightztqz["魔王回首"]={py={2,-31},cp = true}
Fightztqz["牛劲"]={py={2,-31},cp = true}
Fightztqz["幽冥鬼眼"]={py={2,-31},cp = true}
Fightztqz["摄魂"]={py={2,-31},cp = true}
Fightztqz["镇魂诀"]={py={2,-31},cp = true}
Fightztqz["战诀"]={py={2,-31},cp = true}
Fightztqz["杀气诀"]={py={2,-31},cp = true}
Fightztqz["无畏布施"]={py={2,-31},cp = true}
Fightztqz["金蟾"]={py={2,-31},cp = true}
Fightztqz["重伤"]={py={2,-75},cp = true}
Fightztqz["蚀天"]={py={2,-75},cp = true}
Fightztqz["龙战于野"]={py={0,-115},cp = true}
Fightztqz["裂魂"]={py={15,20},cp = true}
Fightztqz["定心术"]={py={0,-115},cp = true}
Fightztqz["天神护体"]={py={0,-115},cp = true}
Fightztqz["木精"]={py={0,-115},cp = true}
Fightztqz["幻镜术"]={py={2,12},cp = true}
Fightztqz["普渡众生"]={py={2,-75},cp = true}
Fightztqz["生命之泉"]={py={2,-75},cp = true}
Fightztqz["炼气化神"]={py={2,-75},cp = true}
Fightztqz["锢魂术"]={py={2,-75},cp = true}
Fightztqz["花语歌谣"]={py={2,-75},cp = true}
Fightztqz["蜜润"]={py={2,-75},cp = true}
Fightztqz["碎甲刃"]={py={2,-75},cp = true}
Fightztqz["魔冥"]={py={2,-75},cp = true}
Fightztqz["自矜"]={py={2,-75},cp = true}
Fightztqz["龙骇"]={py={3,-85},cp = true}
Fightztqz["灵能激发"]={py={3,-85},cp = true}
Fightztqz["明光宝烛"]={py={3,-95},cp = true}
Fightztqz["金身舍利"]={py={3,-95},cp = true}
Fightztqz["金身"]={py={-5,-20},cp = true}
Fightztqz["苍灵雪羽"]={py={3,-95},cp = true}
Fightztqz["璞玉灵钵"]={py={3,-95},cp = true}
Fightztqz["烽火狼烟"]={py={-4,-85},cp = true}
Fightztqz["舞雪冰蝶"]={py={-4,-85},cp = true}
Fightztqz["落花成泥"]={py={0,-75},cp = true}
Fightztqz["无尘扇"]={py={-2,-65},cp = true}
Fightztqz["断线木偶"]={py={-2,-65},cp = true}
Fightztqz["无魂傀儡"]={py={-2,-65},cp = true}
Fightztqz["晕眩"]={py={-2,-100},cp = true}
Fightztqz["风灵"]={py={-3,-95},cp = true}
Fightztqz["移魂化骨"]={py={-3,-95},cp = true}
Fightztqz["碎星诀"]={py={-2,-125},cp = true}
Fightztqz["愈勇"]={py={-2,-125},cp = true}
Fightztqz["瘴气"]={py={-2,-48},cp = true}
Fightztqz["无所遁形"]={py={0,-48},cp = true}
Fightztqz["齐天神通"]={py={0,-48},cp = true}
Fightztqz["清净"]={py={0,-60},cp = true}
Fightztqz["九幽除名"]={py={-2,-48},cp = false}
Fightztqz["真君显灵"]={py={-2,-30},cp = true}
Fightztqz["不动如山"]={py={-2,-30},cp = true}
Fightztqz["天地同寿"]={py={-2,-30},cp = true}
Fightztqz["腾雷"]={py={-2,-30},cp = true}
Fightztqz["护盾"]={py={-5,15},cp = true}
--Fightztqz["铸兵锤"]={py={-5,15},cp = true}


Fighttxz={}
Fighttxz["唧唧歪歪"]=1.15
Fighttxz["横扫千军"]=1.05
Fighttxz["浪涌"]=1.05
Fighttxz["反震"]=1.6
Fighttxz["防御"]=1.6
Fighttxz["捕捉开始"]=1.55
Fighttxz["暴击"]=1.55
Fighttxz["龙卷雨击1"]=1.15
Fighttxz["地裂火"]=1.15
Fighttxz["龙卷雨击2"]=1.5
Fighttxz["龙卷雨击3"]=1.0
Fighttxz["龙卷雨击4"]=1.15
Fighttxz["龙吟"]=1.45
Fighttxz["龙腾"]=1.75
Fighttxz["泰山压顶"]=1.4
Fighttxz["连环击"]=2.4
Fighttxz["天雷斩"]=1.6
Fighttxz["地狱烈火"]=1.6
Fighttxz["水漫金山"]=1.45
Fighttxz["鹰击"]=1.45
Fighttxz["上古灵符"]=1.45
Fighttxz["五雷轰顶"]=1
Fighttxz["天罗地网"]=0.85
Fighttxz["狮搏"]=1.4
Fighttxz["被击中"]=1.28
Fighttxz["二龙戏珠"]=1.28
Fighttxz["法术暴击"]=1.2
Fighttxz["月光"]=2.4
Fighttxz["翻江搅海"]=3
Fighttxz["尘土刃"]=1.5
Fighttxz["泰山压顶1"]=1.25
Fighttxz["泰山压顶2"]=1.4
Fighttxz["泰山压顶3"]=1.1
Fighttxz["归元咒"]=0.95
Fighttxz["乾天罡气"]=0.95
Fighttxz["巨岩破"]=0.95
Fighttxz["推拿"]=0.95
Fighttxz["活血"]=0.95
Fighttxz["推气过宫"]=0.95
Fighttxz["惊心一剑"]=1.05
Fighttxz["牛刀小试"]=1.05
Fighttxz["力劈华山"]=1.05
Fighttxz["食指大动"]=2
--Fighttxz["铸兵锤"]=2


function 取法宝类型(sj)
	local lssj = {"恢复","辅助","防御","攻击","诅咒"}
	return lssj[sj+0]
end

Jingtaicw={}
Jingtaicw["剑会天下·新秀"]={x=-53,y=8}
Jingtaicw["剑会天下·百胜"]={x=-53,y=8}
Jingtaicw["剑会天下·千胜"]={x=-53,y=8}
Jingtaicw["剑会天下·万胜"]={x=-53,y=8}
Jingtaicw["剑会天下·神话"]={x=-53,y=8}

QUshoujuesx={}
QUshoujuesx["高级法术暴击"]="高级法暴"
QUshoujuesx["高级精神集中"]="高级精神"
QUshoujuesx["高级否定信仰"]="高级信仰"
QUshoujuesx["高级魔之心"]="高级魔心"
QUshoujuesx["火属性吸收"]="火吸"
QUshoujuesx["水属性吸收"]="水吸"
QUshoujuesx["土属性吸收"]="土吸"
QUshoujuesx["雷属性吸收"]="雷吸"
QUshoujuesx["高级火属性吸收"]="高级火吸"
QUshoujuesx["高级水属性吸收"]="高级水吸"
QUshoujuesx["高级土属性吸收"]="高级土吸"
QUshoujuesx["高级雷属性吸收"]="高级雷吸"
QUshoujuesx["高级法术抵抗"]="高级法抗"
QUshoujuesx["高级神佑复生"]="高级神佑"
QUshoujuesx["高级鬼魂术"]="高级鬼魂"
QUshoujuesx["高级法术波动"]="高级法波"
QUshoujuesx["高级法术连击"]="高级法连"
QUshoujuesx["高级进击必杀"]="高级进必"
QUshoujuesx["高级进击法暴"]="高级进暴"
function 取兽决缩写(技能)
	if 技能 then
		if QUshoujuesx[技能] then
		    return QUshoujuesx[技能]
		end
        return 技能
	end
end

function 取境界(j,w)
 if w==1 then
	if j == 0 then
		return "#C/略晓变化"
		elseif j == 1 then
		return "#W/略晓变化"
		elseif j == 2 then
		return "#S/驾轻就熟"
		elseif j == 3 then
		return "#S/心领神会"
		elseif j == 4 then
		return "#S/出类拔萃"
		elseif j == 5 then
		return "#S/腾云驾雾"
		elseif j == 6 then
		return "#S/降龙伏虎"
		elseif j == 7 then
		return "#S/神乎其技"
		elseif j == 8 then
		return "#S/纵横三界"
		elseif j == 9 then
		return "#S/不堕轮回"
		end
	elseif w==2 then
		if  j==0 or j==1 then
		return "#C/了然于胸"
		elseif j == 2 then
		return "#W/妙领天机"
		elseif j == 3 then
		return "#S/渐入佳境"
		elseif j == 4 then
		return "#S/预知福祸"
		elseif j == 5 then
		return "#S/脱胎换骨"
		elseif j == 6 then
		return "#S/出神入化"
		elseif j == 7 then
		return "#S/呼风唤雨"
		elseif j == 8 then
		return "#S/随心所欲"
		elseif j == 9 then
		return "#S/登峰造极"
		elseif j == 10 then
		return "#S/道满根归"
		elseif j == 11 then
		return "#S/不堕轮回"
		elseif j == 12 then
		return "#S/法力无边"
		end
	elseif w==3 or w==4 then
		if  j==0 or j==1 then
		return "#C/一日千里"
		elseif j == 2 then
		return "#W/脱胎换骨"
		elseif j == 3 then
		return "#S/负海担山"
		elseif j == 4 then
		return "#S/霞举飞升"
		elseif j == 5 then
		return "#S/移星换斗"
		elseif j == 6 then
		return "#S/变幻莫测"
		elseif j == 7 then
		return "#S/擎日挽月"
		elseif j == 8 then
		return "#S/道满根归"
		elseif j == 9 then
		return "#S/不堕轮回"
		elseif j == 10 then
		return "#S/举世无双"
		elseif j == 11 then
		return "#S/纵横三界"
		elseif j == 12 then
		return "#S/笑傲西游"
		elseif j == 13 then
		return "#S/法力无边"
		elseif j == 14 then
		return "#S/反璞归真"
		elseif j == 15 then
		return "#S/天人合一"
		elseif j == 16 then
		return "#S/物我两忘"
		elseif j == 17 then
		return "#S/再厉尘劫"
		elseif j == 18 then
		return "#S/浴火涅磐"
		end
	end
end

function 武器宽度修正(模型,子类,宽度)
		local fhz = 50
		if 模型 == "飞燕女" then
				if 子类 == 4 then fhz =50 end
				if 子类 == 11 then fhz =50 end
		elseif 模型 == "英女侠" then
				if 子类 == 4 then fhz =50 end
				if 子类 == 10 then fhz =50 end
		elseif 模型 == "巫蛮儿" then
				if 子类 == 13 then fhz =50 end
				if 子类 == 15 then fhz =50 end
		elseif 模型 == "逍遥生" then
				if 子类 == 3 then fhz =50 end
				if 子类 == 7 then fhz =50 end
		elseif 模型 == "剑侠客" then
				if 子类 == 3 then fhz =50 end
				if 子类 == 12 then fhz =50 end
		elseif 模型 == "狐美人" then
				if 子类 == 6 then fhz =50 end
				if 子类 == 10 then fhz =50 end
		elseif 模型 == "骨精灵" then
				if 子类 == 6 then fhz =50 end
				if 子类 == 9 then fhz =50 end
		elseif 模型 == "杀破狼" then
				if 子类 == 14 then fhz =50 end
				if 子类 == 15 then fhz =50 end
		elseif 模型 == "巨魔王" then
				if 子类 == 2 then fhz =50 end
				if 子类 == 12 then fhz =50 end
		elseif 模型 == "虎头怪" then
				if 子类 == 2 then fhz =50 end
				if 子类 == 9 then fhz =50 end
		elseif 模型 == "舞天姬" then
				if 子类 == 5 then fhz =50 end
				if 子类 == 11 then fhz =50 end
		elseif 模型 == "玄彩娥" then
				if 子类 == 5 then fhz =50 end
				if 子类 == 9 then fhz =50 end
		elseif 模型 == "羽灵神" then
				if 子类 == 14 then fhz =50 end
				if 子类 == 13 then fhz =50 end
		elseif 模型 == "神天兵" then
				if 子类 == 1 then fhz =50 end
				if 子类 == 9 then fhz =50 end
		elseif 模型 == "龙太子" then
				if 子类 == 1 then fhz =50 end
				if 子类 == 7 then fhz =50 end
		elseif 模型 == "偃无师" then
				if 子类 == 16 then fhz =50 end
				if 子类 == 3 then fhz =50 end
		elseif 模型 == "鬼潇潇" then
				if 子类 == 17 then fhz =50 end
				if 子类 == 6 then fhz =50 end
		elseif 模型 == "桃夭夭" then
				if 子类 == 18 then fhz =50 end
				if 子类 == 5 then fhz =50 end
		end
		return fhz
end

function 取物品功能(wd)
	local wds = {}
	if wd == "急就章" then
			wds[1] = "【物品类型】儿童用品"
			wds[2] = "#Y【功能】孩子养育过程中需要的道具,提升孩子的养育属性;稚乐居中可以满足孩子需求,提升成长值和亲密度"
	elseif wd == "诗经" then
			wds[1] = "【物品类型】儿童用品"
			wds[2] = "#Y【功能】孩子养育过程中需要的道具,提升孩子的养育属性;稚乐居中可以满足孩子需求,提升成长值和亲密度"
	elseif wd == "道德经" then
			wds[1] = "【物品类型】儿童用品"
			wds[2] = "#Y【功能】孩子养育过程中需要的道具,提升孩子的养育属性;稚乐居中可以满足孩子需求,提升成长值和亲密度"
	elseif wd == "论语" then
			wds[1] = "【物品类型】儿童用品"
			wds[2] = "#Y【功能】孩子养育过程中需要的道具,提升孩子的养育属性;稚乐居中可以满足孩子需求,提升成长值和亲密度"
	elseif wd == "千字文" then
			wds[1] = "【物品类型】儿童用品"
			wds[2] = "#Y【功能】孩子养育过程中需要的道具,提升孩子的养育属性;稚乐居中可以满足孩子需求,提升成长值和亲密度"
	elseif wd == "奇异果" then
			wds[1] = "【物品类型】儿童用品"
			wds[2] = "#Y【功能】儿童食用,减少根骨、定力、武力、智力和念力属性各10点,养育结束后可领悟“潜力激发的特殊技能"
	elseif wd == "蚩尤武决" then
			wds[1] = "【物品类型】儿童用品"
			wds[2] = "#Y【功能】可供儿童学习,减少智力属性40点,减少定力及念力属性各20点,养育结束后可领悟“蚩尤之搏”的特殊技能"
	elseif wd == "还魂秘术" then
			wds[1] = "【物品类型】儿童用品"
			wds[2] = "#Y【功能】可供儿童学习,减少根骨属性40点及武力属性20点,养结束后可领悟“还魂咒”的特殊技能"
	elseif wd == "黄帝内经" then
			wds[1] = "【物品类型】儿童用品"
			wds[2] = "#Y【功能】儿童学习的初级医减少武学悟性30点,成年后可领悟“治疗”的特殊技能已"
	elseif wd == "千年人参" then
			wds[2] = "#Y【要求】夫妻友好度>2000且双方均>65级,有正在入住的房子,夫妻拥有的孩子数量≤3个"
	elseif wd == "孤儿手册" then
			wds[2] = "#Y【使用】玩家带着弧儿名册找马婆婆(长寿村43,71选关于养育小孩一使用儿名册领养小孩即获得一个未或年(幼年)孩子"
	elseif wd == "瑶池蟠桃" then
			wds[1] = "【物品类型】儿童用品"
			wds[2] = "#Y【【功能】孩子养过程中需要的道具,提升孩子的养育属性;稚乐居中可以满足孩子需求,提升成长值和亲密度"
	elseif wd == "玉灵果" then
			wds[1] = "【物品类型】儿童用品"
			wds[2] = "#Y【功能】孩子养过程中需要的道具,提升孩子的养育属性;稚乐居中可以满足孩子需求,提升成长值和亲密度"
	elseif wd == "糖葫芦" then
			wds[1] = "【物品类型】儿童用品"
			wds[2] = "#Y【功能】孩子养过程中需要的道具,提升孩子的养育属性;稚乐居中可以满足孩子需求,提升成长值和亲密度"
	elseif wd == "糖葫芦" then
			wds[1] = "【物品类型】儿童用品"
			wds[2] = "#Y功能】孩子养过程中需要的道具,提升孩子的养育属性;稚乐居中可以满足孩子需求,提升成长值和亲密度"
	elseif wd == "弹弓" then
			wds[1] = "【物品类型】儿童用品"
			wds[2] = "#Y【功能】孩子养过程中需要的道具,提升孩子的养育属性;稚乐居中可以满足孩子需求,提升成长值和亲密度"
	elseif wd == "竹马" then
			wds[1] = "【物品类型】儿童用品"
			wds[2] = "#Y【功能】孩子养过程中需要的道具,提升孩子的养育属性;稚乐居中可以满足孩子需求,提升成长值和亲密度"
	elseif wd == "投掷沙包" then
			wds[1] = "【物品类型】儿童用品"
			wds[2] = "#Y功能】孩子养过程中需要的道具,提升孩子的养育属性;稚乐居中可以满足孩子需求,提升成长值和亲密度"
	elseif wd == "猴皮筋" then
			wds[1] = "【物品类型】儿童用品"
			wds[2] = "#Y【功能】孩子养过程中需要的道具,提升孩子的养育属性;稚乐居中可以满足孩子需求,提升成长值和亲密度"
	elseif wd == "百兽图谱" then
			wds[1] = "【物品类型】儿童用品"
			wds[2] = "#Y【功能】孩子养过程中需要的道具,提升孩子的养育属性;稚乐居中可以满足孩子需求,提升成长值和亲密度"
	elseif wd == "山海经" then
			wds[1] = "【物品类型】儿童用品"
			wds[2] = "#Y【功能】孩子养过程中需要的道具,提升孩子的养育属性;稚乐居中可以满足孩子需求,提升成长值和亲密度"
	elseif wd == "列异传" then
			wds[1] = "【物品类型】儿童用品"
			wds[2] = "#Y【功能】孩子养过程中需要的道具,提升孩子的养育属性;稚乐居中可以满足孩子需求,提升成长值和亲密度"
	elseif wd == "搜神记" then
			wds[1] = "【物品类型】儿童用品"
			wds[2] = "#Y【功能】孩子养过程中需要的道具,提升孩子的养育属性;稚乐居中可以满足孩子需求,提升成长值和亲密度"
	elseif wd == "启蒙棋谱" then
			wds[1] = "【物品类型】儿童用品"
			wds[2] = "#Y【功能】孩子养过程中需要的道具,提升孩子的养育属性;稚乐居中可以满足孩子需求,提升成长值和亲密度"
	elseif wd == "汤圆" then
			wds[1] = "#Y【功能】食用后在元宵节活动中犹如天神附体"
	elseif wd == "巧克力" then
			wds[1] = "#Y【功能】右键打开有可能得到惊喜的礼物"
	elseif wd == "星辰碎片" or "暗淡的星辰碎片" or "微光的星辰碎片" or "发光的星辰碎片" or "耀眼的星辰碎片" or "奇异的星辰碎片" then
			wds[1] = "#Y【右键使用可以点亮天书中星星并获得奖励】"
	elseif wd == "雄黄酒" then
			wds[1] = "#Y【右键子女使用提升子女的成长】"
	elseif wd == "粽子" then
			wds[1] = "#Y【功能】右键食用获得奖励"
	elseif wd == "梦幻精品粽子" then
			wds[1] = "#Y【功能】随机增加属性"
	elseif wd == "红玫瑰" then
			wds[1] = "#Y【效果】异性之间相互赠送,可以增加双方友好度99点"
	end
	return wds
end

function 秘制食谱子类()
	return {"红罗羹","绿芦羹","玉露羹","百岁香","神仙饮","八珍玉液","五味露","福灵沙","千金露","同心肉脯","忠义肉脯"}
end

-- function 取重复名物品(wp)
-- 	local wds = {}
-- 	if wp.名称 == "七彩礼盒" and wp.总类 == 121 then
-- 		if wp.子类 == 1 then
-- 				wp.小模型资源 = 0x6853A6ED
-- 				wp.大模型资源 = 0xB0087763
-- 				wp.资源 = "item.wdf"
-- 		elseif wp.子类 == 2 then
-- 				wp.小模型资源 = 0x9D18052B
-- 				wp.大模型资源 = 0x012BAF1D
-- 				wp.资源 = "item.wdf"
-- 		elseif wp.子类 == 3 then
-- 				wp.小模型资源 = 0x3931E88A
-- 				wp.大模型资源 = 0xF65F219F
-- 				wp.资源 = "item.wdf"
-- 		elseif wp.子类 == 4 then
-- 				wp.小模型资源 = 0x5F91D282
-- 				wp.大模型资源 = 0xC4CB01E8
-- 				wp.资源 = "item.wdf"
-- 		elseif wp.子类 == 5 then
-- 				wp.小模型资源 = 0x16D49102
-- 				wp.大模型资源 = 0x4BBC2FC7
-- 				wp.资源 = "item.wdf"
-- 		elseif wp.子类 == 6 then
-- 				wp.小模型资源 = 0x4D6DB56C
-- 				wp.大模型资源 = 0x85C409C5
-- 				wp.资源 = "item.wdf"
-- 		elseif wp.子类 == 7 then
-- 				wp.小模型资源 = 0xFC9B9B95
-- 				wp.大模型资源 = 0xD3BAD3DB
-- 				wp.资源 = "item.wdf"
-- 		elseif wp.子类 == 8 then
-- 				wp.小模型资源 = 0x9C455417
-- 				wp.大模型资源 = 0x24AA3228
-- 				wp.资源 = "item.wdf"
-- 		elseif wp.子类 == 9 then
-- 				wp.小模型资源 = 0x331341CF
-- 				wp.大模型资源 = 0x94FFDE01
-- 				wp.资源 = "item.wdf"
-- 		end
-- 	elseif wp.名称 == "新春导标旗" and wp.总类 == 11 then
-- 		if wp.子类 == 1 then --黄色
-- 				wp.小模型资源 = 0x3c559d3b
-- 				wp.大模型资源 = 0x1b910784
-- 				wp.资源 = "common/item.wdf"
-- 		elseif wp.子类 == 2 then --绿色
-- 				wp.小模型资源 = 0x3fccfb71
-- 				wp.大模型资源 = 0x7a20f2d
-- 				wp.资源 = "common/item.wdf"
-- 		elseif wp.子类 == 3 then -- 红色
-- 				wp.小模型资源 = 0xb451d176
-- 				wp.大模型资源 = 0x2c95bd3b
-- 				wp.资源 = "common/item.wdf"
-- 		elseif wp.子类 == 4 then -- 蓝色
-- 				wp.小模型资源 = 0x4f20b0c1
-- 				wp.大模型资源 = 0x8756e0d2
-- 				wp.资源 = "common/item.wdf"
-- 		end
-- 	elseif wp.名称 == "精魄灵石" and wp.总类 == "召唤兽镶嵌" then
-- 		if wp.子类 == 1 or wp.子类 == 2 then--红
-- 				wp.小模型资源 = 0xfc91c407
-- 				wp.大模型资源 = 0x6397856f
-- 				wp.资源 = "item.wdf"
-- 		elseif wp.子类 == 3 or wp.子类 == 4 then--黄
-- 				wp.小模型资源 = 0x9b0688a2
-- 				wp.大模型资源 = 0xacac92bb
-- 				wp.资源 = "item.wdf"
-- 		elseif wp.子类 == 5 or wp.子类 == 6 then--蓝
-- 				wp.小模型资源 = 0x3be0a96e
-- 				wp.大模型资源 = 0x489cfc78
-- 				wp.资源 = "item.wdf"
-- 		end
-- 	end
-- 	return wp.小模型资源
-- end
local  function 分解灵犀玉体力消耗(等级)
	print(等级)
	local fhz = 10
	if 等级 == 1 then
	fhz = 10
	return fhz
	elseif 等级 == 2 then
	fhz = 40
	return fhz
	elseif 等级 == 3 then
	fhz = 160
	return fhz
	end
end

local  function 打造金钱公式(等级)
	local fhz = 1000
	fhz = fhz* math.floor(等级/10)
	return fhz
end

local  function 打造体力消耗(等级)
	local fhz = 50
	fhz = math.floor(等级/10)*20
	return fhz
end

local  function 宝石合成体力消耗(等级)
	local fhz = 10
	fhz = 等级*10
	return fhz
end

local  function 宝石合成金钱公式(等级)
	local fhz = 2000
	fhz = fhz* 等级
	return fhz
end

local  function 熔炼消耗(等级)
	local fhz1,fhz2 = 0,0
	if 等级 == 60 then
		fhz1,fhz2 = 8,10000
	elseif 等级 == 70 then
		fhz1,fhz2 = 10,10000
	elseif 等级 == 80 then
		fhz1,fhz2 = 12,20000
	elseif 等级 == 90 then
		fhz1,fhz2 = 14,30000
	elseif 等级 == 100 then
		fhz1,fhz2 = 16,40000
	elseif 等级 == 110 then
		fhz1,fhz2 = 18,50000
	elseif 等级 == 120 then
		fhz1,fhz2 = 20,60000
	elseif 等级 == 130 then
		fhz1,fhz2 = 22,70000
	elseif 等级 ==140 then
		fhz1,fhz2 = 24,80000
	elseif 等级 == 150 then
		fhz1,fhz2 = 26,90000
	elseif 等级 == 160 then
		fhz1,fhz2 = 28,95000
	end
	return fhz1,fhz2
end

local  function 还原消耗(等级)
	local fhz=0
	if 等级 == 60 then
		fhz = 300000
	elseif 等级 == 70 then
		fhz = 400000
	elseif 等级 == 80 then
		fhz = 500000
	elseif 等级 == 90 then
		fhz = 600000
	elseif 等级 == 100 then
		fhz = 700000
	elseif 等级 == 110 then
		fhz = 800000
	elseif 等级 == 120 then
		fhz = 900000
	elseif 等级 == 130 then
		fhz = 1000000
	elseif 等级 ==140 then
		fhz = 1180000
	elseif 等级 == 150 then
		fhz = 1290000
	elseif 等级 == 160 then
		fhz = 1890000
	end
	return fhz
end

function 打造判定消耗(物品1,物品2,分类标识,功能标识)
	local fhz1,fhz2 = 0,0
	if 分类标识 == "打造" then
		if 物品1.名称 == "制造指南书" or 物品1.名称 == "上古锻造图策" or 物品1.名称 == "灵饰指南书"  then
			fhz1,fhz2 = 打造体力消耗(物品1.子类),打造金钱公式(物品1.子类)
		elseif 物品2.名称 == "制造指南书" or 物品2.名称 == "上古锻造图策" or 物品2.名称 == "灵饰指南书"  then
			fhz1,fhz2 = 打造体力消耗(物品2.子类),打造金钱公式(物品2.子类)
		end
	elseif 分类标识 == "合成" then
		if 功能标识 == "宝石" then
			if 物品1.级别限制 == 物品2.级别限制 then
				fhz1,fhz2 = 宝石合成体力消耗(物品1.级别限制),宝石合成金钱公式(物品1.级别限制)
			end
		end
	elseif 分类标识 == "分解" then
		if 功能标识 == "分解灵犀玉" then
			if 物品1.名称 == "灵犀玉" then
				fhz1 = 分解灵犀玉体力消耗(物品1.子类)
			elseif 物品2.名称 == "灵犀玉" then
				fhz1 = 分解灵犀玉体力消耗(物品2.子类)
			end
		end
	elseif 分类标识 == "熔炼" then
		if 功能标识 == "熔炼装备" then
			if 物品1.级别限制 == 物品2.级别限制 then
				fhz1,fhz2 = 熔炼消耗(物品1.级别限制)
			end
		elseif 功能标识 == "还原装备" then
			if 物品1 then
				fhz2 = 还原消耗(物品1.级别限制)
			end
		end
	end
	return fhz1,fhz2
end

function 判定非数字(sj)
	for i=1,#sj do
		local curByte = string.byte(sj, i)
		if curByte < 48 or curByte > 57 then
		 	return false
		end
	end
	return true
end

function 判定数字合法(sj,类型)
	if sj == "" then
		return false
	end
	if not 判定非数字(sj) then
		return false
	end

	sj = sj +0
	if string.len(tostring(sj)) > 14  then
		return false
	elseif sj < 1 and 类型 ~= "银两"  then
		return false
	elseif sj < 0 and 类型 == "银两"  then
		return false
	elseif math.floor(sj)<sj then
		return false
	end
	return true
end

-- 黑暗料理=大米+苹果+对虾
-- 杂烩粥=大米+苹果+鲫鱼
-- 红烧鲤鱼=面粉+鲤鱼
-- 什锦年糕=面粉+苹果/面粉+对虾+肉/面粉+桃子+肉
-- 油焖对虾=对虾+对虾
-- 果蔬粥=大米+苹果/大米+苹果+桃子
-- 白粥=大米
-- 饺子=面粉+肉
-- 果蔬篮=苹果+桃子/桃子/苹果
-- 烤肉=任意鱼+肉/肉（做法超多）
-- 药膳粥=大米+珍贵药材/大米+珍贵药材+其他
-- 红烧鲫鱼=面粉+鲫鱼
-- 年糕=面粉
-- 苹果果脯=苹果
-- 大乱炖=任意水果+肉/珍贵药材/珍贵药材+其他