local 自选系统 = class()
local tp,zts
local insert = table.insert

function 自选系统:初始化(根)
	self.ID = 168
	self.x = 全局游戏宽度/2-180
	self.y = 94--全局游戏高度/2-160
	self.xx = 0
	self.yy = 0
	self.注释 = "自选系统"
	self.可视 = false
	self.鼠标 = false
	self.焦点 = false
	self.可移动 = true
	tp = 根
	self.窗口时间 = 0
	zts = tp.字体表.普通字体
end


function 自选系统:加载资源()
	local 资源 = tp.资源
	local 按钮 = tp._按钮
	local 自适应 = tp._自适应
	self.资源组 = {
		[1] = 自适应.创建(0,1,497+61,440,3,9),
		[2] = 按钮.创建(自适应.创建(18,4,16,16,4,3),0,0,4,true,true),--关闭
		[3] = 按钮.创建(自适应.创建(12,4,78,22,1,3),0,0,4,true,true,"  确定"),
		-- [8] = 资源:载入('nice.wdf',"网易WDF动画",0x0683C414), --已领取
	}
	  -- self.资源组[3]:置偏移(5,0)
end
--1650025321 wzife.wd3任务完成
function 自选系统:打开(标题,附加)
	--table.print(附加)
	if self.可视 then
		self.可视 = false
		self.资源组=nil
		self.物品组={}
		return
	else
		insert(tp.窗口_,self)
		if not self.资源组 then
		    self:加载资源()
		end
		self.标题=标题
		self.附加=附加
		self.选中=0
		self.选中坐标={x=0,y=0}
		self.加入=0
		self:加载物品()
		tp.运行时间 = tp.运行时间 + 1
		self.窗口时间 = tp.运行时间
		self.可视 = true
	end
end
function 自选系统:加载物品()
	self.物品组={}
	if self.标题=="150书铁礼包" then
		local 书铁范围 = {"天龙破城","碧血干戚","霜冷九州","紫电青霜","揽月摘星","忘川三途","浩气长舒","丝萝乔木","狂澜碎岳","牧云清歌","无关风月","业火三灾","碧海潮生","九霄风雷","云雷万里","百辟镇魂","夭桃秾李","浮生归梦","紫金磐龙冠","金珰紫焰冠","紫金碧玺佩","五彩凤翅衣","紫金磐龙甲","磐龙凤翔带","金丝逐日履"}
		local mc={"枪矛","斧钺","剑","双短剑","飘带","爪刺","扇","魔棒","锤","鞭","环圈","刀","法杖","弓弩","宝珠","巨剑","伞","灯笼","头盔","发钗","项链","女衣","男衣","腰带","鞋子"}
		for i=1,#书铁范围 do
			local zy=引擎.取物品(书铁范围[i])
			self.物品组[i]={}
			self.物品组[i].小动画=tp.资源:载入(zy[11],"网易WDF动画",zy[12])
			self.物品组[i].大动画=tp.资源:载入(zy[11],"网易WDF动画",zy[13])
			self.物品组[i].名称=mc[i]
			self.物品组[i].说明=zy[1]
			self.物品组[i].py={0,0}
		end
	elseif self.标题=="150自选无级别礼包" then
		local 书铁范围 = {"天龙破城","碧血干戚","霜冷九州","紫电青霜","揽月摘星","忘川三途","浩气长舒","丝萝乔木","狂澜碎岳","牧云清歌","无关风月","业火三灾","碧海潮生","九霄风雷","云雷万里","百辟镇魂","浮生归梦","夭桃秾李","紫金磐龙冠","金珰紫焰冠","紫金碧玺佩","五彩凤翅衣","紫金磐龙甲","磐龙凤翔带","金丝逐日履"}
		local mc={"枪矛","斧钺","剑","双短剑","飘带","爪刺","扇","魔棒","锤","鞭","环圈","刀","法杖","弓弩","宝珠","巨剑","伞","灯笼","头盔","发钗","项链","女衣","男衣","腰带","鞋子"}
		for i=1,#书铁范围 do
			local zy=引擎.取物品(书铁范围[i])
			self.物品组[i]={}
			self.物品组[i].小动画=tp.资源:载入(zy[11],"网易WDF动画",zy[12])
			self.物品组[i].大动画=tp.资源:载入(zy[11],"网易WDF动画",zy[13])
			self.物品组[i].名称=mc[i]
			self.物品组[i].说明=zy[1]
			self.物品组[i].py={0,0}
		end
	elseif self.标题=="160自选无级别礼包" then
		local 书铁范围 = {"弑皇","裂天","擒龙","浮犀","九霄","离钩","星瀚","醍醐","碎寂","霜陨","朝夕","鸣鸿","弦月","若木","赤明","长息","晴雪","荒尘","浑天玄火盔","乾元鸣凤冕","落霞陨星坠","鎏金浣月衣","混元一气甲","紫霄云芒带","辟尘分光履"}
		local mc={"枪矛","斧钺","剑","双短剑","飘带","爪刺","扇","魔棒","锤","鞭","环圈","刀","法杖","弓弩","宝珠","巨剑","伞","灯笼","头盔","发钗","项链","女衣","男衣","腰带","鞋子"}
		for i=1,#书铁范围 do
			local zy=引擎.取物品(书铁范围[i])
			self.物品组[i]={}
			self.物品组[i].小动画=tp.资源:载入(zy[11],"网易WDF动画",zy[12])
			self.物品组[i].大动画=tp.资源:载入(zy[11],"网易WDF动画",zy[13])
			self.物品组[i].名称=mc[i]
			self.物品组[i].说明=zy[1]
			self.物品组[i].py={0,0}
		end
	elseif self.标题=="自选特殊技能" then
		for i=1,#self.附加 do
			local zy=引擎.取技能(self.附加[i])
			self.物品组[i]={}
			if zy[10] then

				self.物品组[i].小动画=tp.资源:载入("wdf/vvxxzcom/pic"..self.附加[i]..".png","图片")
				self.物品组[i].大动画=tp.资源:载入("wdf/vvxxzcom/pic"..self.附加[i]..".png","图片")
			else
				self.物品组[i].小动画=tp.资源:载入(zy[6],"网易WDF动画",zy[7])
				self.物品组[i].大动画=tp.资源:载入(zy[6],"网易WDF动画",zy[7])
			end
			self.物品组[i].名称=self.附加[i]
			self.物品组[i].说明=zy[1]
			self.物品组[i].py={3,3}
		end
	elseif self.标题=="自选特殊技能2" then
		for i=1,#self.附加 do
			local zy=引擎.取技能(self.附加[i])
			self.物品组[i]={}
			if zy[10] then

				self.物品组[i].小动画=tp.资源:载入("wdf/vvxxzcom/pic"..self.附加[i]..".png","图片")
				self.物品组[i].大动画=tp.资源:载入("wdf/vvxxzcom/pic"..self.附加[i]..".png","图片")
			else
				self.物品组[i].小动画=tp.资源:载入(zy[6],"网易WDF动画",zy[7])
				self.物品组[i].大动画=tp.资源:载入(zy[6],"网易WDF动画",zy[7])
			end
			self.物品组[i].名称=self.附加[i]
			self.物品组[i].说明=zy[1]
			self.物品组[i].py={3,3}
		end
	elseif self.标题=="高级宝石礼包" then
		local 宝石 = {"红玛瑙","太阳石","舍利子","黑宝石","月亮石","光芒石"}
		for i=1,#宝石 do
			local zy=引擎.取物品(宝石[i])
			self.物品组[i]={}
			self.物品组[i].小动画=tp.资源:载入(zy[11],"网易WDF动画",zy[12])
			self.物品组[i].大动画=tp.资源:载入(zy[11],"网易WDF动画",zy[13])
			self.物品组[i].名称=宝石[i]
			self.物品组[i].说明=zy[1]
			self.物品组[i].py={0,0}
		end
	end
end

function 自选系统:显示(dt,x,y)
	self.焦点 = false
	self.资源组[1]:显示(self.x,self.y)
	self.资源组[2]:更新(x,y)
	self.资源组[3]:更新(x,y,self.选中~=0)
	tp.窗口标题背景_:显示(self.x-86+self.资源组[1].宽度/2,self.y)
	引擎.场景.字体表.标题字体:显示(self.x+self.资源组[1].宽度/2,self.y+3,self.标题)
	self.资源组[2]:显示(self.x-18+self.资源组[1].宽度,self.y+3)
	self.资源组[3]:显示(self.x+246,self.y+400)
	local xx = 0
	local yy = 0
	zts:置颜色(白色)
	zts:显示(self.x+10,self.y+30,"你想要下面哪种类型的道具呢？")
	if self.标题=="自选特殊技能" then
		zts:显示(self.x+10+170+34+16,self.y+30+11+330,"支持鼠标中键滚动")
		-- local bbsa = #self.物品组
		local bbsa = math.floor(#self.物品组/28)
		if self.资源组[1]:是否选中(x,y)and 引擎.取鼠标滚轮() ==1 and self.加入 > 0 then--鼠标上滚动
			self.加入=self.加入-1
		elseif self.资源组[1]:是否选中(x,y) and 引擎.取鼠标滚轮() ==-1 then--鼠标下滚动
			if self.加入 <bbsa then
				self.加入 = self.加入 + 1
			end
		end
		local x横,x竖 = 1,1
		for n = 1, 28 do
			tp.物品格子背景_:显示(self.x+92+(x横-2)*80,self.y+55+(x竖-1)*80)
			if self.物品组[n+self.加入*28] then
				self.物品组[n+self.加入*28].小动画:显示(self.x+92+(x横-2)*80+3+self.物品组[n+self.加入*28].py[1],self.y+55+(x竖-1)*80+3+self.物品组[n+self.加入*28].py[2])
				if self.物品组[n+self.加入*28].小动画:是否选中(x,y) then
					self.焦点=true
					tp.物品格子焦点_:显示(self.x+92+(x横-2)*80+3,self.y+55+(x竖-1)*80+3)
					local sadasd=鼠标.x-360
					if sadasd<0 then
						sadasd=0
					end
				    	tp.提示:商城提示(sadasd,鼠标.y,self.物品组[n+self.加入*28].名称,self.物品组[n+self.加入*28].说明,self.物品组[n+self.加入*28].大动画,self.物品组[n+self.加入*28].备注)
				    	if 引擎.鼠标弹起(左键) then
				    		self.选中坐标={x=92+(x横-2)*80+3,y=55+(x竖-1)*80+3}
				    		self.选中=n+self.加入*28
				    	end
				end
			end
			if x横 == 7  then
				x横 = 1
				x竖=x竖+1
			else
				x横 = x横+1
			end
		end
	else
		for i=1,#self.物品组 do
			tp.物品格子背景_:显示(self.x+92+(xx-1)*80,self.y+55+yy*80)
			if self.物品组[i] then
				self.物品组[i].小动画:显示(self.x+92+(xx-1)*80+3+self.物品组[i].py[1],self.y+55+yy*80+3+self.物品组[i].py[2])
				if self.物品组[i].小动画:是否选中(x,y) then
					self.焦点=true
					tp.物品格子焦点_:显示(self.x+92+(xx-1)*80+3,self.y+55+yy*80+3)
					local sadasd=鼠标.x-360
					if sadasd<0 then
						sadasd=0
					end
				    	tp.提示:商城提示(sadasd,鼠标.y,self.物品组[i].名称,self.物品组[i].说明,self.物品组[i].大动画,self.物品组[i].备注)
				    	if 引擎.鼠标弹起(左键) then
				    		self.选中坐标={x=92+(xx-1)*80+3,y=55+yy*80+3}
				    		self.选中=i
				    	end
				end
			end
			xx = xx + 1
			if xx==7 then
				xx=0
				yy=yy+1
			end
		end
	end



	if self.资源组[2]:事件判断() then
		self:打开()
		return
	end
	if self.选中~=0 and self.物品组[self.选中] then
		tp.物品格子确定_:显示(self.x+self.选中坐标.x,self.y+self.选中坐标.y)
		if self.资源组[3]:事件判断() then --20
			发送数据(3817,{道具名称=self.标题,选中=self.选中,名称=self.物品组[self.选中].名称})
			self:打开()
			return
		end
	end
	-- for i=1,5 do
	-- 	if self.物品组[i] then
	-- 	    self.物品组[i].小动画:显示(self.x+61,self.y+0)
	-- 	end
	-- end
end





function 自选系统:检查点(x,y)
	if self.资源组 ~= nil and self.资源组[1]:是否选中(x,y) then
		return true
	end
end

function 自选系统:初始移动(x,y)
	tp.运行时间 = tp.运行时间 + 1
	if not tp.消息栏焦点 then
		self.窗口时间 = tp.运行时间
	end
	if not self.焦点 then
		tp.移动窗口 = true
	end
	if self.鼠标 and  not self.焦点 then
		self.xx = x - self.x
		self.yy = y - self.y
	end
end

function 自选系统:开始移动(x,y)
	if self.鼠标 then
		self.x = x - self.xx
		self.y = y - self.yy
	end
end

return 自选系统