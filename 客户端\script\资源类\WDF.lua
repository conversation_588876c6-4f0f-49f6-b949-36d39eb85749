--======================================================================--
-- @作者: www.wnfj.com，联系QQ：79550111
-- @创建时间:   2018-03-03 02:34:19
-- @Last Modified time: 2024-12-05 17:26:08
-- 更多游戏请访问万能飞机：www.wnfj.com,版本定制授权联系QQ：79550111
--======================================================================--
local ffi  = require("ffi")
local new  = ffi.new
local wdf  = class()
local _Ptr = new('char[10485760]')--全局共享内存 5242880 10485760 20971520
local Key2 = {
        0xBE,0xB4,0xF9,0x81,0x05,0xFD,0x6F,0x2D,0x64,0x62,0xEB,0x7E,0xCA,0x64,0x96,0xB2,0x19,0x15,0x42,0x9A,0xC8,0xB3,0xD0,0x73,0x25,0x2A,0x83,0x85,0xA2,0xB1,0x07,0xCF,0x98,0xC5,0x15,0xFC,0x10,0xE5,0x19,0xFA,0xEE,0x6E,0x35,0x40,0x71,0xF4,0xA7,0xA7,0xD3,0x13,0x04,0xFE,0x55,0x87,0x30,0x0B,0xE3,0x86,0x5C,0xD9,0x7A,0xE9,0x8A,0xB1,0x5B,0x1C,0xE4,0x05,0x4F,0x98,0xA3,0x32,0xFA,0x47,0xAA,0x5A,0x7F,0x90,0x03,0x30,0x81,0xDB,0xB0,0x8F,0xAD,0x08,0x7B,0x12,0x96,0x0B,0x1A,0x88,0x8A,0x3E,0x80,0x06,0xE3,0x82,0xE3,0x55,0x10,0xA3,0x58,0x3B,0x6F,0xD1,0x04,0x0E,0xDC,0xD4,0x8B,0x53,0x83,0x2D,0x59,0x9E,0x39,0xF9,0x04,0x54,0x9F,0xD3,0x6D,0x45,0x7F,0x1B,0xA6,0xBC,0xC5,0xA4,0x76,0xFB,0xAC,0xAD,0x20,0xDD,0x83,0x76,0x21,0xDB,0x04,0x6D,0x34,0xC1,0x7E,0xC6,0x05,0x5E,0xE4,0xE1,0xCC,0xE8,0x7E,0x8F,0xB4,0x3B,0x36,0x14,0xBB,0x54,0x2B,0x46,0x48,0x38,0x5F,0x8E,0xA6,0xD5,0x0D,0x7B,0x1B,0xC1,0xD9,0x28,0x50,0x33,0x9F,0xAB,0xC5,0xB8,0x17,0x49,0xD7,0xE2,0xD7,0x8C,0x7E,0x1D,0xF7,0xE3,0xA7,0x70,0xB9,0xCB,0xB1,0xBD,0x26,0xFB,0x63,0xEF,0x21,0x2E,0x51,0x69,0xC7,0x0B,0x0F,0x57,0x3F,0x82,0x9B,0x89,0x4D,0x04,0x3E,0xE0,0x6D,0x64,0x60,0xD1,0x11,0x89,0xDD,0x28,0xD2,0xA9,0x09,0x0F,0x76,0x92,0x0D,0xA3,0x49,0xD5,0x97,0xE7,0xF7,0x6E,0x1C,0xCC,0x7D,0x27,0xDC,0x76,0x49,0x1E,0x24,0xB6,0x8D,0xCF,0x50,0xC1,0x9C,0x90,0xDE,0x96,0x50
    }
local Key4={0x48,0x75,0x35,0x35,0x75,0x6F,0x50,0x55,0x45,0x4F,0x70,0x33,0x32,0x51,0x31,0x77,0x39,0x53,0x6F,0x69}

local function _解密列表2(Data, Size)
    local Data = ffi.cast("char*", Data)
    local Key_Step = 1
    for i = 0, Size - 1, 1 do
        Data[i] = bit.bxor(Data[i], Key2[Key_Step])
        Key_Step = Key_Step + 1
        if Key_Step == 34 then
            Key_Step = Key_Step + 1
        end
        if Key_Step == 258 then
            Key_Step = 1
        end
    end
end
function 解密4(Data, Size)
    local Data = ffi.cast("char*", Data)
    local Key_Step = 1
    for i = 0, Size - 1, 1 do
        Data[i] = bit.bxor(Data[i], Key4[Key_Step])
        Key_Step=Key_Step+1
        if Key_Step == #Key4 +1 then
           Key_Step = 1
        end
    end
end

function wdf:初始化(路径,模式)
    self.File  = require("文件类")(路径)
    self.List  = {}
    local head = self.File:读入数据(new("WDF_HEADER"))
    local flag = ffi.string(head.Flag,4)
    self.File:移动读写位置(head.Offset,self.File.SEEK_SET)
    self.Clist = self.File:读入数据(new("FILELIST[?]",head.Number))
    if flag == "RXPK" then
        _解密列表2(self.Clist, ffi.sizeof(self.Clist))
    end
    if flag == "XXGE" then --shangc
        解密4(self.Clist, ffi.sizeof(self.Clist))
    end
    for i=0,head.Number-1 do
        if 模式 ~= "zdy.rpk" and 模式 ~= "zdy2.rpk" and 模式 ~= "zdy3.rpk" and 模式 ~= "zdy4.rpk" and 模式 ~= "jy.wdf" and 模式 ~= "yy.wdf" then
            全局资源地址[self.Clist[i].Hash] = 模式
        end
        self.List[self.Clist[i].Hash] = self.Clist[i]
    end
end

function wdf:读数据(Hash)
    if self.List[Hash] then
        self.File:移动读写位置(self.List[Hash].Offset,self.File.SEEK_SET)
        self.File:读入数据(_Ptr,self.List[Hash].Size)
        return _Ptr,self.List[Hash].Size,Hash
    end
end

function wdf:读偏移(Hash)
    if self.List[Hash] then
        return self.List[Hash].Offset,self.List[Hash].Size
    end
end

return wdf