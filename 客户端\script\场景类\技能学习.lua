--======================================================================--
-- @作者: www.wnfj.com，联系QQ：79550111
-- @创建时间:   2018-03-03 02:34:19
-- @Last Modified time: 2024-12-05 17:26:07
--======================================================================--
local 场景类_技能学习 = class()

local floor = math.floor
local bw = require("gge包围盒")(0,0,176,37)
local box = 引擎.画矩形
local tp,zys,fonts
local ARGB = ARGB
local insert = table.insert

function 场景类_技能学习:初始化(根)
	self.ID = 32
	self.x = 222
	self.y = 83
	self.xx = 0
	self.yy = 0
	self.注释 = "技能学习"
	self.可视 = false
	self.鼠标 = false
	self.焦点 = false
	self.可移动 = true
	local 按钮 = 根._按钮
	local 资源 = 根.资源
	local 自适应 = 根._自适应
	self.资源组 = {
		[1] = 自适应.创建(0,1,400,480,3,9),
		[2] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E13),0,0,4,true,true), --guanb
		[3] = 按钮.创建(自适应.创建(20,4,18,19,4,3),0,0,4,true,true),--上滚动
		[4] = 按钮.创建(自适应.创建(21,4,18,19,4,3),0,0,4,true,true),--下滚动
		[5] = 按钮.创建(自适应.创建(20,4,18,19,4,3),0,0,4,true,true),--上滚动
		[6] = 按钮.创建(自适应.创建(21,4,18,19,4,3),0,0,4,true,true),--下滚动
		[7] = 按钮.创建(自适应.创建(12,4,100,22,1,3),0,0,4,true,true,"学 习"),
		[8] = 资源:载入('pic/jnxx.png',"图片"),
		[9] = 自适应.创建(2,1,180,292,3,9),
		[10] = 自适应.创建(34,1,179,132,3,9),
		[11] = 自适应.创建(34,1,179,152,3,9),
		[12] = 自适应.创建(104,1,100,22,1,3),
	}
    self.资源组[7]:置偏移(24,0)
	for n=2,7 do
		self.资源组[n]:绑定窗口_(self.ID)
	end
	self.介绍文本 = 根._丰富文本(165,150,根.字体表.普通字体)
	self.窗口时间 = 0
	self.选中 = nil
	self.选中1 = nil
	self.选中包含技能加入 = nil
	self.师门技能 = nil
	self.包含技能 = nil
	self.本次需求 = nil
	self.刷新文本 = false
	tp = 根
	zys = 资源
	fonts = tp.字体表.普通字体
end

function 场景类_技能学习:打开()
	if self.可视 then
		self.介绍文本:清空()
		self.选中师门技能 = nil
		self.选中包含技能 = nil
		self.选中包含技能加入 = nil
		self.师门技能 = nil
		self.包含技能 = nil
		self.刷新文本 = false
		self.可视 = false
	else
		insert(tp.窗口_,self)
		if tp.队伍[1].门派 == "无门派" then
		    tp.常规提示:打开("#Y/加入门派才可以学习师门技能")
		    return false
		end
		self.师门技能 = {}
		self.包含技能 = {}
		self.选中 = 0
		self.加入 = 0
		tp.运行时间 = tp.运行时间 + 1
	  	self.窗口时间 = tp.运行时间
		self.可视 = true
	end
end

function 场景类_技能学习:显示(dt,x,y)
	self.资源组[2]:更新(x,y)
	self.资源组[3]:更新(x,y,self.选中 ~= 0 and self.加入 > 0)
	self.资源组[4]:更新(x,y,self.选中 ~= 0 and self.加入 < #tp.队伍[1].师门技能[self.选中].包含技能 - 4)
	self.资源组[5]:更新(x,y,false)
	self.资源组[6]:更新(x,y,false)
	self.资源组[7]:更新(x,y,self.选中 ~= 0 and tp.队伍[1].师门技能[self.选中].等级 < 180)
	self.焦点 = false
	if self.鼠标 then
		if self.资源组[2]:事件判断() then
			self:打开()
			return false
		elseif self.资源组[3]:事件判断() or (引擎.取鼠标滚轮() ==1 and self.选中 ~= 0 and self.加入 > 0)  then
			self.加入 = self.加入 - 1
		elseif self.资源组[4]:事件判断() or (引擎.取鼠标滚轮() ==-1 and self.选中 ~= 0 and self.加入 < #tp.队伍[1].师门技能[self.选中].包含技能 - 4) then
			self.加入 = self.加入 + 1
		elseif self.资源组[7]:事件判断() then
			发送数据(3711,{序列=self.选中})
			self.本次需求 = self:学习技能(tp.队伍[1].师门技能[self.选中].等级+1)
			--[[if tp.队伍[1].当前经验 >= self.本次需求.经验 and (tp.金钱 >= self.本次需求.金钱 or tp.储备 >= self.本次需求.金钱) then
				if tp.队伍[1].等级+10 >= tp.队伍[1].师门技能[self.选中].等级 then
					local a = self:升级技能(tp.队伍[1].师门技能[self.选中],self.本次需求)
					tp.队伍[1]:升级技能(tp.队伍[1].师门技能[self.选中])
					for n=1,#tp.队伍[1].师门技能[self.选中].包含技能 do
					 	if tp.队伍[1].师门技能[self.选中].包含技能[n].学会 then
						 	tp.队伍[1].师门技能[self.选中].包含技能[n].等级 = tp.队伍[1].师门技能[self.选中].等级
						end
					end
					self.介绍文本:清空()
					self.介绍文本:添加文本("#N/【介绍】"..tp.队伍[1].师门技能[self.选中].介绍)
					self.介绍文本:添加文本("#N/【等级】"..tp.队伍[1].师门技能[self.选中].等级)
					self.本次需求 = self:学习技能(tp.队伍[1].师门技能[self.选中].等级)
					local fc = self:学习技能(tp.队伍[1].师门技能[self.选中].等级-1)
					self.本次需求.经验 = self.本次需求.经验 - fc.经验
					self.本次需求.金钱 = self.本次需求.金钱 - fc.金钱
				else
					tp.常规提示:打开("#Y/提高人物的等级才能够升级")
				end
			else
				tp.常规提示:打开("#Y/升级条件无法满足呢")
			end --]]
		end
	end
	-- 显示
	self.资源组[1]:显示(self.x,self.y)
	tp.窗口标题背景_:显示(self.x+110,self.y)
	引擎.场景.字体表.标题字体:显示(self.x+self.资源组[1].宽度/2,self.y+3,"技能学习")
	self.资源组[9]:显示(self.x + 14,self.y + 56)
	self.资源组[10]:显示(self.x + 206,self.y + 56)
	self.资源组[11]:显示(self.x + 206,self.y + 196)
	self.资源组[8]:显示(self.x + 13,self.y + 32)
	self.资源组[2]:显示(self.x + 379,self.y + 3)
	self.资源组[3]:显示(self.x + 374,self.y + 61)
	self.资源组[4]:显示(self.x + 374,self.y + 174)
	self.资源组[5]:显示(self.x + 374,self.y + 196)
	self.资源组[6]:显示(self.x + 374,self.y + 335)
	self.资源组[7]:显示(self.x + 154,self.y + 449,true)
    self.资源组[12]:显示(self.x + 77,self.y + 359)
    self.资源组[12]:显示(self.x + 77,self.y + 389)
    self.资源组[12]:显示(self.x + 77,self.y + 419)
    self.资源组[12]:显示(self.x + 286,self.y + 359)
    self.资源组[12]:显示(self.x + 286,self.y + 389)
    self.资源组[12]:显示(self.x + 286,self.y + 419)

	fonts:置颜色(ARGB(255,0,0,0))
	if tp.队伍[1].门派 ~= "无门派" then
		for n=1,7 do
			bw:置坐标(self.x+18,self.y+35+n*40-5)
			if self.选中 ~= n then
				if bw:检查点(x,y) and not tp.消息栏焦点 and self.鼠标 then
					box(self.x+14,self.y+26+n*40,self.x+193,self.y+63+n*40,ARGB(255,201,207,109))
					if 引擎.鼠标弹起(0) then
						self.选中 = n
						self.加入 = 0
						self.包含技能 = {}
						if #tp.队伍[1].师门技能[self.选中].包含技能 > 0 then
							for i=1,#tp.队伍[1].师门技能[self.选中].包含技能 do
								self.包含技能[i] =  zys:载入(tp.队伍[1].师门技能[self.选中].包含技能[i].资源,"网易WDF动画",tp.队伍[1].师门技能[self.选中].包含技能[i].小模型资源)
							end
						end
						self.介绍文本:清空()
						self.介绍文本:添加文本("#N/【介绍】"..tp.队伍[1].师门技能[self.选中].介绍)
						-- self.介绍文本:添加文本("#N/【等级】"..tp.队伍[1].师门技能[self.选中].等级)
						self.本次需求 = self:学习技能(tp.队伍[1].师门技能[self.选中].等级)
					end
					self.焦点 = true
				end
			else
				local ys = ARGB(255,91,90,219)
				if bw:检查点(x,y) then
					ys = ARGB(255,108,110,180)
					self.焦点 = true
				end
				box(self.x+14,self.y+26+n*40,self.x+193,self.y+63+n*40,ys)
			end
			local sm = tp.队伍[1].师门技能[n]
			if self.师门技能[n] == nil then
				self.师门技能[n] = zys:载入(sm.资源,"网易WDF动画",sm.小模型资源)
			end
			local 缩放1x,缩放1y = nil,nil
			if self.师门技能[n] and self.师门技能[n].宽度 > 31 then
	            缩放1x,缩放1y = 等比例缩放公式(31,31,self.师门技能[n].宽度 , self.师门技能[n].高度)
	        end
			-- table.print(self.师门技能[n])
			-- table.print(sm)
			self.师门技能[n]:显示(self.x+18,self.y+30+n*40,缩放1x,缩放1y)
			fonts:显示(self.x + 53,self.y + 37 +n*40,sm.名称)

			fonts:显示(self.x + 130,self.y + 37 +n*40 ,sm.等级.."/180")
		end
		-- print(self.选中)
		-- table.print(tp.队伍[1].师门技能)
		-- 技能信息类
		if self.选中 ~= nil and self.选中 ~= 0 then
			if #tp.队伍[1].师门技能[self.选中].包含技能 > 0 then
				for i=1,4 do
					if self.包含技能[i] ~= nil then
						local 缩放2x,缩放2y = nil,nil
						if self.包含技能[i].宽度 > 24 then
				            缩放2x,缩放2y = 等比例缩放公式(24,24,self.包含技能[i].宽度 , self.包含技能[i].高度)
				        end
						if not tp.队伍[1].师门技能[self.选中].包含技能[i].学会 then
						   self.包含技能[i+self.加入]:灰度级()
				        end
						self.包含技能[i+self.加入]:显示(self.x+212,self.y+i*30+37,缩放2x,缩放2y)

						fonts:显示(self.x+239,self.y+i*30+42,tp.队伍[1].师门技能[self.选中].包含技能[i+self.加入].名称)
						if tp.队伍[1].师门技能[self.选中].包含技能[i+self.加入].被动~=nil then
						   fonts:显示(self.x+262,self.y+i*30+42,"    （被动技能）")
						end
						if self.包含技能[i+self.加入]:是否选中(x,y) and self.鼠标 then
					      tp.提示:技能(x,y,tp.队伍[1].师门技能[self.选中].包含技能[i+self.加入],tp.队伍[1].师门技能[self.选中].包含技能[i+self.加入].剩余冷却回合)
		                end
					end
				end
			end
			fonts:显示(self.x + 85,self.y + 363,tp.队伍[1].当前经验)
			fonts:显示(self.x + 85,self.y + 393,tp.金钱)
			fonts:显示(self.x + 85,self.y + 422,tp.存银)
			if self.本次需求 ~= nil then
				fonts:显示(self.x + 292,self.y + 363,self.本次需求.经验)
				fonts:显示(self.x + 292,self.y + 393,self.本次需求.金钱)
			else
				fonts:显示(self.x + 292,self.y + 362,"未学会技能")
				fonts:显示(self.x + 292,self.y + 385,"未学会技能")
			end
			fonts:显示(self.x + 292,self.y + 422,tp.储备)
		end
	end
	fonts:置颜色(白色)
	fonts:显示(self.x + 15,self.y + 364,"可用经验")
	fonts:显示(self.x + 15,self.y + 393,"可用金钱")
	fonts:显示(self.x + 15,self.y + 422,"存    款")
	fonts:显示(self.x + 192,self.y + 364,"升级所需经验")
	fonts:显示(self.x + 192,self.y + 393,"升级所需金钱")
	fonts:显示(self.x + 192,self.y + 422,"储   备   金")
	-- fonts:置颜色(ARGB(255,0,0,0))
	self.介绍文本:显示(self.x+210,self.y+205)
end

function 场景类_技能学习:检查点(x,y)
	if self.资源组[1]:是否选中(x,y)  then
		 return true
	end
end

function 场景类_技能学习:初始移动(x,y)
	tp.运行时间 = tp.运行时间 + 1
	if not tp.消息栏焦点 then
  		self.窗口时间 = tp.运行时间
 	end
	if not self.焦点 then
		tp.移动窗口 = true
	end
	if  self.鼠标 and  not self.焦点 then
		self.xx = x - self.x
		self.yy = y - self.y
	end
end

function 场景类_技能学习:开始移动(x,y)
	if self.鼠标 then
		self.x = x - self.xx
		self.y = y - self.yy
	end
end

function 场景类_技能学习:升级技能(技能,需求)
	tp.队伍[1].当前经验 = floor(tp.队伍[1].当前经验 - 需求.经验/3)
	if (tp.储备 or 0) >= 需求.金钱 then
		tp.储备 = floor(tp.储备 - 需求.金钱/3)
	else
	    tp.金钱 = floor(tp.金钱 - 需求.金钱/3)
	end
	技能.等级 = 技能.等级 + 1
	return 技能
end

function 场景类_技能学习:学习技能(目标技能等级)
	if 目标技能等级 == 0 or 目标技能等级>=180 then
		目标技能等级= 1
	end
	local 技能消耗={}
  技能消耗[1]={经验=16,金钱=6}
  技能消耗[2]={经验=32,金钱=12}
  技能消耗[3]={经验=52,金钱=19}
  技能消耗[4]={经验=75,金钱=28}
  技能消耗[5]={经验=103,金钱=38}
  技能消耗[6]={经验=136,金钱=51}
  技能消耗[7]={经验=179,金钱=67}
  技能消耗[8]={经验=231,金钱=86}
  技能消耗[9]={经验=295,金钱=110}
  技能消耗[10]={经验=372,金钱=139}
  技能消耗[11]={经验=466,金钱=174}
  技能消耗[12]={经验=578,金钱=216}
  技能消耗[13]={经验=711,金钱=266}
  技能消耗[14]={经验=867,金钱=325}
  技能消耗[15]={经验=1049,金钱=393}
  技能消耗[16]={经验=1260,金钱=472}
  技能消耗[17]={经验=1503,金钱=563}
  技能消耗[18]={经验=1780,金钱=667}
  技能消耗[19]={经验=2096,金钱=786}
  技能消耗[20]={经验=2452,金钱=919}
  技能消耗[21]={经验=2854,金钱=1070}
  技能消耗[22]={经验=3304,金钱=1238}
  技能消耗[23]={经验=3807,金钱=1426}
  技能消耗[24]={经验=4364,金钱=1636}
  技能消耗[25]={经验=4983,金钱=1868}
  技能消耗[26]={经验=5664,金钱=2124}
  技能消耗[27]={经验=6415,金钱=2404}
  技能消耗[28]={经验=7238,金钱=2714}
  技能消耗[29]={经验=8138,金钱=3050}
  技能消耗[30]={经验=9120,金钱=3420}
  技能消耗[31]={经验=10188,金钱=3820}
  技能消耗[32]={经验=11347,金钱=4255}
  技能消耗[33]={经验=12602,金钱=4725}
  技能消耗[34]={经验=13959,金钱=5234}
  技能消耗[35]={经验=15423,金钱=5783}
  技能消耗[36]={经验=16998,金钱=6374}
  技能消耗[37]={经验=18692,金钱=7009}
  技能消耗[38]={经验=20508,金钱=7690}
  技能消耗[39]={经验=22452,金钱=8419}
  技能消耗[40]={经验=24532,金钱=9199}
  技能消耗[41]={经验=26753,金钱=10032}
  技能消耗[42]={经验=29121,金钱=10920}
  技能消耗[43]={经验=31642,金钱=11865}
  技能消耗[44]={经验=34323,金钱=12871}
  技能消耗[45]={经验=37169,金钱=13938}
  技能消耗[46]={经验=40188,金钱=15070}
  技能消耗[47]={经验=43388,金钱=16270}
  技能消耗[48]={经验=46773,金钱=17540}
  技能消耗[49]={经验=50352,金钱=18882}
  技能消耗[50]={经验=54132,金钱=20299}
  技能消耗[51]={经验=58120,金钱=21795}
  技能消耗[52]={经验=62324,金钱=23371}
  技能消耗[53]={经验=66750,金钱=25031}
  技能消耗[54]={经验=71407,金钱=26777}
  技能消耗[55]={经验=76303,金钱=28613}
  技能消耗[56]={经验=81444,金钱=30541}
  技能消耗[57]={经验=86840,金钱=32565}
  技能消耗[58]={经验=92500,金钱=34687}
  技能消耗[59]={经验=98430,金钱=36911}
  技能消耗[60]={经验=104640,金钱=39240}
  技能消耗[61]={经验=111136,金钱=41676}
  技能消耗[62]={经验=117931,金钱=44224}
  技能消耗[63]={经验=125031,金钱=46886}
  技能消耗[64]={经验=132444,金钱=49666}
  技能消耗[65]={经验=140183,金钱=52568}
  技能消耗[66]={经验=148253,金钱=55595}
  技能消耗[67]={经验=156666,金钱=58749}
  技能消耗[68]={经验=165430,金钱=62036}
  技能消耗[69]={经验=174556,金钱=65458}
  技能消耗[70]={经验=184052,金钱=69019}
  技能消耗[71]={经验=193930,金钱=72723}
  技能消耗[72]={经验=204198,金钱=76574}
  技能消耗[73]={经验=214868,金钱=80575}
  技能消耗[74]={经验=225948,金钱=84730}
  技能消耗[75]={经验=237449,金钱=89043}
  技能消耗[76]={经验=249383,金钱=93518}
  技能消耗[77]={经验=261760,金钱=98160}
  技能消耗[78]={经验=274589,金钱=102971}
  技能消耗[79]={经验=287884,金钱=107956}
  技能消耗[80]={经验=301652,金钱=113119}
  技能消耗[81]={经验=315908,金钱=118465}
  技能消耗[82]={经验=330662,金钱=123998}
  技能消耗[83]={经验=345924,金钱=129721}
  技能消耗[84]={经验=361708,金钱=135640}
  技能消耗[85]={经验=378023,金钱=141758}
  技能消耗[86]={经验=394882,金钱=148080}
  技能消耗[87]={经验=412297,金钱=154611}
  技能消耗[88]={经验=430280,金钱=161355}
  技能消耗[89]={经验=448844,金钱=168316}
  技能消耗[90]={经验=468000,金钱=175500}
  技能消耗[91]={经验=487760,金钱=182910}
  技能消耗[92]={经验=508137,金钱=190551}
  技能消耗[93]={经验=529145,金钱=198429}
  技能消耗[94]={经验=550796,金钱=206548}
  技能消耗[95]={经验=573103,金钱=214913}
  技能消耗[96]={经验=596078,金钱=223529}
  技能消耗[97]={经验=619735,金钱=232400}
  技能消耗[98]={经验=644088,金钱=241533}
  技能消耗[99]={经验=669149,金钱=250931}
  技能消耗[100]={经验=694932,金钱=260599}
  技能消耗[101]={经验=721452,金钱=270544}
  技能消耗[102]={经验=748722,金钱=280770}
  技能消耗[103]={经验=776755,金钱=291283}
  技能消耗[104]={经验=805566,金钱=302087}
  技能消耗[105]={经验=835169,金钱=313188}
  技能消耗[106]={经验=865579,金钱=324592}
  技能消耗[107]={经验=896809,金钱=336303}
  技能消耗[108]={经验=928876,金钱=348328}
  技能消耗[109]={经验=961792,金钱=360672}
  技能消耗[110]={经验=995572,金钱=373339}
  技能消耗[111]={经验=1030234,金钱=386337}
  技能消耗[112]={经验=1065190,金钱=399671}
  技能消耗[113]={经验=1102256,金钱=413346}
  技能消耗[114]={经验=1139649,金钱=427368}
  技能消耗[115]={经验=1177983,金钱=441743}
  技能消耗[116]={经验=1217273,金钱=456477}
  技能消耗[117]={经验=1256104,金钱=471576}
  技能消耗[118]={经验=1298787,金钱=487045}
  技能消耗[119]={经验=1341043,金钱=502891}
  技能消耗[120]={经验=1384320,金钱=519120}
  技能消耗[121]={经验=1428632,金钱=535737}
  技能消耗[122]={经验=1473999,金钱=552749}
  技能消耗[123]={经验=1520435,金钱=570163}
  技能消耗[124]={经验=1567957,金钱=587984}
  技能消耗[125]={经验=1616583,金钱=606218}
  技能消耗[126]={经验=1666328,金钱=624873}
  技能消耗[127]={经验=1717211,金钱=643954}
  技能消耗[128]={经验=1769248,金钱=663468}
  技能消耗[129]={经验=1822456,金钱=683421}
  技能消耗[130]={经验=1876852,金钱=703819}
  技能消耗[131]={经验=1932456,金钱=724671}
  技能消耗[132]={经验=1989284,金钱=745981}
  技能消耗[133]={经验=2047353,金钱=767757}
  技能消耗[134]={经验=2106682,金钱=790005}
  技能消耗[135]={经验=2167289,金钱=812733}
  技能消耗[136]={经验=2229192,金钱=835947}
  技能消耗[137]={经验=2292410,金钱=859653}
  技能消耗[138]={经验=2356960,金钱=883860}
  技能消耗[139]={经验=2422861,金钱=908573}
  技能消耗[140]={经验=2490132,金钱=933799}
  技能消耗[141]={经验=2558792,金钱=959547}
  技能消耗[142]={经验=2628860,金钱=985822}
  技能消耗[143]={经验=2700356,金钱=1012633}
  技能消耗[144]={经验=2773296,金钱=1039986}
  技能消耗[145]={经验=2847703,金钱=1067888}
  技能消耗[146]={经验=2923593,金钱=1096347}
  技能消耗[147]={经验=3000989,金钱=1125371}
  技能消耗[148]={经验=3079908,金钱=1154965}
  技能消耗[149]={经验=3160372,金钱=1185139}
  技能消耗[150]={经验=3242400,金钱=1215900}
  技能消耗[151]={经验=6652022,金钱=2494508}
  技能消耗[152]={经验=6822452,金钱=2558419}
  技能消耗[153]={经验=6996132,金钱=2623549}
  技能消耗[154]={经验=7173104,金钱=2689914}
  技能消耗[155]={经验=7353406,金钱=2757527}
  技能消耗[156]={经验=11305620,金钱=4239607}
  技能消耗[157]={经验=11586254,金钱=4344845}
  技能消耗[158]={经验=11872072,金钱=4452027}
  技能消耗[159]={经验=12163140,金钱=4561177}
  技能消耗[160]={经验=12459518,金钱=4672319}
  技能消耗[161]={经验=15033471,金钱=450041 }
  技能消耗[162]={经验=15315219,金钱=4594563}
  技能消耗[163]={经验=15600468,金钱=4680138}
  技能消耗[164]={经验=15889236,金钱=4766769}
  技能消耗[165]={经验=16181550,金钱=4854465}
  技能消耗[166]={经验=16477425,金钱=4943226}
  技能消耗[167]={经验=16776885,金钱=5033064}
  技能消耗[168]={经验=17079954,金钱=5123985}
  技能消耗[169]={经验=17386650,金钱=5215995}
  技能消耗[170]={经验=17697000,金钱=5309100}
  技能消耗[171]={经验=24014692,金钱=7204407}
  技能消耗[172]={经验=24438308,金钱=7331490}
  技能消耗[173]={经验=24866880,金钱=7460064}
  技能消耗[174]={经验=25300432,金钱=7590129}
  技能消耗[175]={经验=25739000,金钱=7721700}
  技能消耗[176]={经验=32728255,金钱=9818475}
  技能消耗[177]={经验=33289095,金钱=9986727}
  技能消耗[178]={经验=33856310,金钱=10156893}
  技能消耗[179]={经验=34492930,金钱=10328979}
  技能消耗[180]={经验=40842000,金钱=12252600}
	return {经验=技能消耗[目标技能等级].经验,金钱=技能消耗[目标技能等级].金钱}
end

return 场景类_技能学习