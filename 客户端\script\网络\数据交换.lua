--======================================================================--

--======================================================================--
封包加密=require("Script/网络/封包加密")
--回调:置收发BUF(封包加密(1024*1024*10), 封包加密(1024*1024*10))
local 数据记录=""
function 回调:初始化()
	self.连接账号 = ""
	self.连接密码 = ""
	self.连接结果 = false
	self.时间 = 0
end

function 回调:数据到达2(序号,内容,时间)
	客户端:数据到达(序号)
end
function 回调:发送数据(序号,内容,数组转换)
	if 内容==nil then
		内容="1"
	end

	if 数组转换~=nil and 序号 ~= 2 and 序号 ~= 3 and 序号 ~= 4 then
		self.时间 = os.time()
		累计发送次数=累计发送次数+1
		内容.数据验证=取MD5("vvxxzQQ79550111$*yyds"..累计发送次数)--累计发送次数--累计发送次数--
		内容.时间 = self.时间
		内容.number=真实id or 当前数字id
		内容=table.tostring(内容)
	end

	-- if 数组转换~=nil then
	-- 	self.时间 = os.time()
	-- 	内容.数据验证=取MD5(self.时间+self.累计发送次数)
	-- 	内容.时间 = self.时间
	-- 	内容.number=真实id or 当前数字id
	-- 	内容=table.tostring(内容)
	-- end


	--print(序号,内容)
	self.组合数据=序号..fgf..内容
	-- if not self.组合数据.时间 then
	-- 	print("未检测到时间")
	-- -- 	return
	-- end
	--if 数据记录==self.组合数据..时间 then return  end
	--self:发送(jm(self.组合数据))
	self:发送(序号,self.组合数据)
	-- 数据记录=self.组合数据..时间
	-- print(self.组合数据)
	内容=nil
end
function 回调:数据到达(s1,s2)

	--内容=jm1(内容)
	if s1==0 or s2=="" then return  end
	local 数据 = {}
	数据 = table.loadstring(s2)
	--table.print(数据)
	-- if s1==5504 then
	--     print(s1,s2)
	-- end

	数据.序号=数据.序号+0
	-- if 调试模式 and 数据.序号~=99 then
		-- print(内容)
		--  print(table.loadstring)
	-- end
	--线程:发送(1, 数据,内容)
	if 数据.序号<=1000 then
		self:系统处理(数据.序号,数据.内容,内容)
	elseif 数据.序号>1000 and 数据.序号<=1500 then
		self:地图处理(数据.序号,数据.内容)
	elseif 数据.序号>1500 and 数据.序号<=2000 then
		self:对话处理(数据.序号,数据.内容)
	elseif 数据.序号>3500 and 数据.序号<=4000 then
		self:道具处理(数据.序号,数据.内容)
	elseif 数据.序号>4000 and 数据.序号<=4500 then
		self:队伍处理(数据.序号,数据.内容)
	elseif 数据.序号>5500 and 数据.序号<=6000 then
		self:战斗处理(数据.序号,数据.内容)
	elseif 数据.序号>6100 and 数据.序号<=6200 then
		self:商会处理(数据.序号,数据.内容)
	elseif 数据.序号>6200 and 数据.序号<=6300 then
		self:神器处理(数据.序号,数据.内容)
	elseif 数据.序号>6300 and 数据.序号<=6400 then
		self:首席处理(数据.序号,数据.内容)
	-- elseif 数据.序号>6400 and 数据.序号<=6500 then
	-- 	self:账号管理(数据.序号,数据.内容)
	elseif 数据.序号>6500 and 数据.序号<=6600 then
		self:夫妻处理(数据.序号,数据.内容)
	-- elseif 数据.序号>6600 and 数据.序号<=6700 then
	-- 	self:翻一翻(数据.序号,数据.内容)

	elseif 数据.序号 == 20063 then
	     tp.窗口.一键附魔:打开(数据.内容)
	elseif 数据.序号 == 20064 then
	     tp.窗口.一键附魔:刷新(数据.内容)

   	elseif 数据.序号 == 20074 then

	        if not tp.窗口.内充系统.可视 then
	            tp.窗口.内充系统:打开(数据.内容)
	        else
	            tp.窗口.内充系统:刷新(数据.内容)
	        end
	 elseif 数据.序号 == 20075 then
	            tp.窗口.内充系统:访问充值地址(数据.内容)

	elseif 数据.序号==420001 then
		if tp.窗口.阿斌_助战系统.可视 == false then
			tp.窗口.阿斌_助战系统:打开(数据.内容)
		end

	elseif 数据.序号==420002 then
			tp.窗口.阿斌_助战系统:刷新助战(数据.内容)
			if tp.窗口.共享仓库类.可视 then
					tp.窗口.共享仓库类:打开()
			end
	elseif 数据.序号==430003 then
	      tp.窗口.阿斌_助战系统:刷新指定助战技能(数据.内容)
	elseif 数据.序号==430004 then
	      tp.窗口.阿斌_助战系统:刷新指定助战队伍信息(数据.内容)
	end
	数据=nil
	s1=nil
	s2=nil
end

-- function 回调:翻一翻(序号,内容)
-- 	if 序号  == 6601 then
-- 		引擎.场景.窗口.商城类:翻一翻开始(内容)
-- 		引擎.场景.窗口.商城类.分类="翻一翻"
-- 		引擎.场景.窗口.商城类:初始系统(dt,x,y,2)
-- 	elseif 序号  == 6602 then
-- 		引擎.场景.窗口.商城类:翻一翻数据刷新(内容)
-- 	elseif 序号  == 6603 then
-- 		引擎.场景.窗口.商城类:翻一翻数据更新(内容)
-- 	elseif 序号  == 6604 then
-- 		引擎.场景.窗口.商城类:翻一翻放弃(内容)
-- 	end
-- end

function 回调:夫妻处理(序号,内容)
	-- print(序号)
	if 序号 == 6501 then
		tp.窗口.文本栏:载入("玩家#Y/"..内容.名称.."#W/向你发起结婚请求？","结婚请求",true)
	elseif 序号  == 6555 then
		if tp.窗口.彩虹争霸赛.可视 == false then
	       tp.窗口.彩虹争霸赛:打开()
	    end
	    tp.窗口.彩虹争霸赛:数据处理(内容)
	elseif 序号 == 6556 then --饰品染色
		for n=1,#引擎.场景.队伍[1].宝宝列表 do --刷新指定列表
			if 内容.认证码== 引擎.场景.队伍[1].宝宝列表[n].认证码 then
				引擎.场景.队伍[1].宝宝列表[n]=内容
				break
			end
		end
		if tp.窗口.召唤兽属性栏.可视 then
			tp.窗口.召唤兽属性栏:置形象()
		end
	elseif 序号 == 6557 then --打字机
		if 引擎.场景.窗口.打字机.可视==false then
			引擎.场景.窗口.打字机:打开(内容.文本,内容.类型,内容.字体,内容.音乐,内容.背景,内容.横排显示,内容.动画调用)
			return
		end
	elseif 序号 == 6558 then --动画
		-- table.print(内容)
		local 人物组,事件组 = 引擎.场景:获取任务事件(内容.调用[1],内容.调用[2])
		if 人物组[1] ~= nil and 事件组[1] ~= nil then
		    tp.第二场景:载入显示(人物组,事件组,2,13,内容.调用)
		end
	elseif 序号 == 6559 then --饰品染色
		if tp.窗口.潜能果.可视 == false then
			tp.窗口.潜能果:打开(内容)
		else
			tp.窗口.潜能果:更新数据(内容)
		end
	elseif 序号 == 6560 then --黑天
		tp.夜间显示 = 内容
	elseif 序号 == 6566 then
		if tp.窗口.游戏设置.可视 == false then
			tp.窗口.游戏设置:打开(内容)
		else
			-- tp.窗口.游戏设置:更新数据(内容)
		end
    elseif 序号 == 6567 then
    	tp.窗口.坐骑修炼:打开(内容.认证码,内容.类型) --宝宝喂忠诚
    elseif 序号 == 6567.1 then
    	tp.窗口.召唤兽驯养:打开()
    elseif 序号 == 6568 then
    	if 引擎.场景.窗口.召唤兽属性栏.可视 then
		    引擎.场景.窗口.召唤兽属性栏:刷新忠诚(内容)
		end
	elseif 序号 == 6569 then
    	if 引擎.场景.窗口.坐骑属性栏.可视 then
		    引擎.场景.窗口.坐骑属性栏:刷新好感度(内容)
		end
		if 引擎.场景.窗口.道具行囊.可视 then
			引擎.场景.窗口.道具行囊:刷新好感度(内容)
		end
	elseif 序号 == 6570 then
		帮战开关=true
	elseif 序号 == 6571 then
		if tp.窗口.帮战建设.可视==false then
			tp.窗口.帮战建设:打开(内容)
		end
	elseif 序号 == 6572 then
		tp.窗口.保护时间:打开()
	elseif 序号==6573 then
		tp.场景.人物:夺旗判断(内容.逻辑)
	elseif 序号==6574 then
		if 引擎.场景.场景.玩家[内容.id] ~= nil then
			引擎.场景.场景.玩家[内容.id]:夺旗判断(内容.逻辑)
		end
	elseif 序号==6575 then
		if tp.窗口.长安保卫战.可视==false then
			tp.窗口.长安保卫战:打开(内容)
		end
	elseif 序号==6580 then
		tp.窗口.剑会天下.右键关闭 = 1
	    tp.窗口.剑会天下.匹配 = true
	    tp.窗口.剑会匹配:打开(内容)
    elseif 序号==6581 then --关闭匹配界面
		tp.窗口.剑会天下.右键关闭 = nil
		tp.窗口.剑会天下.匹配 = false
		if tp.窗口.剑会匹配.可视 then
			tp.窗口.剑会匹配:打开()
		end
	elseif 序号==6583 then
		if tp.窗口.剑会天下.可视==false then
			tp.窗口.剑会天下:打开(内容)
		end
	elseif 序号==6584 then --刷新面板积分数据
		tp.窗口.剑会天下.右键关闭 = nil
		tp.窗口.剑会天下.匹配 = false
	    if tp.窗口.剑会天下.可视 then
	        tp.窗口.剑会天下:加载数据(内容)
	    end
    elseif 序号==6586 then
    	if tp.窗口.剑会排行榜.可视==false then
			tp.窗口.剑会排行榜:打开(内容)
		end
	end
	--数据.序号>6500 and 数据.序号<=6600
	内容=nil
end

function 回调:首席处理(序号,内容)
	if 序号 == 6301 then
		引擎.场景.窗口.首席投票榜:打开(内容)
	elseif 序号 == 6302 then
		Qjjmc=内容
	elseif 序号 == 6304 then
		if tp.窗口.自选系统.可视 == false then
			tp.窗口.自选系统:打开(内容.名称, 内容.附加)
		end

	end
end

-- function 回调:账号管理(序号,内容)
-- 	if 序号  == 6401 then
-- 		-- 引擎.场景.窗口.账号管理:打开(内容)
-- 	elseif 序号  == 6402 then
-- 		-- 引擎.场景.窗口.管理界面:打开()
-- 	elseif 序号  == 6403 then
-- 		-- 引擎.场景.窗口.管理界面:刷新提现(内容)
-- 	end
-- end

function 回调:神器处理(序号,内容) --6200 and 数据.序号<=6300
	if 序号 == 6202 then
		if tp.窗口.神器获得.可视==false then
			tp.窗口.神器获得:打开(内容.mp)
			if tp.窗口.成就提示.可视==false then
	    	    tp.窗口.成就提示:打开({头像="通用",标题="上古神器",说明="获得对应门派的神器"})
	    	else
	    		tp.窗口.成就提示:新增内容({头像="通用",标题="上古神器",说明="获得对应门派的神器"})
	    	end
		end
	elseif 序号==6205 then
	    if tp.窗口.修复神器.可视 then
	        tp.窗口.修复神器:放弃镶嵌(内容)
	    end
	elseif 序号 == 6203 then
		if tp.窗口.法宝.可视 then
			tp.窗口.法宝:神器刷新(内容)
		end
	elseif 序号 == 6203.1 then
		if tp.窗口.法宝.可视 then
			tp.窗口.法宝:打开()
			--tp.窗口.法宝:神器技能刷新(内容)
		end
	elseif 序号==6204 then
		if tp.窗口.修复神器.可视 then
			tp.窗口.修复神器:解锁区域(内容)
		end
	elseif 序号 == 6208 then
		if tp.窗口.神器查看.可视 == false then
			tp.窗口.神器查看:打开(内容)
		end
	elseif 序号 == 6210 then
		if tp.窗口.修复神器.可视==false then
			tp.窗口.修复神器:打开(内容)
		end
	elseif 序号==6215 then
		if tp.窗口.修复神器.可视 then
			tp.窗口.修复神器:激活插槽更新(内容)
		end
	elseif 序号==6216 then
		if tp.窗口.修复神器.可视 then
			tp.窗口.修复神器:更新(内容)
		end
	elseif 序号==6217 then
	    if tp.窗口.修复神器.可视 then
			tp.窗口.修复神器:镶嵌完成(内容)
	    end
	elseif 序号==6218 then
	    if tp.窗口.合成灵犀玉.可视 == false then
			tp.窗口.合成灵犀玉:打开(内容)
		end
	elseif 序号==6219 then
		if tp.窗口.合成灵犀玉.可视 then
		    tp.窗口.合成灵犀玉:合成开始(内容)
		end
	elseif 序号==6201 then
		if tp.窗口.神器更换五行.可视 == false then
			tp.窗口.神器更换五行:打开(内容)
		end
	elseif 序号==6207 then
	    if tp.窗口.神器更换五行.可视 then
	      tp.窗口.神器更换五行:新五行(内容)
	    end
    elseif 序号==6209 then
		if tp.窗口.神器更换五行.可视 then
			tp.窗口.神器更换五行:更新(内容)
		end
	elseif 序号==6211 then
	    if tp.窗口.法宝.可视 then
	       tp.窗口.法宝:佩戴神器(内容)
	    end
    elseif 序号==6212 then
    	tp.队伍[1].门派=内容
    	tp.窗口.法宝:重新载入()
    	tp.窗口.神器查看:重新载入()
    	tp.窗口.修复神器:重新载入()

	end
	内容=nil
end

function 回调:商会处理(序号,内容)
	if 序号 == 6101 then

	elseif 序号 == 6102 then
		引擎.场景.窗口.商会物品上架:刷新仓库(内容.道具,内容.当前页数)
	elseif 序号 == 6103 then
		引擎.场景.窗口.商会物品上架:刷新道具()
	elseif 序号 == 6104 then
		 引擎.场景.窗口.商会物品上架.店名 = 内容
	elseif 序号 == 6105 then
		 引擎.场景.窗口.商会物品上架.资金=内容
	elseif 序号 == 6106 then
		 引擎.场景.窗口.商会列表:打开(内容)
	elseif 序号 == 6107 then
		 引擎.场景.窗口.商会物品界面:打开(内容)
	elseif 序号 == 6108 then
		 引擎.场景.窗口.商会物品界面:刷新道具(内容.道具,内容.当前页数)
	elseif 序号 == 6109 then
		引擎.场景.窗口.商会宠物上架:打开(内容)
	elseif 序号 == 6110 then
		引擎.场景.窗口.商会宠物上架:刷新商会宠物(内容)
	elseif 序号 == 6111 then
		引擎.场景.窗口.商会宠物上架:刷新上架宠物(内容.编号)
	elseif 序号 == 6112 then
		引擎.场景.窗口.商会宠物上架:刷新下架宠物(内容.编号)
	elseif 序号 == 6113 then
		引擎.场景.窗口.商会宠物界面:打开(内容)
	elseif 序号 == 6114 then
		引擎.场景.窗口.商会宠物界面:刷新商会宠物(内容)
	elseif 序号 == 6115 then
		引擎.场景.窗口.商会宠物界面:刷新购买宠物(内容.编号)
	elseif 序号 == 6116 then
		引擎.场景.窗口.生死劫:打开()
	end
	内容=nil
end
function 回调:战斗处理(序号,内容)
	if 序号==5501 then
		--local  c1 = collectgarbage("count")
		--print("进入战斗前,Lua的内存为",c1)
		-- table.print(内容)
		-- tp.战斗窗口_ = {}
		if not 低配模式 then
		    战斗类:进入战斗过度()
		end
		引擎.场景.战斗中=true
		战斗类.观战开关 = false
		if 内容.观战 ~= nil then
			引擎.场景.观战中=true
			战斗类.观战开关 = true
		end
		战斗类.队伍id=内容.id
		战斗类.单位总数=内容.总数
		战斗类.单挑模式=内容.单挑模式
		战斗类.PK战斗=内容.PK战斗

		引擎.场景.音乐:置音量(0)
		tp:战斗音乐类(wdf配置.."/Audio/战斗"..内容.音乐..".mp3")
		__fsyzzd=true
		--test1()
	-- elseif 序号==5502 then
	-- 	战斗类:加载单位(内容)
	elseif 序号==5503 then
		collectgarbage("collect")
		战斗类.排列命令={}
		战斗类.角色操作序号=0
		战斗类.宠物操作序号=0
		战斗类:设置命令回合(内容)
	elseif 序号==5503.5 then
                         战斗类.排列命令[#战斗类.排列命令+1]=table.copy(内容)
	elseif 序号==5504 then
		 --table.print(内容)
		战斗类.排列命令={}
		战斗类:设置战斗流程(内容)
		--test1()
	elseif 序号==5505.5 then
		--table.print(内容)
             	          战斗类.自动数据=内容
             	          战斗类.自动栏:刷新数据()
	elseif 序号==5505 then
		if 引擎.场景.战斗中 then
			 -- 战斗类:退出战斗过度()
			引擎.场景.战斗中=false
			引擎.场景.观战中=false
			战斗类:释放()
			tp.战斗音乐:销毁()
			触碰延时=时间
			引擎.场景.音乐:置音量(tp.音乐音量)
			ljcs = ljcs+1
			战斗类.战斗单位[ljcs]={}
			test1()
			__fsyzzd=false
			-- print(#tp.场景.假人)
			-- for i=1,#tp.场景.假人 do
			-- 	tp.场景.假人[i].显示缓冲=i
			-- 	-- print(i)
			-- end
			collectgarbage("collect")
		end
	elseif 序号==5506 then
		if 内容[1].气血 ~= nil then
			引擎.场景.队伍[1].气血=内容[1].气血
		end
		if 内容[1].最大气血 ~= nil then
			引擎.场景.队伍[1].最大气血=内容[1].最大气血
		end
		if 内容[1].气血上限 ~= nil then
			引擎.场景.队伍[1].气血上限=内容[1].气血上限
		end
		引擎.场景.队伍[1].魔法=内容[1].魔法
		引擎.场景.队伍[1].最大魔法=内容[1].最大魔法
		引擎.场景.队伍[1].愤怒=内容[1].愤怒
		if 内容[2]~=nil then
			--引擎.场景.队伍[1].参战宝宝.气血=内容[2].气血
			--引擎.场景.队伍[1].参战宝宝.最大气血=内容[2].最大气血
			引擎.场景.队伍[1].参战宝宝.魔法=内容[2].魔法
			引擎.场景.队伍[1].参战宝宝.最大魔法=内容[2].最大魔法
		end
	elseif 序号==5507 then
		战斗类:取消状态(内容)
	elseif 序号==5508 then
		战斗类:鬼魂复活(内容)
	elseif 序号==5509 then
		刷新道具行囊(内容)
		战斗类.战斗指令:设置道具()
	elseif 序号==5510 then
		引擎.场景.队伍[1].宝宝列表=内容[1]
		战斗类.战斗指令.召唤界面:打开(引擎.场景.队伍[1],内容[2])
	elseif 序号==5511 then  --开启自动战斗后进入等待
		战斗类.进程="等待"
	elseif 序号==5512 then --战斗发言
		local 编号=内容.id
		if 战斗类.战斗单位[ljcs][编号]==nil then return  end
		战斗类.战斗单位[ljcs][编号].喊话:写入(战斗类.战斗单位[ljcs][编号],战斗类.战斗单位[ljcs][编号].动画.动画,内容.文本,nil,内容.特效,内容.队伍)
	elseif 序号==5512.1 then --超级技能发言
		local 编号=内容.id
		if 战斗类.战斗单位[ljcs][编号]==nil then return  end
		战斗类.战斗单位[ljcs][编号].喊话:写入(战斗类.战斗单位[ljcs][编号],战斗类.战斗单位[ljcs][编号].动画.动画,内容.文本,nil,内容.特效,内容.队伍,内容.超级技能)
	elseif 序号==5513 then
		-- table.print(内容)
		for n=1,#内容 do
		 	战斗类.战斗单位[ljcs][内容[n].id].数据.自动指令=内容[n].自动
		end
	elseif 序号==5514 then
		for n=1,#内容 do
		 	战斗类.战斗单位[ljcs][内容[n].id].数据.自动战斗=内容[n].自动
		end
	elseif 序号==5515 then
		if 内容==nil then
			return
		end
		--table.print(内容)
		for n=1,#内容 do
		    战斗类:加载单位(内容[n])
		end
	elseif 序号==5516 then
		for i=1,#内容 do
			if 战斗类.战斗单位[ljcs][i] ~= nil then
				战斗类.战斗单位[ljcs][i].气血 = 内容[i].气血
			end
		end
	elseif 序号==5517 then
		--table.print(内容)
		战斗类:加载生成怪物(内容)
	elseif 序号==5518 then
		--table.print(内容)
		战斗类:结束流程(内容)
		--test1()
	elseif 序号==5519 then
		战斗类.战斗指令:设置灵宝(内容)
	-- elseif 序号==5521 then
		-- 战斗类.战斗单位[ljcs][内容.id].数据.队长控制=内容.开关
	elseif 序号==5523 then
		刷新道具行囊(内容)
		战斗类.战斗指令:设置道具()
	elseif 序号==5524 then
		战斗类.战斗单位[ljcs][内容.id].追加法术=内容.追加法术
	elseif 序号==5525 then
		战斗类.战斗单位[ljcs][内容.id].如意神通=内容.如意神通
		-- table.print(战斗类.战斗单位[ljcs][内容.id].如意神通)
	-- elseif 序号==5526 then
	--     战斗类:结尾动画流程(内容)
	elseif 序号==5555 then

		for k,v in pairs(内容) do
			local x = 引擎.取技能展示(v)
			local mx = tp.资源:载入(x[6],"网易WDF动画",x[7])
			战斗类.技能展示[k]={名称=v,小模型 = mx,偏移=(x[10] or {6,8})}
		end
	elseif 序号==5555.5 then
             	战斗类.战斗指令.道具栏:打开(内容)
	end
	内容=nil
end

function 回调:队伍处理(序号,内容)
	-- 4001 更改队伍图标
	-- 4002 创建队伍界面
	-- 4003 创建申请界面
	-- 4004 刷新队伍信息
	-- 4005 刷新队伍头像
	-- 4006 添加自身队伍图标
	-- 4007 添加玩家队伍图标
	-- 4008 删除自身队伍图标
	-- 4009 删除玩家队伍图标
	-- 4010 获取申请列表信息
	-- 4011 刷新申请列表
	-- 4012 清除队伍信息
	if 序号==4001 then
		引擎.场景.鼠标.置鼠标("组队")
	elseif 序号==4002 then
		引擎.场景.队伍数据=内容
		引擎.场景.窗口.队伍栏:打开()
		--引擎.场景.窗口.底图框:刷新队伍头像(内容)
	elseif 序号==4004 then
		引擎.场景.队伍数据=内容
		申请队伍=false
		if 引擎.场景.窗口.队伍栏.可视 then
			引擎.场景.窗口.队伍栏:刷新人物()
		end
	elseif 序号==4006 then
		引擎.场景.场景.人物.队长开关=true
	elseif 序号==4007 then
		if 内容.逻辑==nil then
			内容.逻辑=false
		end
		引擎.场景.场景.玩家[内容.id].队长开关=内容.逻辑
	elseif 序号==4008 then
		引擎.场景.场景.人物.队长开关=false
	elseif 序号==4009 then --飞行坐骑
		if 内容.逻辑==nil then
			内容.逻辑=false
		end
		if 引擎.场景.场景 and 引擎.场景.场景.玩家 and 引擎.场景.场景.玩家[内容.id] then
		    引擎.场景.场景.玩家[内容.id].飞行中=内容.逻辑
		end
	elseif 序号==4010 then
		引擎.场景.窗口.队伍申请表:打开(内容)
	elseif 序号==4011 then
		引擎.场景.窗口.队伍申请表:刷新(内容)
		申请队伍=true
	elseif 序号==4012 then
		引擎.场景.队伍数据={}
		if 引擎.场景.窗口.队伍栏.可视 then
			引擎.场景.窗口.队伍栏:打开()
		end
		if 引擎.场景.窗口.队伍申请表.可视 then
			引擎.场景.窗口.队伍申请表:打开()
		end
	elseif 序号==4013 then
		--引擎.场景.窗口.队伍申请表:刷新(内容)
		引擎.场景.窗口.队伍阵型栏:打开(内容)
	elseif 序号==4014 then
		if 内容.逻辑==nil then
			内容.逻辑=false
		end
		if 引擎.场景.场景.玩家[内容.id] ~= nil then
			引擎.场景.场景.玩家[内容.id].战斗开关=内容.逻辑
		end
	elseif 序号==4015 then
		引擎.场景.窗口.好友查看:打开(内容,nil,nil,true)
	elseif 序号==4016 then
		-- table.print(内容)
		if 内容.逻辑==nil then
			内容.逻辑="无"
		end
		if 引擎.场景.场景.玩家[内容.id] ~= nil then
			引擎.场景.场景.玩家[内容.id]:更改队标(内容.逻辑)
		end
	elseif 序号==4018 then
		引擎.场景.窗口.邀请组队:打开(内容)
    elseif 序号==4019 then
    	if 内容.逻辑==nil then
			内容.逻辑="无"
		end
    	引擎.场景.场景.人物:更改队标(内容.逻辑)
    elseif 序号==4020 then --飞行坐骑
		if 内容.逻辑==nil then
			内容.逻辑=false
		end
		引擎.场景.飞行=内容.逻辑

	-- elseif 序号==4017 then --刷新玩家队伍
	-- 	table.print(内容)
	-- 	if 引擎.场景.场景.玩家[内容.id] ~= nil then
	-- 		引擎.场景.场景.玩家[内容.id].队伍=内容.逻辑
	-- 	end
	end
	内容=nil

end

function 回调:道具处理(序号,内容,原内容)
	-- print(序号)
	-- print(内容)
	-- table.print(内容)
	if 序号==3501 then
		 tp.金钱=内容.银子
		    tp.储备=内容.储备
		    tp.存银=内容.存银
		    内容.银子=nil
		    内容.储备=nil
		    内容.存银=nil
		    tp.道具列表=内容.道具
		    if tp.窗口.道具行囊.可视==false then
		      tp.窗口.道具行囊:打开()
		    else
		      tp.窗口.道具行囊:刷新道具资源("道具")
		      tp.窗口.道具行囊.点击类型="道具"
		    end
		-- local 人物组,事件组 = 引擎.场景:获取任务事件(100,3)
		-- if 人物组[1] ~= nil and 事件组[1] ~= nil then
		-- 	tp.第二场景:载入显示(人物组,事件组,2,13)
		--     -- tp.第二场景:载入显示(人物组,事件组,2,13,内容.调用)
		-- end
		--引擎.场景.窗口.对话栏:文本(内容.模型,内容.名称,内容.对话,内容.选项)
		刷新道具行囊(内容)
		--table.print(引擎.场景.道具列表)
		-- local 人物组,事件组 = 引擎.场景:获取任务事件(1,12)
		-- 引擎.场景.第二场景:载入显示(人物组,事件组,1,12)
		if 引擎.场景.窗口.道具行囊.可视==false then
			引擎.场景.窗口.道具行囊:打开()
		else
			引擎.场景.窗口.道具行囊.点击类型="道具"
		end
	elseif 序号==3502 then
		tp.金钱=内容.银子
		    tp.储备=内容.储备
		    tp.存银=内容.存银
		    内容.银子=nil
		    内容.储备=nil
		    内容.存银=nil
		    tp.道具列表=内容.道具
	     if   tp.窗口.道具行囊.可视==false then
		      tp.窗口.道具行囊:打开()
		    else
		      tp.窗口.道具行囊:刷新道具资源("行囊")
		      tp.窗口.道具行囊.点击类型="行囊"
		end

		刷新道具行囊(内容,"行囊")
		if 引擎.场景.窗口.道具行囊.可视==false then
			引擎.场景.窗口.道具行囊:打开()
		else
			引擎.场景.窗口.道具行囊.点击类型="行囊"
		end




	elseif 序号==3503 then --更新装备
		引擎.场景.队伍[1].装备=内容
		引擎.场景.窗口.道具行囊:刷新装备()
	elseif 序号==3504 then --佩戴装备
		引擎.场景.场景.人物:穿戴装备()
	elseif 序号==3505 then --佩戴装备
		引擎.场景.场景.人物:卸下装备()
	elseif 序号==3506 then --更新装备
		引擎.场景.队伍[1].灵饰=内容
		if 引擎.场景.窗口.道具行囊.可视 then
			引擎.场景.窗口.道具行囊:刷新灵饰()
		end
	elseif 序号==3507 then
		刷新道具行囊(内容.道具)
		引擎.场景.窗口.给予:打开(nil,内容.名称,内容.等级,内容.类型)
	elseif 序号==3508 then
		引擎.场景.窗口.交易.锁定状态=true
		引擎.场景.窗口.交易.选择:置打勾框(true)
	elseif 序号==3509 then
		引擎.场景.窗口.交易.选择1:置打勾框(true)
	elseif 序号==3510 then
	 引擎.场景.窗口.交易:设置对方数据(内容)
	elseif 序号==3511 then
		引擎.场景.窗口.交易:打开()
	elseif 序号==3512 then
		引擎.场景.队伍[1].宝宝列表=内容
		if 引擎.场景.窗口.交易.可视 then
		 引擎.场景.窗口.交易.bb={}
		end
	elseif 序号==3513 then
		刷新道具行囊(内容)
	 elseif 序号==3514 then
		引擎.场景.窗口.交易:打开(内容.名称,内容.等级)
 	elseif 序号==3515 then
		tp.窗口.摊位出售:打开(内容.名称,内容.摊主名称,内容.id,内容.物品,内容.bb,内容.制造)
	    摊位名称=内容.名称
 	elseif 序号==3516 then --更改自己摊位名称
		 摊位名称=内容
 	elseif 序号==3517 then
		if tp.窗口.摊位出售.可视 then
			tp.窗口.摊位出售:刷新(内容.名称,内容.摊主名称,内容.id,内容.物品,内容.bb,内容.制造)
		end
 	elseif 序号==3518 then
		 摊位名称=nil
		 if 引擎.场景.窗口.摊位出售.可视 then 引擎.场景.窗口.摊位出售:打开() end
 	elseif 序号==3519 then
	 	引擎.场景.场景.玩家[内容.id].摊位名称=内容.名称
	elseif 序号==3520 then
		-- print(内容)
	 	引擎.场景.金钱=内容+0
 	elseif 序号==3521 then
		if tp.窗口.摊位购买.可视==false then
	      tp.窗口.摊位购买:打开(内容.名称,内容.摊主名称,内容.id,内容.物品,内容.bb,内容.制造)
	    end
	elseif 序号==3522 then
		tp.窗口.摊位购买:刷新(内容.名称,内容.摊主名称,内容.id,内容.物品,内容.bb,内容.制造)
	elseif 序号==3523 then
		-- table.print(内容)
		引擎.场景.窗口.仓库类:打开(内容.道具.道具,内容.道具仓库总数,内容.召唤兽仓库总数,内容.扩展背包数)
	elseif 序号==3523.1 then
		-- table.print(内容)
		引擎.场景.窗口.共享仓库类:打开(内容.道具.道具,内容.道具仓库总数,内容.召唤兽仓库总数,内容.扩展背包数)
	 elseif 序号==3524 then
		if tp.窗口.仓库类.仓库类型=="道具" then
			tp.窗口.仓库类:刷新仓库(内容.道具.道具,内容.页数)
			--引擎.场景.窗口.仓库类:刷新道具()
		elseif tp.窗口.仓库类.仓库类型=="召唤兽" then
			tp.窗口.仓库类:刷新仓库(内容.召唤兽仓库数据,内容.页数)
		end
	elseif 序号==3524.1 then
		if tp.窗口.共享仓库类.仓库类型=="道具" then
			tp.窗口.共享仓库类:刷新仓库(内容.道具.道具,内容.页数)
			--引擎.场景.窗口.仓库类:刷新道具()
		elseif tp.窗口.共享仓库类.仓库类型=="召唤兽" then
			tp.窗口.共享仓库类:刷新仓库(内容.召唤兽仓库数据,内容.页数)
		end
	-- elseif 序号==3525 then
	-- 	引擎.场景.窗口.仓库类:刷新道具()
	elseif 序号==3526 then
		-- table.print(内容)
		tp.窗口.仓库类:刷新召唤兽仓库总数(内容.召唤兽仓库总数,内容.召唤兽仓库数据)
	elseif 序号==3526.1 then
		tp.窗口.共享仓库类:刷新召唤兽仓库总数(内容.召唤兽仓库总数,内容.召唤兽仓库数据)
	elseif 序号==3534 then
		引擎.场景.队伍[1].宝宝列表=内容.宝宝列表
	elseif 序号==3535 then --新增
		if tp.窗口.仓库类.可视 then
			tp.窗口.仓库类:刷新道具(内容.类型,内容.数据)
		end
	elseif 序号==3535.1 then --新增
		if tp.窗口.共享仓库类.可视 then
			tp.窗口.共享仓库类:刷新道具(内容.类型,内容.数据)
		end
	elseif 序号==3527 then
		引擎.场景.法宝列表=内容.法宝
		引擎.场景.法宝佩戴=内容.佩戴
		引擎.场景.灵宝列表=内容.灵宝
		引擎.场景.灵宝佩戴=内容.灵宝佩戴
		-- table.print(内容)
		引擎.场景.窗口.法宝:刷新(内容.是否有神器,内容.是否佩戴神器,内容.神器格子)
 	elseif 序号==3528 then
  		--table.print(内容)
	 	引擎.场景.窗口.法宝:更新法宝经验(内容)
	 elseif 序号==3528.1 then
  		--table.print(内容)
	 	引擎.场景.窗口.法宝:更新灵宝经验(内容)
 	elseif 序号==3529 then
	 	引擎.场景.窗口.合成旗:打开(内容)
	elseif 序号==3530 then
	 	刷新道具行囊(内容.道具)
		引擎.场景.窗口.给予NPC:打开(nil,内容.名称,内容.类型)
	elseif 序号==3530.1 then
	 	tp.窗口.道具行囊:清除抓取道具()
	elseif 序号==3531 then
		刷新道具行囊(内容,"任务包裹")
		if 引擎.场景.窗口.道具行囊.可视==false then
			引擎.场景.窗口.道具行囊:打开()
		else
			引擎.场景.窗口.道具行囊.点击类型="任务包裹"
		end
	elseif 序号==3532 then
		刷新道具行囊(内容,内容.类型)
	elseif 序号==3533 then
		tp.窗口.仓库类:刷新道具仓库总数(内容.道具.道具,内容.总数)
	elseif 序号==3533.1 then
		tp.窗口.共享仓库类:刷新道具仓库总数(内容.道具.道具,内容.总数)
	elseif 序号==3550 then
		if tp.窗口.符石镶嵌.可视 then
			tp.窗口.符石镶嵌:刷新符石(内容.装备,内容.星石操作)
		else
			tp.窗口.符石镶嵌:打开(内容.装备)
		end

	elseif 序号==3555 then
	   if tp.窗口.临时行囊.可视 then
	           tp.窗口.临时行囊:刷新(内容)
	           else
	           tp.窗口.临时行囊:打开(内容)
	      end
	    elseif 序号==3555.1 then
		   if tp.窗口.临时行囊.可视 then
		           tp.窗口.临时行囊:刷新(内容)
		           else
		      end
	  elseif 序号==3556 then
	   if tp.窗口.临时行囊.可视 then
	           tp.窗口.临时行囊:刷新(内容)
	           else
	           tp.窗口.临时行囊:刷新(内容)
	      end



	elseif 序号==3699 then --刷新道具数据

		if 引擎.场景.窗口.道具行囊.可视 then
			-- 引擎.场景.场景.抓取物品 = nil
			-- 引擎.场景.场景.抓取物品ID = nil
			-- 引擎.场景.场景.抓取物品注释 = nil
			if 引擎.场景.窗口.道具行囊.点击类型=="道具" then
				发送数据(3699)
			elseif 引擎.场景.窗口.道具行囊.点击类型=="行囊" then
				发送数据(3700)
			elseif 引擎.场景.窗口.道具行囊.点击类型=="任务包裹" then
				发送数据(3750)
			end
		end
		if 引擎.场景.窗口.打造.可视  or  引擎.场景.窗口.装备开运.可视  or  引擎.场景.窗口.幻化.可视 or  引擎.场景.窗口.宠物炼妖栏.可视  or 引擎.场景.窗口.合成.可视  then --or  引擎.场景.窗口.摊位打造.可视
			发送数据(3707)
		end
	elseif 序号 == 3700 then
		引擎.场景.窗口.商城类:加载数据(内容)
	elseif 序号 == 3700.5 then
		引擎.场景.窗口.商城类:刷新仙玉(内容)
	elseif 序号 == 3700.6 then
		if tp.窗口.商城类.可视 == false then
			tp.窗口.商城类:打开(内容)
		end
	-- elseif 序号 == 3701 then
	-- 	引擎.场景.窗口.商城类:打开(内容)
	elseif 序号 == 3702 then
		if next(引擎.场景.商城数据) ~= nil then
			引擎.场景.商城数据.积分 = 内容
		end
	elseif 序号==3703 then --更新锦衣
		引擎.场景.队伍[1].锦衣=内容
		if 引擎.场景.窗口.锦衣.可视 then
			引擎.场景.窗口.锦衣:刷新()
		end
		if 引擎.场景.窗口.道具行囊.可视 and 引擎.场景.窗口.道具行囊.窗口 =="主人公" then
			引擎.场景.窗口.道具行囊:取人物形象()
		end
	elseif 序号==3704 then --更新锦衣
		引擎.场景.场景.人物:锦衣更换()
	elseif 序号==3705 then --商城数据
		tp.商城数据 = 内容
	elseif 序号==3706 then --勾魂索追杀名单打开
		-- if 引擎.场景.窗口.勾魂索三.可视==false then
		-- 	引擎.场景.窗口.勾魂索三:打开(内容)
		-- end
		tp.窗口.组合输入框:打开("勾魂索",{"请输入玩家ID："})


	elseif 序号==3707 then
		if 引擎.场景.窗口.勾魂索一.可视==false then
			引擎.场景.窗口.勾魂索一:打开()
			引擎.场景.窗口.勾魂索二:打开()
		end
		全局禁止走路=true
    elseif 序号==3708 then --倒计时10秒
    	引擎.场景.窗口.勾魂索一.sss=10
    elseif 序号==3709 then --取消勾魂
    	引擎.场景.常规提示:打开("#Y对方已下线，此次PK对方算失败处理！")
    	if 引擎.场景.窗口.勾魂索一.可视 then
			引擎.场景.窗口.勾魂索一:打开()
		end
		if 引擎.场景.窗口.勾魂索二.可视 then
			引擎.场景.窗口.勾魂索二:打开()
		end
		全局禁止走路=false
	elseif  序号==3710 then --重新上会直接失败
		引擎.场景.常规提示:打开("#R你已进入勾魂索PK战斗，当前不可移动与飞行！")
		全局禁止走路=true
	elseif  序号==3711 then --坐牢不能走动，不能组队，不能使用道具
		全局禁止走路=true
	elseif  序号==3712 then
		if tp.窗口.铃铛抽奖.可视 == false then
			tp.窗口.铃铛抽奖:打开()
		end
	elseif  序号==3713 then
		if tp.窗口.铃铛抽奖.可视 then
	       tp.窗口.铃铛抽奖:播放滚动动画(内容)
	    end
    elseif 序号==3714 then
		if tp.窗口.铃铛抽奖.可视 then
			tp.窗口.铃铛抽奖:清空数据()
		end
	elseif 序号==3715 then
		--table.print(内容)
		if tp.窗口.成长礼包.可视 == false then
			--
			tp.窗口.成长礼包:打开(内容)
		end
	elseif 序号==3715.5 then
		--table.print(内容)
		if tp.窗口.累充礼包.可视 == false then
			tp.窗口.累充礼包:打开(内容)
		end
	elseif 序号==3715.6 then
		--table.print(内容)
		if tp.窗口.充值系统.可视 == false then
			tp.窗口.充值系统:打开(内容)
		end

	elseif 序号==3715.7 then
		tp.回收=内容.物品组
		tp.高级回收=内容.高级物品组
		if tp.窗口.回收系统.可视 == false then
			tp.窗口.回收系统:打开(内容)
		end
	elseif 序号==3715.8 then
		tp.窗口.充值系统:刷新(内容)

	elseif 序号==3715.9 then
		tp.窗口.累充礼包:刷新(内容)

	elseif 序号==3716.3 then
		tp.窗口.累充礼包:七日签到刷新(内容)

	elseif 序号==3716.4 then
		tp.窗口.累充礼包:七日签到(内容)

	elseif 序号==3716.5 then
		tp.窗口.累充礼包:签到物品刷新(内容)






	elseif 序号==3716 then
		tp.窗口.成长礼包:更新礼包(内容)
	elseif 序号==3716.1 then
		tp.窗口.累充礼包:更新礼包(内容)

	elseif 序号==3716.2 then
		--table.print(内容)
		if tp.窗口.每日活跃.可视 == false then
			tp.窗口.每日活跃:打开(内容)
		end

	elseif 序号==3717 then
		if tp.窗口.附魔宝珠.可视 then
			tp.窗口.附魔宝珠:刷新(内容.道具.道具)
		else
			tp.窗口.附魔宝珠:打开(内容.道具.道具)
			tp.窗口.附魔宝珠.宝珠数据 = 内容.宝珠数据
		end
	elseif 序号==3718 then
	    if tp.窗口.附魔宝珠.可视 then
	       tp.窗口.附魔宝珠:打开()
	    end


  elseif 序号 == 3803 then
          if 内容.类型 == 1 or 内容.类型 == 2 then
               tp.窗口.藏宝阁出售:打开(内容)
          else
               tp.窗口.藏宝阁出售:刷新(内容)
          end

  elseif 序号 == 3804 then
         tp.窗口.藏宝阁类:打开(内容.数据,内容.点卡,内容.货币)
  elseif 序号==3805 then
         tp.窗口.藏宝阁类:刷新(内容.数据,内容.类型,内容.点卡)

  elseif 序号==3811 then
        if 内容.类型 == 1 or 内容.类型 == 2 then
             tp.窗口.藏宝阁出售寄存:打开(内容)
        else
             tp.窗口.藏宝阁出售寄存:刷新(内容)
        end
  elseif 序号==3812 then
        if 内容.类型 == 1 or 内容.类型 == 2 then
             tp.窗口.藏宝阁购买寄存:打开(内容)
        else
             tp.窗口.藏宝阁购买寄存:刷新(内容)
        end
  elseif 序号==3813 then
        tp.窗口.藏宝阁上架货币:打开(内容.类型,内容.数量)
  elseif 序号==3814 then
        tp.窗口.藏宝阁类:物品数据刷新(内容.道具,内容.编号+0)





    elseif 序号==3719 then --勾魂索进入战斗
    	if 引擎.场景.窗口.勾魂索一.可视 then
			引擎.场景.窗口.勾魂索一:打开()
		end
		if 引擎.场景.窗口.勾魂索二.可视 then
			引擎.场景.窗口.勾魂索二:打开()
		end
		全局禁止走路=false
	end
	内容=nil
end

-- 3699 刷新所有跟道具有关的界面
function 回调:对话处理(序号,内容)
	if 序号==1501 then
		引擎.场景.窗口.对话栏:文本(内容.模型,内容.名称,内容.对话,内容.选项,nil,nil,内容.下一页)--,内容.寄存
		if 内容.剧情 ~= nil then
			local 数据 =引擎.场景:取剧情描述(引擎.场景.队伍[1].剧情.主线,引擎.场景.队伍[1].剧情.进度,引擎.场景.队伍[1].剧情.分支)
			引擎.场景.窗口.任务栏:删除(数据[1])
			引擎.场景.队伍[1].剧情 = 内容.剧情
			-- if 内容.剧情.暂停~=nil then
			--     引擎.场景.队伍[1].剧情=nil
			-- end
			if 引擎.场景.队伍[1].剧情 ~= nil  and 取剧情是否追踪(引擎.场景.队伍[1].剧情.主线) then--and 引擎.场景.队伍[1].剧情.暂停==nil then
				local 数据 =引擎.场景:取剧情描述(引擎.场景.队伍[1].剧情.主线,引擎.场景.队伍[1].剧情.进度,引擎.场景.队伍[1].剧情.分支)
				local 人物组,事件组 = 引擎.场景:获取任务事件(引擎.场景.队伍[1].剧情.主线,引擎.场景.队伍[1].剧情.进度)
				if 人物组[1] ~= nil and 事件组[1] ~= nil then
					引擎.场景.第二场景:载入显示(人物组,事件组,引擎.场景.队伍[1].剧情.主线,引擎.场景.队伍[1].剧情.进度)
				end
				引擎.场景.窗口.任务栏:添加(数据[1],数据[2],数据[3],数据[4])
			end
		end
	end
	内容=nil
end

function 回调:地图处理(序号,内容)
	if 序号==1001 then
		if 引擎.场景.场景.人物~=nil then
			if 全局禁止走路==false then
				if 引擎.场景.飞行 then
					引擎.场景.场景.人物:设置路径无障碍(内容)
				else
				    引擎.场景.场景.人物:设置路径(内容)
				end
			end
		end
	elseif  序号==1003 then
		引擎.场景.场景:设置假人(内容)

		if 引擎.场景.场景.人物~=nil then
			引擎.场景.场景.人物:停止移动(1)
		end
		引擎.场景.场景.玩家={}
		引擎.场景.场景:设置传送(内容)
	-- elseif  序号==1004 then
	-- 	if 引擎.场景.场景.人物~=nil then
	-- 		引擎.场景.场景.人物:停止移动(1)
	-- 	end
	-- 	引擎.场景.场景.玩家={}
	-- 	引擎.场景.场景:设置传送(内容)
	elseif  序号==1005 then
		--table.print(内容[1],内容[3],内容[2])
		引擎.场景.场景:传送至(内容[1],内容[2],内容[3],true)
		-- if 坐标显示 then
		-- 	引擎.场景.窗口.消息框:添加文本("地图编号:"..内容[1])
		-- end
	elseif  序号==1006 then  --地图添加玩家
		引擎.场景.场景:添加玩家(内容)
	elseif  序号==1007 then--删除玩家
		if 引擎.场景.场景.玩家[内容.id]==nil then
			return
		end
		引擎.场景.场景:删除玩家(内容.id)
	elseif  序号==1008 then
		-- table.print(引擎.场景.场景.玩家)
		if 引擎.场景.场景.玩家[内容.数字id+0]==nil then
			return
		end
		-- 引擎.场景.场景.玩家[内容.数字id+0]:设置路径(内容.路径)
		if 全局禁止走路==false then
			if 引擎.场景.场景.玩家[内容.数字id+0].飞行中 then
				引擎.场景.场景.玩家[内容.数字id+0]:设置路径无障碍(内容.路径) --这里有点不一样
			else
			    引擎.场景.场景.玩家[内容.数字id+0]:设置路径(内容.路径)
			end
		end
	elseif  序号==1009 then
		引擎.场景.场景.玩家[内容.id]:穿戴装备(内容.武器,内容.副武器)
	elseif  序号==1010 then
		引擎.场景.场景.玩家[内容.id]:设置动画(内容.类型)
	elseif  序号==1011 then
		if 引擎.场景.场景.人物~=nil then
			引擎.场景.场景.人物:设置坐标(内容)
		end
	elseif  序号==1012 then
		if 引擎.场景.场景.玩家[内容.id]==nil then
			return
		end
		引擎.场景.场景.玩家[内容.id]:设置坐标(内容)
	elseif  序号==1013 then
		if 引擎.场景.场景.玩家[内容.id]==nil then
			return
		end
		if not 引擎.场景.场景.玩家[内容.id].显示1 then return end
		引擎.场景.场景.玩家[内容.id].染色方案=内容.染色方案
  	    引擎.场景.场景.玩家[内容.id].染色组[1]=内容.染色组[1]
		引擎.场景.场景.玩家[内容.id].染色组[2]=内容.染色组[2]
		引擎.场景.场景.玩家[内容.id].染色组[3]=内容.染色组[3]
	    if 引擎.场景.场景.玩家[内容.id]["静立"] and 引擎.场景.场景.玩家[内容.id]["行走"] then
			引擎.场景.场景.玩家[内容.id]["静立"]:置染色(内容.染色方案,内容.染色组[1],内容.染色组[2],内容.染色组[3],0)
			引擎.场景.场景.玩家[内容.id]["行走"]:置染色(内容.染色方案,内容.染色组[1],内容.染色组[2],内容.染色组[3],0)
		end
	elseif  序号==1014 then
		引擎.场景.假人库:更改地图单位(内容)
	elseif  序号==1015 then
		引擎.场景.假人库:添加地图单位(内容)
	elseif  序号==1016 then  --删除编号
		引擎.场景.假人库:删除指定Npc(内容.编号)
	elseif  序号==1017 then --添加喊话
		引擎.场景.喊话:写入(引擎.场景.场景.人物,引擎.场景.场景.人物.人物,内容.文本,nil,内容.特效,内容.队伍)
	elseif  序号==1018 then
		内容.id=内容.id+0
		if 引擎.场景.场景.玩家[内容.id]==nil then
			return
		end
		if 引擎.场景.战斗中==true then return end
		if not 引擎.场景.场景.玩家[内容.id].显示1 then return end
		引擎.场景.场景.玩家[内容.id].喊话:写入(引擎.场景.场景.玩家[内容.id],引擎.场景.场景.玩家[内容.id].玩家,内容.文本,nil,内容.特效,内容.队伍)
	elseif  序号==1019 then
		if 引擎.场景.场景.玩家[内容.id]==nil then
			return
		end
		引擎.场景.场景.玩家[内容.id].坐骑=内容.坐骑
		if 内容.坐骑==nil then
			引擎.场景.场景.玩家[内容.id]:卸下坐骑()
		else
			引擎.场景.场景.玩家[内容.id]:坐骑改变()
		end
	elseif  序号==1020 then
		if 内容.当前称谓==nil then
			内容.当前称谓=""
		end
		引擎.场景.场景.玩家[内容.id]:更改称谓(内容)
	elseif  序号==1029 then
		if 内容.当前称谓==nil then
			内容.当前称谓=""
		end
		tp.当前称谓 = 内容.当前称谓
	elseif  序号==1021 then
		if 内容==nil then
			return
		end
		for i=1,#内容 do
			引擎.场景.假人库:添加地图单位(内容[i])-- 引擎.场景.假人库:添加地图单位(内容)
		end
	elseif  序号==1022 then  --地图添加玩家
		if 内容==nil then
			return
		end
		for i=1,#内容 do
			引擎.场景.场景:添加玩家(内容[i])-- 引擎.场景.场景:添加玩家(内容)
		end
	elseif  序号==1023 then
			引擎.场景.房屋数据 = 内容[1]
	elseif  序号==1024 then
			引擎.场景.场景.地图:房屋特效(内容[1])
	elseif  序号==1025 then
			引擎.场景.如意符 = true
	elseif  序号==1026 then
		if 内容[2] == "庭院" then
			引擎.场景.房屋数据.庭院装饰 = 内容[1]
			引擎.场景.场景.地图:房屋特效2()
		elseif 内容[2] == "室内" then
			引擎.场景.房屋数据.室内装饰 = 内容[1]
			引擎.场景.场景.地图:房屋特效2()
		end
	elseif  序号==1027 then
		if 内容[2] == "庭院" then
			引擎.场景.房屋数据.庭院装饰 = 内容[1]
			--引擎.场景.场景.地图:房屋特效2()
		elseif 内容[2] == "室内" then
			引擎.场景.房屋数据.室内装饰 = 内容[1]
			--引擎.场景.场景.地图:房屋特效2()
		end
	elseif  序号==1028 then
		引擎.场景.窗口.房屋拜访:打开()
	elseif  序号==1030 then
		引擎.场景.场景.玩家[内容.id].变身数据=内容.变身数据
		引擎.场景.场景.玩家[内容.id]:置模型()
	elseif  序号==1031 then
		引擎.场景.场景.玩家[内容.id].炫彩 = 内容.炫彩
		引擎.场景.场景.玩家[内容.id].炫彩组 = 内容.炫彩组
		引擎.场景.场景.玩家[内容.id]:炫彩染色(内容.炫彩,内容.炫彩组,"人物")
	elseif 序号==1032 then
		引擎.场景.场景.玩家[内容.id]:更换锦衣(内容.锦衣,内容.序号)
	elseif  序号==1033 then  --
		引擎.场景.假人库:npc行走(内容.编号,内容.坐标x,内容.坐标y,内容.喊话)
	elseif  序号==1034 then  --
		引擎.场景.假人库:npc炫彩(内容.编号,内容.炫彩,内容.炫彩组)
	elseif  序号==1035 then  --
		引擎.场景.假人库:更换模型名称(内容.编号,内容.模型,内容.名称)

	-- elseif  序号==1034 then
	-- 	for i=1,#内容 do
	-- 		if 引擎.场景.场景.玩家[内容[i].id]~=nil and 引擎.场景.场景.玩家[内容[i].id].玩家ID~=nil and 引擎.场景.场景.玩家[内容[i].id].玩家ID == 内容[i].id then
	-- 			if 引擎.场景.场景.玩家[内容[i].id].真实坐标.x  ~= 内容[i].坐标.x or 引擎.场景.场景.玩家[内容[i].id].真实坐标.y  ~= 内容[i].坐标.y then
	-- 				if (取两点距离(引擎.场景.场景.玩家[内容[i].id].真实坐标,内容[i].坐标)) > 2  then
	-- 					if 引擎.场景.场景.玩家[内容[i].id].显示1 then
	-- 						引擎.场景.场景:更新行走({x=内容[i].坐标.x/20,y=内容[i].坐标.y/20},内容[i].id)
	-- 						引擎.场景.场景.玩家[内容[i].id].记忆方向 = 内容[i].方向
	-- 					end
	-- 				end
	-- 			end
	-- 		end
	-- 	end
	end
	内容=nil
end

function 回调:系统处理(序号,内容,数据)
	-- print(序号,内容,数据)
	if 序号==1 and 内容.用户==nil then
		-- print(引擎.场景.进程)
		引擎.场景.进程=7 --网关传回信息，然后到登录窗口
		-- print(引擎.场景.进程)
		-- self:发送数据(1,版本号..fgc..系统参数.账号..fgc..系统参数.密码..fgc..f函数.取硬盘序列号())
	elseif 序号==2 then --选择角色
		引擎.场景.进程 = 2
	elseif 序号==3 then--创建角色
		引擎.场景.进程 = 3
	elseif 序号==3.1 then --创建账号成功
		引擎.场景.进程 = 7
	elseif 序号==4 then --选择角色 加载数据
		引擎.场景.读取:添加角色信息(内容)
	elseif 序号==5 then
		if 引擎.场景.登陆方式 == 2 then
			引擎.置宽高(1280,720)
			引擎.场景.系统设置.游戏窗口设置 = "1280*720"
			全局游戏宽度 = 1280
			全局游戏高度 = 720
			引擎.置全屏()
		else
			引擎.置宽高(全局游戏宽度,全局游戏高度)
		end
		引擎.场景.队伍[1]=引擎.场景._队伍.创建()
		引擎.场景.队伍[1]:重置属性(内容)
		引擎.场景.宠物=内容.宠物
		引擎.场景.场景 = require("script/全局/主显").创建(引擎.场景)
		引擎.场景.第二场景 = require("script/场景类/第二场景").创建(引擎.场景)
		引擎.场景.提示误差=0
		-- 引擎.场景.登录类.音乐:停止()
		引擎.场景.音乐:停止()
		引擎.场景.场景:转移(内容.地图数据.编号,内容.地图数据.x,内容.地图数据.y)
		引擎.场景.游戏进程=1
		当前数字id=内容.数字id
		全局登陆内容()
		local 地图等级={}
		地图等级[1],地图等级[2]=引擎.取场景等级(引擎.场景.当前地图)
		if 地图等级[1]~=nil then
			引擎.场景.场景.场景最低等级=地图等级[1]
			引擎.场景.场景.场景最高等级=地图等级[2]
			引擎.场景.窗口.消息框:添加文本("#Y/本场景等级为"..地图等级[1].."-"..地图等级[2].."级","xt")
		else
			引擎.场景.场景.场景最低等级=nil
			引擎.场景.场景.场景最高等级=nil
		end
		-- if 内容.飞行中 then
		    引擎.场景.飞行=内容.飞行中
		-- end
		引擎.置标题(欢迎词.."--"..tp.队伍[1].名称.."--["..tp.队伍[1].数字id.."] ")
		引擎.场景:登陆清空()
		__fsyz=true
		发送数据(6303)
	elseif 序号==6 then
		引擎.场景.窗口.宠物领养栏:打开()
	elseif 序号==6.1 then
		引擎.场景.窗口.孩子兑换:打开(内容)
	elseif 序号==7 then
		if 引擎.场景.进程 == 4 then
			引擎.场景.常规提示:打开(内容)
		else
		    tp.提示:写入(内容)
		end
	elseif 序号==8 then --检查宠物领养
		引擎.场景.宠物=内容
		if 引擎.场景.窗口.宠物状态栏.可视 then
		 引擎.场景.窗口.宠物状态栏:打开()
		 引擎.场景.窗口.宠物状态栏:打开()
		end
	elseif 序号==9 then --商店
		--table.print(内容)
		引擎.场景.窗口.商店:打开(内容.商品,内容.名称,内容.类型,内容.银子)
	elseif 序号==9.1 then --商店
		--table.print(内容)
		引擎.场景.窗口.商店:刷新(内容)


	elseif 序号==10 then  --更新人物界面
		--self.临时装备=table.loadstring(table.tostring(引擎.场景.队伍[1].装备))
		引擎.场景.队伍[1]:重置属性(内容,1)
		 --print(内容.固定伤害)
		if 引擎.场景.窗口.人物状态栏.可视==false then
		 引擎.场景.窗口.人物状态栏:打开()
		end
	elseif 序号==11 then
	 --引擎.场景.场景.人物:加入动画("升级")
	elseif 序号==12 then
		if 引擎.场景.窗口.人物状态栏.可视 then
		 	发送数据(7)
		end
	elseif 序号==13 then
		引擎.场景.窗口.飞行符:打开()
	elseif 序号==26 then
		引擎.场景.窗口.飞行符:打开("新春")
	elseif 序号==26.1 then
		引擎.场景.窗口.世界大地图:打开("新春")
	elseif 序号==14 then
		刷新道具行囊(内容)
	elseif 序号==15 then
		if 引擎.场景.队伍[1].体力==nil then
			return
		end
		引擎.场景.队伍[1].体力=内容.体力
		引擎.场景.队伍[1].活力=内容.活力
	elseif 序号==16 then
		刷新宝宝列表(内容)
	elseif 序号==17 then
		刷新宝宝列表(内容)
		引擎.场景.窗口.召唤兽属性栏:打开()
	elseif 序号==18 then
		for n=1,#引擎.场景.队伍[1].宝宝列表 do
			引擎.场景.队伍[1].宝宝列表[n].参战信息=nil
			if 内容.认证码 == 引擎.场景.队伍[1].宝宝列表[n].认证码 then
				引擎.场景.队伍[1].宝宝列表[n].参战信息=1
				break
			end
		end
		引擎.场景.队伍[1].参战宝宝=内容
	elseif 序号==19 then
		引擎.场景.队伍[1].宝宝列表[内容.序列].名称=内容.名称
	elseif 序号==20 then
		for n=1,#引擎.场景.队伍[1].宝宝列表 do
			if 内容.认证码== 引擎.场景.队伍[1].宝宝列表[n].认证码 then
				引擎.场景.队伍[1].宝宝列表[n]=内容
				break
			end
		end
    elseif 序号==20.1 then --巫医
	 --引擎.场景.场景.人物:加入动画("升级")
		 if tp.队伍[1].参战宝宝 and tp.队伍[1].参战宝宝.气血 then
		 	-- table.print(内容)
		     tp.队伍[1].参战宝宝.气血=内容.气血
		     tp.队伍[1].参战宝宝.最大气血=内容.气血
		     tp.队伍[1].参战宝宝.魔法=内容.魔法
		     tp.队伍[1].参战宝宝.最大魔法=内容.魔法
		 end
	elseif 序号==21 then
		引擎.场景.窗口.召唤兽属性栏:放生()
	elseif 序号==22 then
		引擎.场景.宠物=内容
		引擎.场景.窗口.宠物状态栏:打开()
	elseif 序号==23 then
		 -- 引擎.场景.窗口.宠物炼妖栏:打开(内容.道具,内容.类型)
		 tp.窗口.宠物洗练栏:打开(内容.道具)
	elseif 序号==24 then
		引擎.场景.窗口.宠物洗练栏:刷新道具(内容.道具)
	elseif 序号==25 then
		宝宝队伍图排序=内容
		-- table.print(宝宝队伍图排序)
		-- tp.窗口.召唤兽查看栏:打开(内容)
	-- 	引擎.场景.窗口.宠物洗练栏:清除()
	-- elseif 序号==26 then
	-- 	tp.窗口.宠物合宠栏:清除()
	elseif 序号==27 then  --聊天框文本
		引擎.场景.窗口.消息框:添加文本(内容.文本,内容.频道)
	elseif 序号==28 then
		引擎.场景.窗口.道具行囊:置形象()
	elseif 序号==29 then
		引擎.场景.窗口.染色:打开()
	elseif 序号==30 then
		--引擎.场景.窗口.染色:打开()
		引擎.场景.队伍[1].染色组 = 内容
		引擎.场景.场景.人物:置染色(引擎.场景.队伍[1].染色方案,内容[1],内容[2],内容[3])
	elseif 序号==31 then  --更新人物界面
		-- table.print(内容)
		引擎.场景.队伍[1]:重置属性(内容)
		引擎.场景.金钱=内容.银子
		引擎.场景.储备=内容.储备
		引擎.场景.存银=内容.存银
	elseif 序号==32 then
		引擎.场景.窗口.技能学习:打开()
	elseif 序号==32.1 then
		引擎.场景.窗口.强化技能学习:打开(内容)
	elseif 序号==33 then
		tp.队伍[1].BG=内容.BG
	-- elseif 序号==33 then
		--self.临时装备=table.loadstring(table.tostring(引擎.场景.队伍[1].装备))
		--引擎.场景.队伍[1]:重置属性(内容)
	elseif 序号==34 then --更新辅助技能
		引擎.场景.队伍[1].辅助技能[内容.序列].等级=内容.等级
		if 引擎.场景.窗口.帮派技能学习.可视 then
			引擎.场景.窗口.帮派技能学习:更新技能(内容.等级)
		end
		if 引擎.场景.窗口.人物状态栏.可视 and 引擎.场景.窗口.人物状态栏.状态==3 then
			引擎.场景.窗口.人物状态栏.辅助技能[内容.序列].技能.等级=内容.等级
			local 临时消耗=生活技能消耗(内容.等级+1,内容.名称)
			引擎.场景.窗口.人物状态栏.辅助技能[内容.序列].技能.介绍= 引擎.场景.窗口.人物状态栏.辅助技能[内容.序列].技能.原介绍..'\n'.."#Y/学习消耗："..临时消耗.经验.."点人物经验、"..临时消耗.金钱.."两银子#R/（>=40级的生活技能必须在加入帮派后才可学习）"
		end
	elseif 序号==35 then
		引擎.场景.金钱=内容.银子
		引擎.场景.储备=内容.储备
		引擎.场景.存银=内容.存银
		引擎.场景.队伍[1].当前经验=内容.经验
	elseif 序号==36 then
		引擎.场景.场景.人物:加入动画(内容.动画)
		引擎.场景:播放音效类(内容.动画)
	elseif 序号==37 then
		if 内容=="1" then 内容=nil end
		引擎.场景.队伍[1].变身数据=内容
		引擎.场景.场景.人物:置模型()
		if 内容==nil then
		    引擎.场景.窗口.人物框:删除小图标信息("变身卡")
		end
	elseif 序号==38 then
		if 内容.内容=="天黑了" then
			日夜="黑夜"
		--collectgarbage("collect")
		else
		--collectgarbage("collect")
			日夜="白天"
		end
		-- table.print(内容)
		if 内容.方式 == 1 then
			for i=1,#内容.超链接 do
			    table.insert(引擎.场景.广播数据,内容.超链接[i])
			end
		end
		if #引擎.场景.广播数据>40 then --最多40条连接
			table.remove(引擎.场景.广播数据,1)
		end
		-- -- table.print(引擎.场景.广播数据)
		引擎.场景.窗口.消息框:添加文本(内容.内容,内容.频道)
	elseif 序号==39 then
		if 引擎.场景.窗口.任务栏.可视 then
			发送数据(10)
		end
	elseif 序号==40 then
		if 引擎.场景.窗口.任务栏.可视 then
			引擎.场景.窗口.任务栏:打开()
		end
		引擎.场景.窗口.任务栏:打开(内容)
	elseif 序号==41 then
		引擎.场景.窗口.快捷技能栏:刷新(内容)
	elseif 序号==42 then
		引擎.场景.窗口.快捷技能栏:刷新(内容,1)
	elseif 序号==43 then
		昼夜=内容
	 -- 引擎.场景.窗口.时辰.序列=内容.时辰
	elseif 序号==44 then
	 引擎.场景.队伍[1].修炼=内容.人物
	 引擎.场景.队伍[1].bb修炼=内容.bb
	elseif 序号==45 then
		引擎.场景.窗口.召唤兽属性栏:放生(1)
	elseif 序号==46 then --角色处理类:刷新任务跟踪()
		if 引擎.场景.剧情开关.任务追踪 then
			引擎.场景.窗口.任务追踪栏.数据记录={}
			引擎.场景.窗口.任务追踪栏.介绍文本:清空()
		end
		引擎.场景.窗口.人物框:删除小图标信息("摄妖香")
		引擎.场景.窗口.人物框:删除小图标信息("红罗羹")
		引擎.场景.窗口.人物框:删除小图标信息("绿芦羹")
		引擎.场景.窗口.人物框:删除小图标信息("变身卡")
        if #内容.任务>0 then
            for i=1,#内容.任务 do
				引擎.场景.窗口.任务追踪栏:刷新(内容.任务[i][1],内容.任务[i][2],内容.任务[i][3],内容.任务[i][4])
			end
		else
			引擎.场景.窗口.任务追踪栏:背景计算()
        end
		if 引擎.场景.队伍[1] ~= nil and 引擎.场景.队伍[1].剧情 ~= nil and 取剧情是否追踪(引擎.场景.队伍[1].剧情.主线) then--and 引擎.场景.队伍[1].剧情.暂停 == nil  then
			local 数据 =引擎.场景:取剧情描述(引擎.场景.队伍[1].剧情.主线,引擎.场景.队伍[1].剧情.进度,引擎.场景.队伍[1].剧情.分支)
			引擎.场景.窗口.任务追踪栏:刷新(数据[1],数据[2],数据[3],数据[4])
		end
		-- if 内容.刷新方式 == 1 then
		-- 	for i=1,#内容.任务 do
		-- 		引擎.场景.窗口.任务追踪栏:刷新(内容.任务[i][1],内容.任务[i][2],内容.任务[i][3],内容.任务[i][4])
		-- 	end
		-- 	if 引擎.场景.队伍[1] ~= nil and 引擎.场景.队伍[1].剧情 ~= nil and 引擎.场景.队伍[1].剧情.暂停 == nil  then
		-- 		local 数据 =引擎.场景:取剧情描述(引擎.场景.队伍[1].剧情.主线,引擎.场景.队伍[1].剧情.进度,引擎.场景.队伍[1].剧情.分支)
		-- 		引擎.场景.窗口.任务追踪栏:刷新(数据[1],数据[2],数据[3],数据[4])
		-- 	end
		-- else
		-- 	if 引擎.场景.队伍[1]  ~= nil and 引擎.场景.队伍[1].剧情 ~= nil and 引擎.场景.队伍[1].剧情.暂停 == nil then
		-- 		local 数据 =引擎.场景:取剧情描述(引擎.场景.队伍[1].剧情.主线,引擎.场景.队伍[1].剧情.进度,引擎.场景.队伍[1].剧情.分支)
		-- 		引擎.场景.窗口.任务追踪栏:刷新(数据[1],数据[2],数据[3],数据[4])
		-- 	end
		-- end
 	elseif 序号==47 then
		引擎.场景.队伍[1].气血=内容[1].气血
		引擎.场景.队伍[1].气血上限=内容[1].气血上限
		引擎.场景.队伍[1].最大气血=内容[1].最大气血
		引擎.场景.队伍[1].魔法=内容[1].魔法
		引擎.场景.队伍[1].最大魔法=内容[1].最大魔法
		引擎.场景.队伍[1].愤怒=内容[1].愤怒
 	elseif 序号==48 then
	 	-- 引擎.场景.队伍[1].自动遇怪=内容.遇怪
 	elseif 序号==49 then
	 	引擎.场景.窗口.排行榜:打开(内容)
 	elseif 序号==50 then
	 	引擎.场景.窗口.好友列表:打开(内容)
	elseif 序号==51 then
	 	引擎.场景.窗口.好友列表.查找结果=内容
	elseif 序号==52 then
		if  引擎.场景.窗口.好友列表.可视 then
			-- 发送数据(19)
			发送数据(6959)
		end
	elseif 序号==53 then
		if 引擎.场景.窗口.好友列表.可视 then
		 引擎.场景.窗口.好友列表.数据=内容
		end
	elseif 序号==54 then
		if 引擎.场景.窗口.好友列表.可视 then
			--引擎.场景.窗口.好友列表.数据=内容
			for i, v in pairs(内容.数据) do
				引擎.场景.窗口.好友列表.数据[内容.类型][内容.序列][i]=内容.数据[i]
				if i~="模型" then
				 引擎.场景.窗口.好友列表.显示信息[i]=内容.数据[i]
				end
			end
			--  引擎.场景.窗口.好友列表.显示信息.名称=内容.
		 end
	elseif 序号==55 then
		if 引擎.场景.窗口.好友消息.可视 then
			引擎.场景.窗口.好友消息.丰富文本:添加文本("#C"..内容.信息)
			引擎.场景.窗口.好友消息.丰富文本:添加文本("#H"..内容.内容)
			if 内容.id ~= 4000501 then
				local 临时内容=读入文件([[data\]]..引擎.场景.队伍[1].数字id..[[\]]..内容.id..[[\记录.txt]])
				临时内容=临时内容.."\n".."#C"..内容.信息.."\n".."#H"..内容.内容
				local file =io.open([[data\]]..引擎.场景.队伍[1].数字id..[[\]]..内容.id..[[\记录.txt]],"w")
				file:write(临时内容)
				file:close()
			end
		end
	elseif 序号==56 then
	 	消息闪烁=true
	elseif 序号==57 then
	 	消息闪烁=false
	elseif 序号==58 then
		引擎.场景.窗口.好友消息:打开(内容.名称,内容.id,内容.好友度,内容.等级,内容.模型,内容.类型,消息)
		引擎.场景.窗口.好友消息.丰富文本:添加文本("#C"..内容.信息)
		引擎.场景.窗口.好友消息.丰富文本:添加文本("#H"..内容.内容)
		if 内容.id ~= 4000501 then
			local 临时内容=读入文件([[data\]]..引擎.场景.队伍[1].数字id..[[\]]..内容.id..[[\记录.txt]])
			临时内容=临时内容.."\n".."#C"..内容.信息.."\n".."#H"..内容.内容
			local file =io.open([[data\]]..引擎.场景.队伍[1].数字id..[[\]]..内容.id..[[\记录.txt]],"w")
			file:write(临时内容)
			file:close()
		end
	elseif 序号==59 then
		游戏公告:添加公告(内容)
	elseif 序号==77 then
		if 内容.方式 == 1 then
			for i=1,#内容.超链接 do
			    table.insert(引擎.场景.广播数据,内容.超链接[i])
			end
		end
		if #引擎.场景.广播数据>40 then --最多40条连接
			table.remove(引擎.场景.广播数据,1)
		end
		-- table.print(内容)
		游戏传音:添加传音(内容)
	elseif 序号==60 then
		引擎.场景.队伍[1].坐骑=内容
		if 引擎.场景.队伍[1].坐骑=="1" then 引擎.场景.队伍[1].坐骑=nil end
		if 引擎.场景.队伍[1].坐骑==nil then
			引擎.场景.场景.人物:卸下坐骑()
		else
			引擎.场景.场景.人物:坐骑改变(引擎.场景.队伍[1].模型,引擎.场景.队伍[1].坐骑)
		end
	elseif 序号==61 then
		引擎.场景.坐骑列表=内容
		if 引擎.场景.队伍[1].坐骑~=nil then
			for i=1,#引擎.场景.坐骑列表 do
				if 引擎.场景.坐骑列表[i].认证码 == 引擎.场景.队伍[1].坐骑.认证码 then
					引擎.场景.队伍[1].坐骑 = table.loadstring(table.tostring(引擎.场景.坐骑列表[i]))
					引擎.场景.场景.人物:坐骑改变(引擎.场景.队伍[1].模型,引擎.场景.队伍[1].坐骑)
				end
			end
		end
	elseif 序号==62 then
		if 引擎.场景.窗口.坐骑技能栏.可视 then
		    引擎.场景.窗口.坐骑技能栏:刷新(内容)
		end
		if 引擎.场景.窗口.坐骑属性栏.可视 then
		    引擎.场景.窗口.坐骑属性栏:刷新经验(内容)
		end
		-- 引擎.场景.窗口.坐骑技能栏:刷新(内容)
		-- if 引擎.场景.坐骑列表[内容.编号] ~= nil then
		-- 	引擎.场景.坐骑列表[内容.编号] = 内容.坐骑数据
		-- 	if  内容.技能 == 1 then
		-- 		引擎.场景.窗口.坐骑技能栏:刷新()
		-- 	end
		-- end
	elseif 序号==62.1 then
		if 引擎.场景.窗口.坐骑属性栏.可视 then
		    引擎.场景.窗口.坐骑属性栏:改名(内容)
		end
	elseif 序号==63 then
		tp.窗口.组合输入框:打开("影蛊",{"请输入你想追踪目标的id"})
	elseif 序号==63.1 then
	    tp.窗口.组合输入框:打开("月饼造句",{"少侠饱读诗书，还请使用“月饼”一词造句吧！",ARGB(255,255,255,0)})
	elseif 序号==61.6 then
	    tp.窗口.组合输入框:打开("请抄写：",{"请抄写："..内容,ARGB(255,255,255,0)})

	elseif 序号==64 then----打书炼妖
		tp.窗口.宠物合宠栏:打开(内容.道具)
    elseif 序号==65 then
    	if 内容.类型=="内丹" then
    	    tp.窗口.宠物打书内丹栏:打开(内容.道具,2)
    	elseif 内容.类型=="赐福" then
       	    tp.窗口.宠物打书内丹栏:打开(内容.道具,4,内容.消耗 )
    	else
    	    tp.窗口.宠物打书内丹栏:打开(内容.道具,1)
    	end
	elseif 序号==66 then
		if tp.窗口.宠物打书内丹栏.可视 then
			tp.窗口.宠物打书内丹栏:清除()
		end
	elseif 序号==67 then
		tp.窗口.宠物打书内丹栏:刷新道具(内容.道具)
	-- elseif 序号==67.1 then
	-- 	tp.窗口.宠物打书内丹栏:刷新道具(内容.道具)
    elseif 序号==68 then
    	tp.窗口.组合输入框:打开("更改宝宝造型",{"请输入你想更改的造型。"})
	elseif 序号==69 then
		if 引擎.场景.队伍[1].坐骑~=nil then
			for i=1,#引擎.场景.坐骑列表 do
				if 引擎.场景.坐骑列表[i].认证码 == 内容.认证码 then
					引擎.场景.坐骑列表[i]=内容
					break
				end
			end
		end
		-- tp.窗口.宠物打书内丹栏:更新内丹(内容.道具)
	elseif 序号==75 then--好像不用
		tp.窗口.坐骑属性栏:放生()
	elseif 序号==76 then--好像不用
		if tp.窗口.坐骑属性栏.可视 then
	        tp.窗口.坐骑属性栏:删除统御召唤兽(内容)
	    end
    elseif 序号==78 then
		tp.窗口.组合输入框:打开("帮战报名",{"请输入报名帮费(最低费用50W帮派资金)",ARGB(255,255,255,0)})
	elseif 序号==85 then
		tp.窗口.神秘宝箱:打开(内容)
	elseif 序号==99 then---=================屏蔽fwd向客户端发送时间
		local 临时数据=table.loadstring(数据)

		if 临时数据.内容~=nil and 临时数据.内容.内容~=nil and 临时数据.内容.频道~=nil then
			引擎.场景.窗口.消息框:添加文本(临时数据.内容.内容,临时数据.内容.频道)
		end
	elseif 序号==100 then
			引擎.场景.房屋数据 = 内容
	elseif 序号==101 then
		引擎.场景.金钱=内容.银子
		引擎.场景.窗口.打造:打开(内容.道具)
  elseif 序号==101.1 then
      if tp.窗口.自动抓鬼.可视 then
          tp.窗口.自动抓鬼:刷新(内容)
        else
          tp.窗口.自动抓鬼:打开(内容)
        end

	elseif 序号==102 then
		if 引擎.场景.窗口.召唤兽资质栏.可视 then
	        引擎.场景.窗口.召唤兽资质栏:更新内丹(内容.序号,内容.内丹)
	    end
	    if 引擎.场景.窗口.召唤兽属性栏.可视 then
	    	for n=1,#引擎.场景.队伍[1].宝宝列表 do
				if 内容.bb.认证码== 引擎.场景.队伍[1].宝宝列表[n].认证码 then
					引擎.场景.队伍[1].宝宝列表[n]=内容.bb
					break
				end
			end
	    end
    elseif 序号==108 then
    	if 引擎.场景.窗口.召唤兽资质栏.可视 then
	        引擎.场景.窗口.召唤兽资质栏:更新灵性(内容)
	    end
    elseif 序号==103 then
	    local 事件 = function()
        发送数据(37.1)
        end
        tp.窗口.文本栏:载入("#R再次确认,您真的要解散此帮派吗？",nil,true,事件)
    elseif 序号==104 then
    	   --tp.窗口.人物染色:打开()
	    tp.窗口.焕彩染色:打开(内容.类型)
     elseif 序号==104.1 then
	    tp.窗口.武器染色:打开()
    elseif 序号==105 then
    	if tp.窗口.成就提示.可视==false then
    	    tp.窗口.成就提示:打开(内容)
    	else
    		tp.窗口.成就提示:新增内容(内容)
    	end
    elseif 序号==106 then
    	tp.窗口.鉴定提示:打开(内容)
	elseif 序号==107 then
		if tp.窗口.转盘.可视==false then
    	    tp.窗口.转盘:打开(内容)
    	end

     elseif 序号==106.5 then
     	--table.print(内容)
    	if tp.窗口.每日签到.可视 then
	       tp.窗口.每日签到:刷新(内容)
	    else
	       tp.窗口.每日签到:打开(内容)
	    end
  elseif 序号==92.1 then
  tp.窗口.召唤兽查看栏:打开(内容)

    elseif 序号==109 then
    	if tp.窗口.宝宝进阶.可视==false and tp.队伍[1].参战宝宝.名称 ~= nil then
    	    tp.窗口.宝宝进阶:打开(内容)
    	end

    elseif 序号==109.1 then
    	--table.print(内容)
    	tp.窗口.进化宝宝:刷新资质(内容)

    elseif 序号==109.2 then
    	local bbs=tp.队伍[1].宝宝列表
	if bbs[1]~= nil then
	    	if tp.窗口.进化宝宝.可视==false then
	    	tp.窗口.进化宝宝:打开(内容)
	    	end
	    else
	    	tp.常规提示:打开("#Y/你没有召唤兽宝宝，无法使用进化系统！")
	    end

	elseif 序号==110 then
		tp.窗口.宝宝进阶:刷新(内容)
	elseif 序号==111 then
		tp.窗口.宝宝进阶:刷新进阶(内容)
	elseif 序号==112 then
		tp.窗口.召唤兽资质栏:刷新特性开关(内容)
	elseif 序号==113 then --广播队伍链接
		if 内容.超链接==nil then
		    return
		end
		for i=1,#内容.超链接 do
		    table.insert(引擎.场景.广播数据,内容.超链接[i])
		end
		if #引擎.场景.广播数据>40 then --最多40条连接
			table.remove(引擎.场景.广播数据,1)
		end
		引擎.场景.窗口.消息框:添加文本(内容.内容,内容.频道)
  elseif 序号 == 133.5 then
    if tp.窗口.抽奖系统.可视 then
      tp.窗口.抽奖系统:刷新(内容)
    else
      tp.窗口.抽奖系统:打开(内容)
    end

	elseif 序号==114 then
		刷新宝宝列表(内容.宝宝数据)
    	if 引擎.场景.窗口.召唤兽资质栏.可视 then
	        引擎.场景.窗口.召唤兽资质栏:更新资质(内容.认证码,内容.资质,内容.资质数额)
	    end
	elseif 序号==115 then
		刷新宝宝列表(内容.宝宝数据)
		if 引擎.场景.窗口.召唤兽资质栏.可视 then
	        引擎.场景.窗口.召唤兽资质栏:更新成长(内容.认证码,内容.成长)
	    end

	 elseif 序号==122 then
    		tp.队伍[1].扩展背包 = 内容
	elseif 序号==152 then
             if 引擎.场景.窗口.月卡.可视 then
                 引擎.场景.窗口.月卡:刷新1(内容)
             else
                 引擎.场景.窗口.月卡:打开(内容)
             end

             elseif 序号==190 then
	       tp.窗口.图鉴:激活刷新(内容)
	 elseif 序号==191 then
	         tp.窗口.图鉴:打开(内容)

	elseif 序号==200 then
		刷新道具行囊(内容)
	elseif 序号==201 then
		刷新道具逻辑(内容[1],内容[2],true)
	elseif 序号==202 then
		for i=1,5 do
			if 内容.符石[i] ~= nil and  内容.符石[i].名称 ~= nil then
				引擎.场景.窗口.符石镶嵌.物品[i]:置物品(内容.符石[i])
			else
				引擎.场景.窗口.符石镶嵌.物品[i]:置物品(nil)
			end
		end
		引擎.场景.窗口.符石镶嵌:刷新符石()
	elseif 序号==203 then
		tp.常规提示:打开("#Y/恭喜成功兑换一颗乾元丹！")
		引擎.场景.队伍[1].当前经验 = 内容.当前经验
		引擎.场景.金钱 = 内容.银子
		引擎.场景.队伍[1].装备属性.可用乾元丹 = 内容.可用乾元丹
		引擎.场景.队伍[1].装备属性.可换乾元丹 = 内容.可换乾元丹
		引擎.场景.队伍[1].装备属性.已换乾元丹 = 内容.已换乾元丹
	elseif 序号 == 204 then
		引擎.场景.窗口.创建帮派:打开()
	elseif 序号 == 205 then
		引擎.场景.窗口.帮派界面:打开(内容)
	elseif 序号 == 206 then
		引擎.场景.窗口.加入帮派:打开(内容)
	-- elseif 序号 == 207 then
	-- 	引擎.场景.窗口.帮派管理.数据 = 内容.成员
	-- 	引擎.场景.窗口.帮派管理.数据1 = 内容.申请
	elseif 序号 == 208 then
		-- table.print(内容)
		引擎.场景.窗口.帮派界面:更新数据(内容) --职位变更，删除帮众
	elseif 序号 == 246 then --更新玩家帮派
		引擎.场景.队伍[1].帮派=内容
	elseif 序号 == 209 then
		if 内容.人物修 then
		    引擎.场景.窗口.修炼升级:打开(内容.帮派资材,内容.银子,内容.人物修,内容.储备,"人物",内容.免资材)
		elseif 内容.宠物修 then
			引擎.场景.窗口.修炼升级:打开(内容.帮派资材,内容.银子,内容.宠物修,内容.储备,"bb",内容.免资材)
		end
	elseif 序号 == 210 then
		引擎.场景.窗口.修炼升级:刷新数据(内容)
	elseif 序号 == 211 then
		tp.窗口.帮派技能学习:打开(内容.银子,内容.储备,内容.技能)
	elseif 序号 == 213 then
		if 引擎.场景.队伍[1].炫彩 == nil then
			引擎.场景.队伍[1].炫彩 = 1
			引擎.场景.队伍[1].炫彩组 = {}
		end
		引擎.场景.队伍[1].炫彩 = 内容.炫彩
		引擎.场景.队伍[1].炫彩组 = 内容.炫彩组
		引擎.场景.场景.人物:炫彩染色(引擎.场景.队伍[1].染色方案,引擎.场景.队伍[1].炫彩组,"人物")
	elseif 序号 == 213.1 then
		if 引擎.场景.队伍[1].武器炫彩 == nil then
			引擎.场景.队伍[1].武器炫彩 = 1
			引擎.场景.队伍[1].武器炫彩组 = {}
		end
		引擎.场景.队伍[1].武器炫彩 = 内容.武器炫彩
		引擎.场景.队伍[1].武器炫彩组 = 内容.武器炫彩组
		引擎.场景.场景.人物:炫彩染色(引擎.场景.队伍[1].染色方案,引擎.场景.队伍[1].武器炫彩组,"武器")
	elseif 序号 == 214 then
		for n=1,#引擎.场景.队伍[1].宝宝列表 do
			if 内容.认证码== 引擎.场景.队伍[1].宝宝列表[n].认证码 then
				引擎.场景.队伍[1].宝宝列表[n].炫彩=内容.炫彩
				引擎.场景.队伍[1].宝宝列表[n].炫彩组={}
				引擎.场景.队伍[1].宝宝列表[n].炫彩组=内容.炫彩组
				break
			end
		end
	elseif 序号 == 215 then
		引擎.场景.窗口.人物炫彩:打开()
	elseif 序号 == 216 then
		--print(内容.存银)
		引擎.场景.窗口.银两存取:打开(内容)
	elseif 序号 == 216.5 then
		--print(内容.存银)
		引擎.场景.窗口.银两存取:刷新(内容)
	elseif 序号 == 217 then
		if 引擎.场景.窗口.银两存取.可视==true then
			引擎.场景.窗口.银两存取.银两存取 = 内容.存银
			引擎.场景.窗口.银两存取.银两 = 内容.银子
		end
		引擎.场景.队伍[1].银子 = 内容.银子
		引擎.场景.队伍[1].存银 = 内容.存银
	-- elseif 序号 == 219 then
	-- 	引擎.场景.窗口.领取奖励:打开(内容)
	elseif 序号 == 220 then
		引擎.场景.窗口.召唤兽仓库:存(内容.编号)
	elseif 序号 == 221 then
		引擎.场景.窗口.召唤兽仓库:取(内容.编号)
	-- elseif 序号 == 222 then
		-- 引擎.场景.窗口.钓鱼:打开()
	elseif 序号 == 223 then
		引擎.场景.窗口.商会物品上架:打开(内容)
	elseif 序号 == 224 then
		引擎.场景.队伍[1].体验状态 =  true
	elseif 序号 == 225 then
		引擎.场景.队伍[1].体验状态 =  nil
	elseif 序号 == 226 then
		引擎.场景.队伍[1].当前经验 = 内容.当前经验
		引擎.场景.队伍[1].最大经验 = 内容.最大经验
	elseif 序号 == 227 then
		if 内容.剧情 ~= nil then
			local 数据 =引擎.场景:取剧情描述(引擎.场景.队伍[1].剧情.主线,引擎.场景.队伍[1].剧情.进度,引擎.场景.队伍[1].剧情.分支)
			引擎.场景.窗口.任务栏:删除(数据[1])
			引擎.场景.队伍[1].剧情 = 内容.剧情
			-- if 内容.剧情.暂停~=nil then
			--     引擎.场景.队伍[1].剧情=nil
			-- end

			if 引擎.场景.队伍[1].剧情 ~= nil and 取剧情是否追踪(引擎.场景.队伍[1].剧情.主线) then--and 引擎.场景.队伍[1].剧情.主线~=1111 then
				local 数据 =引擎.场景:取剧情描述(引擎.场景.队伍[1].剧情.主线,引擎.场景.队伍[1].剧情.进度,引擎.场景.队伍[1].剧情.分支)
				引擎.场景.窗口.任务栏:添加(数据[1],数据[2],数据[3],数据[4])
			end
		end
	elseif 序号 == 228 then
		 引擎.场景.窗口.门派选择:打开()
	elseif 序号 == 229 then
		 -- 引擎.场景.窗口.点歌台:打开(内容)
	-- elseif 序号 == 230 then
			-- 引擎.场景.窗口.点歌播放:打开(内容.地址,内容.祝福)
	elseif 序号 == 231 then
		if not 武神坛模式 then
			引擎.场景.窗口.梦幻指引:打开(内容)
		end
			--table.print(内容)
	elseif 序号 == 231.5 then
			引擎.场景.窗口.累充礼包:打开(内容)
	elseif 序号 == 231.6 then
			引擎.场景.窗口.首冲:打开(内容)
	-- elseif 序号 == 232 then
		-- 引擎.场景.队伍[1].符石套装 = 内容
	elseif 序号 == 233 then
		引擎.场景.窗口.鉴定:打开(内容)
	elseif 序号 == 245 then --吸附石
		引擎.场景.窗口.鉴定:打开(nil,nil,true)
	elseif 序号 == 234 then
		if 内容 ~= nil then
			if 内容.类型 == 2 then
				引擎.场景.队伍[1].中秋活动 = 内容.进度
			elseif 内容.类型 == 1 then
				引擎.场景.队伍[1].国庆活动 = 内容.进度
			end
		end
	elseif 序号 == 235 then
		引擎.场景.窗口.神秘宝箱:打开(内容)
	elseif 序号 == 236 then
		if tp.队伍[1].招式特效 ~= nil and tp.队伍[1].招式特效[内容.名称] ~= nil then
			tp.队伍[1].招式特效[内容.名称] = 内容.状态

			引擎.场景.窗口.人物状态栏.资源组[46]:置打勾框(tp.队伍[1].招式特效[tp.队伍[1].师门技能[引擎.场景.窗口.人物状态栏.选中师门技能].包含技能[引擎.场景.窗口.人物状态栏.选中师门法术].名称])
		end
	elseif 序号 == 237 then
		if not 引擎.场景.窗口.改名系统.可视 then
		    引擎.场景.窗口.改名系统:打开(内容)
		end
		if 内容.名称 ~= nil then
			tp.队伍[1].名称 = 内容.名称
			-- tp.场景.人物.名称偏移 = 生成XY(tp.字体表.人物字体:取宽度(tp.队伍[1].名称) / 2,-15)
		end
	elseif 序号 == 238 then
		tp.窗口.摊位打造:打开(内容)
	elseif 序号 == 239 then
		tp.窗口.幻化:打开()
	elseif 序号 == 240 then
		tp.队伍[1].子女列表 = 内容
    elseif 序号 == 241 then
    	tp.常规提示:打开("#Y/恭喜你又学会了一种经脉！")
    	引擎.场景.队伍[1].装备属性.可用乾元丹 = 内容.可用乾元丹
    	引擎.场景.队伍[1].装备属性.已用乾元丹 = 内容.已用乾元丹
		if tp.窗口.经脉流派.可视 then
		    tp.窗口.经脉流派:学习经脉()
		end
    elseif 序号 == 242 then
    	tp.常规提示:打开("#Y/切换经脉流派成功！")
		if tp.窗口.经脉流派.可视 then
		    tp.窗口.经脉流派:切换流派()
		end
    elseif 序号 == 242.1 then
		if tp.窗口.经脉流派.可视 then
		    tp.窗口.经脉流派:打开()
		end
    elseif 序号 == 243 then
    	tp.窗口.角色转换:打开(内容)
	elseif 序号 == 244 then
    	tp.窗口.属性切换:打开(内容)
	elseif 序号 == 247 then
    	tp.窗口.属性切换:刷新(内容)
	elseif 序号 == 300 then --以下内容[1]为系统商会，临时背包 装备开运
		引擎.场景.窗口[内容[1]]:打开(内容[2])
	elseif 序号 == 301 then
		引擎.场景.窗口[内容[1]]:刷新数据(内容[2])
	elseif 序号 == 302 then
		if 引擎.场景.窗口[内容[1]].可视 then
			引擎.场景.窗口[内容[1]]:打开()
		end
	elseif 序号 == 303 then
		引擎.场景.窗口[内容[1]][内容[2]] = 内容[3]
    elseif 序号== 304 then--唱戏
		引擎.场景.窗口.唱戏界面:打开(内容)
	elseif 序号== 305 then--唱戏
		引擎.场景.窗口.唱戏交票:打开(内容)
	elseif 序号== 306 then--符石合成
		if 引擎.场景.窗口.合成.可视 then
			引擎.场景.窗口.合成:刷新()
		else
			引擎.场景.窗口.合成:打开()
		end
	elseif 序号== 307 then--光武拓印
		引擎.场景.窗口.光武拓印:打开(内容)
	elseif 序号== 308 then--光武拓印
		引擎.场景.窗口.光武拓印:刷新(内容)


	elseif 序号== 309 then
           		tp.移动速度1 = 内容.移动速度
      	elseif 序号== 309.1 then
           		tp.移动速度1 = 0
	  elseif 序号 == 310 then
	           tp.攻击速度 = 内容.攻击速度
	  elseif 序号 == 310.1 then
	           tp.攻击速度 = 0
	  elseif 序号 == 312 then
	           tp.施法速度 = 内容.施法速度
	  elseif 序号 == 312.1 then
	           tp.施法速度 = 0
	  elseif 序号 == 313 then
	  	if tp.移动速度1==40 then
	  	tp.施法速度=0
		tp.攻击速度=0
		tp.移动速度1=0
		引擎.场景.常规提示:打开("#Y你关闭了内置加速！")
	  	else
	  	tp.施法速度=1
		tp.攻击速度=1
		tp.移动速度1=40
		引擎.场景.常规提示:打开("#Y你打开了内置加速！")
		end


	elseif 序号==900 then
		local 人物组,事件组 = self:获取任务事件("枯萎的金莲",15)
		引擎.场景.第二场景:载入显示(人物组,事件组)
	elseif 序号==980 then

		 if not tp.窗口.二维码.可视 then

	        	else
	            	tp.窗口.二维码.可视=false
	      	  end

	elseif 序号==987 then
		tp.窗口.排行榜:打开(内容.内容)

	elseif 序号==999 then
		引擎.关闭() --测试模式
		f函数.信息框(内容,"下线通知")
	elseif 序号==998 then
		引擎.关闭() --测试模式
		f函数.信息框(内容,"下线通知")
	elseif 序号==998.3 then----嘎嘎武神坛
  		武神坛模式=true
	elseif 序号==998.4 then
	    	武神坛模式=false
	end
	内容=nil
end

function 回调:断开连接(连接)
	print("与服务器连接被断开1")
end

function 回调:连接断开(连接)
	if 系统退出 then
		系统退出=false
		引擎.场景.进程 = 1
		全局游戏标题="梦幻西游 ONLINE"
		引擎.置标题(全局游戏标题)
	else
		-- 引擎.场景.窗口.消息框:添加文本("服务器连接断开:"..连接) --测试模式
		-- f函数.信息框("服务器连接断开.....")
		-- if 引擎.场景~=nil then
		--   引擎.场景.进程 = 8--1
		--   全局游戏标题="梦幻西游测试 ONLINE"
		--   引擎.置标题(全局游戏标题)
		-- else
			引擎.关闭()
		-- end
		-- 引擎.场景.进程 = 1
	end
end

return 回调