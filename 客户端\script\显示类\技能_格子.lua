--======================================================================--
-- @作者: www.wnfj.com，联系QQ：79550111
-- @创建时间:   2018-03-03 02:34:19
-- @Last Modified time: 2024-12-05 17:26:08
--======================================================================--
local 系统类_技能格子 = class()
local mouse = 引擎.鼠标弹起

function 系统类_技能格子:初始化(x,y,ID,注释)
	self.x = x
	self.y = y
	self.注释 = 注释
	self.技能 = nil
	self.事件 = false
	self.焦点 = false
end

function 系统类_技能格子:置技能(技能,类型,灰色)
	if 技能 ~= nil and 技能.名称 ~= nil then
		-- table.print(技能)
		self.技能 = 技能
		self.剩余冷却回合 = 技能.剩余冷却回合
		local 临时技能
		if 类型 then
			临时技能=引擎.取技能(技能.名称,类型)
		else
	    	临时技能=引擎.取技能(技能.名称,技能.门派)
		end

		if #临时技能~=0 then
			self.技能.类型=临时技能[3]
			self.技能.模型 = 引擎.场景.资源:载入(临时技能[6],"网易WDF动画",临时技能[7])
			self.技能.小模型 = 引擎.场景.资源:载入(临时技能[6],"网易WDF动画",临时技能[8])
			self.技能.介绍=临时技能[1]
			self.技能.消耗说明=临时技能[4]
			self.技能.使用条件=临时技能[5]
			self.技能.冷却=临时技能[12]
			-- print(技能.名称,灰色)
			if 灰色 then
			    self.技能.灰色模型 = 引擎.场景.资源:载入(临时技能[6],"网易WDF动画",临时技能[7])
			    self.技能.灰色模型:灰度级()
			end
		end

		-- if 大模型 == nil then
		-- 	self.技能.模型 = 引擎.场景.资源:载入(技能.资源,"网易WDF动画",技能.大模型资源)
		-- end
		-- if 小模型 == nil and 技能.小模型 ~= nil then
		-- 	self.技能.小模型 = 引擎.场景.资源:载入(技能.资源,"网易WDF动画",技能.小模型资源)
		-- end
	else
		self.技能 = nil
	end
end

function 系统类_技能格子:置经脉(流派,技能)
	-- print(流派,技能.名称)
	if 技能 ~= nil and 技能.名称 ~= nil then
		--table.print(技能)
		self.技能 = 技能
		self.剩余冷却回合 = 技能.剩余冷却回合
		-- print(流派,技能.名称)
		local 临时技能=引擎.取经脉(流派,技能.名称)
		-- print(流派,技能.名称)
		if 临时技能~=nil then
		if #临时技能~=0 then
			self.技能.类型=临时技能[3]
			self.技能.图片=临时技能[10]
			if 临时技能[10]~=nil then
			    self.技能.模型 = 引擎.场景.资源:载入('jmk/'..tp.队伍[1].门派.."/"..技能.名称..".jpg","图片")
				-- self.技能.小模型 = 引擎.场景.资源:载入(临时技能[6],"网易WDF动画",临时技能[8])
				-- table.print(self.技能)
				-- print(技能.名称)
			else
				self.技能.模型 = 引擎.场景.资源:载入(临时技能[6],"网易WDF动画",临时技能[7])
				-- self.技能.小模型 = 引擎.场景.资源:载入(临时技能[6],"网易WDF动画",临时技能[8])
			end
			self.技能.介绍=临时技能[1]
			self.技能.消耗说明=临时技能[4]
			self.技能.使用条件=临时技能[5]
			self.技能.冷却=临时技能[12]
		end
		end

	else
		self.技能 = nil
	end

end

function 系统类_技能格子:显示(x,y,条件,xw,xy,类型,灰色)

	if self.技能 == nil then
		return
	end
	self.事件 = false
	self.焦点 = false
	-- print(self.注释,条件)
	if 灰色 then
	    if 条件 and self.技能.灰色模型:是否选中(x,y) then
			引擎.场景.按钮焦点 = true
			引擎.场景.禁止关闭 = true
			self.焦点 = true
			self.技能.灰色模型:置高亮()
			if mouse(0) then
				self.事件 = true
			end
		else
			self.技能.灰色模型:取消高亮()
		end
		if 类型 == "小" and self.技能.小模型 ~= nil  then
			self.技能.小模型:显示(self.x,self.y,xw,xy)
		else
			self.技能.灰色模型:显示(self.x,self.y,xw,xy)
		end
	else
		if 条件 and self.技能.模型:是否选中(x,y) then
			引擎.场景.按钮焦点 = true
			引擎.场景.禁止关闭 = true
			self.焦点 = true
			if self.技能.图片~=nil then
				self.技能.模型:置混合(1):置颜色(-13158601)
			else
				self.技能.模型:置高亮()
			end
			if mouse(0) then
				self.事件 = true
			end
		else
			if self.技能.图片~=nil then
				self.技能.模型:置混合(0):置颜色(4294967295)
			else
				self.技能.模型:取消高亮()
			end
			-- self.技能.模型:取消高亮()
		end
		if 类型 == "小" and self.技能.小模型 ~= nil  then
			self.技能.小模型:显示(self.x,self.y,xw,xy)
		else
		    self.技能.模型:显示(self.x,self.y,xw,xy)
		end
	end

	if self.剩余冷却回合 ~= nil then
		local 序列=self.剩余冷却回合+1
		self.技能.模型:灰度级()
		if 序列<=10 and self.技能.如意神通==nil then
		    战斗类.法宝图片[序列]:显示(self.x+12,self.y+12)
		end

	end
	local j = string.find(self.技能.名称, "超级")
	--local ani =tp.资源:载入('vvxxzcom/超级技能.wdf',"网易WDF动画",0xF6A06835)
	if j then
	tp.流光:更新()
	local 偏移x,偏移y = 等比例缩放公式(50,50,tp.流光.宽度,tp.流光.高度)
	tp.流光:显示(self.x-5,self.y-5,偏移x,偏移y)
	end
end

function 系统类_技能格子:置坐标(x,y)
	self.x = x
	self.y = y
end

return 系统类_技能格子