--======================================================================--
-- @作者: www.wnfj.com，联系QQ：79550111
-- @创建时间:   2018-03-03 02:34:19

--======================================================================--
local 系统类_物品格子 = class()
local zt,zts
local mouse = 引擎.鼠标弹起
local wps = 引擎.取物品
local 变身资源={
	浣熊=0xA60EB0D9,
	狸=0xA60EB0D9,
	章鱼=0xABB68989,
	赌徒=0x6BE81A68,
	海星=0xE709CDAD,
	大海龟=0x3C7B89E8,
	大蝙蝠=0x2481DFCC,
	海毛虫=0x3BD0B554,
	护卫=0x7003F174,
	巨蛙=0x98E3377F,
	强盗=0xD5C2566E,
	山贼=0x5F7346A8,
	树怪=0x4ED5C9C4,
	野猪=0xEF3A830D,
	蛤蟆精=0x8A53158C,
	黑熊=0xD4D2660A,
	狐狸精=0xDC14E699,
	花妖=0xD294444C,
	老虎=0x463F3E9B,
	羊头怪=0x8F19EF2A,
	骷髅怪=0xD0BE29D3,
	狼=0x92B59426,
	牛妖=0x3AF799AA,
	虾兵=0xE89179D1,
	小龙女=0xE05E7656,
	蟹将=0x65DBE48A,
	野鬼=0xB90EC617,
	龟丞相=0x67E0006E,
	黑熊精=0x35BDCFC8,
	僵尸=0xC7B126C6,
	马面=0xCA322977,
	牛头=0x06971D21,
	蜘蛛精=0xD2C2093D,
	白熊=0x7092E7F5,
	进阶白熊=0x7092E7F5,
	古代瑞兽=0x7728C3B2,
	进阶古代瑞兽=0x7728C3B2,
	黑山老妖=0x95FDC90D,
	蝴蝶仙子=0x976975FB,
	雷鸟人=0x4E646343,
	地狱战神=0xB5FE5920,
	风伯=0xDF2F3035,
	天兵=0x9AB7515F,
	天将=0x7E86C2A9,
	凤凰=0x2A4159F7,
	蛟龙=0xD4442C3A,
	大力金刚=0x64287AE7,
	鬼将=0x023AA63E,
	净瓶女娲=0x7E99A5F4,
	灵符女娲=0x5726FB84,
	灵鹤=0xACEF8DB7,
	律法女娲=0x4ED64302,
	如意仙子=0x411A18C7,
	噬天虎=0x8DC23CAE,
	雾中仙=0xA88F486E,
	吸血鬼=0x9D5FA3EC,
	星灵仙子=0xD226E204,
	巡游天神=0xFD35D4E3,
	炎魔神=0xB9917494,
	夜罗刹=0xAB1EFFB3,
	幽灵=0x62875401,
	芙蓉仙子=0xBAFBFAA8,
	猫灵兽形 =2664942248,
	猫灵人形 =1283629726,
	狂豹兽形 = 3523770813,
	狂豹人形 =1434045443,
	蝎子精 = 129453697,
	混沌兽 =2752955542,
	机关人=0xEA888609,
	画魂=0xB73FB904,
	进阶画魂=0xB73FB904,

	碧水夜叉=0x22B8518F,
	    雨师=0xF53D7AE7,
	    蚌精=0x4BAA9CBE,
	    鲛人=0x0349DFA6,
	        进阶碧水夜叉=0x22B8518F,
    进阶雨师=0xF53D7AE7,
    进阶蚌精=0x4BAA9CBE,
    进阶鲛人=0x0349DFA6,
    进阶阴阳伞=0x60AC19B3,
    阴阳伞=0x60AC19B3,
    巴蛇=0xBE748368,
    进阶巴蛇=0xBE748368,
    进阶踏云兽=0xC6F3C665,
    踏云兽=0xC6F3C665,
    进阶龙龟=0x964E7FB8,
    龙龟=0x964E7FB8,
    进阶红萼仙子=0x2D0D5755,
    红萼仙子=0x2D0D5755,
    进阶机关鸟=0x8423434C,
    机关鸟=0x8423434C,
    进阶葫芦宝贝=0x48E257D4,
    葫芦宝贝=0x48E257D4,
    进阶锦毛貂精=0xEABE811E,
    锦毛貂精=0xEABE811E,
    进阶千年蛇魅=0x65D26225,
    千年蛇魅=0x65D26225,
    进阶百足将军=0xDC338F6E,
    百足将军=0xDC338F6E,
    进阶犀牛将军人形=0x696AF38B,
    犀牛将军人形=0x696AF38B,
	进阶犀牛将军兽形=0xCABF6445,
	犀牛将军兽形=0xCABF6445,
	进阶野猪精=0xF1127EEA,
	野猪精=0xF1127EEA,
	进阶鼠先锋=0xDC338F6E,
	鼠先锋=0xDC338F6E,
	--"item.wd1"
	长眉灵猴 =3194937054,
	巨力神猿 =2583351075,
	修罗傀儡鬼 =1295304252,
	修罗傀儡妖 = 1581882750,
	金身罗汉 =2981531014,
	藤蔓妖花 =1539542949,
	曼珠沙华 =3926577754,
	蜃气妖 =2421106756,
	持国巡守 =1842568427,
	毗舍童子 =2055537949,
	真陀护法 =1518872974,
	--"common/item.wdf"
	灵灯侍者=430207498,
	琴仙=539634096,
	金饶僧=2688340638,
	泪妖=2720971822,
	镜妖=2770049521,
	般若天女=3223340062,
	增长巡守=3436403729,
	变幻莫测=3585454986,
	进阶黑山老妖=0x95FDC90D,
	进阶蝴蝶仙子=0x976975FB,
	进阶雷鸟人=0x4E646343,
	进阶地狱战神=0xB5FE5920,
	进阶风伯=0xDF2F3035,
	进阶天兵=0x9AB7515F,
	进阶天将=0x7E86C2A9,
	进阶凤凰=0x2A4159F7,
	进阶蛟龙=0xD4442C3A,
	进阶大力金刚=0x64287AE7,
	进阶鬼将=0x023AA63E,
	进阶净瓶女娲=0x7E99A5F4,
	进阶灵符女娲=0x5726FB84,
	进阶灵鹤=0xACEF8DB7,
	进阶律法女娲=0x4ED64302,
	进阶如意仙子=0x411A18C7,
	进阶噬天虎=0x8DC23CAE,
	进阶雾中仙=0xA88F486E,
	进阶吸血鬼=0x9D5FA3EC,
	进阶星灵仙子=0xD226E204,
	进阶巡游天神=0xFD35D4E3,
	进阶炎魔神=0xB9917494,
	进阶夜罗刹=0xAB1EFFB3,
	进阶幽灵=0x62875401,
	进阶芙蓉仙子=0xBAFBFAA8,
	进阶猫灵兽形 =2664942248,
	进阶猫灵人形 =1283629726,
	进阶狂豹兽形 = 3523770813,
	进阶狂豹人形 =1434045443,
	进阶蝎子精 = 129453697,
	进阶混沌兽 =2752955542,
	进阶幽萤娃娃=0xD576EDF9,
	幽萤娃娃=0xD576EDF9,
	进阶连弩车=0xD123A916,
	连弩车=0xD123A916,
	进阶机关兽=0x9655B4E8,
	机关兽=0x9655B4E8,
	--"item.wd1"
	进阶长眉灵猴 =3194937054,
	进阶巨力神猿 =2583351075,
	进阶修罗傀儡鬼 =1295304252,
	进阶修罗傀儡妖 = 1581882750,
	进阶金身罗汉 =2981531014,
	进阶藤蔓妖花 =1539542949,
	进阶曼珠沙华 =3926577754,
	进阶蜃气妖 =2421106756,
	进阶持国巡守 =1842568427,
	进阶毗舍童子 =2055537949,
	进阶真陀护法 =1518872974,
	--"common/item.wdf"
	进阶灵灯侍者=0x275209d3,
	进阶琴仙=0x202a29b0,
	进阶金饶僧=0xa8259d75,
	进阶泪妖=0xa22ebc2e,
	进阶镜妖=0xa545e77,
	进阶般若天女=0xf82a359d,
	进阶增长巡守=0xccd35c11,
	进阶变幻莫测=3585454986,
	蝎子精=0x07B74E81,
	}
local 变身资源小={
	灵灯侍者=0x725c0d0c,
	琴仙=0xe8a3bd55,
	金饶僧=0xb2a61b8f,
	泪妖=0x58549952,
	镜妖=0xb6b07738,
	般若天女=0xf966e116,
	增长巡守=0x92f193e0,
	变幻莫测=0x1a501898,
	进阶灵灯侍者=0x5c91f843,
	进阶琴仙=0xc9953a46,
	进阶金饶僧=0x7a0aafeb,
	进阶泪妖=0x62d9a3b8,
	进阶镜妖=0x2767c40b,
	进阶般若天女=0x59e4179d,
	进阶增长巡守=0x81640035,
	进阶变幻莫测=0x1a501898,
	}
function 系统类_物品格子:初始化(x,y,ID,注释,遮挡)
	self.ID = ID
	self.注释 = 注释
	self.物品 = nil
	self.事件 = false
	self.焦点 = false
	self.右键 = false
	self.遮挡 = 遮挡
	self.确定 = false
end


function 系统类_物品格子:置根(根)
	zt = tp.字体表.描边字体
	zts = tp.字体表.描边字体1
end

function 系统类_物品格子:置物品(物品,灰色)
	self.选中=nil
	if 物品 ~= nil then
		if 物品.名称 ~= 0 then
			--self.物品 = 物品
			local item = tp._物品.创建()
			item:置对象(物品.名称)
			self.物品= item
			for n, v in pairs(物品) do
      			self.物品[n]=v
		  	end
			if 物品.名称=="怪物卡片" then
				self.物品.小模型id="item.wdf"
				self.物品.资源="item.wdf"
				if 物品.等级==1 then
					self.物品.小模型资源=0x4A028BEE
				elseif 物品.等级==2 then
					self.物品.小模型资源=0xBB35E1EE
				elseif 物品.等级==3 then
					self.物品.小模型资源=0xE7FC64D2
				elseif 物品.等级==4 then
					self.物品.小模型资源=0xA00740F6
				elseif 物品.等级==5 then
					self.物品.小模型资源=0x21838782
				elseif 物品.等级==6 then
					self.物品.小模型资源=0xF1C84EB5
				elseif 物品.等级==7 then
					self.物品.小模型资源=0xEFA4BA2C
				elseif 物品.等级==8 then
					self.物品.小模型资源=0x2E030271
				elseif 物品.等级==9 then
					self.物品.小模型资源=1991581967
				end
				if 物品.造型 == "长眉灵猴" or 物品.造型 == "巨力神猿"or 物品.造型 == "修罗傀儡鬼"or 物品.造型 == "修罗傀儡妖"or 物品.造型 == "金身罗汉"or 物品.造型 == "藤蔓妖花"or 物品.造型 == "曼珠沙华"or 物品.造型 == "蜃气妖"or 物品.造型 == "持国巡守"or 物品.造型 == "毗舍童子"or 物品.造型 == "真陀护法" then
					self.物品.资源="item.wd1"
				elseif 物品.造型 == "灵灯侍者" or 物品.造型 == "琴仙"or 物品.造型 == "金饶僧"or 物品.造型 == "泪妖"or 物品.造型 == "镜妖" or 物品.造型 == "般若天女"or 物品.造型 == "增长巡守" then
					self.物品.资源="common/item.wdf"
				elseif 物品.造型 == "进阶长眉灵猴" or 物品.造型 == "进阶巨力神猿"or 物品.造型 == "进阶修罗傀儡鬼"or 物品.造型 == "进阶修罗傀儡妖"or 物品.造型 == "进阶金身罗汉"or 物品.造型 == "进阶藤蔓妖花"or 物品.造型 == "进阶曼珠沙华"or 物品.造型 == "进阶蜃气妖"or 物品.造型 == "进阶持国巡守"or 物品.造型 == "进阶毗舍童子"or 物品.造型 == "进阶真陀护法" then
					self.物品.资源="item.wd1"
				elseif 物品.造型 == "进阶灵灯侍者" or 物品.造型 == "进阶琴仙"or 物品.造型 == "进阶金饶僧"or 物品.造型 == "进阶泪妖"or 物品.造型 == "进阶镜妖" or 物品.造型 == "进阶般若天女"or 物品.造型 == "进阶增长巡守" then
					self.物品.资源="common/item.wdf"
				elseif 物品.造型 == "画魂" or 物品.造型 == "进阶画魂" or 物品.造型 == "进阶幽萤娃娃" or 物品.造型 == "幽萤娃娃" then
					self.物品.资源="vvxxzcom/carditem.wdf"
				elseif 物品.造型 == "浣熊" or 物品.造型 == "狸" or 物品.造型 == "章鱼"  or 物品.造型 == "海星"  then
					self.物品.资源="item.wd1"
				end
				self.物品.大模型资源=变身资源[物品.造型]

				 --print(self.物品.大模型资源)
			-- elseif 物品.名称=="祈愿宝箱" then
			-- 	self.物品.pyz2 = {-100,0}
			end
			local 返回值 = 引擎.取同名物品(物品.名称,物品.子类,物品.类型,物品.等级)
			if not 判断是否为空表(返回值) then
				-- table.print(返回值)
				self.物品.资源 = 返回值[5] or 返回值[1]

				self.物品.小模型资源 = 返回值[2]
				if 返回值[5] then
					self.物品.url = 返回值[1]
					self.物品.小模型资源 = 返回值[4]
				end
				self.物品.大模型资源 = 返回值[3]
				if 返回值[6] then
					self.物品.pyz = 返回值[6] --动态
				end
			end

			if self.物品.小模型 == nil then
      			self.物品.小模型 = tp.资源:载入(self.物品.小模型id or self.物品.资源,"网易WDF动画",self.物品.小模型资源)
      			if 灰色 then
      				-- print(灰色)
					self.物品.灰色小模型 = tp.资源:载入(self.物品.小模型id or self.物品.资源,"网易WDF动画",self.物品.小模型资源)
					self.物品.灰色小模型:灰度级()
				end
			end
		end
	else
		self.物品 = nil
	end
	if self.物品~=nil then
		if self.物品.回合~=nil then
			self:置灰色()
		end
	end
end

function 系统类_物品格子:置灰色()
	if self.物品~=nil and self.物品.小模型~=nil then --and not self.物品.小模型.灰度
		self.物品.小模型:灰度级()
	end
end

function 系统类_物品格子:物品禁止(总类)
	if self.物品 ~= nil and ((总类 and 总类 ~= false) and self.物品.总类 ~= 总类[1] and self.物品.总类 ~= 总类[2] and (总类[3]==nil or 总类[3]==false)) then
		return true
	else
		return  false
	end
end

function 系统类_物品格子:显示(dt,x,y,条件,总类,xx,yy,abs,物品详情,不显示数字,灰色) --dt1,x2,y3,条件4,总类5,xx6,yy7,abs8,物品详情9,不显示数字10,灰色11
	if self.确定 or self.选中 then
		-- tp.物品格子确定_:显示(self.x+1,self.y+2)
		tp.物品格子确定_:显示(self.x+2,self.y+4)
	end
	self.事件 = false
	self.焦点 = false
	self.右键 = false
	if self.物品 ~= nil then
		if self.遮挡 ~= nil then
			self.遮挡:显示(self.x-1+(xx or 0),self.y+4+(yy or 0))
		end
		local pyx,pyy = 0,0
		if self.物品.pyz~=nil then
			-- if 灰色 then
			-- 	self.物品.灰色小模型:更新(dt)
			-- else
				self.物品.小模型:更新(dt)
			-- end
			pyx,pyy = self.物品.pyz[1],self.物品.pyz[2]
		end
		if 灰色 then
			self.物品.灰色小模型:显示(self.x-1+pyx,self.y+2+pyy)
		else
			if self.物品.名称=="甜蜜糖果" then
			self.物品.小模型:显示(self.x+24,self.y+27)
			elseif self.物品.名称=="仙露丸" or self.物品.名称=="高级仙露丸"  then
				self.物品.小模型:显示(self.x,self.y+10)
			else

			self.物品.小模型:显示(self.x-1+pyx,self.y+2+pyy)
			end

		end
		-- self.物品.小模型:显示(self.x-1+pyx,self.y+2+pyy)
		if self.物品.回合~=nil then
			local 数量=string.len(tostring(self.物品.回合))
			for i=1,数量 do
				local 序列=string.sub(self.物品.回合,i,i)+1
				if 战斗类.法宝图片[序列]~=nil then
					战斗类.法宝图片[序列]:显示(self.x-数量*2-数量*1.5+20,self.y+20)
				end
			end
		end
		if self.物品.可叠加 then--and 总类 == nil then --这个总类是？
			if 不显示数字~=true then
				zt:置颜色(4294967295)
				zt:置描边颜色(-16777216)
				zt:显示(self.x + 3,self.y + 3,self.物品.数量)
			end
		end

		local 自适应 = tp._自适应
		if self:物品禁止(总类) then
			tp.物品格子禁止_:显示(self.x + 5,self.y + 6)
			-- self:置灰色()
		-- else
		-- 	if self.物品~=nil and self.物品.小模型~=nil and self.物品.小模型.灰度 then
		-- 		self.物品.小模型:重置纹理()
		-- 	end
		end
		if 物品详情 then
			tp.xqdt1:显示(self.x-5,self.y+31)
			xqwz1:显示(self.x + ((44 - xqwz1:取宽度(物品详情))/2),self.y + 31,物品详情)
		elseif self.物品.名称=="特殊兽决·碎片" or self.物品.名称=="超级兽决·碎片" then
			tp.xqdt1:显示(self.x-5,self.y+31)
			xqwz1:显示(self.x + ((44 - xqwz1:取宽度("碎片"))/2),self.y + 31,"碎片")
		end
		if not self.物品.可叠加 and 总类 == nil and 自适应 then
			if  self.物品.附带技能~=nil then
			zts:置颜色(黄色)
			tp.xqdt1:显示(self.x-5,self.y+31)
			zts:显示(self.x + 0,self.y + 30,取兽决缩写(self.物品.附带技能))
			end
		end
		if not self.物品.可叠加 and 自适应 and (self.物品.名称 == "藏宝图" or self.物品.名称 == "高级藏宝图"  or self.物品.名称 == "玲珑宝图") then
			if  self.物品.地图名称~=nil then
			zts:置颜色(黄色)
			tp.xqdt1:显示(self.x-5,self.y+31)
			zts:显示(self.x + -1,self.y + 30,self.物品.地图名称)
			end
		end
		if not self.物品.可叠加 and 自适应 and (self.物品.名称 == "百炼精铁" or self.物品.名称 == "元灵晶石"   )  then
			if  self.物品.子类~=nil then
			zts:置颜色(黄色)
			tp.xqdt1:显示(self.x-5,self.y+31)
			zts:显示(self.x + 8,self.y + 30,self.物品.子类.."级")
			end
		end

		if not self.物品.可叠加 and 自适应 and self.物品.子类 == 1  and self.物品.分类== 1  and self.物品.总类==203  and (self.物品.名称 == "召唤兽内丹" or self.物品.名称 == "高级召唤兽内丹"   )  then
			if  self.物品.特效~=nil then
			zts:置颜色(黄色)
			tp.xqdt1:显示(self.x-5,self.y+31)
				if self.物品.名称 == "高级召唤兽内丹"  then
				zts:显示(self.x + 4,self.y + 30,self.物品.特效)
				else
				zts:显示(self.x + 10,self.y + 30,self.物品.特效)
				end
			end
		end
		if not self.物品.可叠加 and 自适应 and self.物品.子类 == 3  and self.物品.分类== 1  and self.物品.总类=="灵犀玉"  then
			if  self.物品.特性~=nil then
			zts:置颜色(黄色)
			tp.xqdt1:显示(self.x-5,self.y+31)
			zts:显示(self.x + 10,self.y + 30,self.物品.特性)
			end
		end
		-- if not self.物品.可叠加 and 自适应 and self.物品.子类 == 1  and self.物品.分类== 1  and self.物品.总类==30  and (self.物品.名称 == "怪物卡片"  )  then
		-- 	zts:置颜色(黄色)
		-- 	zts:显示(self.x + 10,self.y + 30,self.物品.造型)
		-- end
		if not self.物品.可叠加 and 自适应 and self.物品.总类 == 5 and self.物品.分类== 6 or self.物品.名称 == "炼妖石" or self.物品.名称 == "天眼珠"  or self.物品.名称 == "九眼天珠"   or self.物品.名称 == "三眼天珠"   then
			if  self.物品.名称 ~= "陨铁" and  self.物品.名称 ~= "战魄"  and  self.物品.名称 ~= "精致碎石锤"   then
				if  self.物品.级别限制~=nil then
				zts:置颜色(黄色)
				tp.xqdt1:显示(self.x-5,self.y+31)
				zts:显示(self.x + 10,self.y + 30,self.物品.级别限制.."级")
				end
			end
		end

		if not self.物品.可叠加 and 自适应  and (self.物品.名称 == "天眼珠" or self.物品.名称 == "三眼天珠"  or self.物品.名称 == "九眼天珠" or self.物品.名称 == "炼妖石" or self.物品.名称=="上古锻造图策" )  then
			if  self.物品.次数~=nil then
			local 实际等级=self.物品.次数*10-5
			zts:置颜色(黄色)
			tp.xqdt1:显示(self.x-5,self.y+31)
			zts:显示(self.x + 8,self.y + 30,实际等级.."级")
			end
		end


		if not self.物品.可叠加 and 自适应 and self.物品.名称 == "珍珠" then
			if  self.物品.子类~=nil then
				if self.物品.子类~= 20 then
					zts:置颜色(黄色)
					tp.xqdt1:显示(self.x-5,self.y+31)
					zts:显示(self.x + 10,self.y + 30,self.物品.子类.."级")
				else
				zts:置颜色(黄色)
				tp.xqdt1:显示(self.x-5,self.y+31)
				zts:显示(self.x + 10,self.y + 30,self.物品.级别限制.."级")
				end
			--zts:显示(self.x + -1,self.y + 15,self.物品.种类)
			end
		end
		-- if not self.物品.可叠加 and 自适应 and (self.物品.名称 == "炼妖石" or self.物品.名称 == "天眼珠" or self.物品.名称 == "九眼天珠"   or self.物品.名称 == "三眼天珠"   ) then
		-- 	zts:置颜色(黄色)
		-- 	zts:显示(self.x + -1,self.y + 30,self.物品.级别限制.."级")
		-- end
		if not self.物品.可叠加 and 自适应 and (self.物品.名称 == "精魄灵石"  ) then
			if  self.物品.级别限制~=nil then
			zts:置颜色(黄色)
			tp.xqdt1:显示(self.x-5,self.y+31)
			zts:显示(self.x + 10,self.y + 30,self.物品.级别限制.."级")
			end
		end
		if 自适应 and (self.物品.名称 == "鬼谷子"  ) then
			if self.物品.子类 then
			zts:置颜色(黄色)
			tp.xqdt1:显示(self.x-5,self.y+31)
			zts:显示(self.x + 5,self.y + 30,self.物品.子类)
			else
			zts:置颜色(黄色)
			tp.xqdt1:显示(self.x-5,self.y+31)
			zts:显示(self.x + 1,self.y + 30,"购买随机")
			end
		end
		if 自适应 and (self.物品.名称 == "桂花酒酿元宵"  ) then
			zts:置颜色(黄色)
			tp.xqdt1:显示(self.x-5,self.y+31)
			zts:显示(self.x + 10,self.y + 30,"防御")
		end
		if  自适应 and (self.物品.名称 == "芝麻沁香元宵"  ) then
			zts:置颜色(黄色)
			tp.xqdt1:显示(self.x-5,self.y+31)
			zts:显示(self.x + 10,self.y + 30,"攻击")
		end
		if  自适应 and (self.物品.名称 == "细磨豆沙元宵"  ) then
			zts:置颜色(黄色)
			tp.xqdt1:显示(self.x-5,self.y+31)
			zts:显示(self.x + 10,self.y + 30,"速度")
		end
		if  自适应 and (self.物品.名称 == "蜜糖腰果元宵"  ) then
			zts:置颜色(黄色)
			tp.xqdt1:显示(self.x-5,self.y+31)
			zts:显示(self.x + 10,self.y + 30,"躲避")
		end
		if 自适应 and (self.物品.名称 == "山楂拔丝元宵"  ) then
			zts:置颜色(黄色)
			tp.xqdt1:显示(self.x-5,self.y+31)
			zts:显示(self.x + 10,self.y + 30,"体力")
		end
		if  自适应 and (self.物品.名称 == "滑玉莲蓉元宵"  ) then
			zts:置颜色(黄色)
			tp.xqdt1:显示(self.x-5,self.y+31)
			zts:显示(self.x + 10,self.y + 30,"法力")
		end

		if  自适应 and (self.物品.名称 == "月卡" or  self.物品.名称 == "抽奖卡"  or  self.物品.名称 == "自动抓鬼卡" or  self.物品.名称 == "自动回收卡"   ) then
			zts:置颜色(黄色)
			tp.xqdt1:显示(self.x-5,self.y+31)
			if self.物品.名称 == "月卡" then
			zts:显示(self.x + 10,self.y + 30,self.物品.名称)
			end
			if self.物品.名称 == "抽奖卡" then
			zts:显示(self.x + 4,self.y + 30,self.物品.名称)
			end
			if self.物品.名称 == "自动抓鬼卡"    then
			zts:显示(self.x + 4,self.y + 30,"抓鬼卡")
			end
			if   self.物品.名称 == "自动回收卡" then
			zts:显示(self.x + 4,self.y + 30,"回收卡")
			end
		end
		if not self.物品.可叠加 and 自适应 and self.物品.名称 == "制造指南书" then
			if self.物品.特效~=nil and self.物品.子类~=nil then
	    		local it = tp:取武器子类(self.物品.特效)
			zts:置颜色(黄色)
			tp.xqdt1:显示(self.x-5,self.y+31)
			zts:显示(self.x + -1,self.y + 30,self.物品.子类.."级"..it)
			--zts:显示(self.x + -1,self.y + 15,it)
			end
		end
		if not self.物品.可叠加 and 自适应 and self.物品.名称 == "150制造指南书" then
			if self.物品.特效~=nil and self.物品.子类~=nil then
	    		local it = tp:取武器子类(self.物品.特效)
			zts:置颜色(黄色)
			tp.xqdt1:显示(self.x-5,self.y+31)
			--zts:显示(self.x + -1,self.y + 30,self.物品.子类.."级"..it)
				if it ~= nil then
					zts:显示(self.x + -1,self.y + 15,it)
				end
			end
		end
		if not self.物品.可叠加 and 自适应 and self.物品.名称 == "灵饰指南书" then
			zts:置颜色(黄色)
			tp.xqdt1:显示(self.x-5,self.y+31)
			if self.物品.子类~=nil and self.物品.特效~=nil then
			zts:显示(self.x + -1,self.y + 30,self.物品.子类.."级"..self.物品.特效)
			--zts:显示(self.x + -1,self.y + 15,self.物品.特效)
			end
		end

		if not self.物品.可叠加 and 自适应 and self.物品.名称 == "上古锻造图策" then
			if  self.物品.种类~=nil then
			zts:置颜色(黄色)
			tp.xqdt1:显示(self.x-5,self.y+31)
			zts:显示(self.x + -1,self.y + 30,self.物品.级别限制.."级"..self.物品.种类)
			--zts:显示(self.x + -1,self.y + 15,self.物品.种类)
			end
		end
		if ((总类 and 总类 ~= false) and self.物品.总类 ~= 总类[1] and self.物品.总类 ~= 总类[2] and (总类[3]==nil or 总类[3]==false)) then
			tp.物品格子禁止_:显示(self.x + 5,self.y + 6)
		end
	end





	if not tp.消息栏焦点 then
		if x>=self.x and x<=self.x+50 and y>=self.y and y<=self.y+50 and 条件 then
			tp.按钮焦点 = true
			tp.禁止关闭 = true
			if mouse(0) and not self:物品禁止(总类) then
				self.事件 = true
			elseif mouse(1) then
				self.右键 = true
			end
			if self.格子显示 == nil then
				if self.物品 and self.物品.大模型 == nil then
					if self.物品.url~=nil then
						self.物品.大模型 = tp.资源:载入(self.物品.url,"网易WDF动画",self.物品.大模型资源)
					else
						self.物品.大模型 = tp.资源:载入(self.物品.资源,"网易WDF动画",self.物品.大模型资源)
					end
				end
				if abs == nil then
					-- tp.物品格子焦点_:显示(self.x+1,self.y+1)
					tp.物品格子焦点_:显示(self.x+1,self.y+3)
				end
			end
			self.焦点 = true
		end
	end

end

function 系统类_物品格子:置坐标(x,y)
	self.x,self.y = x,y
end

return 系统类_物品格子