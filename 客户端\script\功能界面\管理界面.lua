--======================================================================--
-- @作者: www.wnfj.com，联系QQ：79550111
-- @创建时间:   2018-03-03 02:34:19
-- @Last Modified time: 2024-12-05 17:26:06
--============================,==========================================--
local 场景类_管理界面 = class()
local qmx = 引擎.取模型
local zts,zts1,tp
local insert = table.insert
--======================================================================--

function 场景类_管理界面:初始化(根)
	self.ID = 101
	self.x = 218
	self.y = 140
	self.xx = 0
	self.yy = 0
	self.注释 = "账号管理"
	self.可视 = false
	self.鼠标 = false
	self.焦点 = false
	self.可移动 = true
	local 资源 = 根.资源
	local 按钮 = 根._按钮
	local 自适应 = 根._自适应
	self.资源组 = {
		[1] = 自适应.创建(0,1,400,329,3,9),
		[2] = 按钮.创建(自适应.创建(18,4,16,16,4,3),0,0,4,true,true),
		[3] = 按钮.创建(自适应.创建(12,4,55,22,1,3),0,0,4,true,true,"读取"),
		[4] = 按钮.创建(自适应.创建(12,4,55,22,1,3),0,0,4,true,true,"公告"),
		[5] = 按钮.创建(自适应.创建(12,4,55,22,1,3),0,0,4,true,true,"消息"),
		[6] = 按钮.创建(自适应.创建(12,4,55,22,1,3),0,0,4,true,true,"确认"),
		[7] = 自适应.创建(3,1,120,19,1,3),
		[8] = 自适应.创建(2,1,185,120,3,9),
	}
	for n=2,6 do
	    self.资源组[n]:绑定窗口_(self.ID)
	end
	self.资源组[3]:置偏移(-1,0)
	self.控件类 = require("ggeui/加载类")()
	local 总控件 = self.控件类:创建控件('消息总控件')
	总控件:置可视(true,true)
	self.消息输入框 = 总控件:创建输入("消息输入",0,0,175,16)
	self.消息输入框:置可视(false,false)
	self.消息输入框:置限制字数(60)
	self.消息输入框:置多行(true)
	self.消息输入框:屏蔽快捷键(true)
	self.消息输入框:置光标颜色(-16777216)
	self.消息输入框:置文字颜色(-16777216)
	self.窗口时间 = 0
	self.提现数据 = {}
	self.偏移x = 1
	self.偏移y = 1
	tp = 根
	zts = tp.字体表.普通字体
	zts1 = tp.字体表.描边字体
end

function 场景类_管理界面:打开()
	if self.可视 then
		self.可视 = false
		self.二维码 = nil
		self.账号数据 = {}
		self.消息输入框:置可视(false,false)
		self.消息输入框:置文本("")
		self.提现数据 = {}
	else
		insert(tp.窗口_,self)
	    tp.运行时间 = tp.运行时间 + 1
	    self.窗口时间 = tp.运行时间
	    self.可视 = true
	end
end

function 场景类_管理界面:刷新提现(sj)
	if sj.收款码 ~= nil then
		local l = string.len(sj.收款码)
		-- self.二维码 = 引擎.场景.资源:载入({sj.收款码,l},"网络图片")
		self.偏移x,self.偏移y = 等比例缩放公式(168,230,self.二维码.宽度,self.二维码.高度)
	end
	self.提现数据.人民币 = sj.人民币
	self.提现数据.id = sj.id
	self.提现数据.账号 = sj.账号
end

function 场景类_管理界面:显示(dt,x,y)
	self.焦点=false
	self.资源组[2]:更新(x,y)
	self.资源组[3]:更新(x,y)
	self.资源组[4]:更新(x,y)
	self.资源组[5]:更新(x,y)
	self.资源组[6]:更新(x,y)
	self.控件类:更新(dt,x,y)
	if self.资源组[6]:事件判断() then
		发送数据(6407,{账号=self.提现数据.账号,id=self.提现数据.id,人民币=self.提现数据.人民币})
		self.二维码 = nil
		self.提现数据 = {}
	elseif self.资源组[5]:事件判断() then
		if self.消息输入框:取文本() ~= "" then
			发送数据(6408,{消息=self.消息输入框:取文本()})
			self.消息输入框:置文本("")
		else
			引擎.场景.常规提示:打开("#Y/温馨提示：你还未输入内容")
		end
	elseif self.资源组[4]:事件判断() then
		if self.消息输入框:取文本() ~= "" then
			发送数据(6409,{消息=self.消息输入框:取文本()})
			self.消息输入框:置文本("")
		else
			引擎.场景.常规提示:打开("#Y/温馨提示：你还未输入内容")
		end
	elseif self.资源组[3]:事件判断() then
		发送数据(6406)
	end
	self.资源组[1]:显示(self.x,self.y)
	tp.窗口标题背景_:显示(self.x-76+self.资源组[1].宽度/2,self.y)
	引擎.场景.字体表.标题字体:显示(self.x+self.资源组[1].宽度/2,self.y+3,"管理界面")
	self.资源组[2]:显示(self.x-18+self.资源组[1].宽度,self.y+2)
	self.资源组[3]:显示(self.x+126,self.y+300)
	self.资源组[4]:显示(self.x+198+130,self.y+300)
	self.资源组[5]:显示(self.x+198+65,self.y+300)
	self.资源组[6]:显示(self.x+198,self.y+300)
	self.资源组[7]:显示(self.x+265,self.y+33)
	self.资源组[7]:显示(self.x+265,self.y+33+(24*1))
	self.资源组[7]:显示(self.x+265,self.y+33+(24*2))
	self.资源组[8]:显示(self.x+198,self.y+105)
	zts:置颜色(0xFFFFFFFF)
	zts:显示(self.x+198,self.y+33,"账    号:")
	zts:显示(self.x+198,self.y+33+(24*1),"人 民 币:")
	zts:显示(self.x+198,self.y+33+(24*2),"I    D:")
	if self.提现数据.账号 ~= nil then
		zts:置颜色(0xFF000000)
		zts:显示(self.x+270,self.y+35,self.提现数据.账号)
		zts:显示(self.x+270,self.y+35+(24*1),self.提现数据.人民币)
		zts:显示(self.x+270,self.y+35+(24*2),self.提现数据.id)
		zts:置颜色(0xFFFFFF00)
		if self.二维码 ~= nil then
			self.二维码:显示(self.x+13,self.y+33,self.偏移x,self.偏移y)
		end
	end
	self.控件类:显示(x,y)
	self.消息输入框:置坐标(self.x+203,self.y+115)
	self.消息输入框:置可视(true,true)
end

function 场景类_管理界面:检查点(x,y)
	if self.可视 and self.资源组[1]:是否选中(x,y)  then
		return true
	end
end

function 场景类_管理界面:初始移动(x,y)
	tp.运行时间 = tp.运行时间 + 1
	if not tp.消息栏焦点 then
  		self.窗口时间 = tp.运行时间
 	end
	if not self.焦点 then
		tp.移动窗口 = true
	end
	if self.可视 and self.鼠标 and  not self.焦点 then
		self.xx = x - self.x
		self.yy = y - self.y
	end
end

function 场景类_管理界面:开始移动(x,y)
	if self.可视 and self.鼠标 then
		self.x = x - self.xx
		self.y = y - self.yy
	end
end

return 场景类_管理界面