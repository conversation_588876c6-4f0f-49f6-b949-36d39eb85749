--======================================================================--
-- @作者: www.wnfj.com，联系QQ：79550111
-- @创建时间:   2018-03-03 02:34:19
-- @Last Modified time: 2024-12-05 17:26:06
--================,======================================================--
local 场景类_宠物领养栏 = class()
local insert = table.insert
local tp,zts1
local mouseb = 引擎.鼠标弹起
local qmx = 引擎.取模型
function 场景类_宠物领养栏:初始化(根)
	self.ID = 27
	self.x = 230
	self.y = 135
	self.xx = 0
	self.yy = 0
	self.注释 = "宠物领养栏"
	self.可视 = false
	self.鼠标 = false
	self.焦点 = false
	self.可移动 = true
	local 资源 = 根.资源
	local 按钮 = 根._按钮
	local 自适应 = 根._自适应
	self.资源组 = {
		[1] = 自适应.创建(0,1,560,346,3,9),
		[2] = 按钮.创建(自适应.创建(18,4,16,16,4,3),0,0,4,true,true),
		[3] = 按钮.创建(自适应.创建(12,4,75,22,1,3),0,0,4,true,true,"  领 养"),
		[4] = 自适应.创建(2,1,98,95,3,9),
	}
	self.宠物说明 = {
	{"生肖鼠","十二生肖里最精明能干的动物，知识丰富充满智慧，既有魅力又充满侵略性。","★鼠可以让召唤兽的灵力成长有轻微的加成"},
	{"生肖牛","稳重.辛勤.诚恳.沉默寡言，是最嘉的完成者和执行者，强大而持久的耐力是牛的优点，但也不可掩饰牛也略微显得缓慢和极端固执。","★牛可以让召唤兽的体质有轻微的加成"},
	{"生肖虎","威猛而充满创造力，特立独行不拘于常规，特别拥有自我意识，老虎似乎天生就有赚钱的运气和本能。","★虎可以让召唤兽的忠诚度有轻微的加成"},
	{"生肖兔","优雅而高贵.对艺术.音乐.历史和文学都有强烈的兴趣。他们喜欢和平安静。但这并不意味兔子是一种软弱可欺的动物。","★兔可以让召唤兽的敏捷有轻微的加成"},
	{"生肖龙","具有权威性.对任何事物都充满热情.而且似乎永不之疲倦，他们的身上有散发着健康的气息和无穷的精力。","★龙可以让召唤兽捕捉成功率提升"},
	{"生肖蛇","美丽的生物，他们都比较孤芳自赏。当然,蛇也确实漂亮和聪明。它们多半控制欲比较强。 ","★蛇可以让召唤兽的法力成长有轻微的加成"},
	{"生肖马","追求自由的动物，可能会有点独断专行。马的学习能力也非常强。不过这也导致马有时候可能三心二意。","★马练妖时对于技能的继承能力很强,经常可以练出继承极品技能的召唤兽来",6},
	{"生肖羊","最具有创造力。不过他们通常都需要有人催逼才能成功。在天赋之下羊又可以承担巨大的工作量，即使是枯燥而漫长的工作也能完成。","★羊可以让炼妖成功的召唤兽成长提高",7},
	{"生肖猴","稳定而且正直，又有点女子气。总是受人喜爱。而最令人欣赏的是猴解决问题的能力。他们多半对什么都很有兴趣。","★猴可以让召唤兽的各方面都有轻微加成",6},
	{"生肖鸡","比较夸张.喜欢追随时尚和流行。热爱修饰。虽然看起来比较另类，其实鸡是很保守的。它们喜欢尝试各种东西。","★鸡可以让召唤兽的寿命大大增加",6},
	{"生肖狗","是正义的代名词，他们考虑周详。聪明有信心.诚实.忠心.勇于献身。对于主人有着最忠诚的个性。狗最擅长契而不舍的完成一件事","★狗可以让召唤兽的耐力有轻微的加成",7},
	{"生肖猪","强壮而温和.吸引人而诚实。不善言辞，但很喜欢舒适的生活。但猪总是相当精明。而且它们也确实有令人羡慕的好运气。","★猪可以让玩家的运气轻微加成",6}
	}
	self.动画组 = {}
	for i=1,12 do
		local n = qmx(self.宠物说明[i][1])
		self.动画组[i] = 资源:载入(n[3],"网易WDF动画",n[1])
		self.动画组[i]:置方向(4)
	end
	self.选中宠物 = 0
	self.介绍文本 = 根._丰富文本(118,600,根.字体表.普通字体)
	for n=2,3 do
	    self.资源组[n]:绑定窗口_(self.ID)
	end
	self.窗口时间 = 0
	tp = 根
	zts1 = 根.字体表.描边字体
end

function 场景类_宠物领养栏:打开()
	if self.可视 then
		self.介绍文本:清空()
		self.选中宠物 = 0
		self.可视 = false
	else
		if tp.宠物.领取 ~= nil then
			tp.常规提示:打开("#Y/已经领养过一次宠物了")
			return
		end
		insert(tp.窗口_,self)
		self.介绍文本:清空()
		self.选中宠物 = 0
		tp.运行时间 = tp.运行时间 + 1
	    self.窗口时间 = tp.运行时间
		self.可视 = true
	end
end

function 场景类_宠物领养栏:显示(dt,x,y)
	self.资源组[2]:更新(x,y)
	self.资源组[3]:更新(x,y,self.选中宠物 ~= 0)
	if self.资源组[2]:事件判断() then
		self:打开()
		return
	elseif self.资源组[3]:事件判断() then
	 	发送数据(6,{cw=self.宠物说明[self.选中宠物][1]},1)
	end
	self.焦点 = false
	self.资源组[1]:显示(self.x,self.y)
	tp.窗口标题背景_:显示(self.x-76+self.资源组[1].宽度/2,self.y)
	引擎.场景.字体表.标题字体:显示(self.x+self.资源组[1].宽度/2,self.y+3,"宠物领养")
	self.资源组[2]:显示(self.x-18+self.资源组[1].宽度,self.y+2)
	self.资源组[3]:显示(self.x + 450,self.y + 309,true)
	self.介绍文本:显示(self.x + 430,self.y + 40)
	local xx = 0
	local yy = 0
	for i=1,12 do
		local jx = self.x+13+xx*102
		local jy = self.y+33+yy*101
		self.资源组[4]:显示(jx,jy)
		tp.影子:显示(jx + 45,jy + 74)
		self.动画组[i]:更新(dt)
		self.动画组[i]:显示(jx + 45,jy + 74)
		self.动画组[i]:取消高亮()
		if self.动画组[i]:是否选中(x,y) then
			self.焦点 = true
			self.动画组[i]:置高亮()
			if mouseb(0) then
				self.选中宠物 = i
				self.介绍文本:清空()
				self.介绍文本:添加文本("#Y/"..self.宠物说明[self.选中宠物][1])
				self.介绍文本:添加文本("#Y/"..self.宠物说明[self.选中宠物][2])
				self.介绍文本:添加文本("#Y/"..self.宠物说明[self.选中宠物][3])
			end
		end
		xx = xx + 1
		if xx > 3 then
			xx = 0
			yy = yy + 1
		end
	end
end

function 场景类_宠物领养栏:检查点(x,y)
	if self.资源组[1]:是否选中(x,y)  then
		return true
	end
end

function 场景类_宠物领养栏:初始移动(x,y)
	tp.运行时间 = tp.运行时间 + 1
	if not tp.消息栏焦点 then
  		self.窗口时间 = tp.运行时间
 	end
	if not self.焦点 then
		tp.移动窗口 = true
	end
	if self.鼠标 and not self.焦点 then
		self.xx = x - self.x
		self.yy = y - self.y
	end
end

function 场景类_宠物领养栏:开始移动(x,y)
	if self.鼠标 then
		self.x = x - self.xx
		self.y = y - self.yy
	end
end

return 场景类_宠物领养栏