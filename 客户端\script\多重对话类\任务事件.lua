--======================================================================--
-- @作者: www.wnfj.com，联系QQ：79550111
-- @创建时间:   2018-03-03 02:34:19
-- @Last Modified time: 2024-12-05 17:26:07
--======================================================================--
function 引擎.场景:获取任务事件(任务,编号,角色,NPC)
	local 人物组 = {}
	local 事件组 = {}
	if 任务 == 1 then
		if 编号 == 12 then
			if self.队伍[1].模型=="影精灵" then
				人物组 = {{名称="",模型="竹筏",X=107,Y=56,方向=0,编号=1},{主角=true,模型="骨精灵坐下",X=107,Y=56,方向=1,编号=2,最后帧方式=2}}
				事件组 = {
					[1]={[[引擎.场景.第二场景:置人物移动(0,1,91,77)]],[[引擎.场景.第二场景:置人物移动(0,2,91,77)]],[[引擎.场景.第二场景:加入喊话(100,2,"游湖游湖好开心#84#84#84#84",9999)]],[[引擎.场景.第二场景:加进度(70,1)]]},
					[2]={[[引擎.场景.第二场景:加入文本(400,nil,"游湖好开心！")]]}
				}
			else
			人物组 = {{名称="",模型="竹筏",X=107,Y=56,方向=0,编号=1},{主角=true,模型=self.队伍[1].模型.."坐下",X=107,Y=56,方向=1,编号=2,最后帧方式=2}}
			事件组 = {
				[1]={[[引擎.场景.第二场景:置人物移动(0,1,91,77)]],[[引擎.场景.第二场景:置人物移动(0,2,91,77)]],[[引擎.场景.第二场景:加入喊话(100,2,"游湖游湖好开心#84#84#84#84",9999)]],[[引擎.场景.第二场景:加进度(70,1)]]},
				[2]={[[引擎.场景.第二场景:加入文本(400,nil,"游湖好开心！")]]}
			}
			end
		end
	elseif 任务 == 2 then
		if 编号 == 14 then
			人物组 = {{名称="神秘人",模型="夜罗刹",X=227,Y=21,方向=3,编号=1},{名称="老孙头",模型="男人_老孙头",X=223,Y=12,方向=1,编号=2},{名称="陈长寿",模型="男人_药店老板",X=220,Y=21,方向=1,编号=3},{名称="罗招弟",模型="小孩_飞儿",X=219,Y=28,方向=1,编号=4},{名称="观音姐姐",模型="观音姐姐",X=235,Y=23,方向=2,编号=5,不允许加入 = true},{主角=true,模型=self.队伍[1].模型,X=235,Y=16,方向=1,编号=6}}
			事件组 = {
				[1]={[[引擎.场景.第二场景:电影片头(0)]],[[引擎.场景.第二场景:加入喊话(50,2,"妖怪啊好可怕#52",100)]],[[引擎.场景.第二场景:加入喊话(100,3,"大家快跑啊#81",100)]],[[引擎.场景.第二场景:加入喊话(150,6,"大家不要慌有我在#81,你是何方妖怪",100)]],[[引擎.场景.第二场景:加入喊话(200,1,"我是谁，到了阎王那你自然就知道了#18",100)]],[[引擎.场景.第二场景:加进度(250,1)]]},
				[2]={[[引擎.场景.第二场景:加入特效(0,6,"烈火")]],[[引擎.场景.第二场景:加入喊话(50,6,"好热好热#15",100)]],[[引擎.场景.第二场景:置入人物(100,{5},{"坐莲"})]],[[引擎.场景.第二场景:加入喊话(150,5,"唵嘛呢叭咪吽！",100)]],[[引擎.场景.第二场景:加入特效(200,6,"杨柳甘露")]],[[引擎.场景.第二场景:加入喊话(250,1,"观音大士，小子算你走运#101",100)]],[[引擎.场景.第二场景:加入喊话(300,6,"多谢观音大士，这个妖怪是谁为什么要来杀我",100)]],[[引擎.场景.第二场景:加入喊话(350,2,"这这........(愣在当场的吃瓜群众#24)",100)]],[[引擎.场景.第二场景:加入喊话(400,5,"你的力量还太弱小，等你变的强大后可去北俱芦洲寻找女娲神迹传送人")]],[[引擎.场景.第二场景:加入喊话(450,6,"好的观音大士我会记住的",100)]],[[引擎.场景.第二场景:结束剧情(650)]]}
			}
		end
	elseif 任务 == 6 then
		if 编号 == 10 then
			人物组 = {{名称="神秘人",模型="夜罗刹",X=25,Y=28,方向=3,编号=1,不允许加入 = true},{名称="女娲神迹传送人",模型="净瓶女娲",X=22,Y=25,方向=0,编号=2,不允许加入 = true},{名称="美猴王",模型="孙悟空",X=28,Y=29,方向=2,编号=3,不允许加入 = true},{主角=true,模型=self.队伍[1].模型,X=29,Y=25,方向=1,编号=4}}
			事件组 = {
				[1]={[[引擎.场景.第二场景:电影片头(0)]],[[引擎.场景.第二场景:置入人物(0,{1},{"杳无音讯"})]],[[引擎.场景.第二场景:加入喊话(50,1,"小子又是你这回看看还有谁能救你",100)]],[[引擎.场景.第二场景:加入喊话(100,4,"你和春三十娘是一伙的#81",100)]],[[引擎.场景.第二场景:加入喊话(150,1,"算你还不是太笨，我们是魔神大人的手下，天命之人受死把",100)]],[[引擎.场景.第二场景:置入人物(200,{2},{"腾云驾雾"})]],[[引擎.场景.第二场景:置入人物(200,{3},{"腾云驾雾"})]],[[引擎.场景.第二场景:加进度(250,1)]]},
				[2]={[[引擎.场景.第二场景:加入喊话(50,3,"妖怪那里走",100)]],[[引擎.场景.第二场景:加入喊话(150,2,"少侠你乃是18位天命之人中的一位所有，魔神的部下才想方设法除掉你，你们18位天命之人的实力还是太若小不足以对抗魔神，希望你们尽快成长起来",100)]],[[引擎.场景.第二场景:加入喊话(350,5,"帮手还真是多啊，你这瓶子罗里吧嗦的话还真多，打我是打不过你们不过你们要想抓住我那是痴心妄想",100)]],[[引擎.场景.第二场景:结束剧情(350)]]}
			}
		end
	elseif 任务 == 40 then
		if 编号 == 4 then
			人物组 = {{名称="玉皇大帝",模型="男人_玉帝",X=29.8,Y=34.8,方向=0,编号=1},{主角=true,模型=self.队伍[1].模型,X=32,Y=39,方向=0,编号=2}}
			事件组 = {
				[1]={[[引擎.场景.第二场景:电影片头(0)]],[[引擎.场景.第二场景:加入特效(50,2,"雷击")]],[[引擎.场景.第二场景:加入喊话(100,2,"啊........",100)]],[[引擎.场景.第二场景:加入特效(100,2,"雷击")]],[[引擎.场景.第二场景:加入特效(150,2,"雷击")]],[[引擎.场景.第二场景:加入喊话(200,2,"啊........",100)]],[[引擎.场景.第二场景:加入特效(200,2,"雷击")]],[[引擎.场景.第二场景:加入特效(250,2,"雷击")]],[[引擎.场景.第二场景:加进度(300,1)]]},
				[2]={[[引擎.场景.第二场景:加入特效(0,2,"奔雷咒")]],[[引擎.场景.第二场景:加入特效(50,2,"奔雷咒")]],[[引擎.场景.第二场景:加入喊话(100,2,"啊........",100)]],[[引擎.场景.第二场景:加入特效(100,2,"奔雷咒")]],[[引擎.场景.第二场景:加入特效(150,2,"奔雷咒")]],[[引擎.场景.第二场景:结束剧情(250)]]}
			}
		end
	elseif 任务 == 100 then
		if 编号 == 1 then
			人物组 = {{主角=true,模型=self.队伍[1].模型,X=16,Y=22,方向=3,编号=1},
				{名称="金满仓",模型="赌霸天",X=22,Y=19,方向=1,编号=2},
			}
			事件组 = {
				[1]={
				[[引擎.场景.第二场景:加入喊话(50,2,"哎哟！可把您给等来了！",200)]],
				[[引擎.场景.第二场景:加入喊话(250,1,"路上耽搁了几日，实在抱歉#17",200)]],
				[[引擎.场景.第二场景:加入喊话(450,2,"您客气了……这次请您来，可是有一件大事托付……",200)]],
				[[引擎.场景.第二场景:加入喊话(650,1,"#24#24",200)]],
				[[引擎.场景.第二场景:加入喊话(850,2,"在下最近预备在龙泉港开一家分铺，专营绸缎布匹所以，要把这边的丝绸布匹运一些过去。",200)]],
				[[引擎.场景.第二场景:加入喊话(1050,2,"此外，还需要在途经的港口采购一些别的货物。我知道少侠您一向在外闯荡，为人又是敦厚稳重。",200)]],
				[[引擎.场景.第二场景:加入喊话(1250,2,"不知，少侠可愿意帮助我走一趟#119事成之后，在下必有重酬！",200)]],
				[[引擎.场景.第二场景:加入喊话(1450,1,"龙泉港，此事倒是不难，不过我要预备预备，后日方可出发。",200)]],
				[[引擎.场景.第二场景:加入喊话(1650,2,"少侠既是应了，在下便去打点货物，备启程的诸项事宜！",200)]],
				[[引擎.场景.第二场景:结束剧情(1900)]],
				},
			}
		elseif 编号 == 2 then
			人物组 = {{主角=true,模型=self.队伍[1].模型,X=14,Y=119,方向=1,编号=1},
				{名称="金满仓",模型="赌霸天",X=18,Y=114,方向=1,编号=2},
				{名称="货物",模型="赤金宝箱",X=20,Y=115,方向=3,编号=3},
				{名称="货物",模型="赤金宝箱",X=22,Y=112,方向=3,编号=4},
				{名称="货物",模型="赤金宝箱",X=25,Y=114,方向=3,编号=5},
				{名称="货物",模型="赤金宝箱",X=23,Y=117,方向=3,编号=6},
			}
			事件组 = {
				[1]={
				[[引擎.场景.第二场景:加入喊话(50,2,"少侠，这诸般货物，连同需要在各个港口采办的货物清单，在下便在此处托付与你了！",200)]],
				[[引擎.场景.第二场景:加入喊话(250,1,"金老板您只管放心此行必是顺风顺水，如期抵达！两月之后，请预备好筵席哦#80",200)]],
				[[引擎.场景.第二场景:加入喊话(450,2,"甚好！在下已备好关酒十坛，等您凯旋共饮！",200)]],
				[[引擎.场景.第二场景:加入喊话(650,1,"既是如此，在下这便启程了#112#112#112",200)]],
				[[引擎.场景.第二场景:结束剧情(900)]],
				},
			}
		elseif 编号 == 3 then
			人物组 =  {{名称="",模型="龙舟",X=19,Y=42,方向=4,编号=2},
				{主角=true,模型=self.队伍[1].模型.."坐下",X=19,Y=40,方向=4,编号=1,最后帧方式=2}
			}
			事件组 = {
				[1]={
				[[引擎.场景.第二场景:加入特效(50,1,"下雨")]],
				[[引擎.场景.第二场景:加入特效(50,1,"打雷")]],
				[[引擎.场景.第二场景:加入喊话(200,1,"雨越下越大了，还有风，这下可怎么办！",200)]],
				[[引擎.场景.第二场景:加入特效(450,1,"雷击")]],
				[[引擎.场景.第二场景:加入喊话(470,1,"啊！！！",200)]],
				[[引擎.场景.第二场景:结束剧情(600)]],
				},
			}
		elseif 编号 == 4 then
			人物组 = {{主角=true,模型=self.队伍[1].模型.."倒地",X=115,Y=15,方向=1,编号=1,最后帧方式=2},
				{名称="水姑娘",模型="香料店老板",X=109,Y=19,方向=3,编号=2},
				{名称="小宝",模型="小孩_雷黑子",X=109,Y=15,方向=0,编号=3},
			}
			事件组 = {
				[1]={
				[[引擎.场景.第二场景:加入喊话(50,3,"水姐姐，这个人醒过来了！",200)]],
				[[引擎.场景.第二场景:加入喊话(250,1,"唔？",200)]],
				[[引擎.场景.第二场景:加入喊话(450,2,"少侠您可算醒来啦！",200)]],
				[[引擎.场景.第二场景:加入喊话(650,1,"我这是……噗……这是在哪里？",200)]],
				[[引擎.场景.第二场景:加入喊话(850,2,"这是我的家呀……前几日海上起了很大的风，风停后，就看到少侠您在那边码头……",200)]],
				[[引擎.场景.第二场景:加入喊话(1050,1,"前几日？",200)]],
				[[引擎.场景.第二场景:加入喊话(1250,2,"可不是！您昏迷了好几天了#17",200)]],
				[[引擎.场景.第二场景:加入喊话(1450,3,"呜呜呜……水姐姐好几天没有陪小宝一起玩了！",200)]],
				[[引擎.场景.第二场景:加入喊话(1650,1,"多谢水姑娘救命之恩！",200)]],
				[[引擎.场景.第二场景:加入喊话(1850,2,"少侠，您莫要谢我啦，我也只是听从村长的吩咐，您要谢，还是谢谢他老人家吧！",200)]],
				[[引擎.场景.第二场景:结束剧情(2000)]],
				},
			}
		elseif 编号 == 5 then --这个可以用打字机 过渡吧
			人物组 = {{主角=true,模型=self.队伍[1].模型,X=131,Y=55,方向=1,编号=1},
				{名称="魔化村民",模型="赌徒",X=111,Y=60,方向=3,编号=2},
				{名称="魔化村民",模型="赌徒",X=108,Y=62,方向=0,编号=3},
				{名称="魔化村民",模型="赌徒",X=109,Y=68,方向=0,编号=4},
				{名称="魔化村民",模型="赌徒",X=116,Y=73,方向=0,编号=5},
				{名称="魔化村民",模型="赌徒",X=127,Y=73,方向=0,编号=6},
			}
			事件组 = {
				[1]={
				[[引擎.场景.第二场景:置人物移动(0,1,125,63)]],
				[[引擎.场景.第二场景:置人物移动(0,2,118,62)]],
				[[引擎.场景.第二场景:置人物移动(0,3,117,64)]],
				[[引擎.场景.第二场景:置人物移动(0,4,120,67)]],
				[[引擎.场景.第二场景:置人物移动(0,5,124,69)]],
				[[引擎.场景.第二场景:置人物移动(0,6,128,69)]],
				[[引擎.场景.第二场景:加入喊话(150,1,"你们要干什么！",200)]],
				[[引擎.场景.第二场景:结束剧情(230)]],
				},
			}
		elseif 编号 == 6 then --结束
			人物组 = {{主角=true,模型=self.队伍[1].模型,X=115,Y=69,方向=3,编号=1},
				{名称="八足海妖",模型="进阶百足将军",X=123,Y=65,方向=1,编号=2},
				{名称="水姑娘",模型="香料店老板",X=110,Y=66,方向=3,编号=3},
			}
			事件组 = {
				[1]={
				[[引擎.场景.第二场景:加入喊话(50,2,"你为何要救那些胆小如鼠的村民！当年，他们可以这样对待黑龙那个傻子，也可以这样对你！啊哈哈哈哈哈！",200)]],
				[[引擎.场景.第二场景:加入喊话(250,1,"你说什么？",200)]],
				[[引擎.场景.第二场景:加入喊话(450,2,"哈哈哈哈！哈哈哈哈哈哈！！！",200)]],
				[[引擎.场景.第二场景:加入喊话(650,2,"恩将仇报！小家伙你学过这个词儿吗？",200)]],
				[[引擎.场景.第二场景:加入喊话(850,2,"水姑娘，他说的是真的吗？",200)]],
				[[引擎.场景.第二场景:加入喊话(1050,3,"他说的没错。当年，我忘忧村为海怪所困，岁岁纳贡，还要将年轻女子献祭与他为妻。否则，便是惊涛骇浪不绝整个忘忧村都要一道遭殃。",200)]],
				[[引擎.场景.第二场景:结束剧情(1250)]],
				},
			}
		end
	end
	-- 加入喊话(延迟帧数,人物编号,文本,时间)
	return 人物组,事件组
end

--[1]={[[引擎.场景.第二场景:加入喊话(5,1,"虎子就在你身后嘛",9999)]],[[引擎.场景.第二场景:加入喊话(5,2,"#117",9999)]],[[引擎.场景.第二场景:置人物方向(30,2,0)]],[[引擎.场景.第二场景:置入人物(45,{3,4},{"水遁","水遁"})]],[[引擎.场景.第二场景:加入喊话(65,2,"怎么会这样！",9999)]],[[引擎.场景.第二场景:加进度(70,1)]]},
--[2]={[[引擎.场景.第二场景:加入文本(10,nil,"虎子居然变成僵尸了，糟了，他们要扑上来了！")]]}