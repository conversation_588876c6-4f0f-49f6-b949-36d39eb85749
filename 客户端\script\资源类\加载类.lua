--======================================================================--
-- @作者: www.wnfj.com，联系QQ：79550111
-- @创建时间:   2018-03-03 02:34:19
-- @Last Modified time: 2025-01-21 14:32:31
-- 更多游戏请访问万能飞机：www.wnfj.com,版本定制授权联系QQ：79550111
--======================================================================--
local 资源类_加载 = class()
local aaaa = require("gge纹理类")
local aaab = require("script/资源类/gge精灵类")
local aaac = require("Fmod类")
local bbbb = require("Script/资源类/动画类")
local bbbc = require("gge图像类1")
-- local 动画类锦衣=require("script/资源类/动画类锦衣")
local yq = 引擎
local ffi = require("ffi")

function 资源类_加载:初始化()
	self.wdf = {}
	self.zipjiam = {}
	self.zipjiam["影精灵.dll"] = 1
	self.zipjiam["cjjn.dll"] = 1
	self.zipjiam["jiandan.dll"] = 1


	self.zipjiam["xx.wdf"] = 1
	self.zipjiam["aaa.wdf"] = 1
	self.zipjiam["jntb.wdf"] = 1
	self.zipjiam["mgtx.wdf"] = 1
	self.zipjiam["nice.wdf"] = 1
	self.zipjiam["Resource.ft"] = 1
	self.zipjiam["qwq.wdf"] = 1
	self.zipjiam["zztx.wdf"] = 1
	self.zipjiam["r3d.dll"] = 1
	self.zipjiam["tfg.rpk"] = 1 --lajiku
	self.zipjiam["nx3d5.dll"] = 1
	self.zipjiam["ceshi.wdf"] = 1
	self.zipjiam["nx3d6.dll"] = 1
	self.zipjiam["sc8844.dll"] = 1

	self.zipjiam["cjwf.dll"] = 1   --空白新增


end

function 资源类_加载:打开()
	引擎.添加资源('wer/xx.gpk',"ggelua")
	引擎.添加资源('wer/pic',"4hnVlvj51P1i1xfHW")
	引擎.添加资源('wdf/vvxxzcom/ceshi',"4hnVlvj51P1i1xfHW")
	引擎.添加资源('wer/aaa.rpk',"GPGsp1wIa4Yn")
	引擎.添加资源('wer/jntb.rpk',"sdasdw1efretrej")
	引擎.添加资源('wer/mgtx.wdf',"hZjXci11378dqrKUbY96Up5sQqG")
	引擎.添加资源('wer/nice.rpk',"1")
	引擎.添加资源('wer/Resource.rpk',"GPG1s1wIa4Yn")
	引擎.添加资源('wer/qwq.rpk',"Su3b1鐮1磋1iKFRUseZ4vhtpz")
	引擎.添加资源('wer/zztx.rpk',"GPGspp1Wz5wIa4Yn")
	引擎.添加资源('wer/npcs.dll',"xSHeNfh111ERp11鍟婄殑Lk")
	引擎.添加资源('wer/r3d.dll',"Su3b鐮磋1iK1FRUseZ4vhtpz$bGFu4MnVGz")
	引擎.添加资源('wer/lajiku.rpk',"Su3b鐮磋1iKFRUseZ4vhtpz")
	引擎.添加资源('wer/nx3d5.dll',"Su3b鐮磋1iKFRUseZ4vhtpz")
	引擎.添加资源('wer/nx3d6.dll',"Su3b鐮磋1iKFRUseZ4vhtpz")
	引擎.添加资源("wdf/vvxxzcom/yjl/影精灵.dll", "")
	引擎.添加资源("wdf/vvxxzcom/yjl/jiandan.dll","jiandan@1647205458@1647205458")
	引擎.添加资源('wdf/vvxxzcom/yjl/cjjn.dll',"*********")
	引擎.添加资源('wdf/cjwf.dll',"qinyinsandietx123")   --空白新增

	self.files = {
		--"JM.dll",
		"xixige_newmall.gep",--要清除
		-- "WP1.dll",--要清除 新增的应该是官方文件
		"vvxxzcom/vvxxzitem.wdf",
		"vvxxzcom/祥瑞坐骑.wdf",
		"vvxxzcom/小巷子排行榜.wdf",
		"vvxxzcom/carditem.wdf",
		"vvxxzcom/monster.wdf",
		"vvxxzcom/超级技能.wdf",
		"mhxiu.wdf",--要清除
		"lg.rpk", --要加密
		"zdy.rpk",
		"zdy2.rpk",
		"zdy3.rpk",
		"zdy4.rpk",
		"zdy5.rpk",
		"addon.wdf",
		"atom.wdf",
		"item.wdf",
		"item.wd1",
		"music.wdf",
		"common/item.wdf",
		"smap.wdf",
		"bc.wdf",
		-- "goods.wdf",
		"shape.wdf",
		"shape.wd2",
		"shape.wd3",
		"shape.wd4",
		"shape.wd5",
		"shape.wd6",
		"shape.wd7",
		"shape.wd8",
		"shape.wd9",
		"shape.wda",
		"shape.wdb",
		"shape.wdc",
		"wzife.wd1",
		"wzife.wd2",
		"wzife.wd3",
		"vvxxzcom/wzife.wd6",
		"common/wzife.wdf",
		"wzife.wd5",
		"wzife.wdf",
		"waddon.wdf",
		"magic.wdf",
		"magic.wd1",
		"common/item.wdf",
		"common/lbc.wdf",
		"common/sml.wdf",
		"common/wdd.wdf",
		"common/general.wdf",
		"common/magic.wdf",
		"common/add.wdf",
		"common/ski.wdf",
		"common/addon.wdf",
		"common/item.wdf",
		"common/shape.wdf",
		"common/wzife.wd1",
		"mapani.wdf",
		"sound.wdf",
		"misc.wdf",
		"sound.wd1",
		"kkjn.wdf",
		 "vvxxzcom/yjl/magic.wdf",
		"vvxxzcom/yjl/cwmx.wdf",-------------------
		 'vvxxzcom/yjl/jlc.wdf',
		  "vvxxzcom/yjl/jmtb.wdf",
		  "vvxxzcom/yjl/影精灵.wdf",
		  "vvxxzcom/yjl/UI.dll",
		  "vvxxzcom/yjl/smap.wdf",
		  "vvxxzcom/yjl/item.wd1",

	}
	--local openwdf = require("script/资源类/锦衣wdf")
	local __wdf = require("script/资源类/WDF")
	local format = string.format
	local a = wdf配置--"WDF/"
	-- self.id2file = {
	-- 	"common/shape.npk",
	-- 	"common/shape0.npk",
	-- 	"common/shape1.npk",
	-- 	"common/shape2.npk",
	-- }
	for n=1,#self.files do
		-- if files[n] == "sound.wdf" or files[n] == "sound.wd1" then
		-- 	a = "./"
		-- end
		self.wdf[self.files[n]] = __wdf(format("%s/%s",a,self.files[n]),self.files[n])

	end
	-- for n=1,#self.id2file do
	-- 	-- if files[n] == "sound.wdf" or files[n] == "sound.wd1" then
	-- 	-- 	a = "./"
	-- 	-- end
	-- 	self.wdf[self.id2file[n]] = openwdf(format("%s/%s",a,self.id2file[n]))
	-- end
end

function 资源类_加载:取偏移(file,id)
	return self.wdf[file]:读偏移(id)
end

function 资源类_加载:读数据(file,id)
	return self.wdf[file]:读数据(id)
end


function 资源类_加载:载入(文件,类型,文件号,音量,附加,fs,fs1,juese)
	-- print(文件,类型,文件号,音量,附加,fs,fs1,juese)
	--collectgarbage("step")
	--print(文件,类型,string.format("%#x",tostring(文件号)),juese)
	-- local fh = nil
	if (文件==nil or 文件=="") and 文件号 ~= nil then
		local t = type(文件号)
		if t == "string" then--and 文件 ~= "1.wpk" then
		    文件号= tonumber(文件号)
		end
	    return self:载入未知WDF(nil,类型,string.format("%#x",tostring(文件号)),音量,附加,fs,fs1)
	end
	if 类型 == "网易WDF动画" then
		-- if 文件=="mgtx.wdf" then
		--     print(文件号)
		-- end
		if self.zipjiam[文件] then
			-- print(文件号)
			-- print(type(文件号))
			if type(文件号) == "number" then
				文件号 = string.upper(string.format("%08x",tonumber(文件号)))
			end
			--print(文件号)
			--print(tostring(文件号)..'.was')
			local 临时 =引擎.资源取文件(tostring(文件号)..'.was')
           			local a=ffi.cast("void*",临时)
			local b=引擎.资源取大小(tostring(文件号)..'.was')
			-- print("==============="..tostring(文件号))
			-- print("==============="..string.format("%#x",tostring(文件号)))
			--print(tostring(文件号),a,b,缓存)
            local ddd =bbbb(文件,tostring(文件号),juese,a,b,缓存)
            引擎.资源释放(临时)
			return ddd
		else
			if 文件==nil and 文件号==nil then
				-- print(文件,类型,文件号,音量,附加,fs,fs1,juese)
				if 引擎.场景 ~=nil then
	    			-- 引擎.场景.窗口.消息框:添加文本("注意WDF载入[ 文件与文件号 ]为NIL")
	    		else
		    		-- print("注意WDF载入没有[ NIL值 ]文件")
		    	end
		    	文件 = "wzife.wdf"
		    	文件号= 0xFCD58523
			elseif 文件~=nil and self.wdf[文件]==nil then
				if 引擎.场景 ~=nil then
		    		-- 引擎.场景.窗口.消息框:添加文本("注意WDF载入没有[ "..文件.." ]文件")
		    	else
		    		-- print("注意WDF载入没有[ "..文件.." ]文件")
		    	end
		    	文件 = "wzife.wdf"
		    	文件号= 0xFCD58523
			elseif 文件~=nil and self.wdf[文件]~=nil and self.wdf[文件]:读数据(文件号)==nil then
				if 引擎.场景 ~=nil then
					-- 引擎.场景.窗口.消息框:添加文本("注意WDF载入[ "..文件.." ]文件没有[ "..文件号.." ]")
					-- print("注意WDF载入[ "..文件.." ]文件没有[ "..文件号.." ]")
				else
					-- print("注意WDF载入[ "..文件.." ]文件没有[ "..文件号.." ]")
				end
				文件 = "wzife.wdf"
		    	文件号= 0xFCD58523
			end
			return bbbb(文件,文件号,juese,self.wdf[文件 or "wzife.wdf"]:读数据(文件号 or 0xFCD58523))
		end
	-- elseif 类型 == "网易锦衣动画" then
	-- 	if 文件号 then
	-- 		return 动画类锦衣(self.wdf[文件]:取文件(文件号 + 0))
	-- 	end
	-- 	return 动画类锦衣(self.wdf["common/shape2.npk"]:取文件(16056870))
	elseif 类型 == "内置png" then
	    return aaab(aaaa(self.wdf[文件]:读数据(文件号)))
	elseif 类型 == "图片" then
		-- print(文件号)
		-- print(文件)
		-- return aaab(aaaa(文件))
		local a,b=引擎.资源取文件(文件),引擎.资源取大小(文件)
		local c=aaaa(a,b)
		引擎.资源释放(a)
		return aaab(c)

	-- elseif 类型 == "网络图片" then
	-- 	return aaab(aaaa(文件[1],文件[2]))
	-- 	--return bbbc(文件[1],文件[2])
	elseif 类型 == "音乐" then
		return aaac(文件,2,nil,nil,0 or 0)
		--require("Fmod类")(资源包,nil,引擎.场景.资源:取偏移(资源包,文件号),2765224)
	elseif 类型 == "加密音乐" then
		local a,b=引擎.资源取文件(文件),引擎.资源取大小(文件)
		local 数据 = ffi.new('uint8_t[?]',b)
		ffi.copy(数据,ffi.cast("uint8_t*",a),b)
		return aaac(数据,b)
	end
end

function 资源类_加载:载入未知WDF(file1,类型,id,音量,附加,fs,fs1,fjsj) -- String 例如 0x00FF 非法, 改为 0xFF 正确   资源:载入未知WDF(nil,"网易WDF动画","0xBECEA063")
	if id and tonumber(id) > 0 and file1 == nil then
		if 全局资源地址[id+0] ~= nil then
			if not self.wdf[全局资源地址[id+0]] then
				self:打开(全局资源地址[id+0])
			end
			return self:载入(全局资源地址[id+0],类型,到整数(id),音量,附加,fs,fs1,fjsj)
		end
		return self:载入(全局资源地址[id+0],类型,到整数(id),音量,附加,fs,fs1,fjsj)
	end
	return self:载入("wzife.wdf",类型,0xFCD58523)
end

return 资源类_加载