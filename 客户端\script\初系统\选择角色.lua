--======================================================================--
--======================================================================--
local 选择角色 = class()

local tp
local qtx = 引擎.取头像
local mouseb = 引擎.鼠标按下
local qmx = 引擎.取模型
local qzd = 引擎.取战斗模型

function 选择角色:初始化(根)
  local 资源 = 根.资源
  tp = 根
  self.右键关闭=1
  -- self.标题背景 = 资源:载入("start.dll","图片")
  self.人族背景 = 资源:载入('lg.rpk',"网易WDF动画",0x00010035)
  self.仙族背景 = 资源:载入('lg.rpk',"网易WDF动画",0x00010033)
  self.魔族背景 = 资源:载入('lg.rpk',"网易WDF动画",0x00010034)
  local 按钮 = 根._按钮
  self.上一步 = 按钮.创建(资源:载入('zdy.rpk',"网易WDF动画",0x1000253),0,0,3,true,true)
  self.创建 = 按钮.创建(资源:载入('zdy.rpk',"网易WDF动画",0x1000257),0,0,3,true,true)
  self.下一步 = 按钮.创建(资源:载入('zdy.rpk',"网易WDF动画",0x1000258),0,0,3,true,true)
  self.角色图片组 = {
    [1] = {模型="飞燕女",种族="人",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010070),0,0,3,true,true),染色方案=nil,介绍="    深山有佳人,灼灼芙蓉姿,飞燕女轻盈飘逸,灵慧动人,自幼怜爱弱小,嫉恶如仇,一生自由自在,是大自然骄纵的宠儿",兵器="可用兵器为：双剑、环圈",门派="可选择门派：大唐官府、方寸山、女儿村、神木林",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010049),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010084),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010043)},
    [2] = {模型="英女侠",种族="人",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010012),0,0,3,true,true),染色方案=nil,介绍="    兰心惠质出名门,英姿飒爽自芳华,英女侠天资聪颖,精通琴棋书画,心怀仁爱,行善不落人后,是位侠骨柔情的奇女子",兵器="可用兵器为：双剑、长鞭",门派="可选择门派：大唐官府、方寸山、女儿村、神木林",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010054),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010085),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010048)},
    [3] = {模型="巫蛮儿",种族="人",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010010),0,0,3,true,true),染色方案=nil,介绍="    嫣然巧笑踏绿萝,一路银铃一路歌,巫蛮儿质朴单纯,灵动可人,生性善良,活泼可爱,花盈翠影出神木,环佩婉转披香来",兵器="可用兵器为：宝珠、法杖",门派="可选择门派：大唐官府、方寸山、女儿村、神木",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010051),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010086),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010045)},
    [4] = {模型="偃无师",种族="人",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010013),0,0,3,true,true),染色方案=nil,介绍="    铁手隐机枢，巧夺天工，猛力执巨剑，志敌万均。偃无师性情冷厉，疏狂不羁，亦有奇谋满腹，铮铮傲骨。",兵器="可用兵器为：巨剑、剑",门派="可选择门派：大唐官府、化生寺、方寸山、神木林",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010053),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010087),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010047)},
    [5] = {模型="逍遥生",种族="人",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010014),0,0,3,true,true),染色方案=nil,介绍="    快意恩仇事,把酒踏歌行,一袭白衫,一纸折扇,逍遥生风流倜傥,潇洒自如,行事光明磊落,是世人乐于结交的谦谦君子",兵器="可用兵器为：剑、扇",门派="可选择门派：大唐官府、化生寺、方寸山、神木林",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010052),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010088),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010046)},
    [6] = {模型="剑侠客",种族="人",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010011),0,0,3,true,true),染色方案=nil,介绍="    霜刃露锋芒,飒沓如流星,剑侠客率情任性,狂放不羁,一生淡泊名利,嗜武如痴,英雄意,儿女情,独闯江湖半生醉,举杯邀月最销魂",兵器="可用兵器为：刀、剑",门派="可选择门派：大唐官府、化生寺、方寸山、神木林",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010050),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010089),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010044)},
    [7] = {模型="狐美人",种族="魔",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010015),0,0,3,true,true),染色方案=nil,介绍="    修眉连娟,斜挑入眉,媚眼如丝,含娇含笑,狐美人柔情绰态,胜似海棠醉日,风情万种,颠倒众生",兵器="可用兵器为：爪刺、鞭",门派="可选择门派：盘丝洞、阴曹地府、魔王寨、无底洞",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010026),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010090),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010025)},
    [8] = {模型="骨精灵",种族="魔",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010019),0,0,3,true,true),染色方案=nil,介绍="    眉黛春山秀,横波剪秋水,骨精灵娇妍俏皮,顾盼神飞,机敏聪慧,好打不平,对世间万物充满好奇",兵器="可用兵器为：爪刺、魔棒",门派="可选择门派：盘丝洞、阴曹地府、魔王寨、无底洞",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010022),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010091),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010021)},
    [9] = {模型="鬼潇潇",种族="魔",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010071),0,0,3,true,true),染色方案=nil,介绍="    寒眸印秋水，魂隐三生途，素手执竹伞，影馀幽冥路。鬼潇潇青丝如墨，红杉如火，一对异色瞳里似乎藏着无尽的忧愁和神秘。",兵器="可用兵器为：爪刺、伞",门派="可选择门派：狮驼岭、魔王寨、阴曹地府、无底洞",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010024),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010092),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010023)},
    [10] = {模型="杀破狼",种族="魔",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010016),0,0,3,true,true),染色方案=nil,介绍="    一啸生风雪,长歌动寒霜,杀破狼飘逸潇洒,气宇轩昂,能文能武,卓尔不群,身具的神秘天狼血统,纵横骄天下,傲立三界间.",兵器="可用兵器为：弓弩、宝珠",门派="可选择门派：狮驼岭、魔王寨、阴曹地府、无底洞",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010032),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010093),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010031)},
    [11] = {模型="巨魔王",种族="魔",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010017),0,0,3,true,true),染色方案=nil,介绍="    一怒震乾坤,杀气凝如山,巨魔王力拔山气兮盖世,肩负魔族神秘使命,叱咤风云,威风凛凛",兵器="可用兵器为：斧钺、刀",门派="可选择门派：狮驼岭、魔王寨、阴曹地府、无底洞",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010030),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010094),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010029)},
    [12] = {模型="虎头怪",种族="魔",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010018),0,0,3,true,true),染色方案=nil,介绍="    戏谑犹可爱,虽有神力不欺人,虎头怪弯弧五百步,长戟八十斤,勇武过人,生性耿直豁达,对朋友忠肝义胆,是顶天立地的大丈夫",兵器="可用兵器为：斧钺、锤",门派="可选择门派：狮驼岭、魔王寨、阴曹地府、无底洞",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010028),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010095),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010027)},
    [13] = {模型="舞天姬",种族="仙",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010004),0,0,3,true,true),染色方案=nil,介绍="    霓裳曳广带,飘拂升天行,舞天姬明眸珠辉,瑰姿艳逸,生性善解人意,令人如沐春风.一舞绡丝动四方,观之心魂俱醉",兵器="可用兵器为：飘带、环圈",门派="可选择门派：天宫、普陀山、龙宫、凌波城",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010064),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010096),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010058)},
    [14] = {模型="玄彩娥",种族="仙",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010009),0,0,3,true,true),染色方案=nil,介绍="    桂露对仙娥,星星下云逗,玄彩娥在花从中蝶翼翩翩,婀娜曼妙,犹聚晨露新聚,奇花初蕊,是集天地灵气于一身的百花仙子",兵器="可用兵器为：飘带、魔棒",门派="可选择门派：龙宫、普陀山、天宫、凌波城",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010065),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010097),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010059)},
    [15] = {模型="桃夭夭",种族="仙",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010005),0,0,3,true,true),染色方案=nil,介绍="    桃夭柳媚梦酣眠，笑语嫣然化春风。一朝春近晴光好，清波潋滟映芳菲，桃夭夭是蟠桃园含花吐蕊的花苞，历经三千毓秀钟灵，化身一个机灵爽朗，骄憨顽皮的少女。",兵器="飘带、灯笼",门派="可选择门派：天宫、龙宫、普陀山、凌波城",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010063),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010101),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010057)},
    [16] = {模型="羽灵神",种族="仙",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010008),0,0,3,true,true),染色方案=nil,介绍="    游侠红尘里,豪情动九天.羽灵神热情正直,率性豁达,游侠三界间,交友遍天下;乐见四海尽升平,愿引凤鸣遍九州",兵器="可用兵器为：弓弩、法杖",门派="可选择门派：天宫、龙宫、普陀山、凌波城",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010066),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010098),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010060)},
    [17] = {模型="神天兵",种族="仙",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010006),0,0,3,true,true),染色方案=nil,介绍="    金甲腾云受天命,神枪破逆卫灵霄,神天兵风采鹰扬,锋芒毕露,守护天庭立天威,所向披靡,妖魔皆闻风丧胆",兵器="可用兵器为：枪矛、锤",门派="可选择门派：龙宫、天宫、五庄观、凌波城",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010062),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010099),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010056)},
    [18] = {模型="龙太子",种族="仙",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010007),0,0,3,true,true),染色方案=nil,介绍="    乘风破浪翔碧海,腾云架雾上青天,龙太子凭借天生的优势领悟仙法精髓,是当之无愧的龙族骄子,身经百战的天界战将",兵器="可用兵器为：枪矛、扇",门派="可选择门派：龙宫、天宫、五庄观、凌波城",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010061),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010100),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010055)},
    --[19] = {模型="影精灵",种族="魔",头像=根._按钮(资源:载入('vvxxzcom/yjl/cwmx.wdf',"网易WDF动画",0x00001271),0,0,3,true,true),染色方案=nil,介绍="    酷似骨精灵的魔族少女，冷酷，理智，不近人情。她曾经是蚩尤残党的领袖，意图复活蚩尤。",兵器="可用兵器为：爪、棒、双斧",门派="可选择门派：狮驼岭、魔王寨、阴曹地府、无底洞、九黎城", 图片=资源:载入('wdf/vvxxzcom/yjl/影精灵.png',"图片"),头像圆图=资源:载入('wdf/vvxxzcom/yjl/影圆.png',"图片"),图片介绍=资源:载入('wdf/vvxxzcom/yjl/文字3.png',"图片")},
    [19] = {模型="影精灵",种族="魔",头像=根._按钮(资源:载入('vvxxzcom/yjl/cwmx.wdf',"网易WDF动画",0x00001271),0,0,3,true,true),染色方案=nil,介绍="    酷似骨精灵的魔族少女，冷酷，理智，不近人情。她曾经是蚩尤残党的领袖，意图复活蚩尤。",兵器="可用兵器为：爪、棒、双斧",门派="可选择门派：狮驼岭、魔王寨、阴曹地府、无底洞、九黎城", 图片=资源:载入('wdf/vvxxzcom/yjl/影精灵.png',"图片"),头像圆图=资源:载入('wdf/vvxxzcom/yjl/影圆.png',"图片"),图片介绍=资源:载入('wdf/vvxxzcom/yjl/文字3.png',"图片")},
  }
  self.可创建组 = {
    [1] = 根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010001),0,0,3,true,true),
    [2] = 根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010001),0,0,3,true,true),
    [3] = 根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010001),0,0,3,true,true),
    [4] = 根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010001),0,0,3,true,true),
    [5] = 根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010001),0,0,3,true,true),
    [6] = 根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010001),0,0,3,true,true),
  }
  self.已创建组 = {
    [1] = 根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010020),0,0,3,true,true),
    [2] = 根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010020),0,0,3,true,true),
    [3] = 根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010020),0,0,3,true,true),
    [4] = 根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010020),0,0,3,true,true),
    [5] = 根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010020),0,0,3,true,true),
    [6] = 根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010020),0,0,3,true,true),
  }
  self.人物下框文字 = 资源:载入('lg.rpk',"网易WDF动画",0x00010003)
  self.人物列表背景 = 资源:载入('lg.rpk',"网易WDF动画",0x00010002)
  人物阴影 =资源:载入('shape.wdf',"网易WDF动画",0xDCE4B562)
  self.角色数据={}
  self.角色信息={}
  self.角色选中号码=0
  self.双击 = nil
  self.时间 = 0
end

function 选择角色:置方向(方向,n)
  self.创建人物精灵[n]["静立"]:置方向(方向)
  self.创建人物精灵[n]["行走"]:置方向(方向)
  self.创建人物精灵[n]["攻击"]:置方向(取四至八方向(方向))
  self.创建人物精灵[n]["施法"]:置方向(取四至八方向(方向))
end

function 选择角色:置染色(人物ID,染色方案,染色ID,方向)
  self.创建人物精灵[人物ID]["静立"]:置染色(染色方案,染色ID,染色ID,染色ID)
  self.创建人物精灵[人物ID]["行走"]:置染色(染色方案,染色ID,染色ID,染色ID)
  self.创建人物精灵[人物ID]["攻击"]:置染色(染色方案,染色ID,染色ID,染色ID)
  self.创建人物精灵[人物ID]["施法"]:置染色(染色方案,染色ID,染色ID,染色ID)
  self:置方向(方向,人物ID)
end

function 选择角色:后退方向()
  if self.方向 < 7 then
    self.方向 = self.方向 + 1
    self:置方向(self.方向,self.选中人物)
  else
    self.方向 = 0
    self:置方向(self.方向,self.选中人物)
  end
end

function 选择角色:前进方向()
  if self.方向 >= 1 then
    self.方向 = self.方向 - 1
    self:置方向(self.方向,self.选中人物)
  else
    self.方向 = 7
    self:置方向(self.方向,self.选中人物)
  end
end

function 选择角色:更新(dt)

end

function 选择角色:显示(dt,x,y)
    if #self.角色数据 == 0 then
        local 偏移x,偏移y = 等比例缩放公式(1024,768,self.人族背景.宽度,self.人族背景.高度)
        self.人族背景:显示(0,0,偏移x,偏移y)
        --self.人族背景:显示(0,0)
    else
        if self.角色选中号码==0 then
            self.角色选中号码=1
        end
        for n=1,19 do
            if self.角色信息[self.角色选中号码].造型 == self.角色图片组[n].模型 then
                if self.角色图片组[n].种族 == "人" then
                    local 偏移x,偏移y = 等比例缩放公式(1024,768,self.人族背景.宽度,self.人族背景.高度)
                    self.人族背景:显示(0,0,偏移x,偏移y)
                elseif self.角色图片组[n].种族 == "魔" then
                    local 偏移x,偏移y = 等比例缩放公式(1024,768,self.魔族背景.宽度,self.魔族背景.高度)
                    self.魔族背景:显示(0,0,偏移x,偏移y)
                elseif self.角色图片组[n].种族 == "仙" then
                    local 偏移x,偏移y = 等比例缩放公式(1024,768,self.仙族背景.宽度,self.仙族背景.高度)
                    self.仙族背景:显示(0,0,偏移x,偏移y)
                end
            end
        end
    end
    self.人物下框文字:显示(30,520)
    self.人物列表背景:显示(515,70)
    self.上一步:更新(x,y)
    self.下一步:更新(x,y)
    self.创建:更新(x,y)
    self.创建:显示(800-160,600-150)
    self.上一步:显示(800-160,600-100)
    self.临时a=0
    for n=1,#self.角色数据 do --#self.角色数据 do
        if n < 3 then
            self.已创建组[n]:更新(x,y,self.角色选中号码~=n)
            self.已创建组[n]:显示(565+n*10,15+n*60)
            for i=1,#self.角色图片组 do
                if self.角色信息[n].造型 == self.角色图片组[i].模型 then
                    self.角色图片组[i].头像圆图:显示(568+n*10,17+n*60)
                end
            end
            if self.角色选中号码 == n then
                tp.字体表.猫猫字体3:置颜色(0xff73df89)
                tp.字体表.猫猫字体3:显示(565+60+n*10,15+13+n*60,self.角色信息[n].名称)
            else
                tp.字体表.猫猫字体3:置颜色(0xfffff2a7)
                tp.字体表.猫猫字体3:显示(565+60+n*10,15+13+n*60,self.角色信息[n].名称)
            end
            tp.字体表.猫猫字体3:置颜色(白色)
            tp.字体表.猫猫字体3:显示(565+60+n*10,15+35+n*60,"门派:"..self.角色信息[n].门派.."  等级:"..self.角色信息[n].等级)
        elseif n < 5 then
            self.已创建组[n]:更新(x,y,self.角色选中号码~=n)
            self.已创建组[n]:显示(592,15+n*60)
            for i=1,#self.角色图片组 do
                if self.角色信息[n].造型 == self.角色图片组[i].模型 then
                    self.角色图片组[i].头像圆图:显示(595,17+n*60)
                end
            end
            if self.角色选中号码 == n then
                tp.字体表.猫猫字体3:置颜色(0xff73df89)
                tp.字体表.猫猫字体3:显示(592+60,15+13+n*60,self.角色信息[n].名称)
            else
                tp.字体表.猫猫字体3:置颜色(0xfffff2a7)
                tp.字体表.猫猫字体3:显示(592+60,15+13+n*60,self.角色信息[n].名称)
            end
            tp.字体表.猫猫字体3:置颜色(白色)
            tp.字体表.猫猫字体3:显示(592+60,15+35+n*60,"门派:"..self.角色信息[n].门派.."  等级:"..self.角色信息[n].等级)
        else
            self.已创建组[n]:更新(x,y,self.角色选中号码~=n)
            self.已创建组[n]:显示(592-(n-4)*10,15+n*60)
            for i=1,#self.角色图片组 do
                if self.角色信息[n].造型 == self.角色图片组[i].模型 then
                    self.角色图片组[i].头像圆图:显示(595-(n-4)*10,17+n*60)
                end
            end
            if self.角色选中号码 == n then
                tp.字体表.猫猫字体3:置颜色(0xff73df89)
                tp.字体表.猫猫字体3:显示(592+60-(n-4)*10,15+13+n*60,self.角色信息[n].名称)
            else
                tp.字体表.猫猫字体3:置颜色(0xfffff2a7)
                tp.字体表.猫猫字体3:显示(592+60-(n-4)*10,15+13+n*60,self.角色信息[n].名称)
            end
                tp.字体表.猫猫字体3:置颜色(白色)
                tp.字体表.猫猫字体3:显示(592+60-(n-4)*10,15+35+n*60,"门派:"..self.角色信息[n].门派.."  等级:"..self.角色信息[n].等级)
            end
            if self.已创建组[n]:事件判断() then
                self.角色选中号码=n
            end

            if self.角色选中号码==n then
                if 引擎.鼠标弹起(0) then
                    if self.双击 == nil then
                        self.事件 = true
                        self.双击 = 1
                        self.角色选中=self.角色选中号码
                    else
                        self.事件 = nil
                        self.载入 = 1
                    end
                end
                if self.双击 == 1 then
                    self.时间 = self.时间 + 1
                    if self.时间 >= 16 then
                        self.双击 = nil
                        self.时间 = 0
                    end
                end
                if self.载入 ~= nil then
                    if self.角色选中~=self.角色选中号码 then
                        self.双击 = nil
                        self.时间 = 0
                        self.载入 = nil
                        return
                    else
                        系统参数.选中玩家id = self.角色信息[self.角色选中号码].id
                        客户端:发送数据(4,self.角色信息[self.角色选中号码].id)
                        全局登陆内容()
                        self.载入 = nil
                    end
                end
            end
        end
        if #self.角色数据<6 then
            for n=#self.角色数据+1,6 do
                if n < 3 then
                    self.可创建组[n]:更新(x,y)
                    self.可创建组[n]:显示(565+n*10,15+n*60)
                elseif n < 5 then
                    self.可创建组[n]:更新(x,y)
                    self.可创建组[n]:显示(592,15+n*60)
                else
                    self.可创建组[n]:更新(x,y)
                    self.可创建组[n]:显示(592-(n-4)*10,15+n*60)
                end
                if self.可创建组[n]:事件判断() then
                    客户端:发送数据(2)
                end
            end
        end

        if self.角色选中号码~=0 then
            tp.字体表.普通字体:置颜色(白色)
            tp.字体表.普通字体:显示(101,527,self.角色信息[self.角色选中号码].名称)
            tp.字体表.普通字体:显示(338,527,self.角色信息[self.角色选中号码].id)
            tp.字体表.普通字体:显示(101,562,self.角色信息[self.角色选中号码].等级)
            tp.字体表.普通字体:显示(338,562,self.角色信息[self.角色选中号码].门派)
            self.角色数据[self.角色选中号码]:更新(dt)
            self.角色数据[self.角色选中号码]:显示(100,450)
            for i=1,#self.角色图片组 do
                if self.角色信息[self.角色选中号码].造型 == self.角色图片组[i].模型 then
                    if self.角色信息[self.角色选中号码].造型=="杀破狼" then
                        self.角色图片组[i].图片:显示(180,50)
                    elseif self.角色信息[self.角色选中号码].造型=="虎头怪" then
                        self.角色图片组[i].图片:显示(80,80)
                    elseif self.角色信息[self.角色选中号码].造型=="巨魔王" then
                        self.角色图片组[i].图片:显示(100,130)
                    elseif self.角色信息[self.角色选中号码].造型=="鬼潇潇" then
                        self.角色图片组[i].图片:显示(160,80)
                    elseif self.角色信息[self.角色选中号码].造型=="狐美人" then
                        self.角色图片组[i].图片:显示(160,160)
                    elseif self.角色信息[self.角色选中号码].造型=="骨精灵" then
                        self.角色图片组[i].图片:显示(170,180)
                    elseif self.角色信息[self.角色选中号码].造型=="英女侠" then
                        self.角色图片组[i].图片:显示(120,110)
                    elseif self.角色信息[self.角色选中号码].造型=="飞燕女" then
                        self.角色图片组[i].图片:显示(110,130)
                    elseif self.角色信息[self.角色选中号码].造型=="剑侠客" then
                        self.角色图片组[i].图片:显示(5,140)
                    elseif self.角色信息[self.角色选中号码].造型=="影精灵" then
                        local 偏移x,偏移y = 等比例缩放公式(480,420,self.角色图片组[i].图片.宽度,self.角色图片组[i].图片.高度)
                        self.角色图片组[i].图片:显示(80,70,偏移x,偏移y)
                    elseif self.角色信息[self.角色选中号码].造型=="巫蛮儿" then
                        self.角色图片组[i].图片:显示(90,120)
                    elseif self.角色信息[self.角色选中号码].造型=="逍遥生" then
                        self.角色图片组[i].图片:显示(70,150)
                    elseif self.角色信息[self.角色选中号码].造型=="偃无师" then
                        self.角色图片组[i].图片:显示(110,130)
                    elseif self.角色信息[self.角色选中号码].造型=="龙太子" then
                        self.角色图片组[i].图片:显示(40,160)
                    elseif self.角色信息[self.角色选中号码].造型=="神天兵" then
                        self.角色图片组[i].图片:显示(80,130)
                    elseif self.角色信息[self.角色选中号码].造型=="桃夭夭" then
                        self.角色图片组[i].图片:显示(150,130)
                    elseif self.角色信息[self.角色选中号码].造型=="舞天姬" then
                        self.角色图片组[i].图片:显示(80,120)
                    elseif self.角色信息[self.角色选中号码].造型=="玄彩娥" then
                        self.角色图片组[i].图片:显示(170,110)
                    elseif self.角色信息[self.角色选中号码].造型=="羽灵神" then
                        self.角色图片组[i].图片:显示(100,110)
                    end
                end
            end
            self.下一步:显示(800-160,600-50)
        end
        if self.创建:事件判断() then
            if #self.角色数据>=6 then
                -- tp.提示:写入("#Y")
                return 0
            end
            tp.创建:刷新位置()
            客户端:发送数据(2)
            tp.创建.种族选中 = "人"
            tp.创建.选中人物 = 1
        elseif self.上一步:事件判断() then
            self.焦点 = 0
            self.上一焦点 = 0
            self.角色选中号码=0
            tp.进程 = 7
            tp.登陆.账号输入框:置可视(true,true)
            tp.登陆.密码输入框:置可视(true,true)
            系统退出=true
        elseif self.下一步:事件判断() or 引擎.按键按下(KEY.ENTER)  then
            if self.角色选中号码==0 then
                tp.提示:写入("#Y/请先选择一个角色")
                return 0
            else
                系统参数.选中玩家id = self.角色信息[self.角色选中号码].id
                客户端:发送数据(4,self.角色信息[self.角色选中号码].id)
            end
        end
        -- print(tp.进程)
end

function 选择角色:添加角色信息(信息)
  self.角色数据={}
  self.角色信息={}
  for n=1,#信息 do
    self.角色数据[n]= require("script/初系统/选择角色动画")(1,tp,信息[n].造型,信息[n].武器数据,信息[n].染色组,信息[n].染色方案,信息[n].锦衣数据,信息[n].翅膀数据,信息[n].光环数据,信息[n].足迹数据,信息[n].炫彩,信息[n].炫彩组,信息[n].id, 信息[n].副武器数据) --信息[n].武器数据
    self.角色信息[n]=信息[n]
    -- self.角色数据[n]= require("script/初系统/选择角色动画")(1,tp,信息[n].造型,信息[n].武器数据,信息[n].染色组,信息[n].染色方案,信息[n].锦衣数据,信息[n].光环数据,信息[n].足迹数据) --信息[n].武器数据
    -- self.角色信息[n]=信息[n]
  end
  tp.进程 = 2
  tp.选中窗口 = nil
end

return 选择角色