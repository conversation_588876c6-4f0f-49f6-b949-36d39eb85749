--======================================================================--
-- @作者: www.wnfj.com，联系QQ：79550111
-- @创建时间:   2018-03-03 02:34:19.
-- @Last Modified time: 2024-12-05 17:26:08
--======================================================================--
local 场景类_经脉流派 = class()

local floor = math.floor
local format = string.format
local insert = table.insert
local remove = table.remove
local type = type
local mouseb = 引擎.鼠标弹起
local tp,zt,zzt
local 门派神器名称 = {
	    大唐官府 = "轩辕剑",化生寺 = "墨魂笔",方寸山 = "黄金甲",女儿村 = "泪痕碗",天宫 = "独弦琴",
	    普陀山 = "华光玉",龙宫 = "清泽谱",五庄观 = "星斗盘",魔王寨 = "明火珠",狮驼岭 = "噬魂齿",
	    盘丝洞 = "昆仑镜",阴曹地府 = "四神鼎",神木林 = "月光草",凌波城 = "天罡印",无底洞 = "玲珑结",
	    花果山 = "鸿蒙石",九黎城 = "魔息角"
	}
local 经脉描述 = {
	    大唐官府 = {"攻无不克，战无不胜","一夫当关，万夫莫开","披坚执锐，智勇双全"},
	    化生寺 = {"怒目金刚，千军无畏","春满杏林，医者侠心","无量箴言，十方普照"},
	    方寸山 = {"逍遥散修，载一抱素","伏魔除妖，正法论道","五气朝元，天地自在"},
	    女儿村 = {"绝代妖娆，智计无双","花雨伊人，暗藏锋芒","花间倩影，一舞翩跹"},
	    天宫 = {"神使执戒，镇妖压邪","踏电行雷，耀武天尊","霹雳手段，百战凌风"},
	    普陀山 = {"莲台端坐，普渡众生","五行制化，咒令乾坤","落伽大士，扶危济厄"},
	    龙宫 = {"碧海青天，踏浪穿波","云龙现身，呼风唤雨","潜龙在渊，脾睨沧海"},
	    五庄观 = {"清风望月，羽客归心","乾坤飞剑，锋刃无形","清净守笃，修斋行道"},
	    魔王寨 = {"威震山河，气慨平天","魔君盖世，独霸一方","妖王怒火，势焰滔天"},
	    狮驼岭 = {"狂兽奔杀，激突猛进","狮咆鹰啸，万兽之王","炽热兽魂，无畏战狂"},
	    盘丝洞 = {"迷瘴之殇，谁解情丝","多姿多情，引魂噬心","百媚婀娜，千丝笼影"},
	    阴曹地府 = {"九幽阎罗，勾魂锢魄","无惧黑夜，不畏轮回","毒刹诛刑，瘴蔽幽冥"},
	    神木林 ={"通天之灵，师法自然","巫神幽语，蛊魅人心","神木之侍，灵佑之恩"},
	    凌波城 = {"九天神力，三界光明","诛魔驱暗，战意凌然","风云荡邪，天眼诛恶"},
	    无底洞 = {"地涌金莲，华光灵照","摄魂萦魄，封心掠窍","幽冥诡巫，夺血为煞"},
	    花果山 = {"圣势齐天，威慑九霄","棒搅乾坤，棍卷风云","大道通天，行者无疆"},
	    九黎城 = {"古神余威，惊天动地","棒搅乾坤，棍卷风云","大道通天，行者无疆"},
	}
local function 法宝名称(mp,lv)
	if mp == "大唐官府" then
		-- if lv>=100 then
		    return "干将莫邪","物理输出","物理输出","物理输出"
		-- end
		-- return "七杀"
	elseif mp == "化生寺" then
		-- if lv>=100 then
		    return "慈悲","治疗回复","增益强化","法术输出"
		-- end
		-- return "罗汉珠"
	elseif mp == "龙宫" then
		-- if lv>=100 then
		    return "镇海珠","法术输出","法术输出","法术输出"
		-- end
		-- return "分水"
	elseif mp == "魔王寨" then
		-- if lv>=100 then
		    return "五火神焰印","法术输出","法术输出","法术输出"
		-- end
		-- return "赤焰"
	elseif mp == "神木林" then
		-- if lv>=100 then
		    return "月影","法术输出","法术输出","法术输出"
		-- end
		-- return "神木宝鼎"
	elseif mp == "方寸山" then
		-- if lv>=100 then
		    return "救命毫毛","封印控制","法术输出","法术输出"
		-- end
		-- return "天师符"
	elseif mp == "女儿村" then
		-- if lv>=100 then
		    return "曼陀罗","封印控制","固定伤害","物理输出"
		-- end
		-- return "织女扇"
	elseif mp == "天宫" then
		-- if lv>=100 then
		    return "伏魔天书","封印控制","法术输出","物理输出"
		-- end
		-- return "雷兽"
	elseif mp == "普陀山" then
		-- if lv>=100 then
		    return "普渡","治疗回复","固定伤害","物理输出"
		-- end
		-- return "金刚杵"
	elseif mp == "盘丝洞" then
		-- if lv>=100 then
		    return "忘情","封印控制","固定伤害","物理输出"
		-- end
		-- return "迷魂灯"
	elseif mp == "阴曹地府" then
		-- if lv>=100 then
		    return "九幽","死亡禁锢","物理输出","物理输出"
		-- end
		-- return "摄魂"
	elseif mp == "狮驼岭" then
		-- if lv>=100 then
		    return "失心钹","物理输出","召唤物","物理输出"
		-- end
		-- return "兽王令"
	elseif mp == "五庄观" then
		-- if lv>=100 then
		    return "奇门五行令","封印控制","物理输出","物理输出"
		-- end
		-- return "定风珠"
	elseif mp == "无底洞" then
		-- if lv>=100 then
		    return "金蟾","治疗回复","封印控制","召唤物"
		-- end
		-- return "宝烛"
	elseif mp == "凌波城" then
		-- if lv>=100 then
		    return "斩魔","物理输出","物理输出","物理输出"
		-- end
		-- return "天煞"
	elseif mp == "花果山" then
		-- if lv>=100 then
		    return "金箍棒","物理输出","物理输出","法术输出"
		-- end
		-- return "琉璃灯"
	elseif mp == "九黎城" then
		return "驭魔笼","物理输出","物理输出","法术输出"
	end
end

local function 特色法术(mp)
	if mp == "大唐官府" then
	    return {"横扫千军","后发制人","杀气诀","翩鸿一击"},{"连破","横扫千军","后发制人","杀气诀"},{"披坚执锐","横扫千军","后发制人","杀气诀"}
	elseif mp == "化生寺" then
		return {"活血","推气过宫","我佛慈悲","佛眷"},{"聚气","金刚护法","金刚护体","推气过宫"},{"唧唧歪歪","谆谆教诲","金刚护体","达摩护体"}
	elseif mp == "龙宫" then
		return {"龙卷雨击","龙腾","龙魂","龙魂"},{"龙卷雨击","龙腾","龙魂","龙魂"},{"龙卷雨击","龙腾","龙魂","龙魂"}
	elseif mp == "魔王寨" then
		return {"三昧真火","飞砂走石","牛劲","魔冥"},{"三昧真火","飞砂走石","牛劲","魔冥"},{"三昧真火","飞砂走石","牛劲","魔冥"}
	elseif mp == "神木林" then
		return {"风灵","落叶萧萧","荆棘舞","鞭挞"},{"风灵","蛊木迷瘴","催化","雾杀"},{"风灵","木精","风萦","疾风秋叶","古藤秘咒"}
	elseif mp == "方寸山" then
		return {"催眠符","凝神术","失心符","落魄符"},{"五雷咒","落雷符","悲恸","奔雷"},{"五雷正法","雷法·崩裂","雷法·震煞","雷法·坤伏","咒符"}
	elseif mp == "女儿村" then
		return {"似玉生香","莲步轻舞","如花解语","自矜"},{"雨落寒沙","子母神针","似玉生香"},{"葬玉焚花","满天花雨","自矜"}
	elseif mp == "天宫" then
		return {"错乱","镇妖","掌心雷","知己知彼"},{"雷霆万钧","天神护体","电芒"},{"风雷斩","霹雳弦惊","雷怒霆激","返璞"}
	elseif mp == "普陀山" then
		return {"普渡众生","自在心法","杨柳甘露"},{"紧箍咒","日光华","莲心剑意"},{"五行珠","日光耀","剑意莲心"}
	elseif mp == "盘丝洞" then
		return {"含情脉脉","神迷","魔音摄魂","天罗地网"},{"含情脉脉","神迷","魔音摄魂","姐妹同心"},{"千蛛噬魂","蛛丝缠绕","神迷","天罗地网"}
	elseif mp == "阴曹地府" then
		return {"锢魂术","尸腐毒","魂飞魄散","阎罗令"},{"锢魂术","尸腐毒","魂飞魄散","六道无量"},{"血影蚀心","百鬼噬魂","魂飞魄散","幽冥鬼眼"}
	elseif mp == "狮驼岭" then
		return {"变身","鹰击","连环击","象形","狮搏"},{"驯兽·幼狮","幼狮之搏","变身","狮搏"},{"狂怒","变身","鹰击","连环击","象形"}
	elseif mp == "五庄观" then
		return {"日月乾坤","生命之泉","炼气化神"},{"烟雨剑法","飘渺式","骤雨"},{"敲金击玉","还丹","金击式"}
	elseif mp == "无底洞" then
		return {"金莲","地涌金莲","燃血术","由己渡人"},{"夺魄令","煞气诀","惊魂掌","燃血术"},{"裂魂","夺命咒","追魂刺","燃血术"}
	elseif mp == "凌波城" then
		return {"战意","天崩地裂","翻江搅海","吞山","饮海"},{"战意","超级战意","天崩地裂","翻江搅海"},{"战意","天眼神通","天崩地裂","翻江搅海"}
	elseif mp == "花果山" then
		return {"如意神通","当头一棒","神针撼海","无所遁形"},{"如意神通","当头一棒","神针撼海","无所遁形"},{"如意神通","棒掀北斗","兴风作浪","无所遁形"}
	elseif mp == "九黎城" then
		return {"枫影二刃","一斧开天","三荒尽灭","铁血生风","力辟苍穹"},{"枫影二刃","一斧开天","三荒尽灭","铁血生风","力辟苍穹"},{"枫影二刃","一斧开天","三荒尽灭","铁血生风","力辟苍穹"}
	end
end
local function sk(mp)
	if mp == "大唐官府" then
		local 技能表={
			浴血豪侠={"目空", "风刃", "扶阵", "翩鸿", "勇武", "长驱直入", "杀意", "念心", "静岳", "干将", "勇念", "神凝", "狂狷", "不惊", "傲视", "破空", "历战", "安神", "额外能力", "无敌", "浴血豪侠"},
			无双战神={"目空", "勇进", "突刺", "翩鸿", "勇武", "长驱直入", "亢强", "念心", "静岳", "干将", "勇念", "神凝", "惊天动地", "不惊", "突进", "破势", "孤勇", "熟练", "额外能力", "破军", "无双战神"},
			虎贲上将={"潜心", "笃志", "昂扬", "效法", "追戮", "烈光", "摧枯拉朽", "肃杀", "厉兵", "怒伤", "奉还", "催迫", "攻伐", "暴突", "诛伤", "破刃", "奋战", "灵能", "额外能力", "披挂上阵", "虎贲上将"}
		}
		return 技能表
	elseif mp == "化生寺" then
		local 技能表={
			杏林妙手={"销武", "止戈", "圣手", "妙手", "仁心", "化瘀", "佛显", "心韧", "归气", "天照", "舍利", "佛佑", "佛法", "佛性", "妙悟", "慈心", "虔诚", "佛缘", "额外能力", "渡劫金身", "杏林妙手"},
			护法金刚={"施他", "佛屠", "销武", "聚念", "仁心", "磅礴", "佛显", "心韧", "归气", "感念", "舍利", "无碍", "佛法", "佛性", "妙悟", "慈心", "映法", "流刚", "额外能力", "诸天看护", "护法金刚"},
			无量尊者={"诵律", "授业", "修习", "诵经", "悲悯", "解惑", "持戒", "生花", "悟彻", "抚琴", "舍利", "静气", "自在", "无量", "慧定", "金刚", "达摩", "韦陀", "额外能力", "坐禅", "无量尊者"}
		}
		return 技能表
	elseif mp == "龙宫" then
		local 技能表={
			海中蛟虬={"波涛", "破浪", "狂浪", "叱咤", "踏涛", "龙啸", "逐浪", "龙珠", "龙息", "龙慑", "傲翔", "飞龙", "骇浪", "月光", "戏珠", "汹涌", "龙魄", "斩浪", "额外能力", "亢龙归海", "海中蛟虬"},
			云龙真身={"波涛", "破浪", "云霄", "呼风", "踏涛", "清吟", "龙息", "龙珠", "唤雨", "龙慑", "傲翔", "飞龙", "戏珠", "月光", "云变", "沐雨", "龙魄", "摧意", "额外能力", "雷浪穿云", "云龙真身"},
			沧海潜龙={"傲岸", "云魂", "雨魄", "盘龙", "踏涛", "叱咤", "凛然", "龙珠", "回灵", "龙慑", "傲翔", "飞龙", "戏珠", "月光", "波涛", "龙钩", "睥睨", "惊鸿", "额外能力", "潜龙在渊", "沧海潜龙"},
		}
		return 技能表
	elseif mp == "魔王寨" then
		local 技能表={
			平天大圣={"充沛", "震怒", "激怒", "蚀天", "邪火", "赤暖", "火神", "震天", "真炎", "神焰", "崩摧", "焚尽", "咆哮", "狂月", "燃魂", "威吓", "连营", "魔心", "额外能力", "魔焰滔天", "平天大圣"},
			盖世魔君={"充沛", "震怒", "炙烤", "烈焰", "赤暖", "邪火", "火神", "震天", "折服", "焰星", "崩摧", "焰威", "咆哮", "狂月", "魔焱", "威吓", "连营", "狂劲", "额外能力", "升温", "盖世魔君"},
			风火妖王={"五蕴神焰", "烈火真言", "漫卷狂沙", "极炙", "咒言", "摧山", "不忿", "震天", "融骨", "神焰", "焦土", "不灭", "烬藏", "固基", "惊悟", "威吓", "旋阳", "魔心", "额外能力", "风火燎原", "风火妖王"},
		}
		return 技能表
	elseif mp == "神木林" then
		local 技能表={
			通天法王={"法身", "风魂", "灵佑", "追击", "咒法", "狂叶", "劲草", "冰锥", "苍埃", "神木", "月影", "薪火", "纯净", "蔓延", "破杀", "星光", "滋养", "灵归", "额外能力", "风卷残云", "通天法王"},
			巫影祭司={"风魂", "迷缚", "法身", "伏毒", "咒法", "灵木", "绞藤", "冰锥", "寄生", "神木", "月影", "薪火", "纯净", "蔓延", "破杀", "激活", "滋养", "毒萃", "额外能力", "凋零之歌", "巫影祭司"},
			灵木神侍={"风魂", "灵秀", "归原", "苍风", "咒法", "焕新", "萦风", "奉愿", "秀木", "神木", "月影", "薪火", "凉秋", "蔓延", "碾杀", "星光", "滋养", "灵精", "额外能力", "枯木逢春", "灵木神侍"},
		}
		return 技能表
	elseif mp == "方寸山" then
		local 技能表={
			拘灵散修={"雷动", "苦缠", "灵咒", "黄粱", "制约", "必果", "补缺", "不倦", "精炼", "化身", "调息", "幻变", "斗法", "吐纳", "专神", "鬼念", "灵威", "碎甲", "额外能力", "顺势而为", "拘灵散修"},
			伏魔天师={"驱雷", "策电", "雷动", "鬼怮", "穿透", "余悸", "宝诀", "妙用", "不灭", "化身", "怒霆", "批亢", "顺势", "炼魂", "吐纳", "灵能", "碎甲", "摧心", "额外能力", "钟馗论道", "伏魔天师"},
			五雷正宗={"震怒", "雷动", "天篆", "咒诀", "穿透", "符威", "宝诀", "妙用", "不灭", "雷法·翻天", "吞雷", "雷法·倒海", "顺势", "神机", "吐纳", "造化", "碎甲", "摧心", "额外能力", "五雷·挪移", "五雷正宗"},
		}
		return 技能表
	elseif mp == "女儿村" then
		local 技能表={
			绝代妖娆={"独尊", "暗伤", "重明", "倩影", "花舞", "风行", "傲娇", "花护", "空灵", "叶护", "国色", "轻霜", "抑怒", "机巧", "毒雾", "嫣然", "磐石", "倾国", "额外能力", "碎玉弄影", "绝代妖娆"},
			花雨伊人={"涂毒", "杏花", "暗伤", "淬芒", "花舞", "暗刃", "傲娇", "花护", "天香", "轻霜", "鸿影", "百花", "毒雾", "毒引", "余韵", "磐石", "飞花", "花殇", "额外能力", "鸿渐于陆", "花雨伊人"},
			花间美人={"花刺", "花骨", "汹涌", "花落", "花开", "花雨", "毒芒", "追毒", "曼珠", "清澈", "轻刃", "怒放", "驯宠", "乘胜", "痴念", "磐石", "轻霜", "毒引", "额外能力", "花谢花飞", "花间美人"},
		}
		return 技能表
	elseif mp == "天宫" then
		local 技能表={
			镇妖神使={"威吓", "疾雷", "轰鸣", "趁虚", "余韵", "缭乱", "震慑", "神念", "藏招", "苏醒", "护佑", "坚壁", "月桂", "怒火", "套索", "神律", "神尊", "洞察", "额外能力", "画地为牢", "镇妖神使"},
			踏雷天尊={"频变", "威吓", "惊曜", "震荡", "轰鸣", "驭意", "电掣", "神念", "伏魔", "雷霆汹涌", "苏醒", "天劫", "怒电", "共鸣", "灵光", "洞察", "仙音", "雷波", "额外能力", "风雷韵动", "踏雷天尊"},
			霹雳真君={"霆震", "疾雷", "激越", "存雄", "余韵", "慨叹", "电掣", "伏魔", "惊霆", "雷吞", "苏醒", "电光火石", "神采", "劲健", "啸傲", "神律", "气势", "洞察", "额外能力", "威仪九霄", "霹雳真君"},
		}
		return 技能表
	elseif mp == "普陀山" then
		local 技能表={
			莲台仙子={"推衍", "化戈", "普照", "莲花心音", "静心", "慈佑", "劳心", "普渡", "度厄", "甘露", "清净", "莲动", "法华", "灵动", "感念", "玉帛", "雨润", "道衍", "额外能力", "波澜不惊", "莲台仙子"},
			五行咒师={"庄严", "借灵", "推衍", "默诵", "静心", "莲花心音", "赐咒", "普渡", "慧眼", "无怖", "清净", "秘术", "感念", "莲心剑意", "灵动", "道衍", "缘起", "法咒", "额外能力", "五行制化", "五行咒师"},
			落伽神女={"湛然", "因缘", "莲音", "安忍", "静心", "低眉", "顿悟", "怒目", "馀威", "清净", "业障", "困兽", "无尽", "抖擞", "莲华", "相生", "智念", "执念", "额外能力", "万象", "落伽神女"},
		}
		return 技能表
	elseif mp == "盘丝洞" then
		local 技能表={
			风华舞圣={"粘附", "妖气", "怜心", "迷瘴", "鼓乐", "魔音", "玲珑", "安抚", "丹香", "迷梦", "忘川", "连绵", "情劫", "绝殇", "幻镜", "结阵", "媚态", "绝媚", "额外能力", "落花成泥", "风华舞圣"},
			迷情妖姬={"粘附", "妖气", "怜心", "迷瘴", "鼓乐", "忘忧", "玲珑", "安抚", "倾情", "连绵", "忘川", "意乱", "情劫", "魔瘴", "迷意", "结阵", "绝媚", "利刃", "额外能力", "偷龙转凤", "迷情妖姬"},
			百媚魔姝={"粘附", "杀戮", "罗网", "天网", "凌弱", "制怒", "狂击", "千蛛", "引诛", "附骨", "亡缚", "罗刹", "障眼", "连绵", "意乱", "结阵", "牵魂蛛丝", "扑袭", "额外能力", "绝命毒牙", "百媚魔姝"},
		}
		return 技能表
	elseif mp == "阴曹地府" then
		local 技能表={
			勾魂阎罗={"阎罗", "回旋", "夜行", "入骨", "聚魂", "拘魄", "索魂", "伤魂", "瘴幕", "黄泉", "幽冥", "冥视", "幽光", "泉爆", "鬼火", "魂飞", "汲魂", "扼命", "额外能力", "魍魉追魂", "勾魂阎罗"},
			六道魍魉={"阎罗", "回旋", "夜行", "聚魂", "狱火", "六道", "索魂", "伤魂", "百炼", "黄泉", "幽冥", "百爪狂杀", "咒令", "泉爆", "鬼火", "恶焰", "汲魂", "噬毒", "额外能力", "夜之王者", "六道魍魉"},
			诛刑毒师={"毒炽", "回旋", "阴翳", "聚魂", "狱火", "入魂", "毒慑", "破印", "瘴幕", "无赦咒令", "幽冥", "通瞑", "狂宴", "鬼火", "轮回", "蚀骨", "汲魂", "恶焰", "额外能力", "生杀予夺", "诛刑毒师"},
		}
		return 技能表
	elseif mp == "狮驼岭" then
		local 技能表={
			嗜血狂魔={"爪印", "迅捷", "驭兽", "化血", "宁息", "兽王", "威压", "怒象", "鹰啸", "九天", "魔息", "协战", "怒火", "狂袭", "癫狂", "死地", "乱击", "肝胆", "额外能力", "背水", "嗜血狂魔"},
			万兽之王={"拟形", "念主", "夜视", "宁息", "饮血", "健壮", "守势", "狂化", "矫健", "协同", "九天", "争宠", "羁绊", "狂袭", "钢牙", "追逐", "逞凶", "肝胆", "额外能力", "功勋", "万兽之王"},
			狂怒斗兽={"狂躁", "狂化", "狂啸", "攫取", "屏息", "不羁", "狮噬", "象踏", "长啸", "九天", "魔息", "协战", "羁绊", "狂袭", "狂血", "狂乱", "雄风", "狩猎", "额外能力", "困兽之斗", "狂怒斗兽"},
		}
		return 技能表
	elseif mp == "五庄观" then
		local 技能表={
			清心羽客={"体恤", "运转", "行气", "心浪", "养生", "蓄志", "归本", "修心", "存思", "修身", "同辉", "守中", "乾坤", "意境", "存神", "陌宝", "心随意动", "玄机", "额外能力", "清风望月", "清心羽客"},
			乾坤力士={"体恤", "锤炼", "神附", "心浪", "养生", "强击", "无极", "修心", "混元", "修身", "剑气", "雨杀", "意境", "起雨", "滂沱", "剑势", "心随意动", "致命", "额外能力", "天命剑法", "乾坤力士"},
			万寿真仙={"木摧", "道果", "饮露", "炼果", "心浪", "聚力", "无极", "修心", "混元", "刺果", "修身", "三元", "凝神", "纳气", "气盛", "剑势", "还元", "致命", "额外能力", "落土止息", "万寿真仙"},
		}
		return 技能表
	elseif mp == "无底洞" then
		local 技能表={
			妙谛金莲={"灵照", "秉幽", "护法", "涌泉", "绝处逢生", "烛照", "华光", "风墙", "血潮", "精进", "救人", "灵身", "持戒", "罗汉", "灵通", "忍辱", "暗潮", "噬魂", "额外能力", "同舟共济", "妙谛金莲"},
			摄魂迷影={"阴魅", "诡印", "萦魄", "御兽", "绝处逢生", "陷阱", "椎骨", "风墙", "血潮", "灵身", "精进", "救人", "烈煞", "持戒", "罗汉", "忍辱", "暗潮", "噬魂", "额外能力", "妖风四起", "摄魂迷影"},
			幽冥巫煞={"弥愤", "魂守", "刺骨", "余咒", "鬼袭", "羽裂", "分魄", "盛怒", "血潮", "夺血", "灵变", "深刻", "牵动", "独一", "聚魂", "纠缠", "灵身", "踏魄", "额外能力", "冥煞", "幽冥巫煞"},
		}
		return 技能表
	elseif mp == "凌波城" then
		local 技能表={
			九天武圣={"山破", "战诀", "无双", "聚气", "贯通", "魂聚", "神躯", "斩魔", "不动", "力战", "破击", "巧变", "海沸", "怒火", "煞气", "强袭", "混元", "再战", "额外能力", "天神怒斩", "九天武圣"},
			灵霄斗士={"石摧", "战诀", "天泽", "聚气", "贯通", "魂聚", "神躯", "斩魔", "不动", "妙得", "闪雷", "惊涛", "海沸", "怒火", "煞气", "乘势", "追袭", "再战", "额外能力", "真君显灵", "灵霄斗士"},
			风云战将={"山破", "战诀", "天泽", "凝息", "贯通", "魂聚", "神躯", "斩魔", "不动", "威震", "盛势", "天眼", "海沸", "怒火", "煞气", "蓄势", "杀罚", "再战", "额外能力", "耳目一新", "风云战将"},
		}
		return 技能表
	elseif mp == "花果山" then
		local 技能表={
			齐天武圣={"威仪", "逐胜", "愈勇", "斗志", "忘形", "贪天", "显圣", "火眼", "棒打雄风", "闹天", "铁骨", "填海", "伏妖", "豪胆", "压邪", "翻天", "圈养", "荡魔", "额外能力", "齐天神通", "齐天武圣"},
			斗战真神={"顽心", "逐胜", "自在", "变通", "忘形", "顽性", "显圣", "金睛", "棒打雄风", "通天", "铁骨", "威震", "伏妖", "豪胆", "压邪", "朝拜", "圈养", "荡魔", "额外能力", "战神", "斗战真神"},
			通天行者={"威仪", "闹海", "愈勇", "斗志", "忘形", "顽性", "显圣", "逞胜", "得意", "斗战", "添威", "胜意", "大圣", "冲霄", "锻炼", "朝拜", "圈养", "荡魔", "额外能力", "齐天神通", "通天行者"},
		}
		return 技能表
	elseif mp == "九黎城" then
		local 技能表={
			铁火战魔={"枫魂","怒刃","震怒","俾睨","识破","得势","飞扬","凌人","生风","蛮横","难保","乘风","擎天","族魂","魂力","狂暴","驭魔","野蛮","额外能力","魔神之刃","铁火战魔"}
		}
		return 技能表
	end
end

function 场景类_经脉流派:初始化(根)
	self.ID = 113
	self.x = 118
	self.y = 77
	self.xx = 0
	self.yy = 0
	self.注释 = "经脉流派"
	self.可视 = false
	self.鼠标 = false
	self.焦点 = false
	self.可移动 = true
	local 资源 = 根.资源
	local 按钮 = 根._按钮
	local 自适应 = 根._自适应
	self.资源组 = {
		[1] = 自适应.创建(0,1,605,467,3,9),
		[2] = 按钮.创建(自适应.创建(18,4,16,16,4,3),0,0,4,true,true),
		[3] = 资源:载入('pic/经脉/123.png',"图片"),
		[4] = 按钮.创建(自适应.创建(12,4,150,22,1,3),0,0,4,true,true),
		[5] = 按钮.创建(自适应.创建(12,4,150,22,1,3),0,0,4,true,true),
		[6] = 按钮.创建(自适应.创建(12,4,150,22,1,3),0,0,4,true,true),
		[7] = 资源:载入('pic/经脉/124.png',"图片"),
		[8] = 资源:载入('pic/经脉/输出.png',"图片"),
		[9] = 资源:载入('pic/经脉/封印.png',"图片"),
		[10] = 资源:载入('pic/经脉/辅助.png',"图片"),
		[11] = {},
		[12] = {},
		[13] = {},
		[14] = 资源:载入('common/wzife.wdf',"网易WDF动画",0x22D22D6D), --光效
		[15] = 按钮.创建(自适应.创建(12,4,99,22,1,3),0,0,4,true,true," 启用此流派"),
		[16] = 资源:载入('pic/经脉/dg.png',"图片"),--资源:载入('wzife.wd3',"网易WDF动画",2286168415),
		[17] = 资源:载入('pic/经脉/输出1.png',"图片"),
		[18] = 资源:载入('pic/经脉/封印1.png',"图片"),
		[19] = 资源:载入('pic/经脉/辅助1.png',"图片"),
	-- 打勾  294257081 common/wzife.wdf
	}
	for i=4,6 do
		self.资源组[i]:绑定窗口_(self.ID)
	end
	self.资源组[2]:绑定窗口_(self.ID)
	self.资源组[15]:绑定窗口_(self.ID)
	self.神器格子=资源:载入('pic/经脉/sqgz.png',"图片")
	self.红=资源:载入('pic/经脉/hong.png',"图片")
	self.黑=资源:载入('pic/经脉/hei.png',"图片")
	local jn = 根._技能格子
	for i=1,21 do
		self.资源组[11][i] = jn(0,0,n,"奇经八脉")
		self.资源组[12][i] = jn(0,0,n,"奇经八脉")
		self.资源组[13][i] = jn(0,0,n,"奇经八脉")
	end
	self.技能展示1={}
	self.技能展示2={}
	self.技能展示3={}
	for n=1,6 do
		self.技能展示1[n]=jn(0,0,n,"奇经八脉1")
		self.技能展示2[n]=jn(0,0,n,"奇经八脉2")
		self.技能展示3[n]=jn(0,0,n,"奇经八脉3")
	end
	self.选中 = nil
	tp = 根
	self.窗口时间 = 0
	zt=tp.字体表.普通字体
	zzt=tp.字体表.猫猫字体1
end

function 场景类_经脉流派:打开()
	if self.可视 then
		self.选中 = nil
		self.显示界面=1
		self.编号=1
		self.可视 = false
	else
		if tp.队伍[1].门派 == "无门派" then
			tp.常规提示:打开("#Y/加入门派方可查看")
			return
		end
		insert(tp.窗口_,self)
		for i=1,3 do
			if tp.队伍[1].奇经八脉["当前流派"] == tp.队伍[1].奇经八脉[i] then
				self.显示界面=i
				self.编号=i+10
				break
			end
		end
		self.神器 = {}
		self.神器.名称 = 门派神器名称[tp.队伍[1].门派]
		local 神器资源=引擎.取物品(self.神器.名称)
		self.神器.小动画 = tp.资源:载入(神器资源[11],"网易WDF动画",神器资源[12])
		self.神器.大动画 = tp.资源:载入(神器资源[11],"网易WDF动画",神器资源[13])
		self.神器.说明 = 神器资源[1]
		self.法宝 = {}
		self.法宝.名称,self.定位1,self.定位2,self.定位3 = 法宝名称(tp.队伍[1].门派)
		self.输出1=nil
		self.辅助1=nil
		self.封印1=nil
		self.输出2=nil
		self.辅助2=nil
		self.封印2=nil
		self.输出3=nil
		self.辅助3=nil
		self.封印3=nil

		if self.定位1=="物理输出" or self.定位1=="法术输出" or self.定位1=="固定伤害" then
		    self.输出1=true
		elseif self.定位1=="死亡禁锢" or self.定位1=="封印控制" then
			self.封印1=true
		elseif self.定位1=="治疗回复" or self.定位1=="增益强化" then
			self.辅助1=true
		end
		if self.定位2=="物理输出" or self.定位2=="法术输出" or self.定位2=="固定伤害" then
		    self.输出2=true
		elseif self.定位2=="死亡禁锢" or self.定位2=="封印控制" then
			self.封印2=true
		elseif self.定位2=="治疗回复" or self.定位2=="增益强化" then
			self.辅助2=true
		end
		if self.定位3=="物理输出" or self.定位3=="法术输出" or self.定位3=="固定伤害" then
		    self.输出3=true
		elseif self.定位3=="死亡禁锢" or self.定位3=="封印控制" then
			self.封印3=true
		elseif self.定位3=="治疗回复" or self.定位3=="增益强化" then
			self.辅助3=true
		end

		local 法宝资源=引擎.取物品(self.法宝.名称)
		self.法宝.小动画 = tp.资源:载入(法宝资源[11],"网易WDF动画",法宝资源[12])
		self.法宝.大动画 = tp.资源:载入(法宝资源[11],"网易WDF动画",法宝资源[13])
		self.法宝.说明 = 法宝资源[1]
		local mmww1,mmww2,mmww3=特色法术(tp.队伍[1].门派)

		for i=1,#mmww1 do
		    local 临时技能=tp._技能.创建()
		    临时技能:置对象(mmww1[i],2,"精美礼品")
		  	self.技能展示1[i]:置技能(临时技能,nil,true)
		end
		for i=1,#mmww2 do
		    local 临时技能=tp._技能.创建()
		    临时技能:置对象(mmww2[i],2,"精美礼品1")
		  	self.技能展示2[i]:置技能(临时技能,nil,true)
		end
		for i=1,#mmww3 do
		    local 临时技能=tp._技能.创建()
		    临时技能:置对象(mmww3[i],2,"精美礼品2")
		  	self.技能展示3[i]:置技能(临时技能,nil,true)
		end

		-- print(tp.队伍[1].门派)
		local qj = sk(tp.队伍[1].门派)
		 --table.print(tp.队伍[1].奇经八脉)
		 if #tp.队伍[1].奇经八脉 ==1 then
		 	for m=1,21 do
			local jj = tp._技能()
			jj:置对象(qj[tp.队伍[1].奇经八脉[1]][m])
			self.资源组[11][m]:置经脉(tp.队伍[1].奇经八脉[1],jj)
			end
		 else
			for m=1,21 do
				local jj = tp._技能()
				jj:置对象(qj[tp.队伍[1].奇经八脉[1]][m])
				self.资源组[11][m]:置经脉(tp.队伍[1].奇经八脉[1],jj)
			end
			for m=1,21 do
				local jj = tp._技能()
				jj:置对象(qj[tp.队伍[1].奇经八脉[2]][m])
				self.资源组[12][m]:置经脉(tp.队伍[1].奇经八脉[2],jj)
			end
			for m=1,21 do
				local jj = tp._技能()
				jj:置对象(qj[tp.队伍[1].奇经八脉[3]][m])
				self.资源组[13][m]:置经脉(tp.队伍[1].奇经八脉[3],jj)
			end
		end

		if tp.队伍[1].奇经八脉.技能树 == nil then
		    tp.队伍[1].奇经八脉.技能树 = {1,2,3}
		end

		if tp.队伍[1].奇经八脉.技能树 ~= nil and type(tp.队伍[1].奇经八脉.技能树) ~= "number" then
			for n=1,#tp.队伍[1].奇经八脉.技能树 do
				self.资源组[self.编号][tp.队伍[1].奇经八脉.技能树[n]].技能树 = true
			end
		end
		tp.运行时间 = tp.运行时间 + 1
	 	self.窗口时间 = tp.运行时间
    	self.可视 = true
	end
end

function 场景类_经脉流派:学习经脉()
	for i=1,3 do
		if tp.队伍[1].奇经八脉["当前流派"] == tp.队伍[1].奇经八脉[i] then
			self.显示界面=i
			self.编号=i+10
			break
		end
	end

	self.资源组[self.编号][self.选中].尝试 = true
	local qj = sk(tp.队伍[1].门派)
	for m=1,21 do
		local jj = tp._技能()
		-- print(qj[tp.队伍[1].奇经八脉[self.显示界面]][m])
		jj:置对象(qj[tp.队伍[1].奇经八脉[self.显示界面]][m])
		self.资源组[self.编号][m]:置经脉(tp.队伍[1].奇经八脉[self.显示界面],jj)
		self.资源组[self.编号][m].技能树 = nil
	end

	tp.队伍[1].奇经八脉[self.资源组[self.编号][self.选中].技能.名称] = 1

	if self.选中 >= 20 then
		tp.队伍[1].奇经八脉[self.资源组[self.编号][19].技能.名称] = 1
		tp.队伍[1].奇经八脉[self.资源组[self.编号][21].技能.名称] = 1
	end
	local 技能树 = self:技能树(self.选中)
	local qj = sk(tp.队伍[1].门派)[tp.队伍[1].奇经八脉.当前流派]
	for m,v in pairs(qj) do
		local jj = tp._技能()
		jj:置对象(qj[m])
		self.资源组[self.编号][m]:置经脉(tp.队伍[1].奇经八脉[self.显示界面],jj)
		self.资源组[self.编号][m].技能树 = nil
		self.资源组[self.编号][m].尝试 = nil
		if 技能树 ~= nil and 技能树[1] == m then
			self.资源组[self.编号][m].技能树 = true
			remove(技能树, 1)
		end
	end
	tp.队伍[1].奇经八脉.技能树 = self:技能树(self.选中) or 1
end

function 场景类_经脉流派:切换流派()
	for i=1,3 do
		if tp.队伍[1].奇经八脉["当前流派"] == tp.队伍[1].奇经八脉[i] then
			self.显示界面=i
			self.编号=i+10 --这里的编号是 11-13的技能组
			break
		end
	end
	for i=11,13 do
		for n=1,21 do --先把技能树全归零
			if self.资源组[i] and self.资源组[i][n] then
			    self.资源组[i][n].技能树=nil
			end
		end
	end
	-- print(self.编号)
	local qj = sk(tp.队伍[1].门派)
	for m=1,21 do
		local jj = tp._技能()
		-- print(qj[tp.队伍[1].奇经八脉[1]][m])
		jj:置对象(qj[tp.队伍[1].奇经八脉[1]][m])
		self.资源组[11][m]:置经脉(tp.队伍[1].奇经八脉[1],jj)
	end
	for m=1,21 do
		local jj = tp._技能()
		jj:置对象(qj[tp.队伍[1].奇经八脉[2]][m])
		self.资源组[12][m]:置经脉(tp.队伍[1].奇经八脉[2],jj)
	end
	for m=1,21 do
		local jj = tp._技能()
		jj:置对象(qj[tp.队伍[1].奇经八脉[3]][m])
		self.资源组[13][m]:置经脉(tp.队伍[1].奇经八脉[3],jj)
	end
	-- if tp.队伍[1].奇经八脉.技能树 == nil then
    -- tp.队伍[1].奇经八脉.技能树 = {1,2,3}
	-- end
	-- if tp.队伍[1].奇经八脉.技能树 ~= nil and type(tp.队伍[1].奇经八脉.技能树) ~= "number" then
	for n=1,3 do
		-- print(tp.队伍[1].奇经八脉.技能树[n])
		self.资源组[self.编号][n].技能树 = true --前面三个可点选经脉刷新
		-- self.资源组[self.编号][tp.队伍[1].奇经八脉.技能树[n]].技能树 = true
	end
	-- end

end

function 场景类_经脉流派:显示(dt,x,y)
	self.焦点 = false
	self.资源组[2]:更新(x,y)
	self.资源组[1]:显示(self.x,self.y)
	self.资源组[3]:显示(self.x,self.y+50)
	if self.资源组[2]:事件判断() then
		self:打开()
		return false
	end
	tp.窗口标题背景_:显示(self.x-76+self.资源组[1].宽度/2,self.y)
	引擎.场景.字体表.标题字体:显示(self.x+self.资源组[1].宽度/2+13,self.y+3,"经脉流派")
	self.资源组[2]:显示(self.x-18+self.资源组[1].宽度,self.y+2)
	for i=4,6 do
		self.资源组[i]:更新(x,y,self.显示界面 ~= i)
		if self.资源组[i]:事件判断() then
			self.显示界面 = i-3
		end
	end
	if #tp.队伍[1].奇经八脉 ==1 then
		self.资源组[4]:显示(self.x+75,self.y+26,nil,nil,nil,self.显示界面==1,2)
	else
	self.资源组[4]:显示(self.x+75,self.y+26,nil,nil,nil,self.显示界面==1,2)
	self.资源组[5]:显示(self.x+75+160,self.y+26,nil,nil,nil,self.显示界面==2,2)
	self.资源组[6]:显示(self.x+75+160+160,self.y+26,nil,nil,nil,self.显示界面==3,2)
	end
	self.神器格子:显示(self.x+31,self.y+361)
	self.神器格子:显示(self.x+31+175,self.y+361)
	self.法宝.小动画:显示(self.x+31,self.y+361)
	self.神器.小动画:显示(self.x+31+175,self.y+361)
	if self.神器.小动画:是否选中(x,y) then
		tp.物品格子焦点_:显示(self.x+202+6,self.y+358+5)
		tp.提示:商城提示(x,y,self.神器.名称,self.神器.说明,self.神器.大动画)
	elseif self.法宝.小动画:是否选中(x,y) then
		tp.物品格子焦点_:显示(self.x+27+6,self.y+358+5)
		tp.提示:商城提示(x,y,self.法宝.名称,self.法宝.说明,self.法宝.大动画)
	end
	if tp.队伍[1].奇经八脉.当前流派==tp.队伍[1].奇经八脉[1] then
		self.资源组[7]:显示(self.x+54,self.y+19)
	elseif tp.队伍[1].奇经八脉.当前流派==tp.队伍[1].奇经八脉[2] then
		self.资源组[7]:显示(self.x+212,self.y+19)
	else
	    self.资源组[7]:显示(self.x+371,self.y+19)
	end

	if #tp.队伍[1].奇经八脉 ==1 then
		self.资源组[4]:置文字("     "..tp.队伍[1].奇经八脉[1])
	else
	self.资源组[4]:置文字("     "..tp.队伍[1].奇经八脉[1])
	self.资源组[5]:置文字("     "..tp.队伍[1].奇经八脉[2])
	self.资源组[6]:置文字("     "..tp.队伍[1].奇经八脉[3])
	end

	if self.显示界面==1 then
	    local xx = 0
		local yy = 1
		local xxx=0
		local yyy=0
		local 灰色 = nil
		if tp.队伍[1].奇经八脉.当前流派==tp.队伍[1].奇经八脉[1] then
			-- print(1)
			self.资源组[16]:显示(self.x+245,self.y+435)
			self.资源组[14]:更新(dt)
			self.红:显示(self.x+98,self.y+148)
			zzt:置颜色(0xffCC0033):显示(self.x+102,self.y+152,self.定位1)
			if self.输出1 then
				self.资源组[8]:显示(self.x+24,self.y+74)
			elseif self.封印1 then
				self.资源组[9]:显示(self.x+24,self.y+74)
			elseif self.辅助1 then
				self.资源组[10]:显示(self.x+24,self.y+74)
			end
			for n=1,21 do
				self.资源组[11][n]:置坐标(self.x + xx * 58 + 35+365,self.y + yy * 53 - 5+17)
				if tp.队伍[1].奇经八脉[self.资源组[11][n].技能.名称] == nil and self.资源组[11][n].尝试 == nil then
					self.资源组[11][n].技能.模型:灰度级()
				end
				self.资源组[11][n]:显示(x,y,self.鼠标)
				if self.资源组[11][n].技能树 ~= nil then
					for i=1,#tp.队伍[1].奇经八脉.技能树 do
						if n == tp.队伍[1].奇经八脉.技能树[i] then
							self.资源组[14]:显示(self.x + xx * 58 + 25+365,self.y + yy * 53 - 14+17)
						end
					end
				end
				if self.资源组[11][n].技能 ~= nil and self.资源组[11][n].焦点 then
					tp.提示:技能(x,y,self.资源组[11][n].技能)
					if mouseb(0) and self.资源组[11][n].技能树 ~= nil and tp.队伍[1].奇经八脉[self.资源组[11][n].技能.名称] == nil then
						if tp.队伍[1].装备属性.可用乾元丹>0 then
							self.选中 = n
							tp.窗口.经脉加点:打开(self.选中)
							tp.窗口.经脉加点.x = self.x + 560
							tp.窗口.经脉加点.y = self.y + 100
						else
							-- if tp.队伍[1].等级<69 then --测试模式
							--     tp.常规提示:打开("#R/人物达到69级方可开启！")
							-- else
								tp.常规提示:打开("#R/可用乾元丹不足！")
							-- end
						end
					end
				end

				xx = xx + 1
				if xx >= 3 then
					xx = 0
					yy = yy + 1
				end
			end
		else
			灰色=true
			self.资源组[15]:更新(x,y)
			self.资源组[15]:显示(self.x+245,self.y+437)
			self.黑:显示(self.x+98,self.y+148)
			zzt:置颜色(0xff666666):显示(self.x+102,self.y+152,self.定位1)
			if self.资源组[15]:事件判断() then
				tp.窗口.经脉切换:打开(tp.队伍[1].奇经八脉.当前流派,tp.队伍[1].奇经八脉[1])
			end
		    for n=1,21 do
				self.资源组[11][n]:置坐标(self.x + xx * 58 + 35+365,self.y + yy * 53 - 5+17)
				self.资源组[11][n].技能.模型:灰度级()
				self.资源组[11][n]:显示(x,y,self.鼠标)
				if self.资源组[11][n].技能 ~= nil and self.资源组[11][n].焦点 then
					tp.提示:技能(x,y,self.资源组[11][n].技能)
				end
				xx = xx + 1
				if xx >= 3 then
					xx = 0
					yy = yy + 1
				end
			end
			if self.输出1 then
				self.资源组[17]:显示(self.x+24,self.y+74)
			elseif self.封印1 then
				self.资源组[18]:显示(self.x+24,self.y+74)
			elseif self.辅助1 then
				self.资源组[19]:显示(self.x+24,self.y+74)
			end
		end
		-- print("111",灰色)
		for i=1,6 do
			self.技能展示1[i]:置坐标(self.x + xxx * 120 + 35,self.y + yyy * 56 - 5+17+200)
			if self.技能展示1[i].技能 ~= nil then
				self.技能展示1[i]:显示(x,y,self.鼠标,nil,nil,nil,灰色)
				zt:置颜色(0xFF111111)

				zt:显示(self.x + xxx * 120 + 35+45,self.y + yyy * 56 - 5+17+200+15,self.技能展示1[i].技能.名称)
				if self.技能展示1[i].焦点 then
					tp.提示:技能(x,y,self.技能展示1[i].技能)
				end
			end
			xxx = xxx + 1
			if xxx >= 3 then
				xxx = 0
				yyy = yyy + 1
			end
		end
		tp.字体表.经脉文字:置颜色(黑色):显示(self.x+90,self.y+88,tp.队伍[1].奇经八脉[1])
		zzt:置颜色(黑色):显示(self.x+90,self.y+152-38,经脉描述[tp.队伍[1].门派][1])


	elseif self.显示界面==2 then

	    local xx = 0
		local yy = 1
		local xxx=0
		local yyy=0
		local 灰色 = nil
		if tp.队伍[1].奇经八脉.当前流派==tp.队伍[1].奇经八脉[2] then
			-- print(2)
			self.资源组[16]:显示(self.x+245,self.y+435)
			self.资源组[14]:更新(dt)
			self.红:显示(self.x+98,self.y+148)
			zzt:置颜色(0xffCC0033):显示(self.x+102,self.y+152,self.定位2)
			if self.输出2 then
				self.资源组[8]:显示(self.x+24,self.y+74)
			elseif self.封印2 then
				self.资源组[9]:显示(self.x+24,self.y+74)
			elseif self.辅助2 then
				self.资源组[10]:显示(self.x+24,self.y+74)
			end
			for n=1,21 do
				self.资源组[12][n]:置坐标(self.x + xx * 58 + 35+365,self.y + yy * 53 - 5+17)
				if tp.队伍[1].奇经八脉[self.资源组[12][n].技能.名称] == nil and self.资源组[12][n].尝试 == nil then
					-- table.print(self.资源组[12][n])
					self.资源组[12][n].技能.模型:灰度级()
				end
				self.资源组[12][n]:显示(x,y,self.鼠标)
				-- print(self.资源组[12][n].技能树)
				if self.资源组[12][n].技能树 ~= nil then
					self.资源组[14]:显示(self.x + xx * 58 + 25+365,self.y + yy * 53 - 14+17)
				end
				if self.资源组[12][n].技能 ~= nil and self.资源组[12][n].焦点 then
					tp.提示:技能(x,y,self.资源组[12][n].技能)
					if mouseb(0) and self.资源组[12][n].技能树 ~= nil and tp.队伍[1].奇经八脉[self.资源组[12][n].技能.名称] == nil then
						if tp.队伍[1].装备属性.可用乾元丹>0 then
							self.选中 = n
							tp.窗口.经脉加点:打开(self.选中)
							tp.窗口.经脉加点.x = self.x + 560
							tp.窗口.经脉加点.y = self.y + 100
						else
						 --    if tp.队伍[1].等级<69 then --测试模式
							--     tp.常规提示:打开("#R/人物达到69级方可开启！")
							-- else
								tp.常规提示:打开("#R/可用乾元丹不足！")
							-- end
						end
					end
				end

				xx = xx + 1
				if xx >= 3 then
					xx = 0
					yy = yy + 1
				end
			end
		else
			灰色=true
			self.资源组[15]:更新(x,y)
			self.资源组[15]:显示(self.x+245,self.y+437)
			self.黑:显示(self.x+98,self.y+148)
			zzt:置颜色(0xff666666):显示(self.x+102,self.y+152,self.定位2)
			if self.资源组[15]:事件判断() then
				if tp.队伍[1].门派=="狮驼岭" then
					tp.常规提示:打开("#R/该流派正在完善中…")
			    else
					tp.窗口.经脉切换:打开(tp.队伍[1].奇经八脉.当前流派,tp.队伍[1].奇经八脉[2])
				end
			end
		    for n=1,21 do
		    	if self.资源组[12][n] and self.资源组[12][n].技能 and self.资源组[12][n].技能.模型 then
					self.资源组[12][n]:置坐标(self.x + xx * 58 + 35+365,self.y + yy * 53 - 5+17)
					self.资源组[12][n].技能.模型:灰度级()
					self.资源组[12][n]:显示(x,y,self.鼠标)
					if self.资源组[12][n].技能 ~= nil and self.资源组[12][n].焦点 then
						tp.提示:技能(x,y,self.资源组[12][n].技能)
					end
				end
				xx = xx + 1
				if xx >= 3 then
					xx = 0
					yy = yy + 1
				end
			end
			if self.输出2 then
				self.资源组[17]:显示(self.x+24,self.y+74)
			elseif self.封印2 then
				self.资源组[18]:显示(self.x+24,self.y+74)
			elseif self.辅助2 then
				self.资源组[19]:显示(self.x+24,self.y+74)
			end
		end
		-- print("222",灰色)
		for i=1,6 do
			self.技能展示2[i]:置坐标(self.x + xxx * 120 + 35,self.y + yyy * 56 - 5+17+200)
			if self.技能展示2[i].技能 ~= nil then
				self.技能展示2[i]:显示(x,y,self.鼠标,nil,nil,nil,灰色)
				zt:置颜色(0xFF111111)
				zt:显示(self.x + xxx * 120 + 35+45,self.y + yyy * 56 - 5+17+200+15,self.技能展示2[i].技能.名称)
				if self.技能展示2[i].焦点 then
					tp.提示:技能(x,y,self.技能展示2[i].技能)
				end
			end
			xxx = xxx + 1
			if xxx >= 3 then
				xxx = 0
				yyy = yyy + 1
			end
		end
		tp.字体表.经脉文字:置颜色(黑色):显示(self.x+90,self.y+88,tp.队伍[1].奇经八脉[2])
		zzt:置颜色(黑色):显示(self.x+90,self.y+152-38,经脉描述[tp.队伍[1].门派][2])

	elseif self.显示界面==3 then

	    local xx = 0
		local yy = 1
		local xxx=0
		local yyy=0
		local 灰色 = nil
		if tp.队伍[1].奇经八脉.当前流派==tp.队伍[1].奇经八脉[3] then
			-- print(3)
			self.资源组[16]:显示(self.x+245,self.y+435)
			self.资源组[14]:更新(dt)
			self.红:显示(self.x+98,self.y+148)
			zzt:置颜色(0xffCC0033):显示(self.x+102,self.y+152,self.定位3)
			if self.输出3 then
				self.资源组[8]:显示(self.x+24,self.y+74)
			elseif self.封印3 then
				self.资源组[9]:显示(self.x+24,self.y+74)
			elseif self.辅助3 then
				self.资源组[10]:显示(self.x+24,self.y+74)
			end
			for n=1,21 do
				self.资源组[13][n]:置坐标(self.x + xx * 58 + 35+365,self.y + yy * 53 - 5+17)

				if tp.队伍[1].奇经八脉[self.资源组[13][n].技能.名称] == nil and self.资源组[13][n].尝试 == nil then
					self.资源组[13][n].技能.模型:灰度级()
				end
				self.资源组[13][n]:显示(x,y,self.鼠标)
				if self.资源组[13][n].技能树 ~= nil then
					self.资源组[14]:显示(self.x + xx * 58 + 25+365,self.y + yy * 53 - 14+17)
				end
				if self.资源组[13][n].技能 ~= nil and self.资源组[13][n].焦点 then
					tp.提示:技能(x,y,self.资源组[13][n].技能)
					if mouseb(0) and self.资源组[13][n].技能树 ~= nil and tp.队伍[1].奇经八脉[self.资源组[13][n].技能.名称] == nil then
						if tp.队伍[1].装备属性.可用乾元丹>0 then
							self.选中 = n
							tp.窗口.经脉加点:打开(self.选中)
							tp.窗口.经脉加点.x = self.x + 560
							tp.窗口.经脉加点.y = self.y + 100
						else
						 --    if tp.队伍[1].等级<69 then --测试模式
							--     tp.常规提示:打开("#R/人物达到69级方可开启！")
							-- else
								tp.常规提示:打开("#R/可用乾元丹不足！")
							-- end
						end
					end
				end

				xx = xx + 1
				if xx >= 3 then
					xx = 0
					yy = yy + 1
				end
			end
		else
			灰色=true
			self.资源组[15]:更新(x,y)
			self.资源组[15]:显示(self.x+245,self.y+437)
			self.黑:显示(self.x+98,self.y+148)
			zzt:置颜色(0xff666666):显示(self.x+102,self.y+152,self.定位3)
			if self.资源组[15]:事件判断() then
				if  tp.队伍[1].门派==""  then
					tp.常规提示:打开("#R/该流派正在完善中…")
				else
			    	tp.窗口.经脉切换:打开(tp.队伍[1].奇经八脉.当前流派,tp.队伍[1].奇经八脉[3])
				end
			end
		    for n=1,21 do
		    	if self.资源组[13][n] and self.资源组[13][n].技能 and self.资源组[13][n].技能.模型 then
		    	    self.资源组[13][n]:置坐标(self.x + xx * 58 + 35+365,self.y + yy * 53 - 5+17)
					self.资源组[13][n].技能.模型:灰度级()
					self.资源组[13][n]:显示(x,y,self.鼠标)
					if self.资源组[13][n].技能 ~= nil and self.资源组[13][n].焦点 then
						tp.提示:技能(x,y,self.资源组[13][n].技能)
					end
		    	end
				xx = xx + 1
				if xx >= 3 then
					xx = 0
					yy = yy + 1
				end
			end
			if self.输出3 then
				self.资源组[17]:显示(self.x+24,self.y+74)
			elseif self.封印3 then
				self.资源组[18]:显示(self.x+24,self.y+74)
			elseif self.辅助3 then
				self.资源组[19]:显示(self.x+24,self.y+74)
			end
		end
		-- print("333",灰色)
		for i=1,6 do
			self.技能展示3[i]:置坐标(self.x + xxx * 120 + 35,self.y + yyy * 56 - 5+17+200)
			if self.技能展示3[i].技能 ~= nil then
				self.技能展示3[i]:显示(x,y,self.鼠标,nil,nil,nil,灰色)
				zt:置颜色(0xFF111111)
				zt:显示(self.x + xxx * 120 + 35+45,self.y + yyy * 56 - 5+17+200+15,self.技能展示3[i].技能.名称)
				if self.技能展示3[i].焦点 then
					tp.提示:技能(x,y,self.技能展示3[i].技能)
				end
			end
			xxx = xxx + 1
			if xxx >= 3 then
				xxx = 0
				yyy = yyy + 1
			end
		end
		tp.字体表.经脉文字:置颜色(黑色):显示(self.x+90,self.y+88,tp.队伍[1].奇经八脉[3])
		zzt:置颜色(黑色):显示(self.x+90,self.y+152-38,经脉描述[tp.队伍[1].门派][3])
	end

end

function 场景类_经脉流派:技能树(a)
	if a == 1 or a == 4 or a == 7 or a == 10 or a == 13 or a == 16 then
		if a == 16 then
			return {20}
		else
			return {a+3,a+4}
		end
	elseif a == 2 or a == 5 or a == 8 or a == 11 or a == 14 or a == 17 then
		if a == 17 then
			return {20}
		else
			return {a+2,a+3,a+4}
		end
	elseif a == 3 or a == 6 or a == 9 or a == 12 or a == 15 or a == 18 then
		if a == 18 then
			return {20}
		else
			return {a+2,a+3}
		end
	end
end

function 场景类_经脉流派:检查点(x,y)
	if self.可视 and self.资源组[1]:是否选中(x,y)  then
		return true
	end
end

function 场景类_经脉流派:初始移动(x,y)
	tp.运行时间 = tp.运行时间 + 1
	if not tp.消息栏焦点 then
  		self.窗口时间 = tp.运行时间
 	end
	if not self.焦点 then
		tp.移动窗口 = true
	end
	if self.可视 and self.鼠标 and  not self.焦点 then
		self.xx = x - self.x
		self.yy = y - self.y
	end
end

function 场景类_经脉流派:开始移动(x,y)
	if self.可视 and self.鼠标 then
		self.x = x - self.xx
		self.y = y - self.yy
	end
end

return 场景类_经脉流派