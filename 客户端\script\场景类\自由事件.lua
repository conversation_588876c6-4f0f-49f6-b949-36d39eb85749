--======================================================================--
-- @作者: www.wnfj.com，联系QQ：79550111
-- @创建时间:   2018-03-03 02:34:19
-- @Last Modified time: 2024-12-05 17:26:07
--======================================================================--
local 场景类_自由事件 = class()

local floor = math.floor

function 场景类_自由事件:初始化(标记组)
	self.坐标 = 生成XY(floor(标记组[1]*20),floor(标记组[2]*20))
	self.唯一标识 = 标记组[7]
end

function 场景类_自由事件:显示()
	if 取两点距离(引擎.场景.角色坐标,self.坐标) < 200 then
		local a = 引擎.取自由事件(self.唯一标识)
	end
end

return 场景类_自由事件