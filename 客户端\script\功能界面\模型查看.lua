--======================================================================--
--	☆ 作者：作者QQ：79550111
--======================================================================--
local 场景类_模型查看 = class()
local tx = 引擎.取头像
local bw = require("gge包围盒")(0,0,140,34)
local ani = 引擎.取战斗模型
local format = string.format
local floor = math.floor
local min = math.min
local max = math.max
local tp,zts,zts1,zts2,ztstt3
local ceil = math.ceil
local tostring = tostring
local mousea = 引擎.鼠标按住
local mouseb = 引擎.鼠标弹起
local insert = table.insert
local 模型表 = {
	"竹节双剑_飞燕女_普通","八卦_虎头怪_普通","骨精灵_爪刺","影精灵_双巨斧","无极丝_玄彩娥_普通",
	"天将_普通","羽灵神_法杖","狼牙锤_虎头怪_普通","狸_普通","影精灵_爪刺",
	"腾云杖_巫蛮儿_普通","逍遥生_普通","游龙剑_剑侠客_普通","雨师_普通",
	"青铜短剑_逍遥生_普通","野猪_普通","大海龟_普通","飞燕女_普通",
	"腾云杖_巫蛮儿_普通","逍遥生_普通","游龙剑_剑侠客_普通","雨师_普通",
	"腾云杖_巫蛮儿_普通","逍遥生_普通","游龙剑_剑侠客_普通","雨师_普通",
	"腾云杖_巫蛮儿_普通","逍遥生_普通","游龙剑_剑侠客_普通","雨师_普通",
	"腾云杖_巫蛮儿_普通","逍遥生_普通","游龙剑_剑侠客_普通","雨师_普通",
	"腾云杖_巫蛮儿_普通","逍遥生_普通","游龙剑_剑侠客_普通","雨师_普通",
	"腾云杖_巫蛮儿_普通","逍遥生_普通","游龙剑_剑侠客_普通","雨师_普通",
	"腾云杖_巫蛮儿_普通","逍遥生_普通","游龙剑_剑侠客_普通","雨师_普通",
	"腾云杖_巫蛮儿_普通","逍遥生_普通","游龙剑_剑侠客_普通","雨师_普通",
	"腾云杖_巫蛮儿_普通","逍遥生_普通","游龙剑_剑侠客_普通","雨师_普通",
	"腾云杖_巫蛮儿_普通","逍遥生_普通","游龙剑_剑侠客_普通","雨师_普通",
	"腾云杖_巫蛮儿_普通","逍遥生_普通","游龙剑_剑侠客_普通","雨师_普通",
	"腾云杖_巫蛮儿_普通","逍遥生_普通","游龙剑_剑侠客_普通","雨师_普通",
	"腾云杖_巫蛮儿_普通","逍遥生_普通","游龙剑_剑侠客_普通","雨师_普通",
	"腾云杖_巫蛮儿_普通","逍遥生_普通","游龙剑_剑侠客_普通","雨师_普通",
	"腾云杖_巫蛮儿_普通","逍遥生_普通","游龙剑_剑侠客_普通","雨师_普通",
	"腾云杖_巫蛮儿_普通","逍遥生_普通","游龙剑_剑侠客_普通","雨师_普通",
	"腾云杖_巫蛮儿_普通","逍遥生_普通","游龙剑_剑侠客_普通","雨师_普通",
	"腾云杖_巫蛮儿_普通","逍遥生_普通","游龙剑_剑侠客_普通","雨师_普通",
	"腾云杖_巫蛮儿_普通","逍遥生_普通","游龙剑_剑侠客_普通","雨师_普通",
	"腾云杖_巫蛮儿_普通","逍遥生_普通","游龙剑_剑侠客_普通","雨师_普通",
	"腾云杖_巫蛮儿_普通","逍遥生_普通","游龙剑_剑侠客_普通","雨师_普通",
	"腾云杖_巫蛮儿_普通","逍遥生_普通","游龙剑_剑侠客_普通","雨师_普通",
	"腾云杖_巫蛮儿_普通","逍遥生_普通","游龙剑_剑侠客_普通","雨师_普通",
	"腾云杖_巫蛮儿_普通","逍遥生_普通","游龙剑_剑侠客_普通","雨师_普通",


}
function 场景类_模型查看:初始化(根)
	self.ID = 97
	self.x = 300
	self.y = 35
	self.xx = 0
	self.yy = 0
	self.注释 = "伙伴"
	self.可视 = false
	self.鼠标 = false
	self.焦点 = false
	self.可移动 = true
	local 资源 = 根.资源
	local 按钮 = 根._按钮
	local 自适应 = 根._自适应
	self.资源组 = {
		[0] = 自适应.创建(1,1,332,18,1,3,nil,18),
		[1] = 自适应.创建(0,1,340,453,3,9),
		[2] = 按钮.创建(自适应.创建(18,4,16,16,4,3),0,0,4,true,true),
		[3] = 按钮.创建(自适应.创建(12,4,43,22,1,3),0,0,4,true,true,"上"),
		[4] = 按钮.创建(自适应.创建(12,4,43,22,1,3),0,0,4,true,true,"下"),
	}
	for i=2,4 do
		self.资源组[i]:绑定窗口_(self.ID)
	end
	self.当前=1
	tp = 根
	zts = tp.字体表.普通字体
	zts1 = tp.字体表.普通字体__
	zts2 = tp.字体表.描边字体
	ztstt3 = tp.字体表.描边字体
end

function 场景类_模型查看:打开(sj)
	if self.可视 then
		self.加入 = 0
		self.选中 = 0
		self.头像组 = {}
		self.窗口时间 = 0
		self.可视 = false
		self.伙伴数据 = {}
		self.资源组[35]:置起始点(0)
		if tp.窗口.召唤兽资质栏.可视 then
			tp.窗口.召唤兽资质栏:打开()
		end
	else
		insert(tp.窗口_,self)
		tp.运行时间 = tp.运行时间 + 1
	    self.窗口时间 = tp.运行时间
	    self.可视 = true
	end
end

function 场景类_模型查看:置形象()
	self.资源组[24] = nil
	self.资源组[25] = nil
	if self.伙伴数据[self.选中]  ~= nil then
		local n = ani(self.伙伴数据[self.选中].模型)
		self.资源组[24] = tp.资源:载入(n[10],"网易WDF动画",n[6])
		self.资源组[24]:置方向(0)
	end
end

function 场景类_模型查看:显示(dt,x,y)
	self.焦点 = false
	self.资源组[2]:更新(x,y)
	self.资源组[3]:更新(x,y,bb ~= nil)
	self.资源组[4]:更新(x,y,bb ~= nil)
	-- 显示
	self.资源组[1]:显示(self.x,self.y)
	self.资源组[0]:显示(self.x+6,self.y+3)
	tp.窗口标题背景_:置区域(0,0,92,16)
	tp.窗口标题背景_:显示(self.x+132,self.y)
	zts2:置字间距(2)
	zts2:显示(self.x+138,self.y+3,"  伙  伴")
	zts2:置字间距(0)
end

function 场景类_模型查看:检查点(x,y)
	if self.资源组[1]:是否选中(x,y)  then
		return true
	end
end

function 场景类_模型查看:初始移动(x,y)
	tp.运行时间 = tp.运行时间 + 1
	if not tp.消息栏焦点 then
  		self.窗口时间 = tp.运行时间
 	end
	if not self.焦点 then
		tp.移动窗口 = true
	end
	if self.鼠标 and  not self.焦点 then
		self.xx = x - self.x
		self.yy = y - self.y
	end
end

function 场景类_模型查看:开始移动(x,y)
	if self.鼠标 then
		self.x = x - self.xx
		self.y = y - self.yy
	end
end

return 场景类_模型查看