--======================================================================--
-- @作者: www.wnfj.com，联系QQ：79550111
-- @创建时间:   2018-03-03 02:34:19
-- @Last Modified time: 2024-12-05 17:26:06
-- 更多游戏请访问万能飞机：www.wnfj.com,版本定制授权联系QQ：79550111
--======================================================================--
local 登录类 = class()

local tp
local mouseb = 引擎.鼠标按下

function 登录类:初始化(根)
     系统参数.服务器={名称="绘梦西游",ip=全局ip,端口=全局端口}
    -- 系统参数.服务器={名称="绘梦西游",ip="************",端口=8084}
	local 资源 = 根.资源
    local 按钮 = 根._按钮
    self.右键关闭=1
	self.上一步 = 根._按钮(资源:载入('zdy.rpk',"网易WDF动画",0x1000255),0,0,3,true,true)
	self.创建 = 根._按钮(资源:载入('wzife.wdf',"网易WDF动画",0x75D9CC0E),0,0,3,true,true)
	self.下一步 = 根._按钮(资源:载入('zdy.rpk',"网易WDF动画",0x1000254),0,0,3,true,true)
    self.注册账号 = 根._按钮(资源:载入('zdy.rpk',"网易WDF动画",0x1000246),0,0,3,true,true)
  -- self.电脑登陆 = 按钮.创建(资源:载入('wzife.wdf',"网易WDF动画",3076562527),0,0,4,true,true,"电脑登陆")
  -- self.云电脑登陆 = 按钮.创建(资源:载入('wzife.wdf',"网易WDF动画",3076562527),0,0,4,true,true,"云电脑登陆")
  -- self.电脑登陆:置偏移(40,4)
  -- self.云电脑登陆:置偏移(40,4)
  -- self.电脑登陆:描边颜色(0xFFFFFF00,0xFF20E69B)
  -- self.云电脑登陆:描边颜色(0xFFFFFF00,0xFF20E69B)
	tp = 根
	self.背景图片=资源:载入('zdy.rpk',"网易WDF动画",0x1000260)
    --self.背景图片=资源:载入('wdf/vvxxzcom/pic/背景.jpg',"图片")
  self.上一步:置颜色(ARGB(230,255,255,255))
  self.下一步:置颜色(ARGB(230,255,255,255))
  self.背景图片:置颜色(ARGB(220,255,255,255))
	self.控件类 = require("ggeui/加载类")()
	local 总控件 = self.控件类:创建控件('创建控件')
	总控件:置可视(true,true)
	self.账号输入框 = 总控件:创建输入("账号输入",425,329,240,14,根,根.字体表.华康14)
    --输入账号的框，和它所在位置的坐标
	self.账号输入框:置可视(false,false)
	self.账号输入框:置限制字数(15)
	self.账号输入框:置光标颜色(4294967295)
	self.账号输入框:置文字颜色(4294967295)
	self.账号输入框:置文本("888888888")
  self.账号输入框:置帐号模式()
	self.密码输入框 = 总控件:创建输入("密码输入",425,375,200,14,根,根.字体表.华康14)
       --输入密码的框，和它所在位置的坐标
	self.密码输入框:置可视(false,false)
	self.密码输入框:置限制字数(12)
	self.密码输入框:置光标颜色(4294967295)
	self.密码输入框:置文字颜色(4294967295)
	self.密码输入框:置密码模式()
	self.账号输入框:置文本(f函数.读配置(程序目录.."config.ini","mhxy","账号"))
	self.密码输入框:置文本(f函数.读配置(程序目录.."config.ini","mhxy","密码"))
	self.事件 = false
	self.焦点 = false
	self.开启 = false
	self.编号 = 编号
	self.双击 = nil
	self.时间 = 0
end

function 登录类:更新(dt)

end

function 登录类:字符串判定(str,item)
    local t = {}
    local l = {}
    local index = 0
    for i = 1, string.len(str) do
        table.insert(t, string.byte(string.sub(str, i, i)))
    end

    for i = 1, string.len(item) do
        table.insert(l, string.byte(string.sub(item, i, i)))
    end
    if #l > #t then
        return false
    end

    for k, v1 in pairs(t) do
        index = index + 1
        if v1 == l[1] then
            local iscontens = true
            for i = 1, #l do
                if t[index + i - 1] ~= l[i] then
                    iscontens = false
                end
            end
            if iscontens then
                return iscontens
            end
        end
    end
    return false
end
function 登录类:显示(dt,x,y)
    引擎.置标题(欢迎词.." - 请输入账号密码")
    self.账号输入框:置可视(true,true)
    self.密码输入框:置可视(true,true)

    self.背景图片:显示(325,238)
       --位置的坐标

    self.控件类:更新(dt,x,y)
    self.控件类:显示(x,y)
    --self.上一步:更新(x,y)
    self.下一步:更新(x,y)
    --self.电脑登陆:更新(x,y,not (tp.登陆方式 == 1),2)
    --self.云电脑登陆:更新(x,y,not (tp.登陆方式 == 2),2)
    --self.上一步:显示(253,424)
    self.下一步:显示(541,475)
    self.注册账号:更新(x,y)
    -- self.注册账号:显示(660,225)
    self.注册账号:显示(353,475)
    --self.电脑登陆:显示(370-140,207,true,nil,nil,tp.登陆方式 == 1,2)
    --self.云电脑登陆:显示(540-140,207,true,nil,nil,tp.登陆方式 == 2,2)

    if 引擎.按键弹起(键盘符号.tab) then
        if self.账号输入框:取焦点() then
            self.账号输入框:置焦点(false)
            self.密码输入框:置焦点(true)
        elseif self.密码输入框:取焦点() then
            self.密码输入框:置焦点(false)
            self.账号输入框:置焦点(true)
        else
            self.账号输入框:置焦点(true)
        end
    end
    if self.上一步:事件判断() then
        tp.进程 = 8
    elseif self.注册账号:事件判断() then
        tp.进程 = 5
        -- elseif self.电脑登陆:事件判断() and tp.登陆方式 == 2 then
        --   tp.登陆方式 = 1
        -- elseif self.云电脑登陆:事件判断() and tp.登陆方式 == 1 then
        --   tp.登陆方式 = 2
    elseif self.下一步:事件判断() or 引擎.按键按下(KEY.ENTER) then
        if self.账号输入框:取文本()=="" or self.密码输入框:取文本()=="" then
            tp.提示:写入("#Y/请先输入账号或密码")
            return 0
        elseif #self.账号输入框:取文本() <6 or #self.密码输入框:取文本() <6  then
            tp.提示:写入("#Y/账号或密码格式不正确")
            return 0
        elseif self:字符串判定(self.账号输入框:取文本(),"\\") then
            tp.提示:写入("#Y账号账号存在非法字符")
        elseif self:字符串判定(self.账号输入框:取文本(),"/") then
            tp.提示:写入("#Y账号账号存在非法字符")
        else
            系统参数.账号=self.账号输入框:取文本()
            系统参数.密码=self.密码输入框:取文本()
            f函数.写配置(程序目录.."config.ini","mhxy","账号",系统参数.账号)
            f函数.写配置(程序目录.."config.ini","mhxy","密码",系统参数.密码)
            self.密码输入框:置文本("")
            self.账号输入框:置文本("")
            self.账号输入框:置可视(false)
            self.密码输入框:置可视(false)
            -- print(系统参数.服务器.ip,系统参数.服务器.端口)
            -- 客户端:连接处理(系统参数.服务器.ip,系统参数.服务器.端口)
            -- print(1,版本号..fgc..系统参数.账号..fgc..系统参数.密码..fgc..f函数.取硬盘序列号())
            客户端:发送数据(1,版本号..fgc..系统参数.账号..fgc..系统参数.密码..fgc..f函数.取硬盘序列号())
        end
    end
end

return 登录类