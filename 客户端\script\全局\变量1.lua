--########################################################?自己修改?##########################################
require("gge函数")
require("script/全局/自己_专用")--引用头
f函数=require("ffi函数2")

全局游戏宽度 = math.ceil(读配置("./config.ini","mhxy","宽度")) or 1024      --1000
全局游戏高度 = math.ceil(读配置("./config.ini","mhxy","高度")) or 768     --620
wdf配置 = "wdf"--读配置("./config.ini","mhxy","wdf") or "WDF"
_ds=true
鼠标延时2=0

--========================================
时间=0
触碰延时=0
PK选中玩家=0

战斗连击显示 = {}
战斗连击单位 = 0
多重战斗开关 = false
临时染色列表 = {}

调试模式=__gge.isdebug
if 调试模式 == nil then
	调试模式 = false
end

昼夜=1
-- 聊天框寄存=""
-- 工具模式 = false
本地调试=调试模式
-- print(调试模式)
连点模式=false
坐标显示=true
消息闪烁=false
推广模式=false
程序目录=取当前目录()..[[\]]
人物点击=false
鼠标={x=0,y=0}
申请队伍=false
全局临时路径=nil
系统退出=false
全局资源缓存 = {}
全局资源地址 = {}
--=========================================
if 全局游戏宽度 < 799 then
	全局游戏宽度=800
end
if 全局游戏高度 < 599 then
	全局游戏高度=600
end
--=====
if 全局游戏宽度 > 1281 then
	全局游戏宽度=1280
end
if 全局游戏高度 > 822 then
	全局游戏高度=820
end
外部窗口宽度 = 260

全局时辰微秒 = 0
全局时辰秒 = 0
-- 全局时辰秒2=0
抽奖全局秒 = 0
ljcs = 1
local s时间,上次时间=0,0
function 数额尾数转换(数值)
  数值 = tonumber(数值)
   if 数值 < 10000 then
     return 数值
   elseif 数值 >= 10000 and 数值 < 100000000 then
     return string.format("%s%s",math.floor(数值/10000),"万")
   elseif 数值 >= 100000000 and 数值 < 1000000000000  then
     return string.format("%s%s",math.floor(数值/100000000),"亿")
   elseif  数值 >= 1000000000000 and 数值 < 10000000000000000  then
     return string.format("%s%s",math.floor(数值/1000000000000),"兆")
   elseif  数值 >= 10000000000000000  then
     return string.format("%s%s",math.floor(数值/10000000000000000),"京")
  end
end
function 全局时辰刷新()
	s时间=os.time()
	if s时间-上次时间>=1 then
		时间=s时间
		上次时间=os.time()
	end
	if 全局时辰微秒~= 60 then
		全局时辰微秒 = 全局时辰微秒 + 1
	else
		全局时辰微秒 = 0
		if not 引擎.场景.战斗中 then
			--collectgarbage("step")
			collectgarbage("collect")
		end
	end
	-- 引擎.场景.窗口.任务追踪栏:刷新时间()
 -- 	if 全局时辰秒 >= 60 then--1分
	-- 	if 引擎.场景.窗口.人物框.图标组.摄妖香~=nil and 引擎.场景.窗口.人物框.图标组.摄妖香.变量>0 then
	-- 		引擎.场景.窗口.人物框.图标组.摄妖香.变量 =  引擎.场景.窗口.人物框.图标组.摄妖香.变量 - 1
	-- 	end
	-- 	if 引擎.场景.窗口.人物框.图标组.变身卡~=nil and 引擎.场景.窗口.人物框.图标组.变身卡.变量>0 then
	-- 		引擎.场景.窗口.人物框.图标组.变身卡.变量 =  引擎.场景.窗口.人物框.图标组.变身卡.变量 - 1
	-- 	end
	-- 	全局时辰秒 = 0
	-- end
end

function pwd(as)
  if pwd资源 == nil then
    pwd资源 = require("script/资源类/资源类")("wdf/wzife.wd4")
    pwd资源:添加包('wdf/wzife.wd4','mmncc')
  end
  -- if as == nil then
  --   错误追溯()
  -- end
  return 加密动画资源(pwd资源:取文件(as..".was"),0)
end

function 取百分比转换(加入值,可减少值,总值)
	加入值 = 加入值 or 0
	return 加入值/(总值-可减少值)
end

function 重建tp()
	yq.场景 = require("script/全局/主控")()
	yq.垂直同步(true)
	local tp = yq.场景
end

function 引擎创建完成()

end

function 判断是否为空表(t)
    return _G.next( t ) == nil
end

function 银两显示(字体,数额,x,y)
	银两个数=0
	if 数额~=nil then
		银两个数=string.len(数额)
	end

	if(银两个数==5)then
			字体:置颜色(蓝色)
	elseif(银两个数==6)then
			字体:置颜色(绿色)
	elseif(银两个数==7)then
			字体:置颜色(红色)
	elseif(银两个数==8)then
			字体:置颜色(紫色)
	elseif(银两个数>=9)then
			字体:置颜色(黄色)
	else
			字体:置颜色(黑色)
	end
	字体:显示(x,y,数额)
end

function 引擎.取金钱颜色(s)
	s=s+0
	if s<10000 then
		return 黑色
	elseif s<100000 then
		return 蓝色
	elseif s<1000000 then
		return 绿色
	elseif s<10000000 then
		return 红色
	elseif s<100000000 then
		return 紫色
	else
		return 黄色
	end
end

function 引擎.取摊位金钱颜色(s)
	s=s+0
	if s<10000 then
		return 黑色
	elseif s<100000 then
		return 蓝色
	elseif s<1000000 then
		return 0xff009900
	elseif s<10000000 then--红
		return 0xffCC0033
	elseif s<100000000 then --9933CC
		return 0xff9933FF
	else
		return 0xFFFFFF00
	end
end


随机序列=0
function 取随机数(q,w)
	随机序列=随机序列+1
	if 随机序列>=1000 then
		随机序列=0
	end
	if q==nil or w==nil then
		q=1 w=100
	else
	end
	math.randomseed(tostring(os.clock()*os.time()*随机序列))
	return  math.random(math.floor(q),math.floor(w))
end

function 取五行()
	local 五行_ = {"金","木","水","火","土"}
	return 五行_[取随机数(1,5)]
end

function 数字转大写(a)
	if a==1 then
		return "一"
	elseif a==2 then
		return "二"
	elseif a==3 then
		return "三"
	elseif a==4 then
		return "四"
	elseif a==5 then
		return "五"
	elseif a==6 then
		return "六"
	else
		return "七"
	end
end

function 名称显示(名称,x,选中)
	local 文字坐标={x=x.x-10,y=x.y+20}
	文字坐标.x=文字坐标.x-(string.len(tostring(名称))*2.5)
	--名称="比武大会精锐组状元"
	--名称="时尚宠儿"
	if 名称=="游戏管理员" or 名称=="GM" or 名称=="老猫" then
		名称字体:置颜色(红色):置阴影颜色(ARGB(170,0,0,0))
		名称字体:显示(文字坐标.x,文字坐标.y,名称)
	else
		if 选中~=nil then
			名称字体:置颜色(红色):置阴影颜色(ARGB(170,0,0,0))
			名称字体:显示(文字坐标.x,文字坐标.y,名称)
		else
			名称字体:置颜色(ARGB(255,95,218,102)):置阴影颜色(ARGB(255,0,0,0))
			名称字体:显示(文字坐标.x,文字坐标.y,名称)
		end
	end
end

function 场景名称显示(名称,x)

end

function 取名称颜色(名称,化圣,类型)
	if 类型 == "人物" then
		if 名称 == "游戏管理员" or 名称=="GM" or 名称=="老猫" then
			return 0xFFFF0000
		elseif 化圣 == true then
			return 0xFFFF00FF
		end
		return  0xFF70FC70--ARGB(255,95,218,102) --4285922956
	else
    	return 0xFFFFFF33--ARGB(255,225,230,10) --3是高亮 2是深度
	end
end

function 取称谓颜色(称谓,类型)
	if 称谓 == "生死劫·止戈" or 称谓 == "GM" then
		return 0xFFFF80FF
	elseif 称谓 == "生死劫·清心" then
		return 0xFFFF80FF
	elseif 称谓 == "生死劫·雷霆" then
		return 0xFFFF80FF
	elseif 称谓 == "生死劫·惜花" then
		return 0xFFFF80FF
	elseif 称谓 == "生死劫·忘情" then
		return 0xFFFF80FF
	elseif 称谓 == "生死劫·卧龙" then
		return 0xFFFF80FF
	elseif 称谓 == "生死劫·天象" then
		return 0xFFFF80FF
	elseif 称谓 == "生死劫·轮回" then
		return 0xFFFF80FF
	elseif 称谓 == "生死劫·娑罗" then
		return 0xFFFF80FF
	elseif 称谓 == "超凡入圣" then
		return 0xFFFF80FF
	elseif 称谓 == "超凡入圣" then
		return 0xFFFF80FF
             elseif 称谓 == "精锐群雄·全服冠军" or  称谓 == "勇武群雄·全服冠军" or  称谓 == "神威群雄·全服冠军" or  称谓 == "天元群雄·全服冠军" then
		return 0xFF00FFFF
             elseif 称谓 == "精锐群雄·全服亚军" or  称谓 == "勇武群雄·全服亚军" or  称谓 == "神威群雄·全服亚军" or  称谓 == "天元群雄·全服亚军" then
		return 0xFF00FFFF
             elseif 称谓 == "精锐群雄·全服季军" or  称谓 == "勇武群雄·全服季军" or  称谓 == "神威群雄·全服季军" or  称谓 == "天元群雄·全服季军" then
		return 0xFF00FFFF
	end
	return ARGB(255,127,190,255)
end

function 角度算四方向(角)
	local 方向 = 0
	if(角 >0 and 角 < 91) then
		方向 = 0 --"东北"
	elseif(角 > 90 and 角 < 181) then
		方向 = 1 --"西北"
	elseif(角 > 180 and 角 < 271) then
		方向 = 2 --"西南"
	elseif(角 > 270 or  角 == 0) then
		方向 = 3 --"东南"
	end
	return 方向
end

function 角度算八方向(角)
	local 方向 = 0
	if(角 > 157 and 角 < 203) then
		方向 = 5 --"左"
	elseif(角 >202 and 角 < 248) then
		方向 = 2 --"左上"
	elseif(角 > 247 and 角 < 293) then
		方向 = 6 --"上"
	elseif(角 > 292 and 角 < 338) then
		方向 = 3 --"右上"
	elseif(角 > 337 or 角 < 24 ) then
		方向 = 7        --"右"
	elseif( 角 > 23 and 角 < 69 ) then
		方向 = 0       --"右下"
	elseif(角 > 68 and 角 < 114 )then
		方向 = 4 --"下"
	elseif(角 > 113 ) then
		方向 = 1 --"左下"
	end
	return 方向

end

function 阵法克制(我阵型,敌阵型)
    --无克制=0 克制=1 被克=2
    local 克制 = 0
    if 我阵型=="普通" then
       if 敌阵型=="天覆阵" or 敌阵型=="风扬阵"or 敌阵型=="龙飞阵"or 敌阵型=="鸟翔阵"or 敌阵型=="鹰啸阵" then
            克制 = 2
       elseif 敌阵型=="地载阵" or 敌阵型=="云垂阵"or 敌阵型=="虎翼阵"or 敌阵型=="蛇蟠阵"or 敌阵型=="雷绝阵" then
            克制 = 1
       end
    elseif 我阵型=="天覆阵" then
       if 敌阵型=="普通" or 敌阵型=="鸟翔阵"or 敌阵型=="鹰啸阵"  then
           克制 = 1
       elseif 敌阵型=="地载阵"or 敌阵型=="龙飞阵"  then
           克制 = 1
       elseif 敌阵型=="风扬阵"or 敌阵型=="蛇蟠阵"  then
           克制 = 2
       elseif 敌阵型=="云垂阵"or 敌阵型=="虎翼阵"or 敌阵型=="雷绝阵"  then
           克制 = 2
       end
    elseif 我阵型=="地载阵" then
       if 敌阵型=="云垂阵"or 敌阵型=="蛇蟠阵"  then
           克制 = 1
       elseif 敌阵型=="风扬阵"or 敌阵型=="虎翼阵"or 敌阵型=="雷绝阵" then
           克制 = 1
       elseif 敌阵型=="天覆阵"or 敌阵型=="鸟翔阵"or 敌阵型=="鹰啸阵" then
           克制 = 2
       elseif 敌阵型=="普通" or 敌阵型=="龙飞阵"  then
           克制 = 2
       end
    elseif 我阵型=="风扬阵" then
       if 敌阵型=="普通"or 敌阵型=="天覆阵"or 敌阵型=="蛇蟠阵"  then
           克制 = 1
       elseif 敌阵型=="风扬阵" or 敌阵型=="鸟翔阵"or 敌阵型=="鹰啸阵" then
           克制 = 1
       elseif 敌阵型=="地载阵"or 敌阵型=="虎翼阵"or 敌阵型=="雷绝阵" then
           克制 = 2
       elseif 敌阵型=="云垂阵"or 敌阵型=="龙飞阵"  then
           克制 = 2
       end
    elseif 我阵型=="云垂阵" then
       if 敌阵型=="天覆阵" or 敌阵型=="风扬阵"  then
           克制 = 1
       elseif 敌阵型=="鸟翔阵"or 敌阵型=="蛇蟠阵"or 敌阵型=="鹰啸阵" then
           克制 = 1
       elseif 敌阵型=="龙飞阵"or 敌阵型=="虎翼阵" or 敌阵型=="雷绝阵" then
           克制 = 2
       elseif 敌阵型=="普通" or 敌阵型=="地载阵"  then
           克制 = 2
       end
    elseif 我阵型=="龙飞阵" then
       if 敌阵型=="普通"or 敌阵型=="地载阵" or 敌阵型=="风扬阵"  then
          克制 = 1
       elseif 敌阵型=="云垂阵"or 敌阵型=="雷绝阵" then
          克制 = 1
       elseif 敌阵型=="天覆阵"or 敌阵型=="鹰啸阵"then
           克制 = 2
       elseif 敌阵型=="虎翼阵" or 敌阵型=="鸟翔阵"or 敌阵型=="蛇蟠阵"  then
           克制 = 2
       end
    elseif 我阵型=="虎翼阵" then
       if 敌阵型=="天覆阵"or 敌阵型=="龙飞阵"or 敌阵型=="雷绝阵"  then
           克制 = 1
       elseif 敌阵型=="风扬阵"or 敌阵型=="云垂阵" then
           克制 = 1
       elseif 敌阵型=="地载阵"or 敌阵型=="鸟翔阵"then
           克制 = 2
       elseif 敌阵型=="普通"or 敌阵型=="蛇蟠阵"or 敌阵型=="鹰啸阵"  then
           克制 = 2
       end
    elseif 我阵型=="鸟翔阵" then
       if 敌阵型=="普通"or 敌阵型=="龙飞阵"or 敌阵型=="鹰啸阵"  then
          克制 = 1
       elseif 敌阵型=="地载阵"or 敌阵型=="虎翼阵" then
          克制 = 1
       elseif 敌阵型=="风扬阵"or 敌阵型=="云垂阵"then
          克制 = 2
       elseif 敌阵型=="天覆阵"or 敌阵型=="蛇蟠阵"or 敌阵型=="雷绝阵"  then
          克制 = 2
       end
    elseif 我阵型=="蛇蟠阵" then
       if 敌阵型=="地载阵"or 敌阵型=="风扬阵"or 敌阵型=="龙飞阵"or 敌阵型=="虎翼阵"or 敌阵型=="鸟翔阵"or 敌阵型=="雷绝阵"  then
           克制 = 1
       elseif 敌阵型=="天覆阵" then
           克制 = 1
       elseif 敌阵型=="云垂阵"then
           克制 = 2
       elseif 敌阵型=="普通"or 敌阵型=="鹰啸阵"  then
           克制 = 2
       end
    elseif 我阵型=="鹰啸阵" then
       if 敌阵型=="普通"or 敌阵型=="虎翼阵"or 敌阵型=="蛇蟠阵"  then
           克制 = 1
       elseif 敌阵型=="地载阵"or 敌阵型=="龙飞阵" then
           克制 = 1
       elseif 敌阵型=="风扬阵"or 敌阵型=="云垂阵"or 敌阵型=="鸟翔阵"or 敌阵型=="雷绝阵"then
           克制 = 2
       elseif 敌阵型=="天覆阵"  then
           克制 = 2
       end
    elseif 我阵型=="雷绝阵" then
       if 敌阵型=="天覆阵"or 敌阵型=="鸟翔阵"or 敌阵型=="鹰啸阵"then
           克制 = 1
       elseif 敌阵型=="风扬阵"or 敌阵型=="云垂阵" then
           克制 = 1
       elseif 敌阵型=="地载阵"or 敌阵型=="龙飞阵" then
           克制 = 2
       elseif 敌阵型=="普通"or 敌阵型=="虎翼阵"or 敌阵型=="蛇蟠阵"  then
           克制 = 2
       end
    end
    return 克制
end
function 取染色方案(id)
	for k,v in pairs(染色信息) do
		if v.id == id then
			return {v.方案[1],v.方案[2],0,0}
		end
	end
	 return {1,0,0,0}
	-- body
end




染色信息={
通用={方案={[1]=1,[2]=1},id=301},
护卫={方案={[1]=1,[2]=0},id=2051},
泡泡={方案={[1]=1,[2]=0},id=701},
恶魔泡泡={方案={[1]=1,[2]=0},id=701},
树怪={方案={[1]=1,[2]=0},id=56},
大海龟={方案={[1]=1,[2]=0},id=69},
巨蛙={方案={[1]=1,[2]=0},id=70},
章鱼={方案={[1]=2},id=119},
海星={方案={[1]=0},id=119},
野猪={方案={[1]=1,[2]=0},id=52},
大蝙蝠={方案={[1]=1,[2]=0},id=66},
海毛虫={方案={[1]=1,[2]=0},id=67},
狸={方案={[1]=5},id=2079},
浣熊={方案={[1]=5},id=2079},
强盗={方案={[1]=1,[2]=0},id=74},
山贼={方案={[1]=1,[2]=0},id=75},
赌徒={方案={[1]=1,[2]=0},id=76},
狐狸精={方案={[1]=1,[2]=0},id=81},
羊头怪={方案={[1]=1,[2]=0},id=54},
花妖={方案={[1]=1,[2]=0},id=58},
骷髅怪={方案={[1]=1,[2]=0},id=60},
蛤蟆精={方案={[1]=1,[2]=0},id=61},
老虎={方案={[1]=1,[2]=0},id=64},
黑熊={方案={[1]=1,[2]=0},id=65},
野鬼={方案={[1]=1,[2]=0},id=80},
虾兵={方案={[1]=1,[2]=0},id=83},
蟹将={方案={[1]=1,[2]=0},id=84},
牛妖={方案={[1]=1,[2]=0},id=87},
小龙女={方案={[1]=1,[2]=0},id=63},
狼={方案={[1]=1,[2]=0},id=68},
牛头={方案={[1]=1,[2]=0},id=77},
马面={方案={[1]=1,[2]=0},id=78},
僵尸={方案={[1]=1,[2]=0},id=79},
龟丞相={方案={[1]=1,[2]=0},id=85},
蜘蛛精={方案={[1]=1,[2]=0},id=80},
兔子怪={方案={[1]=1,[2]=0},id=51},
黑熊精={方案={[1]=1,[2]=0},id=53},
黑山老妖={方案={[1]=1,[2]=1},id=93},
蝴蝶仙子={方案={[1]=1,[2]=0},id=57},
雷鸟人={方案={[1]=1,[2]=0},id=62},
白熊={方案={[1]=1,[2]=1},id=72},
古代瑞兽={方案={[1]=1,[2]=1},id=73},
善财童子={方案={[1]=1,[2]=1},id=703},
哮天犬={方案={[1]=1,[2]=1},id=701},
天兵={方案={[1]=1,[2]=0},id=82},
天将={方案={[1]=1,[2]=0},id=71},
风伯={方案={[1]=1,[2]=0},id=89},
地狱战神={方案={[1]=1,[2]=1},id=94},
花铃={方案={[1]=1,[2]=1},id=702},
蛟龙={方案={[1]=1,[2]=1},id=90},
凤凰={方案={[1]=1,[2]=1},id=91},
蚌精={方案={[1]=1},id=20306},
鲛人={方案={[1]=1,[2]=1},id=2057},
碧水夜叉={方案={[1]=1},id=2059},
雨师={方案={[1]=1,[2]=0},id=114},
月影仙={方案={[1]=1,[2]=0},id=702},
星灵仙子={方案={[1]=1,[2]=1},id=88},
巡游天神={方案={[1]=1,[2]=1},id=92},
犀牛将军人形={方案={[1]=1,[2]=0},id=704},
犀牛将军兽形={方案={[1]=0,[2]=5},id=20104},
锦毛貂精={方案={[1]=0,[2]=3},id=20103},
芙蓉仙子={方案={[1]=1,[2]=1},id=55},
如意仙子={方案={[1]=1,[2]=1},id=59},
千年蛇魅={方案={[1]=5,[2]=0},id=20104},
野猪精={方案={[1]=1,[2]=1},id=107},
百足将军={方案={[1]=1,[2]=1},id=108},
鼠先锋={方案={[1]=1,[2]=0},id=109},
泪妖={方案={[1]=6},id=20113},
镜妖={方案={[1]=1,[2]=0},id=2062},
吸血鬼={方案={[1]=1,[2]=0},id=96},
幽灵={方案={[1]=1,[2]=0},id=97},
灵符女娲={方案={[1]=1,[2]=0},id=98},
律法女娲={方案={[1]=1,[2]=0},id=99},
阴阳伞={方案={[1]=1,[2]=0},id=2070},
鬼将={方案={[1]=1,[2]=0},id=95},
净瓶女娲={方案={[1]=1,[2]=0},id=100},
幽萤娃娃={方案={[1]=4,[2]=0},id=20113},
画魂={方案={[1]=4,[2]=0},id=20113},
云游火={方案={[1]=1,[2]=0},id=704},
狐不归={方案={[1]=1,[2]=0},id=706},
大力金刚={方案={[1]=1,[2]=0},id=101},
夜罗刹={方案={[1]=1,[2]=0},id=104},
雾中仙={方案={[1]=0,[2]=1},id=102},
灵鹤={方案={[1]=1,[2]=0},id=103},
炎魔神={方案={[1]=1,[2]=0},id=105},
噬天虎={方案={[1]=1,[2]=0},id=106},
琴仙={方案={[1]=1,[2]=0},id=2071},
金饶僧={方案={[1]=0,[2]=1},id=2069},
月魅={方案={[1]=1,[2]=0},id=707},
踏云兽={方案={[1]=0,[2]=1},id=110},
红萼仙子={方案={[1]=0,[2]=1},id=111},
葫芦宝贝={方案={[1]=1,[2]=0},id=2071},
蝎子精={方案={[1]=1,[2]=0},id=2071},
龙龟={方案={[1]=0,[2]=1},id=112},
机关人人形={方案={[1]=1,[2]=0},id=708},
机关人车={方案={[1]=1,[2]=0},id=2070},
猫灵兽形={方案={[1]=0,[2]=1},id=2057},
狂豹兽形={方案={[1]=1,[2]=0},id=2065},
机关兽={方案={[1]=1,[2]=0},id=2070},
连弩车={方案={[1]=1,[2]=0},id=2070},
机关鸟={方案={[1]=1,[2]=0},id=2070},
巴蛇={方案={[1]=1,[2]=0},id=2070},
长眉灵猴={方案={[1]=1,[2]=0},id=2051},
巨力神猿={方案={[1]=0,[2]=1},id=2079},
修罗傀儡鬼={方案={[1]=1,[2]=0},id=2065},
藤蔓妖花={方案={[1]=1,[2]=0},id=2042},
蜃气妖={方案={[1]=5,[2]=0},id=2079},
猫灵人形={方案={[1]=1,[2]=0},id=723},
狂豹人形={方案={[1]=3,[2]=0},id=20230},
混沌兽={方案={[1]=6,[2]=0},id=2078},
修罗傀儡妖={方案={[1]=5,[2]=0},id=2078},
金身罗汉={方案={[1]=2,[2]=0},id=2000},
曼珠沙华={方案={[1]=1,[2]=0},id=2070},
持国巡守={方案={[1]=1,[2]=0},id=709},
毗舍童子={方案={[1]=1,[2]=0},id=84},
魔化毗舍童子={方案={[1]=1,[2]=0},id=84},
真陀护法={方案={[1]=1,[2]=0},id=710},
增长巡守={方案={[1]=1,[2]=0},id=711},
灵灯侍者={方案={[1]=1,[2]=0},id=712},
般若天女={方案={[1]=1,[2]=0},id=713},
沙暴={方案={[1]=1,[2]=0},id=2000},
涂山雪={方案={[1]=1,[2]=0},id=702},
自在心猿={方案={[1]=1,[2]=0},id=702},
雷龙={方案={[1]=1,[2]=0},id=702},
谛听={方案={[1]=1,[2]=0},id=704},
进阶黑山老妖={方案={[1]=1,[2]=0},id=706},
进阶蝴蝶仙子={方案={[1]=1,[2]=0},id=714},
进阶雷鸟人={方案={[1]=1,[2]=0},id=711},
进阶白熊={方案={[1]=1,[2]=0},id=711},
进阶古代瑞兽={方案={[1]=1,[2]=1},id=711},
进阶善财童子={方案={[1]=1,[2]=0},id=710},
进阶哮天犬={方案={[1]=1,[2]=0},id=715},
进阶天兵={方案={[1]=1,[2]=0},id=714},
进阶风伯={方案={[1]=1,[2]=0},id=716},
进阶地狱战神={方案={[1]=1,[2]=0},id=705},
进阶花铃={方案={[1]=1,[2]=0},id=702},
进阶天将={方案={[1]=1,[2]=0},id=716},
进阶蛟龙={方案={[1]=1,[2]=0},id=703},
进阶凤凰={方案={[1]=1,[2]=0},id=714},
进阶蚌精={方案={[1]=1,[2]=0},id=714},
进阶鲛人={方案={[1]=1,[2]=0},id=712},
进阶碧水夜叉={方案={[1]=1,[2]=0},id=710},
进阶雨师={方案={[1]=1,[2]=0},id=717},
进阶月影仙={方案={[1]=1,[2]=0},id=702},
进阶星灵仙子={方案={[1]=1,[2]=0},id=704},
进阶巡游天神={方案={[1]=1,[2]=0},id=708},
进阶犀牛将军人形={方案={[1]=1,[2]=0},id=712},
进阶犀牛将军兽形={方案={[1]=1,[2]=0},id=712},
进阶锦毛貂精={方案={[1]=1,[2]=0},id=715},
进阶芙蓉仙子={方案={[1]=1,[2]=0},id=712},
进阶如意仙子={方案={[1]=1,[2]=0},id=708},
进阶千年蛇魅={方案={[1]=1,[2]=0},id=718},
进阶野猪精={方案={[1]=1,[2]=1},id=711},
进阶百足将军={方案={[1]=1,[2]=1},id=711},
进阶鼠先锋={方案={[1]=1,[2]=0},id=715},
进阶泪妖={方案={[1]=1,[2]=0},id=702},
进阶镜妖={方案={[1]=1,[2]=0},id=710},
进阶吸血鬼={方案={[1]=1,[2]=0},id=702},
进阶幽灵={方案={[1]=1,[2]=0},id=719},
进阶灵符女娲={方案={[1]=1,[2]=0},id=712},
进阶律法女娲={方案={[1]=1,[2]=0},id=714},
进阶阴阳伞={方案={[1]=1,[2]=0},id=712},
进阶鬼将={方案={[1]=1,[2]=1},id=708},
进阶净瓶女娲={方案={[1]=1,[2]=0},id=719},
进阶幽萤娃娃={方案={[1]=1,[2]=0},id=710},
进阶画魂={方案={[1]=1,[2]=0},id=712},
进阶云游火={方案={[1]=1,[2]=0},id=702},
进阶狐不归={方案={[1]=1,[2]=0},id=702},
进阶大力金刚={方案={[1]=1,[2]=0},id=702},
进阶夜罗刹={方案={[1]=1,[2]=0},id=715},
进阶雾中仙={方案={[1]=1,[2]=0},id=708},
进阶灵鹤={方案={[1]=1,[2]=0},id=709},
进阶炎魔神={方案={[1]=1,[2]=0},id=702},
进阶噬天虎={方案={[1]=1,[2]=0},id=702},
进阶琴仙={方案={[1]=1,[2]=0},id=712},
进阶金饶僧={方案={[1]=1,[2]=0},id=714},
进阶月魅={方案={[1]=1,[2]=0},id=702},
进阶踏云兽={方案={[1]=1,[2]=0},id=702},
进阶红萼仙子={方案={[1]=1,[2]=0},id=712},
进阶葫芦宝贝={方案={[1]=1,[2]=0},id=712},
进阶蝎子精={方案={[1]=1,[2]=0},id=712},
进阶龙龟={方案={[1]=1,[2]=0},id=714},
进阶机关人人形={方案={[1]=1,[2]=0},id=716},
进阶猫灵兽形={方案={[1]=1,[2]=0},id=703},
进阶狂豹兽形={方案={[1]=1,[2]=0},id=708},
进阶机关兽={方案={[1]=1,[2]=0},id=702},
进阶机关鸟={方案={[1]=1,[2]=0},id=702},
进阶连弩车={方案={[1]=1,[2]=0},id=702},
进阶巴蛇={方案={[1]=1,[2]=0},id=720},
进阶长眉灵猴={方案={[1]=1,[2]=0},id=712},
进阶巨力神猿={方案={[1]=1,[2]=0},id=712},
进阶修罗傀儡鬼={方案={[1]=1,[2]=0},id=712},
进阶藤蔓妖花={方案={[1]=1,[2]=0},id=712},
进阶蜃气妖={方案={[1]=1,[2]=0},id=702},
进阶猫灵人形={方案={[1]=1,[2]=0},id=718},
进阶狂豹人形={方案={[1]=1,[2]=0},id=714},
进阶混沌兽={方案={[1]=1,[2]=0},id=702},
进阶修罗傀儡妖={方案={[1]=1,[2]=0},id=712},
进阶金身罗汉={方案={[1]=1,[2]=0},id=712},
进阶曼珠沙华={方案={[1]=1,[2]=0},id=716},
进阶持国巡守={方案={[1]=1,[2]=0},id=708},
进阶毗舍童子={方案={[1]=1,[2]=0},id=717},
进阶真陀护法={方案={[1]=1,[2]=0},id=716},
进阶增长巡守={方案={[1]=1,[2]=0},id=712},
进阶灵灯侍者={方案={[1]=1,[2]=0},id=712},
进阶般若天女={方案={[1]=1,[2]=0},id=712},
进阶涂山雪={方案={[1]=1,[2]=0},id=702},
进阶谛听={方案={[1]=1,[2]=0},id=702},
小毛头={方案={[1]=1,[2]=0},id=704},
小丫丫={方案={[1]=1,[2]=0},id=703},
小魔头={方案={[1]=1,[2]=0},id=719},
小精灵={方案={[1]=1,[2]=0},id=715},
小仙灵={方案={[1]=1,[2]=0},id=705},
小仙女={方案={[1]=1,[2]=0},id=715},
进阶小毛头={方案={[1]=1,[2]=0},id=714},
进阶小丫丫={方案={[1]=1,[2]=0},id=714},
进阶小魔头={方案={[1]=1,[2]=0},id=708},
超级红孩儿={方案={[1]=1,[2]=0},id=1708},
进阶小精灵={方案={[1]=1,[2]=0},id=716},
进阶小仙灵={方案={[1]=1,[2]=0},id=710},
进阶小仙女={方案={[1]=1,[2]=0},id=714},
牛魔王={方案={[1]=1,[2]=0},id=716},
大大王={方案={[1]=1,[2]=0},id=716},
程咬金={方案={[1]=1,[2]=0},id=711},
观音菩萨={方案={[1]=1,[2]=0},id=702},
空度禅师={方案={[1]=1,[2]=0},id=711},
孙婆婆={方案={[1]=1,[2]=0},id=722},
地涌夫人={方案={[1]=1,[2]=0},id=711},
地藏菩萨={方案={[1]=1,[2]=0},id=711},
白晶晶={方案={[1]=1,[2]=0},id=702},
阎罗王={方案={[1]=1,[2]=0},id=719},
镇元子={方案={[1]=1,[2]=0},id=702},
李天王={方案={[1]=1,[2]=0},id=719},
巫奎虎={方案={[1]=1,[2]=0},id=702},
东海龙王={方案={[1]=1,[2]=0},id=702},
菩提老祖={方案={[1]=1,[2]=0},id=703},
二郎神={方案={[1]=1,[2]=0},id=702},
周杰伦={方案={[1]=1,[2]=0},id=708},
齐天大圣={方案={[1]=1,[2]=0},id=702},
猪八戒={方案={[1]=1,[2]=0},id=702},
烟花占卜师={方案={[1]=1,[2]=0},id=714},
春十三娘={方案={[1]=1,[2]=0},id=715},
有个和尚={方案={[1]=1,[2]=0},id=715},
郑镖头={方案={[1]=1,[2]=0},id=715},
天马={方案={[1]=1,[2]=0},id=702},
九头虫={方案={[1]=1,[2]=0},id=702},
九灵元圣={方案={[1]=1,[2]=0},id=702},
腾蛇炫卡={方案={[1]=1,[2]=0},id=702},
小象炫卡={方案={[1]=1,[2]=0},id=702},
雪人炫卡={方案={[1]=1,[2]=0},id=712},
蚩尤={方案={[1]=1,[2]=0},id=702},
知了王={方案={[1]=1,[2]=0},id=702},
九色鹿={方案={[1]=1,[2]=0},id=702},
沙和尚={方案={[1]=1,[2]=0},id=702},
自在天魔={方案={[1]=1,[2]=0},id=718},
自在天魔宝珠={方案={[1]=1,[2]=0},id=702},
自在天魔刀={方案={[1]=1,[2]=0},id=702},
自在天魔宝剑={方案={[1]=1,[2]=0},id=702},
自在天魔经筒={方案={[1]=1,[2]=0},id=702},
自在天魔弓弩={方案={[1]=1,[2]=0},id=702},
自在天魔法杖={方案={[1]=1,[2]=0},id=702},
自在天魔斧钺={方案={[1]=1,[2]=0},id=702},
飞燕女={方案={[1]=1,[2]=1},id=3},
英女侠={方案={[1]=1,[2]=1},id=4},
巫蛮儿={方案={[1]=1,[2]=1},id=201},
逍遥生={方案={[1]=1,[2]=1},id=1},
剑侠客={方案={[1]=1,[2]=1},id=2},
狐美人={方案={[1]=1,[2]=1},id=7},
骨精灵={方案={[1]=1,[2]=1},id=8},
杀破狼={方案={[1]=1,[2]=1},id=202},
巨魔王={方案={[1]=1,[2]=1},id=5},
虎头怪={方案={[1]=1,[2]=1},id=6},
舞天姬={方案={[1]=1,[2]=1},id=11},
玄彩娥={方案={[1]=1,[2]=1},id=12},
羽灵神={方案={[1]=1,[2]=1},id=203},
神天兵={方案={[1]=1,[2]=1},id=9},
龙太子={方案={[1]=1,[2]=1},id=10},
桃夭夭={方案={[1]=1,[2]=1},id=204},
偃无师={方案={[1]=1,[2]=1},id=205},
鬼潇潇={方案={[1]=1,[2]=1},id=206},
影精灵={方案={[1]=1,[2]=1},id=19},
}



kemy={}
mab = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/*=.，'
key={["B"]="fj1,",["S"]="8y2,",["5"]="Mj3,",["D"]="3u4,",["c"]="0V5,",["E"]="d96,",["b"]="lq7,",["3"]="ng8,",["s"]="FP9,",["N"]="640,",["d"]="EU,",["6"]="Xm,",["7"]="dA,",["e"]="tq,",["t"]="Hi,",["8"]="VM,",["4"]="d2,",["W"]="v3,",["9"]="VJ,",["H"]="E5,",["G"]="ok,",["g"]="0C,",["I"]="S7,",["X"]="aH,",["m"]="7D,",["w"]="EC,",["Y"]="yV,",["V"]="FI,",["F"]="qM,",["z"]="2n,",["K"]="lL,",["f"]="ea,",["J"]="3P,",["x"]="Vt,",["y"]="fw,",["v"]="6V,",["L"]="QS,",["u"]="Kj,",["k"]="L3,",["M"]="6Y,",["j"]="CP,",["r"]="hA,",["q"]="iS,",["T"]="Fj,",["l"]="qp,",["0"]="ut,",["n"]="Nr,",["O"]="Kp,",["1"]="1m,",["i"]="t6,",["h"]="oe,",["C"]="HL,",["A"]="Sg,",["P"]="qg,",["U"]="xV,",["o"]="z3,",["Q"]="bT,",["R"]="qd,",["2"]="u6,",["Z"]="Ab,",["a"]="ga,",["p"]="tL,"}
function jm(数据)
	数据=encodeBase641(数据)
	local jg=""
	for n=1,#数据 do
		local z=string.sub(数据,n,n)
		if z~="" then
			if key[z]==nil then
				jg=jg..z
			else
				jg=jg..key[z]
			end
		end
	end
	return jg
end

function jm1(数据)
	local jg=数据
	for n=1,#mab do
		local z=string.sub(mab,n,n)
		if z=="," then
			-- print(66)
		end
		if key[z]~=nil then
			jg=string.gsub(jg,key[z],z)
		end
	end
 return decodeBase641(jg)
end

键盘符号={
		左键=0x00,
		中键=0x02,
		右键=0x01,
		退格=0x08,
		回车=0x0D,
		空格=0x20,
		tab=0x9,
		左=0x25,
		上=0x26,
		右=0x27,
		下=0x28
}
function 读入文件(fileName)
	local f = assert(io.open(fileName,'r'))
	local content = f:read('*all')
	f:close()
	return content
end

阵法位置={
		鸟翔阵=[[do local ret={[7]={["y"]=357,["x"]=450},[1]={["y"]=367,["x"]=587},[2]={["y"]=359,["x"]=703},[4]={["y"]=287,["x"]=689},[8]={["y"]=274,["x"]=575},[9]={["y"]=385,["x"]=382},[5]={["y"]=429,["x"]=449},[10]={["y"]=243,["x"]=636},[3]={["y"]=436,["x"]=582},[6]={["y"]=327,["x"]=521}} return ret end]]
		,天覆阵=[[do local ret={[7]={["y"]=357,["x"]=450},[1]={["y"]=384,["x"]=596},[2]={["y"]=383,["x"]=500},[4]={["y"]=412,["x"]=429},[8]={["y"]=274,["x"]=575},[9]={["y"]=385,["x"]=382},[5]={["y"]=283,["x"]=686},[10]={["y"]=243,["x"]=636},[3]={["y"]=320,["x"]=613},[6]={["y"]=324,["x"]=522}} return ret end]]
		,云垂阵=[[do local ret={[7]={["y"]=357,["x"]=450},[1]={["y"]=428,["x"]=670},[2]={["y"]=383,["x"]=500},[4]={["y"]=412,["x"]=429},[8]={["y"]=274,["x"]=575},[9]={["y"]=385,["x"]=382},[5]={["y"]=282,["x"]=673},[10]={["y"]=243,["x"]=636},[3]={["y"]=320,["x"]=613},[6]={["y"]=326,["x"]=526}} return ret end]]
		,地载阵=[[do local ret={[7]={["y"]=357,["x"]=450},[1]={["y"]=384,["x"]=597},[2]={["y"]=345,["x"]=552},[4]={["y"]=390,["x"]=490},[8]={["y"]=274,["x"]=575},[9]={["y"]=385,["x"]=382},[5]={["y"]=415,["x"]=650},[10]={["y"]=243,["x"]=636},[3]={["y"]=320,["x"]=613},[6]={["y"]=320,["x"]=518}} return ret end]]
		,风扬阵=[[do local ret={[7]={["y"]=357,["x"]=450},[1]={["y"]=400,["x"]=637},[2]={["y"]=360,["x"]=696},[4]={["y"]=321,["x"]=638},[8]={["y"]=274,["x"]=575},[9]={["y"]=385,["x"]=382},[5]={["y"]=394,["x"]=512},[10]={["y"]=243,["x"]=636},[3]={["y"]=432,["x"]=573},[6]={["y"]=322,["x"]=525}} return ret end]]
		,龙飞阵=[[do local ret={[7]={["y"]=357,["x"]=450},[1]={["y"]=448,["x"]=584},[2]={["y"]=467,["x"]=639},[4]={["y"]=440,["x"]=451},[8]={["y"]=274,["x"]=575},[9]={["y"]=385,["x"]=382},[5]={["y"]=395,["x"]=526},[10]={["y"]=243,["x"]=636},[3]={["y"]=291,["x"]=690},[6]={["y"]=323,["x"]=525}} return ret end]]
		,虎翼阵=[[do local ret={[7]={["y"]=357,["x"]=450},[1]={["y"]=444,["x"]=699},[2]={["y"]=359,["x"]=703},[4]={["y"]=287,["x"]=689},[8]={["y"]=274,["x"]=575},[9]={["y"]=385,["x"]=382},[5]={["y"]=429,["x"]=449},[10]={["y"]=243,["x"]=636},[3]={["y"]=436,["x"]=582},[6]={["y"]=327,["x"]=521}} return ret end]]
		,蛇蟠阵=[[do local ret={[7]={["y"]=357,["x"]=450},[1]={["y"]=408,["x"]=650},[2]={["y"]=441,["x"]=577},[4]={["y"]=403,["x"]=519},[8]={["y"]=274,["x"]=575},[9]={["y"]=385,["x"]=382},[5]={["y"]=447,["x"]=720},[10]={["y"]=243,["x"]=636},[3]={["y"]=368,["x"]=712},[6]={["y"]=327,["x"]=521}} return ret end]]
}
迭代修正={[1198567883]=1,[1322867204]=1,[2194816210]=1,[2774401093]=1,[92383142]=1,[803245291]=1,[4026933254]=1,
[972949979]=1,[2234123373]=1,[4291788538]=1,[4123730740]=1,[4126393258]=1,[4216374671]=1,[594523269]=1,[1993746922]=1,
[1846203257]=1,[659922551]=1,[1238878062]=1,[1337608376]=1,[857114492]=1,[3601844930]=1,[493656179]=1,[2291528828]=1,
[2471916192]=1,[556288161]=1,[3970292553]=1,[4201959108]=1,[1525915374]=1,[4232546957]=1,[1776923645]=1,[2089007572]=1,
[1865084478]=1,[1489146739]=1,[1015215437]=1,[2076610755]=1,[3467601219]=1,[339045070]=1,[2796680623]=1,[1411593358]=1,
[569209373]=1,[2578285773]=1,[621990072]=1,[3809623002]=1,[3574158970]=1,[2418368925]=1,[116130982]=1,[2814262767]=1,
[1670580820]=1,[4111787407]=1,[3101694266]=1,[4098637790]=1,[2656336023]=1,[1150439470]=1,[1705376638]=1,[1782439566]=1,
[581552700]=1,[2157921022]=1,[4274012337]=1,[2902858133]=1,[2512127013]=1,[1520820580]=1,[740646126]=1,[607870271]=1,
[619189213]=1,[2087916801]=1,[2750701818]=1,[2809877988]=1,[2273495960]=1,[303785981]=1,[3778545729]=1,[3107549612]=1,
[333930119]=1,[3947923690]=1,[3516494869]=1,[508905397]=1,[3025008437]=1,[1433167185]=1,[4189229797]=1,[2314268805]=1,
[610824997]=1,[1173564955]=1,[3043244041]=1,[3587373588]=1,[1897292561]=1,[3077630676]=1,[1284179344]=1,[3423872749]=1,
[939506143]=1,[2951893280]=1,[2137852331]=1,[4264373016]=1,[2213567130]=1,[3139413485]=1,[1629704855]=1,[1042710685]=1,
[3644222844]=1,[3951179215]=1,[4259873334]=1,[1242027238]=1,[958352525]=1,[3942685609]=1,[2650714435]=1,[2609628011]=1,
[3698644870]=1,[1510334912]=1,[436461360]=1,[4118221086]=1,[2910864082]=1,[463523872]=1,[210825756]=1,[2708080383]=1,
[2009204344]=1,[3836622392]=1,[2801151609]=1,[2782931557]=1,[2826457680]=1,[1037580978]=1,[789159257]=1,[1607593791]=1,
[1377649672]=1,[1617545683]=1,[3966136859]=1,[571142035]=1,[4111908170]=1,[1423658829]=1,[2489545420]=1,[2732785821]=1,
[3322229368]=1,[459229062]=1,[3657445131]=1,[1690442339]=1,[2278008400]=1,[1315316002]=1,[3107944047]=1,[2603954881]=1,
[1927395527]=1,[3988533543]=1,[672940014]=1,[3120405949]=1,[947996020]=1,[4193428466]=1,[2034116134]=1,[3443216643]=1,
[1492865095]=1,[4097908221]=1,[330112035]=1,[1512265752]=1,[397538195]=1,[3157786639]=1,[1997554435]=1,[4109342901]=1,
[2801629264]=1,[1539613628]=1,[1050893021]=1,[2212215444]=1,[3703129836]=1,[125412319]=1,[1740983547]=1,[1467466161]=1,
[3188334211]=1,[2670697489]=1,[2545689750]=1,[3277294463]=1,[2250171609]=1,[2029057739]=1,[1883982028]=1,[2678510899]=1,
[139790792]=1,[155999227]=1,[3716296248]=1,[838300931]=1,[747524185]=1,[2195934020]=1,[2896805672]=1,[4229617854]=1,
[2024668974]=1,[1857394109]=1,[2946813932]=1,[2482790664]=1,[4219044997]=1,[1027157748]=1,[776342191]=1,[2452516043]=1,
[3643786206]=1,[533876880]=1,[1734212905]=1,[3715462681]=1,[4141274598]=1,[2466813308]=1,[3911067979]=1,[2103710679]=1,
[3749438]=1,[1018070922]=1,[995698620]=1,[3508788176]=1,[3413273682]=1,[112478236]=1,[1465303491]=1,[3043670818]=1,
[1051618199]=1,[1849101638]=1,[3212527142]=1,[2115558165]=1,[3004755192]=1,[1011046408]=1,[3413255956]=1,[3417945932]=1,
[3738039458]=1,[1728526455]=1,[375405860]=1,[2740228553]=1,[965863785]=1,[2132309639]=1,[2657283590]=1,[3209370954]=1,
[143164260]=1,[1851258377]=1,[728904944]=1,[3753105193]=1,[583981435]=1,[247333098]=1,[802441688]=1,[2956176231]=1,
[1873404188]=1,[812821174]=1,[1786109105]=1,[1241775590]=1,[1646683356]=1,[304945535]=1,[3502426669]=1,[1539170847]=1,
[2068177574]=1,[241140809]=1,[3477875588]=1,[3870010961]=1,[184048410]=1,[2162415082]=1,[1138804365]=1,[318420431]=1,
[1585787695]=1,[3996525736]=1,[1362263605]=1,[3234089534]=1,[86275401]=1,[1501032219]=1,[1621392417]=1,[208494758]=1,
[29450010]=1,[947396698]=1,[4081980249]=1,[2714401860]=1,[708419920]=1,[1518169819]=1,[2944637360]=1,[2207457325]=1,
[2639845870]=1,[3741446813]=1,[4117103569]=1,[214419052]=1,[3704114510]=1,[3105334669]=1,[1063711236]=1,[2585033176]=1,
[1635724392]=1,[1682075539]=1,[1899323518]=1,[452334353]=1,[4218301282]=1,[2866269075]=1,[1649555822]=1,[3168300506]=1,
[3952279767]=1,[2506294709]=1,

}
阵法位置1={
		天覆阵={[1]={x=138,y=145},--x=60  y=30
		[2]={x=115,y=200},
		[3]={x=235,y=140},
		[4]={x=55,y=230},
		[5]={x=295,y=110},
		[6]={x=220,y=210},--x=60  y=30
		[7]={x=160,y=240},
		[8]={x=280,y=180},
		[9]={x=100,y=270},
		[10]={x=340,y=150}}

		,云垂阵={[1]={x=118,y=125},--x=60  y=30
		[2]={x=115,y=200},
		[3]={x=235,y=140},
		[4]={x=55,y=230},
		[5]={x=295,y=110},
		[6]={x=220,y=210},--x=60  y=30
		[7]={x=160,y=240},
		[8]={x=280,y=180},
		[9]={x=100,y=270},
		[10]={x=340,y=150}}

		,地载阵={[1]={x=135,y=135},--x=60  y=30
		[2]={x=215,y=85},
		[3]={x=55,y=170},
		[4]={x=175,y=170},
		[5]={x=98,y=105},
		[6]={x=220,y=210},--x=60  y=30
		[7]={x=160,y=240},
		[8]={x=280,y=180},
		[9]={x=100,y=270},
		[10]={x=340,y=150}}

		,蛇蟠阵={[1]={x=135,y=135},--x=60  y=30
		[2]={x=215,y=85},
		[3]={x=55,y=170},
		[4]={x=260,y=115},
		[5]={x=98,y=105},
		[6]={x=220,y=210},--x=60  y=30
		[7]={x=160,y=240},
		[8]={x=280,y=180},
		[9]={x=100,y=270},
		[10]={x=340,y=150}}

		,鸟翔阵={[1]={x=175,y=170},--x=60  y=30
		[2]={x=55,y=240},
		[5]={x=60,y=180},
		[4]={x=200,y=115},
		[3]={x=295,y=110},
		[6]={x=220,y=210},--x=60  y=30
		[7]={x=160,y=240},
		[8]={x=280,y=180},
		[9]={x=100,y=270},
		[10]={x=340,y=150}}

		,虎翼阵={[1]={x=98,y=115},--x=60  y=30
		[2]={x=55,y=240},
		[5]={x=60,y=180},
		[4]={x=200,y=115},
		[3]={x=295,y=110},
		[6]={x=220,y=210},--x=60  y=30
		[7]={x=160,y=240},
		[8]={x=280,y=180},
		[9]={x=100,y=270},
		[10]={x=340,y=150}}

		,龙飞阵={[1]={x=135,y=135},--x=60  y=30
		[2]={x=175,y=160},
		[5]={x=98,y=115},
		[4]={x=240,y=140},
		[3]={x=55,y=230},
		[6]={x=220,y=210},--x=60  y=30
		[7]={x=160,y=240},
		[8]={x=280,y=180},
		[9]={x=100,y=270},
		[10]={x=340,y=150}}

		,风扬阵={[1]={x=135,y=135},--x=60  y=30
		[2]={x=215,y=85},
		[3]={x=55,y=170},
		[4]={x=240,y=140},
		[5]={x=118,y=205},
		[6]={x=220,y=210},--x=60  y=30
		[7]={x=160,y=240},
		[8]={x=280,y=180},
		[9]={x=100,y=270},
		[10]={x=340,y=150}}
}

