-- @Author: 作者QQ381990860
-- @Date:   2022-04-19 00:31:09
-- @Last Modified by:   作者QQ381990860
-- @Last Modified time: 2024-07-12 10:33:34
-- @Author: 作者QQ381990860
-- @Date:   2021-09-19 08:45:06
-- @Last Modified by:   作者QQ381990860
-- @Last Modified time: 2022-04-19 13:29:27
--======================================================================--

--======================================================================--
local 内充系统 = class()
local floor = math.floor
local tp,zts,zt
local format = string.format
local insert = table.insert
local mousea = 引擎.鼠标按住
local mouseb = 引擎.鼠标弹起
local lcurl = require("lcurl")
function 内充系统:初始化(根)
	self.ID = 20072
	self.x = 195
	self.y = 150
	self.xx = 0
	self.yy = 0
	self.注释 = "充值系统"
	self.可视 = false
	self.鼠标 = false
	self.焦点 = false
	self.可移动 = true
	tp = 根
	self.窗口时间 = 0
	zts = tp.字体表.道具字体
	zt = tp.字体表.普通字体
	local wz = require("gge文字类")
	self.描边文字14 = wz.创建(程序目录.."wdf/vvxxzcom/pic/txmsj.ttf",14,false,true,true)
	self.描边文字14:置描边颜色(0xFFFFFFFF)
	self.描边文字16 = wz.创建(程序目录.."wdf/vvxxzcom/pic/txmsj.ttf",16,false,true,true)
	self.描边文字16:置描边颜色(0xFFFFFFFF)
	self.描边文字18 = wz.创建(程序目录.."wdf/vvxxzcom/pic/txmsj.ttf",18,false,true,true)
	self.描边文字18:置描边颜色(0xFFFFFFFF)
	self.描边文字20 = wz.创建(程序目录.."wdf/vvxxzcom/pic/txmsj.ttf",20,false,true,true)
	self.描边文字20:置描边颜色(0xFFFFFFFF)

	self.普通文字12 = wz.创建(程序目录.."wdf/vvxxzcom/pic/txmsj.ttf",12,false,false,true)
	self.普通文字14 = wz.创建(程序目录.."wdf/vvxxzcom/pic/txmsj.ttf",14,false,false,true)
	self.普通文字16 = wz.创建(程序目录.."wdf/vvxxzcom/pic/txmsj.ttf",16,false,false,true)
	self.普通文字18 = wz.创建(程序目录.."wdf/vvxxzcom/pic/txmsj.ttf",18,false,false,true)
	self.普通文字20 = wz.创建(程序目录.."wdf/vvxxzcom/pic/txmsj.ttf",20,false,false,true)

	local 资源 = tp.资源
	local 按钮 = tp._按钮
	local 自适应 = tp._自适应
	self.控件类 = require("ggeui/加载类")()
	local 总控件 = self.控件类:创建控件('序号控件')
	总控件:置可视(true,true)
	self.输入框 = 总控件:创建输入("充值点数",0,0,100,14)
	self.输入框:置可视(false,false)
	self.输入框:置限制字数(7)
	self.输入框:屏蔽快捷键(true)
	self.输入框:置光标颜色(-16777216)
	self.输入框:置文字颜色(-16777216)

	self.资源组 = {
		[1] = 自适应.创建(99,1,600,410,3,9),--资源:载入(程序目录..'wdf/vvxxzcom/pic/pay/Bg.png',"图片"),--签到背景
		[2] = 资源:载入(程序目录..'wdf/vvxxzcom/pic/pay/Select.png',"图片"),
		[3] = 资源:载入(程序目录..'wdf/vvxxzcom/pic/pay/Tags_1.png',"图片"),
		[4] = 自适应.创建(2,1,450,360,3,9),--选择框
		[5] = 资源:载入(程序目录..'wdf/vvxxzcom/pic/pay/Bg_1.png',"图片"),
		[6] = 资源:载入(程序目录..'wdf/vvxxzcom/pic/pay/Tags_2.png',"图片"),
		[7] = 自适应.创建(3,1,120,22,1,3),--选择框
		[8] = 自适应.创建(3,1,80,22,1,3),--选择框
		[9] = 按钮.创建(自适应.创建(18,4,16,16,4,3),0,0,4,true,true),--关闭


	}

	local 按钮状态 = {"正常","选中","按下","无效"}
	self.快捷充值 = {}
	self.确认充值 = {}
	self.领取充值 = {}
	self.换银子 = {}
	self.换仙玉 = {}
	self.换抓鬼 = {}
	self.换抽奖 = {}
	self.换月卡 = {}
	self.兑换充值 = {}
	self.CDK充值 = {}
	self.内置加速 = {}
	self.在线充值 = {}
	--self.领取充值=按钮.创建(自适应.创建(17,4,70,22,1,1),0,0,4,true,true,"领取充值")
	for i = 1,#按钮状态 do
		self.快捷充值[按钮状态[i]] = 资源:载入(程序目录..'wdf/vvxxzcom/pic/pay/Buttons_1_0'..i..'.png',"图片")
		self.确认充值[按钮状态[i]] = 资源:载入(程序目录..'wdf/vvxxzcom/pic/pay/Buttons_2_0'..i..'.png',"图片")
		self.领取充值[按钮状态[i]] = 资源:载入(程序目录..'wdf/vvxxzcom/pic/pay/Buttons_2_0'..i..'.png',"图片")
		self.换银子[按钮状态[i]] = 资源:载入(程序目录..'wdf/vvxxzcom/pic/pay/Buttons_1_0'..i..'.png',"图片")
		self.换仙玉[按钮状态[i]] = 资源:载入(程序目录..'wdf/vvxxzcom/pic/pay/Buttons_1_0'..i..'.png',"图片")
		self.换抓鬼[按钮状态[i]] = 资源:载入(程序目录..'wdf/vvxxzcom/pic/pay/Buttons_1_0'..i..'.png',"图片")
		self.换抽奖[按钮状态[i]] = 资源:载入(程序目录..'wdf/vvxxzcom/pic/pay/Buttons_1_0'..i..'.png',"图片")
		self.换月卡[按钮状态[i]] = 资源:载入(程序目录..'wdf/vvxxzcom/pic/pay/Buttons_1_0'..i..'.png',"图片")
		self.CDK充值[按钮状态[i]] = 资源:载入(程序目录..'wdf/vvxxzcom/pic/pay/Buttons_1_0'..i..'.png',"图片")
		self.内置加速[按钮状态[i]] = 资源:载入(程序目录..'wdf/vvxxzcom/pic/pay/Buttons_1_0'..i..'.png',"图片")
		self.兑换充值[按钮状态[i]] = 资源:载入(程序目录..'wdf/vvxxzcom/pic/pay/Buttons_2_0'..i..'.png',"图片")
		self.在线充值[按钮状态[i]] = 资源:载入(程序目录..'wdf/vvxxzcom/pic/pay/Buttons_1_0'..i..'.png',"图片")
		-- self.快捷充值[按钮状态[i]] = 按钮.创建(自适应.创建(100,4,125,27,1,3),0,0,4,true,true)--,资源:载入(程序目录..'wdf/vvxxzcom/pic/pay/Buttons_1_0'..i..'.png',"图片")
		-- self.确认充值[按钮状态[i]] = 按钮.创建(自适应.创建(100,4,125,27,1,3),0,0,4,true,true)--资源:载入(程序目录..'wdf/vvxxzcom/pic/pay/Buttons_2_0'..i..'.png',"图片")


	end
	local 快捷点数 = {500,1000,2000,5000,10000,20000}
	self.快捷选项 = {}
	for i = 1,#快捷点数 do
		self.快捷选项[快捷点数[i]] = 资源:载入(程序目录..'wdf/vvxxzcom/pic/pay/Cards_'..快捷点数[i]..'.png',"图片")
	end

	local 渠道 = {"微  信","支付宝"}
	self.勾选框 = {}
	for i = 1,#渠道 do
		self.勾选框[渠道[i]] = {
			OK = 资源:载入(程序目录..'wdf/vvxxzcom/pic/pay/Checkthebox_OK.png',"图片"),
			NG = 资源:载入(程序目录..'wdf/vvxxzcom/pic/pay/Checkthebox_NG.png',"图片"),
		}
	end
	self.渠道 = 0
	self.页面=1
end

function 内充系统:打开(数据)
	if self.可视 then
		self.可视 = false
		self.输入框:置可视(false,false)
		return
	else
		insert(tp.窗口_,self)
		local 资源 = tp.资源
		local 按钮 = tp._按钮
		local 自适应 = tp._自适应
		self.数据=数据
		tp.运行时间 = tp.运行时间 + 1
		self.窗口时间 = tp.运行时间
		self.可视 = true
		self.输入框:置文本("0")
		self.输入框:置可视(true,true)
		self.渠道 = 0
		if 授权码 =="9xRVCmGmz33nf5dRwnhfYregfrrBz5" or 授权码=="EspXekp7uUkKS9GJaN6S8yw4XWb9Dp" or 授权码=="ukStGu7rt2DVdSRhwK5UbU87kjC8uR" then
			self.页面=2
		else
		self.页面=1
		end

	end
end


function 内充系统:开始充值(数据)

local encodedUrl = 数据.内容
local 金额= 数据.金额+0
local 支付方式=数据.支付
local 订单号=数据.订单号
local decodedUrl = encodedUrl:gsub("\\/", "/"):gsub("([^:])//", "%1/"):gsub("([^:])//", "%1/")

decodedUrl = decodedUrl:gsub("([^%d/])cn", "%1.cn")

decodedUrl = decodedUrl:gsub("(.cn)([^/])", "%1/%2")

decodedUrl = decodedUrl:gsub("%.%.cn", ".cn")
--createDirectory(downloadDirectory)

-- 构建文件保存路径
local localFilename = 程序目录.."wdf/vvxxzcom/pic/code.png"

local urlToDownload = decodedUrl

self:downloadFile(urlToDownload, localFilename,金额,支付方式,订单号)

end



function 内充系统:downloadFile(url, filename,金额,支付方式,订单号)
    local easy = lcurl.easy()

    easy:setopt(lcurl.OPT_URL, url)
    easy:setopt(lcurl.OPT_FOLLOWLOCATION, 1)
    easy:setopt(lcurl.OPT_SSL_VERIFYPEER, 0)
    easy:setopt(lcurl.OPT_SSL_VERIFYHOST, 0)

    -- 打开文件用于写入
    local file, err = io.open(filename, "wb")

    if not file then
        return
    end

    -- 设置写入文件的回调函数
    easy:setopt(lcurl.OPT_WRITEFUNCTION, function(data)
        return file:write(data)
    end)

    local _, err = easy:perform()

    file:close()
    easy:close()

    if err then
        print("下载文件时发生错误:", err)
    else
    	 if not tp.窗口.二维码.可视 then
    	 	self.x=self.x-150
	            tp.窗口.二维码:打开(self.x+530,self.y+1,{金额,支付方式,订单号})
	        else
	            tp.窗口.二维码:打开(self.x+530,self.y+1,{金额,支付方式,订单号})
	        end
    end
end

function 内充系统:访问充值地址(数据)
	if 数据.动作 ==99 then
		self:开始充值(数据)
		return
	end
	self.访问地址 = 数据.内容
end



function 内充系统:刷新(数据)
	self.数据=数据
	if 数据.动作 == 1 then
		self.访问地址 = 数据.内容
	elseif 数据.动作 == 2 then
		self.数据.仙玉 = 数据.点卡
	end
end

function 内充系统:显示(dt,x,y)
	if tonumber(self.输入框:取文本()) == nil then
		self.输入框:置文本("0")
	end

	self.焦点 = false
	self.资源组[9]:更新(x,y)
	self.资源组[1]:显示(self.x,self.y)
	self.资源组[9]:显示(self.x-18+self.资源组[1].宽度,self.y+3)
	tp.窗口标题背景_:显示(self.x-86+self.资源组[1].宽度/2,self.y)
	self.描边文字20:置颜色(0xffFFFFFF):置描边颜色(0xff000000):显示(self.x+260,self.y+1,self.注释)
	self.资源组[4]:显示(self.x+140,self.y+35)




	if 授权码 ~="9xRVCmGmz33nf5dRwnhfYregfrrBz5" or 授权码=="EspXekp7uUkKS9GJaN6S8yw4XWb9Dp"  or 授权码=="ukStGu7rt2DVdSRhwK5UbU87kjC8uR" then
	local xx,yy = 10,35
	if  self.页面== 1 then
		self.快捷充值["按下"]:显示(self.x+xx,self.y+yy)
	else
	self.快捷充值["正常"]:显示(self.x+xx,self.y+yy)
	if self.快捷充值["正常"]:是否选中(x,y) then
		self.快捷充值["选中"]:显示(self.x+xx,self.y+yy)
		if mousea(0) then
			self.快捷充值["按下"]:显示(self.x+xx,self.y+yy)
			self.焦点 = true
		elseif mouseb(0) then
			self.页面=1
		end
	end
	end
	self.普通文字16:置颜色(0xffffffff):显示(self.x+xx+31,self.y+yy+2,"扫码充值")
	end




	if  授权码~= "ugzaDy7b4wSHsNY" then
		local xx,yy = 10,75
		if 授权码 =="9xRVCmGmz33nf5dRwnhfYregfrrBz5" or 授权码=="EspXekp7uUkKS9GJaN6S8yw4XWb9Dp" or 授权码=="ukStGu7rt2DVdSRhwK5UbU87kjC8uR"  then
			xx,yy = 10,35
		end
		if  self.页面== 2 then
			self.换银子["按下"]:显示(self.x+xx,self.y+yy)
		else
		self.换银子["正常"]:显示(self.x+xx,self.y+yy)
		if self.换银子["正常"]:是否选中(x,y) then
			self.换银子["选中"]:显示(self.x+xx,self.y+yy)
			if mousea(0) then
				self.换银子["按下"]:显示(self.x+xx,self.y+yy)
				self.焦点 = true
			elseif mouseb(0) then
				self.页面=2
			end
		end
		end
		self.普通文字16:置颜色(0xffffffff):显示(self.x+xx+31,self.y+yy+2,"兑换银子")

		local xx,yy = 10,115
		if 授权码 =="9xRVCmGmz33nf5dRwnhfYregfrrBz5" or 授权码=="EspXekp7uUkKS9GJaN6S8yw4XWb9Dp" or 授权码=="ukStGu7rt2DVdSRhwK5UbU87kjC8uR" then
			xx,yy = 10,75
		end
		if  self.页面== 3 then
			self.换仙玉["按下"]:显示(self.x+xx,self.y+yy)
		else
		self.换仙玉["正常"]:显示(self.x+xx,self.y+yy)
		if self.换仙玉["正常"]:是否选中(x,y) then
			self.换仙玉["选中"]:显示(self.x+xx,self.y+yy)
			if mousea(0) then
				self.换仙玉["按下"]:显示(self.x+xx,self.y+yy)
				self.焦点 = true
			elseif mouseb(0) then
				self.页面=3
			end
		end
		end
		self.普通文字16:置颜色(0xffffffff):显示(self.x+xx+31,self.y+yy+2,"兑换仙玉")


		local xx,yy = 10,155
		if 授权码 =="9xRVCmGmz33nf5dRwnhfYregfrrBz5" or 授权码=="EspXekp7uUkKS9GJaN6S8yw4XWb9Dp" or 授权码=="ukStGu7rt2DVdSRhwK5UbU87kjC8uR"  then
			xx,yy = 10,115
		end
		if  self.页面== 4 then
			self.换抓鬼["按下"]:显示(self.x+xx,self.y+yy)
		else
		self.换抓鬼["正常"]:显示(self.x+xx,self.y+yy)
		if self.换抓鬼["正常"]:是否选中(x,y) then
			self.换抓鬼["选中"]:显示(self.x+xx,self.y+yy)
			if mousea(0) then
				self.换抓鬼["按下"]:显示(self.x+xx,self.y+yy)
				self.焦点 = true
			elseif mouseb(0) then
				self.页面=4
			end
		end
		end
		self.普通文字16:置颜色(0xffffffff):显示(self.x+xx+31,self.y+yy+2,"兑换鬼卡")


		local xx,yy = 10,195
		if 授权码 =="9xRVCmGmz33nf5dRwnhfYregfrrBz5" or 授权码=="EspXekp7uUkKS9GJaN6S8yw4XWb9Dp" or 授权码=="ukStGu7rt2DVdSRhwK5UbU87kjC8uR"  then
			xx,yy = 10,155
		end
		if  self.页面== 5 then
			self.换抽奖["按下"]:显示(self.x+xx,self.y+yy)
		else
		self.换抽奖["正常"]:显示(self.x+xx,self.y+yy)
		if self.换抽奖["正常"]:是否选中(x,y) then
			self.换抽奖["选中"]:显示(self.x+xx,self.y+yy)
			if mousea(0) then
				self.换抽奖["按下"]:显示(self.x+xx,self.y+yy)
				self.焦点 = true
			elseif mouseb(0) then
				self.页面=5
			end
		end
		end
		self.普通文字16:置颜色(0xffffffff):显示(self.x+xx+31,self.y+yy+2,"兑换抽奖")


		local xx,yy = 10,235
		if 授权码 =="9xRVCmGmz33nf5dRwnhfYregfrrBz5" or 授权码=="EspXekp7uUkKS9GJaN6S8yw4XWb9Dp"  or 授权码=="ukStGu7rt2DVdSRhwK5UbU87kjC8uR" then
			xx,yy = 10,195
		end
		if  self.页面== 6 then
			self.换月卡["按下"]:显示(self.x+xx,self.y+yy)
		else
		self.换月卡["正常"]:显示(self.x+xx,self.y+yy)
		if self.换月卡["正常"]:是否选中(x,y) then
			self.换月卡["选中"]:显示(self.x+xx,self.y+yy)
			if mousea(0) then
				self.换月卡["按下"]:显示(self.x+xx,self.y+yy)
				self.焦点 = true
			elseif mouseb(0) then
				self.页面=6
			end
		end
		end
		self.普通文字16:置颜色(0xffffffff):显示(self.x+xx+31,self.y+yy+2,"兑换特权")


		local xx,yy = 10,275
		if 授权码 =="ukStGu7rt2DVdSRhwK5UbU87kjC8uR" then
			xx,yy = 10,235

		self.在线充值["正常"]:显示(self.x+xx,self.y+yy)
			if self.在线充值["正常"]:是否选中(x,y) then
				self.在线充值["选中"]:显示(self.x+xx,self.y+yy)
				if mousea(0) then
					self.在线充值["按下"]:显示(self.x+xx,self.y+yy)
					self.焦点 = true
				elseif mouseb(0) then
					引擎.运行("https://faka.555fkw.com//links/0A34FA68")
				end
			end
			self.普通文字16:置颜色(0xffffffff):显示(self.x+xx+31,self.y+yy+2,"在线充值")

		end



	else


		local xx,yy = 10,75
		if  self.页面== 3 then
			self.换仙玉["按下"]:显示(self.x+xx,self.y+yy)
		else
		self.换仙玉["正常"]:显示(self.x+xx,self.y+yy)
		if self.换仙玉["正常"]:是否选中(x,y) then
			self.换仙玉["选中"]:显示(self.x+xx,self.y+yy)
			if mousea(0) then
				self.换仙玉["按下"]:显示(self.x+xx,self.y+yy)
				self.焦点 = true
			elseif mouseb(0) then
				self.页面=3
			end
		end
		end
		self.普通文字16:置颜色(0xffffffff):显示(self.x+xx+31,self.y+yy+2,"兑换仙玉")


		local xx,yy = 10,115
		if  self.页面== 6 then
			self.换月卡["按下"]:显示(self.x+xx,self.y+yy)
		else
		self.换月卡["正常"]:显示(self.x+xx,self.y+yy)
		if self.换月卡["正常"]:是否选中(x,y) then
			self.换月卡["选中"]:显示(self.x+xx,self.y+yy)
			if mousea(0) then
				self.换月卡["按下"]:显示(self.x+xx,self.y+yy)
				self.焦点 = true
			elseif mouseb(0) then
				self.页面=6
			end
		end
		end
		self.普通文字16:置颜色(0xffffffff):显示(self.x+xx+31,self.y+yy+2,"兑换特权")


		local xx,yy = 10,155
			if  self.页面== 4 then
				self.换抓鬼["按下"]:显示(self.x+xx,self.y+yy)
			else
			self.换抓鬼["正常"]:显示(self.x+xx,self.y+yy)
			if self.换抓鬼["正常"]:是否选中(x,y) then
				self.换抓鬼["选中"]:显示(self.x+xx,self.y+yy)
				if mousea(0) then
					self.换抓鬼["按下"]:显示(self.x+xx,self.y+yy)
					self.焦点 = true
				elseif mouseb(0) then
					self.页面=4
				end
			end
			end
		self.普通文字16:置颜色(0xffffffff):显示(self.x+xx+31,self.y+yy+2,"兑换鬼卡")


	end




	--if 授权码 =="hJFsneCQNf2ph76WUnnN" or 全局ip=="************"  then-----钻石:J a s o n
	if 授权码 ~="9xRVCmGmz33nf5dRwnhfYregfrrBz5" or 授权码=="EspXekp7uUkKS9GJaN6S8yw4XWb9Dp"  then
		local xx,yy = 10,275+100

		self.CDK充值["正常"]:显示(self.x+xx,self.y+yy)
		if self.CDK充值["正常"]:是否选中(x,y) then
			self.CDK充值["选中"]:显示(self.x+xx,self.y+yy)
			if mousea(0) then
				self.CDK充值["按下"]:显示(self.x+xx,self.y+yy)
				self.焦点 = true
			elseif mouseb(0) then

			                	发送数据(94.8)


			end
		end
		self.普通文字16:置颜色(0xffffffff):显示(self.x+xx+31,self.y+yy+2,"CDK充值")


		if self.数据.内置加速开关==1 then
		local xx,yy = 10,275+60
		self.内置加速["正常"]:显示(self.x+xx,self.y+yy)
		if self.内置加速["正常"]:是否选中(x,y) then
			self.内置加速["选中"]:显示(self.x+xx,self.y+yy)
			if mousea(0) then
				self.内置加速["按下"]:显示(self.x+xx,self.y+yy)
				self.焦点 = true
			elseif mouseb(0) then

			                	发送数据(207)


			end
		end
		self.普通文字16:置颜色(0xffffffff):显示(self.x+xx+31,self.y+yy+2,"内置加速")
		end


	else
		local xx,yy = 10,275+100
		self.内置加速["正常"]:显示(self.x+xx,self.y+yy)
		if self.内置加速["正常"]:是否选中(x,y) then
			self.内置加速["选中"]:显示(self.x+xx,self.y+yy)
			if mousea(0) then
				self.内置加速["按下"]:显示(self.x+xx,self.y+yy)
				self.焦点 = true
			elseif mouseb(0) then

			                	发送数据(207)


			end
		end
		self.普通文字16:置颜色(0xffffffff):显示(self.x+xx+31,self.y+yy+2,"内置加速")

	end











--end
	if self.访问地址 ~= nil then
		引擎.运行(self.访问地址)
		self.访问地址 = nil
	end




	if  self.页面== 1 then                --充值页面
		if   tonumber(math.floor(self.输入框:取文本())) ~= tonumber(self.输入框:取文本())  then
			self.输入框:置文本(math.floor(self.输入框:取文本()))
		end

		self.资源组[6]:显示(self.x+160,self.y+45)

	local 渠道 = {"微  信","支付宝"}
	if self.渠道 ~= 0 then
		self.描边文字18:置颜色(0xFFFF4500):显示(self.x+250,self.y+46,渠道[self.渠道])
	else
		self.描边文字16:置颜色(0xff00FF00):显示(self.x+245,self.y+46,"请先选择渠道")
	end
	self.普通文字16:置颜色(0xff000000):显示(self.x+160,self.y+80,"请输入充值点卡")
	self.资源组[7]:显示(self.x+280,self.y+80)
	self.输入框:置坐标(self.x+290,self.y+82)
	self.控件类:更新(dt,x,y)
	self.控件类:显示(dt,x,y)
	if self.输入框._已碰撞 then
		self.焦点 = true
	end





	local xx = 0
	for i = 1,#渠道 do
		if self.渠道 == i then
			self.勾选框[渠道[i]].OK:显示(self.x+380+xx*100,self.y+45)
			if mouseb(0) and self.勾选框[渠道[i]].OK:是否选中(x,y) then
				self.渠道 = 0
			end
		else
			self.勾选框[渠道[i]].NG:显示(self.x+380+xx*100,self.y+45)
			if mouseb(0) and self.勾选框[渠道[i]].NG:是否选中(x,y) then
				if i == 1 and self.数据.渠道.WX == 0 then
					tp.提示:写入("#Y本服尚未开启微信充值渠道.")
				elseif i == 2 and self.数据.渠道.ZFB == 0 then
					tp.提示:写入("#Y本服尚未开启支付宝充值渠道.")
				elseif i == 3 and self.数据.渠道.QQ == 0 then
					tp.提示:写入("#Y本服尚未开启QQ充值渠道.")
				else
					self.渠道 = i
				end
			end
		end
		zt:显示(self.x+400+xx*100,self.y+46,渠道[i])
		xx = xx + 1
	end

	self.资源组[5]:显示(self.x+160,self.y+115)
	local 快捷点数 = {500,1000,2000,5000,10000,20000}
	local xx,yy=0,0
	for i = 1 , 6 do
		self.快捷选项[快捷点数[i]]:显示(self.x+167+xx*138,self.y+125+yy*90)
		if mouseb(0) and self.快捷选项[快捷点数[i]]:是否选中(x,y) then
			self.输入框:置文本(快捷点数[i])
		end
		if tonumber(self.输入框:取文本()) == 快捷点数[i] then
			self.资源组[2]:显示(self.x+168+xx*138,self.y+127+yy*90)
		end
		xx = xx + 1
		if xx >= 3 then
			xx = 0
			yy = yy + 1
		end
	end



	self.普通文字14:置颜色(0xff000000):显示(self.x+160,self.y+322,"当前账户点卡")
	self.资源组[7]:显示(self.x+260,self.y+320)
	self.普通文字12:置颜色(0xff000000):显示(self.x+270,self.y+322,self.数据.点卡)


	local 金额 = tonumber(self.输入框:取文本()) / self.数据.比例
	金额 = tonumber(string.format("%.2f", 金额))
	self.普通文字14:置颜色(红色):显示(self.x+410,self.y+322,"支付金额")
	self.资源组[8]:显示(self.x+480,self.y+320)
	self.普通文字12:置颜色(红色):显示(self.x+490,self.y+322,金额.."元")

	local 额外赠送 = 0
	for key, value in pairs(self.数据.满赠) do
		    if 金额 >= key then
		        额外赠送 = math.max(额外赠送, value)
		    end
	end

	if 额外赠送 >0  then
	--self.普通文字14:置颜色():显示(self.x+405,self.y+82,"+额外赠送".. 额外赠送)
	--tp.字体表.描边字体:显示(self.x+405,self.y+82,"额外赠送".. 额外赠送)
	self.描边文字16:置颜色(0xff00FF00):显示(self.x+425,self.y+81,"额外赠送".. 额外赠送*self.数据.比例.."点卡")
	--self.描边文字18:置颜色(0xFFFF4500):显示(self.x+405,self.y+80,"额外赠送".. 额外赠送)
	end



	local 允许充值 = false
	if self.渠道 ~= 0 and 金额 > 0 then
		允许充值 = true
	end





	local xx,yy = 210,350
	if 允许充值 then
		self.确认充值["正常"]:显示(self.x+xx,self.y+yy)
		if self.确认充值["正常"]:是否选中(x,y) then
			self.确认充值["选中"]:显示(self.x+xx,self.y+yy)
			if mousea(0) then
				self.确认充值["按下"]:显示(self.x+xx,self.y+yy)
				self.焦点 = true
			elseif mouseb(0) then
				if 金额 < self.数据.最低金额 or 金额 > self.数据.最高金额 then
					--tp.提示:写入("#Y单笔充值金额限制"..self.数据.最低金额.."元~"..self.数据.最高金额.."元.")
					tp.常规提示:打开("#Y单笔充值金额限制"..self.数据.最低金额.."元~"..self.数据.最高金额.."元.")
				elseif  金额 ~= math.floor(金额) then
					tp.常规提示:打开("#Y请正确输入金额，不要带小数点.")

				else
					发送数据(115,{动作=2,渠道=self.渠道,金额=金额})
				end
			end
		end
		self.描边文字16:置颜色(0xffFFD700):置描边颜色(0xffFF8C00):显示(self.x+xx+34,self.y+yy+2,"充值点卡")
	else
		self.确认充值["无效"]:显示(self.x+xx,self.y+yy)
		self.描边文字16:置颜色(0xffFFFAFA):置描边颜色(0xff778899):显示(self.x+xx+34,self.y+yy+2,"充值点卡")
	end


	local xx,yy = 380,350
	self.领取充值["正常"]:显示(self.x+xx,self.y+yy)
	if self.领取充值["正常"]:是否选中(x,y) then
		self.领取充值["选中"]:显示(self.x+xx,self.y+yy)
		if mousea(0) then
			self.领取充值["按下"]:显示(self.x+xx,self.y+yy)
			self.焦点 = true
		elseif mouseb(0) then
			发送数据(115,{动作=3})
		end
	end
	self.普通文字16:置颜色(0xffffffff):显示(self.x+xx+34,self.y+yy+2,"领取充值")


	if self.资源组[9]:事件判断() then
		self:打开()
		return
	-- elseif self.领取充值:事件判断() then
	-- 	发送数据(115,{动作=3})
	end


	elseif  self.页面== 2 then    --兑换银子
		if tonumber(self.输入框:取文本()) == nil then
			self.输入框:置文本("0")
		elseif   tonumber(self.输入框:取文本()) > self.数据.点卡 then
			self.输入框:置文本(self.数据.点卡)
		elseif   tonumber(math.floor(self.输入框:取文本())) ~= tonumber(self.输入框:取文本())  then
			self.输入框:置文本(math.floor(self.输入框:取文本()))
		end
		self.资源组[6]:显示(self.x+160,self.y+45)
		self.资源组[7]:显示(self.x+330,self.y+230)
		self.资源组[7]:显示(self.x+330,self.y+280)
		self.输入框:置坐标(self.x+340,self.y+232)
		self.控件类:更新(dt,x,y)
		self.控件类:显示(dt,x,y)
		if self.输入框._已碰撞 then
			self.焦点 = true
		end
		self.普通文字16:置颜色(0xff000000):显示(self.x+210,self.y+230,"请输入点卡金额")



		self.普通文字14:置颜色(0xff000000):显示(self.x+240,self.y+91,"当前账户点卡")
		self.资源组[7]:显示(self.x+330,self.y+90)
		self.普通文字12:置颜色(0xff000000):显示(self.x+340,self.y+92,self.数据.点卡)



		self.普通文字14:置颜色(0xff000000):显示(self.x+240,self.y+141,"当前账户银子")
		self.资源组[7]:显示(self.x+330,self.y+140)
		self.普通文字12:置颜色(0xffFFD700):显示(self.x+340,self.y+142,self.数据.银子)


		tp.字体表.人物字体_:置颜色(黑色):显示(self.x+250,self.y+185,"当前兑换比例为1："..self.数据.换银子比例)
		self.描边文字16:置颜色(0xff00FF00):显示(self.x+310,self.y+47,"点卡兑换银子")


		local 金额 = tonumber(self.输入框:取文本()) * self.数据.换银子比例
		金额 = tonumber(string.format("%.2f", 金额))

		self.普通文字16:置颜色(0xff000000):显示(self.x+210,self.y+280,"兑换可获得银子")
		self.普通文字12:置颜色(0xff000000):显示(self.x+340,self.y+282,数额尾数转换(金额))



		local xx,yy = 295,320
		self.领取充值["正常"]:显示(self.x+xx,self.y+yy)
		if self.领取充值["正常"]:是否选中(x,y) then
			self.领取充值["选中"]:显示(self.x+xx,self.y+yy)
			if mousea(0) then
				self.领取充值["按下"]:显示(self.x+xx,self.y+yy)
				self.焦点 = true
			elseif mouseb(0) then
				发送数据(115,{动作=4,类型="银子",金额=金额,点卡消耗=self.输入框:取文本()})
			end
		end
		self.普通文字16:置颜色(0xffffffff):显示(self.x+xx+34,self.y+yy+2,"确认兑换")



	elseif  self.页面== 3 then    --兑换仙玉
		if tonumber(self.输入框:取文本()) == nil then
			self.输入框:置文本("0")
		elseif   tonumber(self.输入框:取文本()) > self.数据.点卡 then
			self.输入框:置文本(self.数据.点卡)
		elseif   tonumber(math.floor(self.输入框:取文本())) ~= tonumber(self.输入框:取文本())  then
			self.输入框:置文本(math.floor(self.输入框:取文本()))
		end
		self.资源组[6]:显示(self.x+160,self.y+45)
		self.资源组[7]:显示(self.x+330,self.y+230)
		self.资源组[7]:显示(self.x+330,self.y+280)
		self.输入框:置坐标(self.x+340,self.y+232)
		self.控件类:更新(dt,x,y)
		self.控件类:显示(dt,x,y)
		if self.输入框._已碰撞 then
			self.焦点 = true
		end
		self.普通文字16:置颜色(0xff000000):显示(self.x+210,self.y+230,"请输入点卡金额")



		self.普通文字14:置颜色(0xff000000):显示(self.x+240,self.y+91,"当前账户点卡")
		self.资源组[7]:显示(self.x+330,self.y+90)
		self.普通文字12:置颜色(0xff000000):显示(self.x+340,self.y+92,self.数据.点卡)



		self.普通文字14:置颜色(0xff000000):显示(self.x+240,self.y+141,"当前账户仙玉")
		self.资源组[7]:显示(self.x+330,self.y+140)
		self.普通文字12:置颜色(0xffFFD700):显示(self.x+340,self.y+142,self.数据.仙玉)


		tp.字体表.人物字体_:置颜色(黑色):显示(self.x+250,self.y+185,"当前兑换比例为1："..self.数据.换仙玉比例)
		self.描边文字16:置颜色(0xff00FF00):显示(self.x+310,self.y+47,"点卡兑换仙玉")


		local 金额 = tonumber(self.输入框:取文本()) * self.数据.换仙玉比例
		金额 = tonumber(string.format("%.2f", 金额))

		self.普通文字16:置颜色(0xff000000):显示(self.x+210,self.y+280,"兑换可获得仙玉")
		self.普通文字12:置颜色(0xff000000):显示(self.x+340,self.y+282,数额尾数转换(金额))



		local xx,yy = 295,320
		self.领取充值["正常"]:显示(self.x+xx,self.y+yy)
		if self.领取充值["正常"]:是否选中(x,y) then
			self.领取充值["选中"]:显示(self.x+xx,self.y+yy)
			if mousea(0) then
				self.领取充值["按下"]:显示(self.x+xx,self.y+yy)
				self.焦点 = true
			elseif mouseb(0) then
				发送数据(115,{动作=4,类型="仙玉",金额=金额,点卡消耗=self.输入框:取文本()})
			end
		end
		self.普通文字16:置颜色(0xffffffff):显示(self.x+xx+34,self.y+yy+2,"确认兑换")



	elseif  self.页面== 4 then    --兑换抓鬼
		if tonumber(self.输入框:取文本()) == nil then
			self.输入框:置文本("0")
		elseif   tonumber(self.输入框:取文本()) > self.数据.点卡 then
			self.输入框:置文本(self.数据.点卡)
		elseif   tonumber(math.floor(self.输入框:取文本())) ~= tonumber(self.输入框:取文本())  then
			self.输入框:置文本(math.floor(self.输入框:取文本()))
		end
		self.资源组[6]:显示(self.x+160,self.y+45)
		self.资源组[7]:显示(self.x+330,self.y+230)
		self.资源组[7]:显示(self.x+330,self.y+280)
		self.输入框:置坐标(self.x+340,self.y+232)
		self.控件类:更新(dt,x,y)
		self.控件类:显示(dt,x,y)
		if self.输入框._已碰撞 then
			self.焦点 = true
		end
		self.普通文字16:置颜色(0xff000000):显示(self.x+210,self.y+230,"请输入点卡金额")



		self.普通文字14:置颜色(0xff000000):显示(self.x+240,self.y+91,"当前账户点卡")
		self.资源组[7]:显示(self.x+330,self.y+90)
		self.普通文字12:置颜色(0xff000000):显示(self.x+340,self.y+92,self.数据.点卡)



		self.普通文字14:置颜色(0xff000000):显示(self.x+240,self.y+141,"当前抓鬼次数")
		self.资源组[7]:显示(self.x+330,self.y+140)
		self.普通文字12:置颜色(0xffFFD700):显示(self.x+340,self.y+142,self.数据.自动抓鬼)


		tp.字体表.人物字体_:置颜色(黑色):显示(self.x+250,self.y+185,"当前兑换比例为1："..self.数据.换抓鬼比例)
		self.描边文字16:置颜色(0xff00FF00):显示(self.x+300,self.y+47,"点卡兑换抓鬼次数")


		local 金额 = tonumber(self.输入框:取文本()) * self.数据.换抓鬼比例
		金额 = tonumber(string.format("%.2f", 金额))

		self.普通文字16:置颜色(0xff000000):显示(self.x+210,self.y+280,"兑换可得抓鬼数")
		self.普通文字12:置颜色(0xff000000):显示(self.x+340,self.y+282,金额)



		local xx,yy = 295,320
		self.领取充值["正常"]:显示(self.x+xx,self.y+yy)
		if self.领取充值["正常"]:是否选中(x,y) then
			self.领取充值["选中"]:显示(self.x+xx,self.y+yy)
			if mousea(0) then
				self.领取充值["按下"]:显示(self.x+xx,self.y+yy)
				self.焦点 = true
			elseif mouseb(0) then
				发送数据(115,{动作=4,类型="抓鬼",金额=金额,点卡消耗=self.输入框:取文本()})
			end
		end
		self.普通文字16:置颜色(0xffffffff):显示(self.x+xx+34,self.y+yy+2,"确认兑换")



	elseif  self.页面== 5 then    --兑换抽奖
		if tonumber(self.输入框:取文本()) == nil then
			self.输入框:置文本("0")
		elseif   tonumber(self.输入框:取文本()) > self.数据.点卡 then
			self.输入框:置文本(self.数据.点卡)
		elseif   tonumber(math.floor(self.输入框:取文本())) ~= tonumber(self.输入框:取文本())  then
			self.输入框:置文本(math.floor(self.输入框:取文本()))
		end
		self.资源组[6]:显示(self.x+160,self.y+45)
		self.资源组[7]:显示(self.x+330,self.y+230)
		self.资源组[7]:显示(self.x+330,self.y+280)
		self.输入框:置坐标(self.x+340,self.y+232)
		self.控件类:更新(dt,x,y)
		self.控件类:显示(dt,x,y)
		if self.输入框._已碰撞 then
			self.焦点 = true
		end
		self.普通文字16:置颜色(0xff000000):显示(self.x+210,self.y+230,"请输入点卡金额")



		self.普通文字14:置颜色(0xff000000):显示(self.x+240,self.y+91,"当前账户点卡")
		self.资源组[7]:显示(self.x+330,self.y+90)
		self.普通文字12:置颜色(0xff000000):显示(self.x+340,self.y+92,self.数据.点卡)



		self.普通文字14:置颜色(0xff000000):显示(self.x+240,self.y+141,"当前抽奖次数")
		self.资源组[7]:显示(self.x+330,self.y+140)
		self.普通文字12:置颜色(0xffFFD700):显示(self.x+340,self.y+142,self.数据.抽奖)


		tp.字体表.人物字体_:置颜色(黑色):显示(self.x+250,self.y+185,"当前兑换比例为"..self.数据.换抽奖比例.."：1")
		self.描边文字16:置颜色(0xff00FF00):显示(self.x+300,self.y+47,"点卡兑换抽奖次数")


		local 金额 = tonumber(self.输入框:取文本()) / self.数据.换抽奖比例
		金额 = tonumber(string.format("%.2f", 金额))

		self.普通文字16:置颜色(0xff000000):显示(self.x+210,self.y+280,"兑换可得抽奖数")
		self.普通文字12:置颜色(0xff000000):显示(self.x+340,self.y+282,金额)



		local xx,yy = 295,320
		self.领取充值["正常"]:显示(self.x+xx,self.y+yy)
		if self.领取充值["正常"]:是否选中(x,y) then
			self.领取充值["选中"]:显示(self.x+xx,self.y+yy)
			if mousea(0) then
				self.领取充值["按下"]:显示(self.x+xx,self.y+yy)
				self.焦点 = true
			elseif mouseb(0) then
				if not 十的倍数(self.输入框:取文本(),self.数据.换抽奖比例) then
					tp.常规提示:打开("#Y请正确输入金额，必须是"..self.数据.换抽奖比例.."的倍数.")
				return
				end
				发送数据(115,{动作=4,类型="抽奖",金额=金额,点卡消耗=self.输入框:取文本()})
			end
		end
		self.普通文字16:置颜色(0xffffffff):显示(self.x+xx+34,self.y+yy+2,"确认兑换")





	elseif  self.页面== 6 then    --兑换月卡

		if tonumber(self.输入框:取文本()) == nil then
			self.输入框:置文本("0")
		elseif   tonumber(self.输入框:取文本()) > self.数据.点卡 then
			self.输入框:置文本(self.数据.点卡)
		elseif   tonumber(math.floor(self.输入框:取文本())) ~= tonumber(self.输入框:取文本())  then
			self.输入框:置文本(math.floor(self.输入框:取文本()))
		end
		self.资源组[6]:显示(self.x+160,self.y+45)
		self.资源组[7]:显示(self.x+330,self.y+230)
		self.输入框:置坐标(self.x+340,self.y+232)
		self.控件类:更新(dt,x,y)
		self.控件类:显示(dt,x,y)
		if self.输入框._已碰撞 then
			self.焦点 = true
		end
		self.普通文字16:置颜色(0xff000000):显示(self.x+210,self.y+230,"请输入点卡金额")



		self.普通文字14:置颜色(0xff000000):显示(self.x+240,self.y+91,"当前账户点卡")
		self.资源组[7]:显示(self.x+330,self.y+90)
		self.普通文字12:置颜色(0xff000000):显示(self.x+340,self.y+92,self.数据.点卡)



		self.普通文字14:置颜色(0xff000000):显示(self.x+240,self.y+121,"当前特权状态")
		self.普通文字14:置颜色(0xff000000):显示(self.x+240,self.y+152,"特权到期时间")
		self.资源组[7]:显示(self.x+330,self.y+120)
		if self.数据.月卡生效 then
			self.普通文字12:置颜色(0xffFFD700):显示(self.x+350,self.y+122,"特权已生效")
			self.普通文字12:置颜色(0xffFFD700):显示(self.x+330,self.y+152,时间转换(self.数据.月卡到期时间))
		--self.普通文字12:置颜色(0xffFFD700):显示(self.x+340,self.y+142,self.数据.抽奖)
		else
			self.资源组[7]:显示(self.x+330,self.y+150)
			self.普通文字12:置颜色(红色):显示(self.x+350,self.y+122,"特权未生效")
			self.普通文字12:置颜色(红色):显示(self.x+350,self.y+152,"特权未生效")
		end



		tp.字体表.人物字体_:置颜色(黑色):显示(self.x+250,self.y+190,"当前兑换比例为"..self.数据.换月卡比例.."：1")
		self.描边文字16:置颜色(0xff00FF00):显示(self.x+310,self.y+47,"点卡兑换特权")


		local 金额 = tonumber(self.输入框:取文本()) / self.数据.换月卡比例
		金额 = tonumber(string.format("%.2f", 金额))

		--self.资源组[7]:显示(self.x+330,self.y+280)
		self.普通文字16:置颜色(0xff000000):显示(self.x+210,self.y+260,"兑换特权总天数")
		self.普通文字16:置颜色(0xff000000):显示(self.x+210,self.y+290,"兑换到期时间数")
		self.资源组[7]:显示(self.x+330,self.y+260)

		if tonumber(self.输入框:取文本()) ~= 0    then
			if   self.数据.月卡生效  then
				local  总天数= math.floor(金额) *(self.数据.月卡时间/86400)
				local  月卡最终时间 = math.floor(金额)  * self.数据.月卡时间 + self.数据.月卡到期时间
				self.普通文字12:置颜色(红色):显示(self.x+330,self.y+292,时间转换(月卡最终时间))
				self.普通文字12:置颜色(红色):显示(self.x+340,self.y+262,总天数.."天")
			else
				local  总天数= math.floor(金额) *(self.数据.月卡时间/86400)
				local  月卡最终时间 = math.floor(金额)  * self.数据.月卡时间 + os.time()
				self.普通文字12:置颜色(红色):显示(self.x+330,self.y+292,时间转换(月卡最终时间))
				self.普通文字12:置颜色(红色):显示(self.x+340,self.y+262,总天数.."天")
			end
		else
			local  月卡最终时间 = math.floor(金额)  * self.数据.月卡时间 + os.time()
			--self.普通文字12:置颜色(0xff000000):显示(self.x+330,self.y+282,时间转换(月卡最终时间) or "0")
			self.资源组[7]:显示(self.x+330,self.y+290)
			self.普通文字12:置颜色(红色):显示(self.x+335,self.y+292,"未输入点卡兑换金额")
			self.普通文字12:置颜色(红色):显示(self.x+335,self.y+262,"未输入点卡兑换金额")
		end





		local xx,yy = 295,320
		self.领取充值["正常"]:显示(self.x+xx,self.y+yy)
		if self.领取充值["正常"]:是否选中(x,y) then
			self.领取充值["选中"]:显示(self.x+xx,self.y+yy)
			if mousea(0) then
				self.领取充值["按下"]:显示(self.x+xx,self.y+yy)
				self.焦点 = true
			elseif mouseb(0) then
				if not 十的倍数(self.输入框:取文本(),self.数据.换月卡比例) then
					tp.常规提示:打开("#Y请正确输入金额，必须是"..self.数据.换月卡比例.."的倍数.")
				return
				end
				发送数据(115,{动作=4,类型="月卡",金额=金额,点卡消耗=self.输入框:取文本()})
			end
		end
		self.普通文字16:置颜色(0xffffffff):显示(self.x+xx+34,self.y+yy+2,"确认兑换")






	end











end

function 内充系统:检查点(x,y)
	if self.资源组 ~= nil and self.资源组[1]:是否选中(x,y)  then
		return true
	end
end

function 内充系统:初始移动(x,y)
	tp.运行时间 = tp.运行时间 + 1
	if not tp.消息栏焦点 then
  		self.窗口时间 = tp.运行时间
 	end
	if not self.焦点 then
		tp.移动窗口 = true
	end
	if self.鼠标 and  not self.焦点 then
		self.xx = x - self.x
		self.yy = y - self.y
	end
end

function 内充系统:开始移动(x,y)
	if self.鼠标 then
		self.x = x - self.xx
		self.y = y - self.yy
	end
end

return 内充系统