--======================================================================--

--======================================================================--
local 系统类_回收系统 = class()
local floor = math.floor
local tp,zts,zt
local format = string.format
local insert = table.insert
	if f函数.文件是否存在([[回收/回收数据.txt]])==false then
		写出文件([[回收/回收数据.txt]],table.tostring({}))
	end

物品回收数据=table.loadstring(读入文件([[回收/回收数据.txt]]))

local 物品回收资源数据={"如意丹","九转金丹","一级宝石","强化石阵法","炼妖石","上古锻造图策","怪物卡片","金银锦盒","魔兽要诀","月华露","金柳露","净瓶玉露","符石卷轴","未激活的符石","彩果","召唤兽内丹","高级召唤兽内丹","超级净瓶玉露","超级金柳露","高级魔兽要诀","特赦令牌","制造指南书","百炼精铁","灵饰指南书","元灵晶石","五宝","修炼果","珍珠","陨铁","环暗器乐器花","元宵钨金","宝图相关","神秘石",
}
if 授权码 =="9xRVCmGmz33nf5dRwnhfYregfrrBz5"  then
	物品回收资源数据={"如意丹","九转金丹","五级以下宝石","强化石阵法","炼妖石","上古锻造图策","怪物卡片","金银锦盒","魔兽要诀","月华露","金柳露","净瓶玉露","符石卷轴","未激活的符石","彩果","召唤兽内丹","高级召唤兽内丹","超级净瓶玉露","超级金柳露","高级魔兽要诀","特赦令牌","制造指南书","百炼精铁","灵饰指南书","元灵晶石","五宝","修炼果","珍珠","陨铁","环暗器乐器花",
}
end


function 系统类_回收系统:初始化(根)
	self.x = 180
	self.y = 170
	self.xx = 0
	self.yy = 0
	self.注释 = " 回 收 系 统"
	self.可视 = false
	self.鼠标 = false
	self.焦点 = false
	self.可移动 = true
	tp = 根
	self.窗口时间 = 0
	zts = tp.字体表.一般字体
	zt = tp.字体表.描边字体
	self.进程=1

	self.控件类 = require("ggeui/加载类")()
	local 总控件 = self.控件类:创建控件('序号控件')
	总控件:置可视(true,true)
	self.输入框 = 总控件:创建输入("卡号输入",0,0,180,14)
	self.输入框:置可视(false,false)
	self.输入框:置限制字数(30)
	self.输入框:屏蔽快捷键(true)
	self.输入框:置光标颜色(-16777216)
	self.输入框:置文字颜色(-16777216)
	--self.自动回收 =false
	-- self.数量框 = 总控件:创建输入("数量输入",0,0,180,14)
	-- self.数量框:置可视(false,false)
	-- self.数量框:置限制字数(10)
	-- self.数量框:置数字模式()
	-- self.数量框:屏蔽快捷键(true)
	-- self.数量框:置光标颜色(-16777216)
	-- self.数量框:置文字颜色(-16777216)


end

function 系统类_回收系统:打开(内容)

	if self.可视 then
		self.可视 = false
		self.输入框:置可视(false,false)
		--self.数量框:置可视(false,false)
		self.资源组 = nil
		return
	else
		insert(tp.窗口_,self)
		local 资源 = tp.资源
		local 按钮 = tp._按钮
		local 自适应 = tp._自适应
		self.资源组 = {
			[1] = 自适应.创建(0,1,403,465,3,9),
			[2] = 按钮.创建(自适应.创建(18,4,16,16,4,3),0,0,4,true,true),--关闭
			[7] = 资源:载入('wzife.wdf',"网易WDF动画",0x2DA9D4EC),
			[8] = 资源:载入('wzife.wdf',"网易WDF动画",0x479E857C),
			[9] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[10] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
		}
		self.资源组[65] = 按钮.创建(自适应.创建(12,4,85,23.,1,3),0,0,4,true,true," 一键回收")
		self.资源组[66] = 按钮.创建(自适应.创建(12,4,80,23,1,3),0,0,4,true,true,"环装出售")
		self.资源组[67] = 按钮.创建(自适应.创建(12,4,80,23.,1,3),0,0,4,true,true,"高级回收")
		self.回收资源组={
			[1] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[2] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[3] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[4] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[5] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[6] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[7] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[8] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[9] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[10] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[11] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[12] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[13] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[14] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[15] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[16] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[17] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[18] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[19] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[20] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[21] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[22] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[23] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[24] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[25] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[26] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[27] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[28] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[29] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[30] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[31] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[32] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[33] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			}

		--self.内容=数据
		tp.运行时间 = tp.运行时间 + 1
		    self.窗口时间 = tp.运行时间
		    self.可视 = true
		    self.输入框:置可视(true,true)
		    self.回收id=内容.回收id
		    self.助战id=内容.助战id
		    回收id=self.回收id
		-- self.数量框:置可视(true,true)
		-- self.数量框:置文本("输入兑换的仙玉数额")
		self.资源组[9]:置打勾框(tp.自动回收)
		self.资源组[10]:置打勾框(tp.助战自动回收)
		self.发送回收数据={}

		if  物品回收数据[self.回收id]==nil then
			物品回收数据[self.回收id]={

		}
			写出文件([[回收/回收数据.txt]],table.tostring(物品回收数据))
		end

		for i=1,33 do
			if 物品回收数据[self.回收id][i]==nil then
				物品回收数据[self.回收id][i] ={}
			end
			self.回收资源组[i]:置打勾框(物品回收数据[self.回收id][i][i])
		end

	end
end



function 系统类_回收系统:显示(dt,x,y)


	self.资源组[2]:更新(x,y)
	self.资源组[9]:更新(x,y)
	self.资源组[10]:更新(x,y)

	for i=1,33 do
		self.回收资源组[i]:更新(x,y)
	end

	-- self.回收资源组[1]:更新(x,y)
	-- self.回收资源组[2]:更新(x,y)
	-- self.回收资源组[3]:更新(x,y)
	-- self.回收资源组[4]:更新(x,y)
	-- self.回收资源组[5]:更新(x,y)
	-- self.回收资源组[6]:更新(x,y)
	-- self.回收资源组[7]:更新(x,y)
	-- self.回收资源组[8]:更新(x,y)
	-- self.回收资源组[9]:更新(x,y)
	-- self.回收资源组[10]:更新(x,y)
	self.焦点 = false
	self.资源组[1]:显示(self.x+70,self.y)
	self.资源组[2]:显示(self.x + 455,self.y+3)
	self.资源组[65]:更新(x,y)
	self.资源组[66]:更新(x,y)
	--self.资源组[67]:更新(x,y)
	self.资源组[65]:显示(self.x + 350,self.y +380+20+30)
	--self.资源组[66]:显示(self.x + 300,self.y +380+20)
	--self.资源组[67]:显示(self.x + 210,self.y +380+20)

	zts:置颜色(黄色):显示(self.x+250,self.y+402+30,"自动回收")
	zts:置颜色(黄色):显示(self.x+115,self.y+402+30,"分角色自动")

	local xx = 0
	local yy = 0
	for i=1,33 do
		self.回收资源组[i]:显示(self.x+80+(xx)*133,self.y+40+yy*30,true,nil,nil,物品回收数据[self.回收id][i][i],2)
		for k,v in pairs(物品回收资源数据) do
			if i == k then
				zt:显示(self.x+110+(xx)*133,self.y+43+yy*30,v)
			end
		end

		xx = xx + 1
			if xx==3 then
			    xx=0
			    yy=yy+1
			end

	end


	引擎.场景.字体表.华康14:置颜色(红色):显示(self.x+220-180+75,self.y+100+50+280-80+30,"出售道具栏特定物品，该功能出售的物品无法恢复！")
	引擎.场景.字体表.华康14:置颜色(红色):显示(self.x+220-180+110,self.y+100+50+280-55+30,"勾选上方所需回收的物品后再进行操作！")
	引擎.场景.字体表.标题字体:显示(self.x+self.资源组[1].宽度/2+70,self.y+3,"回收系统")

	for i=1,33 do
		if  self.回收资源组[i]:事件判断() then
			物品回收数据[self.回收id][i][i] = not  self.回收资源组[i].打勾框
			self.回收资源组[i]:置打勾框(物品回收数据[self.回收id][i][i])

			for k,v in pairs(物品回收资源数据) do
				for n,m in pairs(物品回收数据[self.回收id][i]) do
					if  n== k then
						物品回收数据[self.回收id][i].名称=v
					end
				end
			end
			if not 物品回收数据[self.回收id][i][i]  then
				物品回收数据[self.回收id][i].名称=nil
			end
		end
	end

	if self.资源组[65]:事件判断() then
		写出文件([[回收/回收数据.txt]],table.tostring(物品回收数据))
		发送数据(3785.1,{回收数据=物品回收数据[self.回收id]})
	-- elseif self.资源组[66]:事件判断() then
	-- 	发送数据(3785.2)
	-- elseif self.资源组[67]:事件判断() then
	-- 	发送数据(3785.3)
	elseif self.资源组[2]:事件判断() then
		self:打开()
		return


	elseif self.资源组[9]:事件判断() then
		tp.自动回收 =  not self.资源组[9].打勾框
		自动回收开始 = os.time()
		self.资源组[9]:置打勾框(tp.自动回收)

		写出文件([[回收/回收数据.txt]],table.tostring(物品回收数据))
		发送数据(3785.4,{回收数据=物品回收数据[self.回收id]})


	elseif self.资源组[10]:事件判断() then
		tp.助战自动回收 =  not self.资源组[10].打勾框
		--助战自动回收开始 = os.time()
		self.资源组[10]:置打勾框(tp.助战自动回收)

		if  self.助战id[1] then
			助战自动回收开始 = os.time()
		else
			tp.常规提示:打开("#Y/当前没有分角色在线，功能无法生效！")
		end
		--写出文件([[回收/回收数据.txt]],table.tostring(物品回收数据))
		-- 发送数据(3785.4,{回收数据=物品回收数据[self.回收id]})

	end






	if  self.资源组[65]:是否选中(x,y) then
        		--tp.提示:装备回收(x+20,y+440,tp.回收)
        	elseif self.资源组[66]:是否选中(x,y) then
        		tp.提示:自定义(x+10,y+10,"环装出售")
        	elseif self.资源组[67]:是否选中(x,y) then
        		--tp.提示:装备回收(x+20,y+440,tp.高级回收)
        	elseif self.资源组[9]:是否选中(x,y) then
        		tp.提示:自定义(x+10,y+10,"勾选开启自动回收，5分钟一次")
        	elseif self.资源组[10]:是否选中(x,y) then
        		tp.提示:自定义(x+10,y+10,"勾选开启所有助战分角色自动回收,\n回收内容统一为本界面勾选内容，\n5分钟一次\n#R说明：勾选状态下上下线多角色不会中断已有的自动回收状态，新加入需要回收的分角色时请重新打开此回收系统并重新勾选此选项！")
        	end
self.资源组[9]:显示(self.x+220,self.y+399+30,true,nil,nil,tp.自动回收,2) --自动回收
self.资源组[10]:显示(self.x+85,self.y+399+30,true,nil,nil,tp.助战自动回收,2) --助战自动回收



end





function 系统类_回收系统:检查点(x,y)
	if self.资源组 ~= nil and self.资源组[1]:是否选中(x,y)  then
		return true
	end
end

function 系统类_回收系统:初始移动(x,y)
	tp.运行时间 = tp.运行时间 + 1
	if not tp.消息栏焦点 then
  		self.窗口时间 = tp.运行时间
 	end
	if not self.焦点 then
		tp.移动窗口 = true
	end
	if self.鼠标 and  not self.焦点 then
		self.xx = x - self.x
		self.yy = y - self.y
	end
end

function 系统类_回收系统:开始移动(x,y)
	if self.鼠标 then
		self.x = x - self.xx
		self.y = y - self.yy
	end
end

return 系统类_回收系统