-- @Author: 作者QQ381990860
-- @Date:   2023-03-04 23:35:57
-- @Last Modified by:   作者QQ381990860
-- @Last Modified time: 2024-04-03 14:11:26

local 阿斌_助战系统 = class(窗口逻辑)
local insert = table.insert
local remove = table.remove
local keyaz = 引擎.按键按住
local keyax = 引擎.按键按下
local keytq = 引擎.按键弹起
local floor = math.floor
local fc,tp,zts,zts1,zts2,zts3,zts4
local 图像类=require("gge图像类1")
local bwh1=require("gge包围盒")(0,0,185,50)
local bwh2=require("gge包围盒")(0,0,140,34)--291  20
local bwh3=require("gge包围盒")(0,0,291,20)
local bwh4=require("gge包围盒")(0,0,110,40)
local bwh5=require("gge包围盒")(0,0,135,44)
local 基础属性={"气血","魔法","命中","伤害","防御","速度","法伤","法防"}
local 进阶属性={"","名称","体质","魔力","力量","耐力","敏捷","潜能"}
local 人物装备={"头盔","项链","武器","衣服","腰带","鞋子"}
local 人物灵饰={"耳饰","佩饰","戒指","手镯"}
local 人物法宝={"法宝","法宝","法宝","法宝"}
local 人物锦衣={"锦衣","光环","足迹","翅膀"}
local 背包状态={"道具","法宝","锦衣","神器","灵宝"}
local 属性={"体质","魔力","力量","耐力","敏捷"}
local 修炼1={"攻击","防御","法术","法抗"}
local 宝宝1={"气血","魔法","伤害","防御","速度","灵力","忠诚"}
local 宝宝2={"名称","体质","魔力","力量","耐力","敏捷","潜能"}
local 背包类别={
      装备={"头盔","项链","武器","衣服","腰带","鞋子","手镯","佩饰","戒指","耳饰","助战背包"},
      法宝={"法宝","助战背包"},
      锦衣={"锦衣","定制锦衣","助战背包"},
      灵宝={"灵宝","助战背包"},
}
local 头像xx={
             {"飞燕女","英女侠","巫蛮儿","偃无师","逍遥生","剑侠客"},
             {"狐美人","骨精灵","鬼潇潇","杀破狼","虎头怪","巨魔王"},
             {"舞天姬","玄彩娥","桃夭夭","羽灵神","神天兵","龙太子"}
}



function 阿斌_助战系统:刷新助战(数据)
    self.数据={助战=数据}
    local 按钮 = tp._按钮
    local 资源 = tp.资源
    local 自适应 = tp._自适应
    local 小型选项栏=tp._小型选项栏
    self.头像组={}
    -- for k,v in pairs(self.数据.助战) do
    --     self.头像组[k]=按钮(资源:载入(模型数据[v.造型].头像资源,"网易WDF动画",模型数据[v.造型].中头像),0,0,1)
    -- end

     for k,v in pairs(self.数据.助战) do
         local x=引擎.取头像(v.模型)
         self.头像组[k]=按钮(资源:载入(x[7],"网易WDF动画",x[2]),0,0,1)
    end
    self:加载资源(数据)

    --  if #self.数据.助战 >= 1 then
    --   self.助战选中=1
    --   self:置装备数据()
    --   self:置模型()
    --   self.改名输入:置文本(self.数据.助战[self.助战选中].名称)
    -- else
    -- self.助战选中=0
    -- end


end

function 阿斌_助战系统:刷新主角经验(经验)
   self.主角经验 = 经验
end

function 阿斌_助战系统:刷新指定助战(数据)
    self.数据.助战[数据[1]]=数据[2]
    if 数据[3]=="造型" then
    elseif 数据[3]=="刷新技能" then
       self:置技能数据(self.技能选中)
    elseif 数据[3]=="刷新经脉" then
       self:刷新经脉数据()
    elseif 数据[3] then
        tp.提示:写入("#Y/"..数据[3])
    end
    self:置模型()
end

function 阿斌_助战系统:加载资源(数据)
    self.数据={助战=数据}
    local 按钮 = tp._按钮
    local 资源 = tp.资源
    local 自适应 = tp._自适应
    local 小型选项栏=tp._小型选项栏
    local 图像类=require("gge图像类1")
    zts1 = tp.字体表.普通字体
    self.资源组={
                [1]=自适应.创建(0,1,450,330,3,9),
                --[2]=按钮(资源:载入('迭代.wdf',"网易WDF动画",0x00000001),0,0,4,true,true),
                [3]=自适应.创建(2,1,190,270,3,9),
                [4]=按钮.创建(自适应.创建(25,4,19,19,4,3),0,0,4,true,true),--拓展
                [5]=自适应.创建(2,1,120,170,3,9),--选中模型
                [6]=自适应.创建(3,1,100,22,1,3),--数值框
                [7]=按钮.创建(自适应.创建(12,4,72,20,1,3),0,0,4,true,true,"加入队伍"),
                [8]=按钮.创建(自适应.创建(12,4,72,20,1,3),0,0,4,true,true,"师门技能"),
                [9]=按钮.创建(自适应.创建(12,4,72,20,1,3),0,0,4,true,true,"师门技能"),
                [10]=按钮.创建(自适应.创建(12,4,72,20,1,3),0,0,4,true,true,"加入队伍"),
                [11]=按钮.创建(自适应.创建(12,4,72,20,1,3),0,0,4,true,true,"加入门派"),
                [12]=按钮.创建(自适应.创建(12,4,72,20,1,3),0,0,4,true,true,"综合提升"),
                [13]=按钮.创建(自适应.创建(12,4,43,21,1,3),0,0,4,true,true,"洗点"),
                [14]=按钮.创建(自适应.创建(12,4,43,21,1,3),0,0,4,true,true,"改名"),
                [16]=按钮.创建(自适应.创建(25,4,19,19,4,3),0,0,4,true,true),
                [17]=按钮.创建(自适应.创建(25,4,19,19,4,3),0,0,4,true,true),
                [18]=按钮.创建(自适应.创建(25,4,19,19,4,3),0,0,4,true,true),
                [19]=按钮.创建(自适应.创建(25,4,19,19,4,3),0,0,4,true,true),
                [20]=按钮.创建(自适应.创建(25,4,19,19,4,3),0,0,4,true,true),
                [21]=按钮.创建(自适应.创建(26,4,19,19,4,3),0,0,4,true,true),
                [22]=按钮.创建(自适应.创建(26,4,19,19,4,3),0,0,4,true,true),
                [23]=按钮.创建(自适应.创建(26,4,19,19,4,3),0,0,4,true,true),
                [24]=按钮.创建(自适应.创建(26,4,19,19,4,3),0,0,4,true,true),
                [25]=按钮.创建(自适应.创建(26,4,19,19,4,3),0,0,4,true,true),
                [26]=按钮.创建(自适应.创建(12,4,68,21,1,3),0,0,4,true,true,"推荐加点"),
                [27]=按钮.创建(自适应.创建(12,4,43,21,1,3),0,0,4,true,true,"属性"),
                [28]=按钮.创建(自适应.创建(12,4,43,21,1,3),0,0,4,true,true,"升级"),
                [29]=按钮.创建(自适应.创建(12,4,68,21,1,3),0,0,4,true,true,"确认加点"),
                [30]=资源:载入('aaa.wdf',"网易WDF动画",0x1F73D092).精灵,
                [31]=资源:载入('aaa.wdf',"网易WDF动画",0x1F73D092).精灵,
                [32]=资源:载入('aaa.wdf',"网易WDF动画",0x1F73D092).精灵,
                [33]=资源:载入('aaa.wdf',"网易WDF动画",0x1F73D092).精灵,
                [34]=资源:载入('aaa.wdf',"网易WDF动画",0x1F73D092).精灵,
                [35]=资源:载入('aaa.wdf',"网易WDF动画",0x1F73D092).精灵,
                [36]=资源:载入('aaa.wdf',"网易WDF动画",0x1F73D092).精灵,
                [37]=资源:载入('aaa.wdf',"网易WDF动画",0x1F73D092).精灵,
                [38]=资源:载入('aaa.wdf',"网易WDF动画",0x1F73D092).精灵,
                [39]=资源:载入('aaa.wdf',"网易WDF动画",0x1F73D092).精灵,
                [40]=资源:载入('aaa.wdf',"网易WDF动画",0x1F73D092).精灵,
                [41]=资源:载入('aaa.wdf',"网易WDF动画",0x1F73D092).精灵,
                [42]=资源:载入('aaa.wdf',"网易WDF动画",0x1F73D092).精灵,
                [43]=资源:载入('aaa.wdf',"网易WDF动画",0x1F73D092).精灵,     --资源:载入('wzife.wdf',"网易WDF动画",0xA393A808),
                [44]=资源:载入("data/pic/道具底图.png", "图片"),
                [45]=按钮.创建(自适应.创建(12,4,55,21,1,3),0,0,4,true,true,"道具"),
                [46]=按钮.创建(自适应.创建(12,4,55,21,1,3),0,0,4,true,true,"法宝"),
                [47]=按钮.创建(自适应.创建(12,4,55,21,1,3),0,0,4,true,true,"锦衣"),
                [48]=按钮.创建(自适应.创建(12,4,55,21,1,3),0,0,4,true,true,"神器"),
                [49]=按钮.创建(自适应.创建(12,4,55,21,1,3),0,0,4,true,true,"灵宝"),
                [50]=按钮.创建(自适应.创建(12,4,55,21,1,3),0,0,4,true,true,"确定"),
                [51]=按钮.创建(自适应.创建(12,4,55,21,1,3),0,0,4,true,true,"取消"),
                [52]=按钮.创建(自适应.创建(12,4,42,21,1,3),0,0,4,true,true,"师门"),
                [53]=按钮.创建(自适应.创建(12,4,42,21,1,3),0,0,4,true,true,"辅助"),
                [54]=按钮.创建(自适应.创建(12,4,42,21,1,3),0,0,4,true,true,"辅助"),
                [55]=按钮.创建(自适应.创建(12,4,42,21,1,3),0,0,4,true,true,"经脉"),
                [56]=按钮.创建(自适应.创建(12,4,42,21,1,3),0,0,4,true,true,"潜能"),
                --[57]=自适应.创建(1003,1,150,285,3,9),
                --[58]=自适应.创建(93,1,150,22,1,3),
                [59]=按钮.创建(自适应.创建(12,4,68,21,1,3),0,0,4,true,true," 学  习"),
                [60]=tp._丰富文本(145,139,tp.字体表.华康字体),
                [61]=按钮.创建(自适应.创建(12,4,78,20,1,3),0,0,4,true,true,"设为当前"),
                [62]=自适应.创建(2,1,300,130,3,9),
                [63]=按钮.创建(自适应.创建(12,4,70,21,1,3),0,0,4,true,true,"提升"),
                [64]=按钮.创建(自适应.创建(12,4,70,21,1,3),0,0,4,true,true,"提升10次"),
                [65]=按钮.创建(自适应.创建(12,4,70,21,1,3),0,0,4,true,true,"提升"),
                [66]=按钮.创建(自适应.创建(12,4,70,21,1,3),0,0,4,true,true,"提升10次"),
                [67]=自适应.创建(2,1,310,200,3,9),
                [68]=按钮.创建(自适应.创建(12,4,70,21,1,3),0,0,4,true,true,"兑  换"),
                [69]=自适应.创建(2,1,240,400,3,9),
                [70]=按钮.创建(自适应.创建(12,4,70,21,1,3),0,0,4,true,true,"确定加点"),
                [71]=按钮.创建(自适应.创建(12,4,70,21,1,3),0,0,4,true,true,"取消加点"),
                [72]=按钮.创建(自适应.创建(12,4,70,21,1,3),0,0,4,true,true,"重置经脉"),
                [73]=资源:载入('JM.dll',"网易WDF动画",0x22D22D6D),
                [74]=自适应.创建(2,1,110,300,3,9),
                [75]=自适应.创建(2,1,200,300,3,9),
                [76]=按钮.创建(自适应.创建(12,4,70,21,1,3),0,0,4,true,true,"骑  乘"),
                [77]=资源:载入('wzife.wdf',"网易WDF动画",0xA393A808),
                --[78]=自适应.创建(1004,1,150,220,3,9),
                [79]=自适应.创建(2,1,170,190,3,9),
                --[80]=tp._滑块(tp._自适应(1005,4,8,40,2,3,nil),1,7,201,2),
                [81]=按钮.创建(自适应.创建(12,4,70,21,1,3),0,0,4,true,true,"参  战"),
                [82]=资源:载入('wzife.wdf',"网易WDF动画",0xA393A808),
                [83]=资源:载入('wzife.wdf',"网易WDF动画",0xA393A808),
                [84]=资源:载入('wzife.wdf',"网易WDF动画",0xA393A808),
                [85]=按钮.创建(自适应.创建(12,4,72,20,1,3),0,0,4,true,true,"更多功能"),
                [89]=按钮.创建(自适应.创建(12,4,42,21,1,3),0,0,4,true,true,"强化"),
                [90]=自适应.创建(2,1,310,125,3,9),
                [91]=按钮.创建(自适应.创建(12,4,72,21,1,3),0,0,4,true,true,"元神提升"),
                [92]=按钮.创建(自适应.创建(12,4,72,21,1,3),0,0,4,true,true,"精灵系统"),
                [93]=按钮.创建(自适应.创建(12,4,72,21,1,3),0,0,4,true,true,"龙魂系统"),
                [94]=按钮.创建(自适应.创建(12,4,72,21,1,3),0,0,4,true,true,"渡劫提修"),
                [95]=按钮.创建(资源:载入('JM.dll',"网易WDF动画",0xFF205590),0,0,1,true,true),
                [114]=tp._丰富文本(75,144),
                [120]=按钮.创建(自适应.创建(12,4,72,20,1,3),0,0,4,true,true,"挑战飞升"),
                [121]=按钮.创建(自适应.创建(12,4,72,20,1,3),0,0,4,true,true,"挑战渡劫"),
                [122]=按钮.创建(自适应.创建(12,4,72,20,1,3),0,0,4,true,true,"挑战化圣"),
                [123]=按钮.创建(自适应.创建(12,4,72,20,1,3),0,0,4,true,true,"修炼调整"),
                [127]=资源:载入('ZHS.dll',"网易WDF动画",0x2cc4fe10),
                [128]=资源:载入('ZHS.dll',"网易WDF动画",0xb6c7e183),
                [129]=资源:载入('ZHS.dll',"网易WDF动画",0xd309b2d8),
                --[130]=自适应.创建(97,1,300,240,3,9),

                --[140] = 图像类("lib/picture/sqbj.png"),
                --[141]=自适应.创建(1000,1,198,198,3,9),
                [142]=按钮.创建(自适应.创建(12,4,55,20,1,3),0,0,4,true,true,"更换"),
                [143]=按钮.创建(资源:载入('xtb.dll',"网易WDF动画",100090076),0,0,4,true,true," 修 复 神 器"),
                [144]=自适应.创建(6,1,260,465,3,9),
                [150]=自适应.创建(6,1,315,450,3,9),
                [151]=require("gge文字类")(程序目录.."wdf/font/hkyt_w6.ttf",20,false,true,true):置描边颜色(0xFF000000),
                [152]=tp._丰富文本(165,110,tp.字体表.按钮字体),
                [153]=按钮.创建(自适应.创建(12,4,43,20,1,3),0,0,4,true,true,"突破"),
                [154]=自适应.创建(2,1,230,350,3,9),
                [155]=自适应.创建(2,1,140,140,3,9),
                [156]=自适应.创建(87,1,60,19,1,3),
                [157]=资源:载入('other.dll',"网易WDF动画",0xA1EC0000),
                [158]=资源:载入('JM.dll',"网易WDF动画",0x3906F9F1),
                [159]=按钮.创建(自适应.创建(12,4,50,20,1,3),0,0,4,true,true,"培养"),
                [160]=按钮.创建(自适应.创建(12,4,70,20,1,3),0,0,4,true,true,"开启显示"),
                [161]=自适应.创建(87,1,150,19,1,3),
                [162]=按钮.创建(自适应.创建(12,4,43,21,1,3),0,0,4,true,true,"称谓"),
                [163]=按钮.创建(自适应.创建(12,4,72,21,1,3),0,0,4,true,true,"称号系统"),
                [164]=按钮.创建(自适应.创建(12,4,72,20,1,3),0,0,4,true,true,"助战飞升"),
                [165]=按钮.创建(自适应.创建(12,4,72,20,1,3),0,0,4,true,true,"助战渡劫"),
                [166]=按钮.创建(自适应.创建(12,4,72,20,1,3),0,0,4,true,true,"助战化圣"),
                [167]=按钮.创建(自适应.创建(12,4,72,20,1,3),0,0,4,true,true,"修炼调整"),
                [168]=按钮(tp.资源:载入("JM.dll","网易WDF动画",0xCEC838D7),0,0,1,true,true),
                [169]=按钮(tp.资源:载入("JM.dll","网易WDF动画",0xCEC838D7),0,0,1,true,true),
                [170]=按钮.创建(自适应.创建(12,4,51,21,1,3),0,0,4,true,true,"灵饰"),
                [171]=按钮.创建(自适应.创建(12,4,51,21,1,3),0,0,4,true,true,"法宝"),
                [172]=按钮.创建(自适应.创建(12,4,51,21,1,3),0,0,4,true,true,"锦衣"),
                [173]=按钮.创建(自适应.创建(12,4,72,20,1,3),0,0,4,true,true,"切换角色"),



                [260] = 资源:载入(程序目录.."Data/pic/zhen.png", "图片"),
                [261] = 资源:载入(程序目录.."Data/pic/kun.png", "图片"),
                [262] = 资源:载入(程序目录.."Data/pic/dui.png", "图片"),
                [263] = 资源:载入(程序目录.."Data/pic/gen.png", "图片"),
                [264] = 资源:载入(程序目录.."Data/pic/li.png", "图片"),
                [265] = 资源:载入(程序目录.."Data/pic/zhuan.png", "图片"),
                [266] = 资源:载入(程序目录.."Data/pic/kan.png", "图片"),
                [267] = 资源:载入(程序目录.."Data/pic/qian.png", "图片"),
                -- [268] = 按钮.创建(自适应.创建(14,4,72,20,1,3),0,0,4,true,true,"查看效果"),
                -- [269] = 按钮.创建(自适应.创建(14,4,72,20,1,3),0,0,4,true,true,"提升等级"),
                -- [270] = 按钮.创建(自适应.创建(14,4,72,20,1,3),0,0,4,true,true,"突破等级"),
                -- [271] = 按钮.创建(自适应.创建(14,4,72,20,1,3),0,0,4,true,true,"洗练属性"),
    }
    for n=45,49 do
        self.资源组[n]:置偏移(5,-1)
    end
    self.资源组[56]:置偏移(-2,0)
    self.资源组[59]:置偏移(-2,0)
    self.资源组[60]:清空()
    self.资源组[61]:置偏移(3,0)
    self.资源组[63]:置偏移(14,0)
    self.资源组[65]:置偏移(14,0)
    self.资源组[68]:置偏移(6,0)
    self.资源组[76]:置偏移(6,0)
    self.资源组[81]:置偏移(6,0)
    self.资源组[170]:置偏移(3,0)
    self.资源组[171]:置偏移(3,0)
    self.资源组[172]:置偏移(3,0)
    for n=52,56 do
        self.资源组[n]:置偏移(-1,0)
    end
    self.头像组={}
    self.控件类 = require("ggeui/加载类")()
    local 总控件 = self.控件类:创建控件('改名总控件')
    总控件:置可视(true,true)
    self.改名输入 = 总控件:创建输入("输入",0,0,110,14)
    self.改名输入:置可视(false,false)
    self.改名输入:置光标颜色(-16777216)
    self.改名输入:置文字颜色(-16777216)
    self.改名输入:置限制字数(12)
    for k,v in pairs(self.数据.助战) do
         local x=引擎.取头像(v.模型)
         self.头像组[k]=按钮(资源:载入(x[7],"网易WDF动画",x[2]),0,0,1)
    end
    self.经脉技能树={}
    for n=1,21 do
        self.经脉技能树[n]=tp._技能格子(0,0,n,"奇经八脉")
        self.经脉技能树[n]:置技能({名称="体恤",3})
    end
    self.物品={}
    for n=1,20 do
        self.物品[n]=tp._物品格子(0,0,n,n)
        self.物品[n]:置物品()
    end
    self.经脉流派=""
    if #self.数据.助战 >= 1 then
      self.助战选中=1
      self:置装备数据()
      self:置模型()
      self.改名输入:置文本(self.数据.助战[self.助战选中].名称)
    else
    self.助战选中=0
    end
    self.展示状态="灵饰"

    self.装备坐标 = {x={10,10+51*3+9*3,10,10+51*3+9*3,10,10+51*3+9*3},y={125,125,125+59*1,125+59*1,125+59*2,125+59*2}}
    self.灵饰坐标 = {x={10+60*1,10+60*2,10,10+60*3},y={67,67,67,67}}
    --self.状态,self.子状态="综合提升","师门"
    self.模型头像={}
    for kk,vv in pairs(头像xx) do
        self.模型头像[kk]={}
        for k,v in pairs(vv) do
             local x=引擎.取头像(v)
             self.模型头像[kk][k]=按钮(资源:载入(x[7],"网易WDF动画",x[2]),0,0,1)
             self.模型头像[kk][k].注释=v
             self.模型头像[kk][k]:绑定窗口_(163)
        end
    end
    self.龙魂 = {}
    for i=35,42 do
        self.龙魂[i] = tp._物品格子(0,0,i,"龙魂")
    end
    for i=35,42 do
        self.龙魂[i]:置物品(nil)
        self.龙魂[i].确定=false
    end
    for k,v in pairs(self.资源组) do
        if v.按钮 then
           v:绑定窗口_(163)
        end
    end
end

function 阿斌_助战系统:神器(dt,x,y)
   local Data = self.数据.助战[self.助战选中]
   self.资源组[144]:显示(self.x+370,self.y+30)
   --self.资源组[140]:显示(self.x+400,self.y+35)--加成，技能背景
   zts2:置描边颜色(0xFF000000):置颜色(0xFFFF6666)
   for n=1,table.maxn(self.神器数据.属性) do
       zts2:显示(self.x+390,self.y+80+n*20-20,self.神器数据.属性[n].类型.."+"..self.神器数据.属性[n].数额)
   end
   zts2:置描边颜色(0xFF000000):置颜色(0xFFFFCC00)
   zts2:显示(self.x+540,self.y+80,self.神器数据.技能)
   zts2:置颜色(-1404907):显示(self.x+400,self.y+282,"剩余灵气："..self.神器数据.灵气)
   引擎.画线(self.x+540,self.y+100,self.x+610,self.y+100)
   引擎.画线(self.x+540,self.y+101,self.x+610,self.y+101)
   zts2:置颜色(0xFFFFFFFF)
   --tp.横排花纹背景_:置区域(0,0,18,202)
  -- tp.横排花纹背景_:显示(self.x+285,self.y+78)
  -- tp.横排花纹背景_:置区域(0,0,380,18)
   --tp.横排花纹背景_:显示(self.x+15,self.y+280)
   self.资源组[114]:显示(self.x+538,self.y+110) --技能文本介绍
   --self.资源组[141]:显示(self.x+400,self.y+260)--神器背景
   self.资源组[142]:更新(x,y)
   self.资源组[142]:显示(self.x+635,self.y+150)--更换技能
   self.资源组[143]:更新(x,y)
   self.资源组[143]:显示(self.x+510,self.y+460)--修复神器
   引擎.画线(self.x+380,self.y+255,self.x+620,self.y+255)
   --Picture[self.神器数据.名称]:显示(self.x+404,self.y+264)--神器
   --Picture[self.神器数据.名称]:更新(dt)
   if self.资源组[142]:事件判断() then
      tp.窗口.文本栏:载入("#H/每次消耗1万仙玉，你确定需要更换吗？","洗炼技能",true,nil,nil,self.助战选中)
   elseif self.资源组[143]:事件判断() then
      发送数据2(self.助战选中,39,677,"1")
   end
end



function 阿斌_助战系统:更多功能(dt,x,y)
   local Data = self.数据.助战[self.助战选中]
   local anz={91,92,93,162,163,164,165,166,167}
   for k,v in pairs(anz) do
       self.资源组[v]:更新(x,y)
   end
   self.资源组[162]:显示(self.x+370,self.y+40)
   self.资源组[161]:显示(self.x+420,self.y+40)
   zts2:置颜色(0xFF000000)
   zts2:显示(self.x+425,self.y+42,Data.称谓.当前 or "无")
   self.资源组[163]:显示(self.x+580,self.y+40)

   self.资源组[91]:显示(self.x+370,self.y+70)
   self.资源组[92]:显示(self.x+450,self.y+70)
   self.资源组[93]:显示(self.x+530,self.y+70)

   self.资源组[164]:显示(self.x+370,self.y+100)
   self.资源组[165]:显示(self.x+450,self.y+100)
   self.资源组[166]:显示(self.x+530,self.y+100)
   --self.资源组[167]:显示(self.x+610,self.y+100)
   if self.资源组[91].事件 then
      if Data.门派=="无" then
         tp.提示:写入("#Y/请先加入门派")
      else
         self.状态="元神提升"
      end
   elseif self.资源组[92].事件 then
      self.状态="精灵系统"
   elseif self.资源组[93].事件 then
      发送数据2(self.助战选中,46,677)
   elseif self.资源组[162].事件 then
      发送数据2(self.助战选中,40,677)
   elseif self.资源组[163].事件 then
      发送数据2(self.助战选中,44,677)
    elseif self.资源组[164]:事件判断() then
      self.状态="助战飞升"
   --    发送数据2(self.助战选中,30,677)
   elseif self.资源组[165]:事件判断() then
      self.状态="助战渡劫"
      --发送数据2(self.助战选中,31,677)
   elseif self.资源组[166]:事件判断() then
      self.状态="助战化圣"
    --  发送数据2(self.助战选中,32,677)

   end
end

function 阿斌_助战系统:龙魂系统(dt,x,y)
   local Data = self.数据.助战[self.助战选中]
   local cjx=235
   local cjy=35
   self.资源组[267]:显示(self.x+160+cjx,self.y+30+cjy)--乾
   self.资源组[264]:显示(self.x+300+cjx,self.y+30+cjy)--兑
   self.资源组[262]:显示(self.x+230+cjx,self.y+30+cjy)--离
   self.资源组[261]:显示(self.x+370+cjx,self.y+30+cjy)--坤
   self.资源组[263]:显示(self.x+160+cjx,self.y+290+cjy)--垠
   self.资源组[265]:显示(self.x+300+cjx,self.y+290+cjy)--撰
   self.资源组[266]:显示(self.x+230+cjx,self.y+290+cjy)--坎
   self.资源组[260]:显示(self.x+370+cjx,self.y+290+cjy)--震
   self.资源组[268]:更新(x,y,self.龙魂数据[43]~=0)
   --self.资源组[268]:显示(self.x+260+cjx,self.y+365+cjy)
   self:龙魂背包(dt,x,y)
   for n=1,3 do
       self.资源组[n+268]:更新(x,y)
   end
   --self.资源组[269]:显示(self.x+180+cjx,self.y+400+cjy)
   --self.资源组[270]:显示(self.x+260+cjx,self.y+400+cjy)
   --self.资源组[271]:显示(self.x+340+cjx,self.y+400+cjy)
   if self.资源组[271].事件 and self.龙魂选中 then
      发送数据2(self.助战选中,48,677,"包裹",self.龙魂选中)
   elseif self.资源组[269].事件 and self.龙魂选中 then
      发送数据2(self.助战选中,49,677,"包裹",self.龙魂选中)
   elseif self.资源组[270].事件 and self.龙魂选中 then
      发送数据2(self.助战选中,50,677,"包裹",self.龙魂选中)
   end
   for i=35,42 do
    if i == 35 then
        self.龙魂[i]:置坐标(self.x+160+2+cjx,self.y+30+cjy)
        elseif i == 36 then
        self.龙魂[i]:置坐标(self.x+300+2+cjx,self.y+30+cjy)
        elseif i == 37 then
        self.龙魂[i]:置坐标(self.x+230+2+cjx,self.y+30+cjy)
        elseif i == 38 then
        self.龙魂[i]:置坐标(self.x+370+2+cjx,self.y+30+cjy)
        elseif i == 39 then
        self.龙魂[i]:置坐标(self.x+160+2+cjx,self.y+290+cjy)
        elseif i == 40 then
        self.龙魂[i]:置坐标(self.x+300+2+cjx,self.y+290+cjy)
        elseif i == 41 then
        self.龙魂[i]:置坐标(self.x+230+2+cjx,self.y+290+cjy)
        elseif i == 42 then
        self.龙魂[i]:置坐标(self.x+370+2+cjx,self.y+290+cjy)
    end
    self.龙魂[i]:显示(dt,x,y,self.鼠标,nil,0.7)
    if self.龙魂[i].焦点 then
          tp.提示:道具行囊(x,y,self.龙魂[i].物品)
    end
    if self.龙魂[i].事件 then
       if self.龙魂[i].焦点 and tp.抓取物品 == nil and self.龙魂[i].物品 ~= nil then
          for q=35,42 do
              self.龙魂[q].确定=false
          end
          self.龙魂[i].确定 = true
          self.龙魂选中=i
       end
    elseif self.龙魂[i].右键 and self.龙魂[i].物品 then
       发送数据2(i,47,677,"包裹",self.助战选中)
    end
  end
  if self.资源组[268]:是否选中(x,y) then
     local tzzs=""
        if self.龙魂数据[43]==0 then
           tzzs="#y/需要佩戴8个门派相同/颜色相同方可激活龙魂套"
        elseif self.龙魂数据[43]== "大唐官府" then
           tzzs="#y/大唐官府:#g/横扫千军，后发制人，破釜沉舟提升5%/随机/20%的效果"
        elseif self.龙魂数据[43]== "化生寺" then
           tzzs="#y/化生寺:#g/金刚护体，金刚护法，一苇渡江，推气过宫，救死扶伤提升5%/随机/20%的效果"
        elseif self.龙魂数据[43]== "女儿村" then
           tzzs="#y/女儿村:#g/一笑倾城，似玉生香提升5%/随机/20%的封印"
        elseif self.龙魂数据[43]== "方寸山" then
           tzzs="#y/方寸山:#g/失心符, 失魂符提升5%/随机/20%的效果"
        elseif self.龙魂数据[43]== "天宫" then
           tzzs="#y/天宫:#g/雷霆万钧提升5%/随机/20%的伤害"
        elseif self.龙魂数据[43]== "龙宫" then
           tzzs="#y/龙宫:#g/龙卷雨击，龙腾提升5%/随机/20%的伤害"
        elseif self.龙魂数据[43]== "五庄观" then
           tzzs="#y/五庄观:#g/日月乾坤提升/5%/随机/20%的命中率"
        elseif self.龙魂数据[43]== "普陀山" then
           tzzs="#y/普陀山:#g/普渡众生，灵动九天提升5%/随机/20%效果"
        elseif self.龙魂数据[43]== "魔王寨" then
           tzzs="#y/魔王寨:#g/飞砂走石，三味真火提升5%/随机/20%的伤害"
        elseif self.龙魂数据[43]== "狮驼岭" then
           tzzs="#y/狮驼岭:#g/连环击，狮搏，鹰击提升5%/随机/20%的伤害"
        elseif self.龙魂数据[43]== "盘丝洞" then
           tzzs="#y/盘丝洞:#g/天罗地网提升5%/随机/20%的伤害"
        elseif self.龙魂数据[43]== "阴曹地府" then
           tzzs="#y/阴曹地府:#g/判官令，阎罗令，黄泉之息伤害提升5%/随机/20%的伤害"
        elseif self.龙魂数据[43]== "凌波城" then
           tzzs="#y/凌波城:#g/浪涌，翻江倒海，惊涛怒，断岳势，天崩地裂伤害提升5%/随机/20%的伤害"
        elseif self.龙魂数据[43]== "无底洞" then
           tzzs="#y/无底洞:#g/地涌金莲，明光宝烛，金身舍利提升5%/随机/20%的效果"
        elseif self.龙魂数据[43]== "神木林" then
           tzzs="#y/神木林:#g/落叶萧萧，荆棘舞，冰川怒，尘士刃有5%/随机/20%的伤害"
        elseif self.龙魂数据[43]== "花果山" then
           tzzs="#y/花果山:#g/泼天乱棒，当头一棒，杀威铁棒，神针撼海提升5%/随机/20%的伤害"
        elseif self.龙魂数据[43]== "天机城" then
           tzzs="#y/天机城:#g/匠心畜锐提升5%/随机/20%的效果"
        elseif self.龙魂数据[43]== "女魃墓" then
           tzzs="#y/女魃墓:#g/人物和参战召唤兽进入战斗提升10%/随机/40%临时气血"
        end
        tp.提示:自定义(x,y+40,tzzs)
  end
end

function 阿斌_助战系统:龙魂背包(dt,x,y)
   local cjx=45
   local cjy=35
   self.资源组[44]:显示(self.x+352+cjx,self.y+85+cjy)
   local xx,yy=0,1
   for n=1,20 do
       yy=(xx>=5 and yy+1) or yy
       xx=(xx>=5 and 1) or xx+1
       self.物品[n].屏蔽焦点=self.资源组[29].可视
       self.物品[n]:置坐标(self.x+354+xx*51-51+cjx,self.y+83+yy*51-51+cjy)
       self.物品[n]:显示(dt,x,y,self.鼠标,{"龙魂"})
       if self.物品[n].焦点 and self.物品[n].物品 and not self.物品[n].屏蔽焦点 then
          tp.提示:道具行囊(x,y,self.物品[n].物品)
          if 引擎.鼠标弹起(1) and not self.物品[n].屏蔽 then
             发送数据2(n,45,677,"包裹",self.助战选中)
          end
       end
   end
end
function 阿斌_助战系统:助战飞升(dt,x,y)
  local Data = self.数据.助战[self.助战选中]
   self.资源组[120]:更新(x,y)
   self.资源组[123]:更新(x,y)
   self.资源组[127]:更新(dt)
   --self.资源组[130]:显示(self.x+385,self.y+30)
   self.资源组[127]:显示(self.x+530,self.y+190)
   tp.影子:显示(self.x+530,self.y+200)

   self.资源组[120]:显示(self.x+450,self.y+280)--飞升按钮
   self.资源组[123]:显示(self.x+545,self.y+280)--修炼调整
   zts2:置颜色(0xFF000000)
   zts2:显示(self.x+425,self.y+42,Data.飞升 or "未飞升")
   if self.资源组[120]:事件判断() then
      发送数据2(self.助战选中,30,677)
   elseif self.资源组[123]:事件判断() then
      发送数据2(self.助战选中,35,677)
   end
end
function 阿斌_助战系统:助战渡劫(dt,x,y)
  local Data = self.数据.助战[self.助战选中]
   self.资源组[121]:更新(x,y)
   self.资源组[128]:更新(dt)
   --self.资源组[130]:显示(self.x+385,self.y+30)
   self.资源组[128]:显示(self.x+530,self.y+190)
   tp.影子:显示(self.x+530,self.y+200)
   self.资源组[121]:显示(self.x+450,self.y+280)--渡劫按钮
   if self.资源组[121]:事件判断() then
      发送数据2(self.助战选中,31,677)
   end
end
function 阿斌_助战系统:助战化圣(dt,x,y)
  local Data = self.数据.助战[self.助战选中]
   self.资源组[122]:更新(x,y)
   self.资源组[129]:更新(dt)
   --self.资源组[130]:显示(self.x+385,self.y+30)
   self.资源组[129]:显示(self.x+530,self.y+190)
   tp.影子:显示(self.x+530,self.y+200)
   self.资源组[122]:显示(self.x+450,self.y+280)--化圣按钮
   if self.资源组[122]:事件判断() then
      发送数据2(self.助战选中,32,677)
   end
end
function 阿斌_助战系统:精灵系统(dt,x,y)
   local Data = self.数据.助战[self.助战选中]
   self.资源组[155]:显示(self.x+380,self.y+137)
   local xx={"等 级","气 血","魔 法","命 中","伤 害","防 御","速 度","灵 力"}
   local xxx={"等级","气血","魔法","命中","伤害","防御","速度","灵力"}
   for n=0,7 do
       self.资源组[156]:显示(self.x+571,self.y+137+24*n)
       zts2:置描边颜色(0xFF000000):置颜色(0xFF000000):显示(self.x+579,self.y+139+24*n,Data.精灵[xxx[n+1]])
       zts2:置描边颜色(0xFF000000):置颜色(0xFFFFFFFF):显示(self.x+525,self.y+137+24*n,xx[n+1])
   end
   self.资源组[157]:更新(dt)
   self.资源组[157]:显示(self.x+430,self.y+300)
   tp.经验背景_:置宽高1(115,16)
   tp.经验背景_:显示(self.x+571,self.y+329)
   zts2:置描边颜色(0xFF000000):置颜色(0xFFFFFFFF):显示(self.x+525,self.y+329,"经 验")
   self.资源组[158]:置区域(0,0,math.min(math.floor(Data.精灵.当前经验 / Data.精灵.最大经验 * 115),115),16)
   self.资源组[158]:显示(self.x + 571,self.y + 329)
   local ts = string.format("%d/%d",Data.精灵.当前经验,Data.精灵.最大经验)
   zts2:置颜色(0xFFFFFFFF):置颜色(0xFF000000):显示(self.x + 566+((115 - zts2:取宽度(ts))/2)+8,self.y + 329,ts)
   self.资源组[159]:更新(x,y)
   self.资源组[160]:更新(x,y)
   self.资源组[159]:显示(self.x+425,self.y+295)
   self.资源组[160].按钮文字=(Data.精灵.显示==1 and "关闭显示") or "开启显示"
   self.资源组[160]:显示(self.x+415,self.y+320)
   if self.资源组[159]:事件判断() then
       发送数据2(self.助战选中,250,13,1,1)
   elseif self.资源组[160]:事件判断() then
       发送数据2(self.助战选中,252,13,1,1)
   end
end


function 阿斌_助战系统:元神提升(dt,x,y)
   local Data = self.数据.助战[self.助战选中]
   self.资源组[154]:显示(self.x+416,self.y+80)
   local yx=self:sk(Data.门派)
   Data.元神=Data.元神 or 0
   local yssj=Data.元神 or 0
   if yx then
      self.元神组[Data.门派]:显示(self.x+455,self.y+117)
      self.资源组[152]:清空()
      self.资源组[152]:添加文本("#H/【当前阶段#H/】#R/"..yssj.."#H/阶段")
      self.资源组[152]:显示(self.x+456,self.y+225)
      self.资源组[152]:清空()
      self.资源组[152]:添加文本("#H/【当前效果#H/】"..(self.元神组[Data.门派.."元神"][yssj] or " 暂无效果"))
      self.资源组[152]:显示(self.x+456,self.y+255)
      self.资源组[152]:清空()
      self.资源组[152]:添加文本("#H/【#R突破阶段#H/】#R/"..((yssj==5 and 5) or (yssj+1)).."#H/阶段")
      self.资源组[152]:显示(self.x+456,self.y+305)
      self.资源组[152]:清空()
      self.资源组[152]:添加文本("#H/【#R/突破效果#H/】"..(self.元神组[Data.门派.."元神"][((yssj==5 and 5) or (yssj+1))] or " 暂无效果"))
      self.资源组[152]:显示(self.x+456,self.y+335)
      self.资源组[153]:更新(x,y)
      self.资源组[153]:显示(self.x+512,self.y+400)
      if self.资源组[153]:事件判断() then
         发送数据2(self.助战选中,22,677)
      end
   end
end

function 阿斌_助战系统:参战宠物(dt,x,y)
   local Data=self.数据.助战[self.助战选中]
   --self.资源组[78]:显示(self.x+370,self.y+30)
   self.资源组[79]:显示(self.x+525,self.y+30)
   --self.资源组[80]:显示(self.x+511,self.y+57,x,y,self.鼠标,true)
   if self.资源组[80].接触 then
      self.bb加入=(#self.bb数据>4 and math.min(math.ceil((#self.bb数据-4)*self.资源组[80]:取百分比()),#self.bb数据-4)) or 0
      self.焦点=true
   end
   for n=1,(self.bb数据 and 4) or 0 do
       local bb=n+(self.bb加入 or 0)
       bwh5:置坐标(self.x+370,self.y+60+n*47-47)
       if #self.bb数据>0 and bwh5:检查点(x,y) and self.bb数据[bb] then
          self.焦点 = true
          引擎.画矩形(self.x+370,self.y+58+n*47-47,self.x+508,self.y+100+n*47-47,ARGB(255,201,207,109))
          if 引擎.鼠标弹起(0) then
             self.bb选中=bb
             self:置宝宝模型()
          end
       end
       if self.bb选中==bb and self.bb数据[bb] then
          引擎.画矩形(self.x+370,self.y+58+n*47-47,self.x+508,self.y+100+n*47-47,ARGB(255,108,110,180))
       end
       if self.bb数据[bb] then
          tp.宠物头像背景_:显示(self.x+375,self.y+60+n*47-47)
          self.bb头像[bb]:显示(self.x+378,self.y+63+n*47-47)
          zts:置颜色(0xFF000000):显示(self.x+418,self.y+63+n*47-47,self.bb数据[bb].名称)
          zts:置颜色(0xFF000000):显示(self.x+418,self.y+79+n*47-47,self.bb数据[bb].等级.."级")
          if self.bb数据[bb].参战名称 then
             local 目标="主人"
             for k,v in pairs(self.数据.助战) do
                 if v.名称==self.bb数据[bb].参战名称 then
                    目标=k.."号"
                    break
                 end
             end
             zts:置颜色(0xFFFF0000):显示(self.x+470,self.y+79+n*47-47,目标)
          end
       end
   end
   self.资源组[81].按钮文字="参  战"
   if self.bb选中~=0 and self.bb模型 then
      self.bb模型:更新(dt)
      self.bb模型:显示(self.x+525+(self.资源组[79].宽度/2)-3,self.y+185)
      tp.影子:显示(self.x+525+(self.资源组[79].宽度/2)-3,self.y+185)
      zts1:置颜色(0xFF000000):显示(self.x+530,self.y+40,"参战者:")
      zts1:置颜色(0xFFFF0000):显示(self.x+585,self.y+40,self.bb数据[self.bb选中].参战名称 or "无")
      if self.bb数据[self.bb选中].参战名称 then
         self.资源组[81].按钮文字="休  息"
      end
   end
   self.资源组[81]:更新(x,y)
   self.资源组[81]:显示(self.x+575,self.y+225)
   if self.资源组[81].事件 then
      发送数据2(self.助战选中,15,677,nil,self.bb选中)
   end
   for n=1,3 do
       self.资源组[81+n]:显示(self.x+440+n*60-60,self.y+255)
       if self.bb装备 then
          self.bb装备[n]:置坐标(self.x+439+n*60-60,self.y+251)
          self.bb装备[n]:显示(dt,x,y,self.鼠标)
          if self.bb装备[n].焦点 and self.bb装备[n].物品 then
             tp.提示:道具行囊(x,y,self.bb装备[n].物品)
          end
       end
   end




   self.资源组[6]:置宽高1(100,18)
   local Pet=self.bb数据 and self.bb数据[self.bb选中]
   for k,v in pairs(宝宝1) do
       zts1:置颜色(0xFFFFFFFF):显示(self.x+380,self.y+315+k*25-25,v)
       self.资源组[6]:显示(self.x+415,self.y+313+k*25-25)
       if Pet then
          zts1:置颜色(0xFF000000):显示(self.x+420,self.y+316+k*25-25,v=="气血" and Pet.当前气血 or v=="魔法" and Pet.当前魔法 or Pet[v])
       end
   end
   for k,v in pairs(宝宝2) do
       zts1:置颜色(0xFFFFFFFF):显示(self.x+540,self.y+315+k*25-25,v)
       self.资源组[6]:显示(self.x+575,self.y+313+k*25-25)
       if Pet then
          zts1:置颜色(0xFF000000):显示(self.x+580,self.y+316+k*25-25,Pet[v])
       end
   end
   --self.资源组[74]:显示(self.x+370,self.y+60)
   --self.资源组[75]:显示(self.x+490,self.y+60)
end

function 阿斌_助战系统:置宝宝模型()
   local n = 取模型(self.bb数据[self.bb选中].造型)
   self.bb模型 = tp.资源:载入(n.战斗资源,"网易WDF动画",n.待战)
   self.bb装备={}
   for n=1,3 do
       self.bb装备[n] = tp._物品格子(0,0,n,n)
       self.bb装备[n]:置物品()
       if self.bb数据[self.bb选中].道具数据 and self.bb数据[self.bb选中].道具数据[n] then
          self.bb装备[n]:置物品(self.bb数据[self.bb选中].道具数据[n])
       end
   end
end

function 阿斌_助战系统:加载bb数据(内容)
   self.bb头像={}
   self.bb数据=内容
   self.bb加入=0
   for n=1,table.maxn(self.bb数据) do
       if self.bb数据[n] then
          local lszy = 取模型(self.bb数据[n].造型)
          self.bb头像[n]=tp.资源:载入(lszy.头像资源,"网易WDF动画",lszy.小头像)
       end
   end
   self.资源组[80]=tp._滑块(tp._自适应(1005,4,8,math.floor(190*((4/#self.bb数据)>1 and 1 or 4/#self.bb数据)),2,3,nil),1,7,190,2)
   self.bb选中=内容.选中 or 0
   self.bb模型=nil
   fc=self.bb选中~=0 and self:置宝宝模型()
end

function 阿斌_助战系统:刷新指定助战技能(数据)
     self.数据.助战[数据[1]].师门技能=数据[2]
     self.状态,self.子状态="综合提升","师门"
     self:置技能数据(数据[3])
end
function 阿斌_助战系统:刷新指定助战队伍信息(数据)
     self.数据.助战[数据[1]].状态=数据[2]
end

function 阿斌_助战系统:底图(dt,x,y)
   local Data=self.数据.助战[self.助战选中]
   self.资源组[1]:显示(self.x,self.y)
   --Picture.标题:显示(self.x+self.资源组[1].宽度/2-90,self.y)
   zts1:置颜色(0xFFFFFFFF):显示(self.x+self.资源组[1].宽度/2-27,self.y+2,"多开系统")
   --self.资源组[2]:显示(self.x+690,self.y+3)
   --self.资源组[2]:更新(x,y)

   --local 事件 = (self.资源组[2].事件 and self:打开())
   local xx,yy=1,0
   for n=7,8 do
       yy=yy+1
       if yy==4 then
          xx=xx+1
          yy=1
       end
       self.资源组[n]:更新(x,y)
       --self.资源组[n]:显示(self.x+210+xx*80-80,self.y+190+yy*27-27)
   end
   self.资源组[173]:更新(x,y)
   self.资源组[7]:显示(self.x+250,self.y+190+27-27+110)
   self.资源组[173]:显示(self.x+370,self.y+190+27-27+110)
   --self.资源组[8]:显示(self.x+400+80-80,self.y+190+27-27+115)
   --self.资源组[85]:更新(x,y)
   --self.资源组[85]:显示(self.x+210,self.y+190+27*3)
   --self.资源组[8].按钮文字=not Data and "加入门派" or ""
   if Data then
      self.资源组[7].按钮文字=Data.状态=="入队" and "退出队伍" or "加入队伍"
      --self.资源组[8].按钮文字=Data.门派=="无" and "加入门派" or "退出门派"
      if self.资源组[7].事件 then
         发送数据2(self.助战选中,13,677)
      elseif self.资源组[8].事件 then
         发送数据2(self.助战选中,110,677)
      elseif self.资源组[8].事件 then
         if self.资源组[8].按钮文字=="退出门派" then
            发送数据2(self.助战选中,54,677)
         else
            self.状态,self.子状态="加入门派"
         end
      elseif self.资源组[13].事件 then
        tp.窗口.文本栏:载入("#H/每次重置属性消耗500W银子？","重置属性",true,nil,nil,self.助战选中)
         --发送数据2(self.助战选中,21,677)
      elseif self.资源组[12].事件 then
         self.状态,self.子状态="综合提升","师门"
         self:置技能数据()
      elseif self.资源组[8].事件 then
         发送数据2(self.助战选中,14,677)
         self.状态=self.资源组[8].按钮文字
      elseif self.资源组[7].事件 then
         --发送数据2(self.助战选中,14,677)
         --self.状态=self.资源组[7].按钮文字
      elseif self.资源组[8].事件 then
         发送数据2(self.助战选中,14,677)
         self.状态=self.资源组[8].按钮文字
      elseif self.资源组[85].事件 then
         self.状态=self.资源组[85].按钮文字
       elseif self.资源组[173].事件 then
        累计发送次数=-2
        发送数据2(self.数据.助战[self.助战选中].ID,57,677)
      end
   end
   self.资源组[170]:更新(x,y,self.展示状态~="灵饰")
   self.资源组[170]:显示(self.x+10,self.y+30)              --
   self.资源组[171]:更新(x,y,self.展示状态~="法宝")
   self.资源组[171]:显示(self.x+70,self.y+30)
   self.资源组[172]:更新(x,y,self.展示状态~="锦衣")
   self.资源组[172]:显示(self.x+130,self.y+30)
   if self.资源组[170].事件 then
      self.展示状态="灵饰"
   elseif self.资源组[171].事件 then
      self.展示状态="法宝"
   elseif self.资源组[172].事件 then
      self.展示状态="锦衣"
   end
   if self.展示状态=="灵饰" then
      self:展示灵饰(dt,x,y)
   elseif self.展示状态=="法宝" then
      self:展示法宝(dt,x,y)
   elseif self.展示状态=="锦衣" then
      self:展示锦衣(dt,x,y)
   end
   self:助战头像(dt,x,y)
end

function 阿斌_助战系统:展示锦衣(dt,x,y)
  local xx,yy=0,1

  for s=1,6 do
       self.资源组[29+s]:显示(self.x+self.装备坐标.x[s]+1,self.y+self.装备坐标.y[s]-1)
      end
  for s=1,4 do
       self.资源组[29+s]:显示(self.x+self.灵饰坐标.x[s]+1,self.y+self.灵饰坐标.y[s]-1)
      end

   for n=1,10 do
       xx=xx+1
       if xx==7 then
          xx,yy=1,yy+1
       elseif xx==5 and yy==2 then
          xx,yy=1,yy+1
       end
       --zts1:置颜色(n==14 and 0xFFFF6633 or 0xFFFFFFFF):显示(self.x+20+xx*53-53-(n==14 and 20 or 0),self.y+300+yy*76-76,(n<=6 and 人物装备[xx]) or (n<=10 and 人物锦衣[xx]) or (n<=14 and 人物法宝[xx]))
       --self.资源组[29+n]:显示(self.x+12+xx*53-53,self.y+323+yy*76-76)
       if n<=6 and self.装备数据 then
        self.装备数据[n]:置坐标(self.x+self.装备坐标.x[n]+1,self.y+self.装备坐标.y[n]-1,nil,nil,4,-1)
          --self.装备数据[n]:置坐标(self.x+11+xx*53-53,self.y+319+yy*76-76)
          self.装备数据[n]:显示(dt,x,y,self.鼠标)

          if self.装备数据[n].焦点 and self.装备数据[n].物品 then
             tp.提示:道具行囊(x,y,self.装备数据[n].物品)
             if 引擎.鼠标弹起(1) then
                --发送数据2(self.助战选中,12,677,"装备",n)
             end
          end

       elseif n<=10 and self.锦衣数据 then
          n=n-6
          self.锦衣数据[n]:置坐标(self.x + self.灵饰坐标.x[n]+1,self.y + self.灵饰坐标.y[n]-1,nil,nil,4,-1)
          --self.锦衣数据[n]:置坐标(self.x+11+xx*53-53,self.y+319+yy*76-76)
          self.锦衣数据[n]:显示(dt,x,y,self.鼠标)
          if self.锦衣数据[n].焦点 and self.锦衣数据[n].物品 then
             tp.提示:道具行囊(x,y,self.锦衣数据[n].物品)
             if 引擎.鼠标弹起(1) then
               --发送数据2(self.助战选中,12,677,"灵饰",n)
             end
          end
       end
   end
end


function 阿斌_助战系统:展示法宝(dt,x,y)
  local xx,yy=0,1
  for s=1,6 do
       self.资源组[29+s]:显示(self.x+self.装备坐标.x[s]+1,self.y+self.装备坐标.y[s]-1)
      end
  for s=1,4 do
       self.资源组[29+s]:显示(self.x+self.灵饰坐标.x[s]+1,self.y+self.灵饰坐标.y[s]-1)
      end


   for n=1,10 do
       xx=xx+1
       if xx==7 then
          xx,yy=1,yy+1
       elseif xx==5 and yy==2 then
          xx,yy=1,yy+1
       end
       --zts1:置颜色(n==14 and 0xFFFF6633 or 0xFFFFFFFF):显示(self.x+20+xx*53-53-(n==14 and 20 or 0),self.y+300+yy*76-76,(n<=6 and 人物装备[xx]) or (n<=10 and 人物法宝[xx]) or (n<=14 and 人物法宝[xx]))
       --self.资源组[29+n]:显示(self.x+12+xx*53-53,self.y+323+yy*76-76)
       if n<=6 and self.装备数据 then
        self.装备数据[n]:置坐标(self.x+self.装备坐标.x[n]+1,self.y+self.装备坐标.y[n]-1,nil,nil,4,-1)
          --self.装备数据[n]:置坐标(self.x+11+xx*53-53,self.y+319+yy*76-76)
          self.装备数据[n]:显示(dt,x,y,self.鼠标)
          if self.装备数据[n].焦点 and self.装备数据[n].物品 then
             tp.提示:道具行囊(x,y,self.装备数据[n].物品)
             if 引擎.鼠标弹起(1) then
                --发送数据2(self.助战选中,12,677,"装备",n)
             end
          end
       elseif n<=10 and self.法宝数据 then
          n=n-6
          self.法宝数据[n]:置坐标(self.x + self.灵饰坐标.x[n]+1,self.y + self.灵饰坐标.y[n]-1,nil,nil,4,-1)
          --self.法宝数据[n]:置坐标(self.x+11+xx*53-53,self.y+319+yy*76-76)
          self.法宝数据[n]:显示(dt,x,y,self.鼠标)
          if self.法宝数据[n].焦点 and self.法宝数据[n].物品 then
             tp.提示:道具行囊(x,y,self.法宝数据[n].物品)
             if 引擎.鼠标弹起(1) then
               --发送数据2(self.助战选中,12,677,"灵饰",n)
             end
          end
       end
   end
end

function 阿斌_助战系统:展示灵饰(dt,x,y)
  local xx,yy=0,1
  for s=1,6 do
       self.资源组[29+s]:显示(self.x+self.装备坐标.x[s]+1,self.y+self.装备坐标.y[s]-1)
      end
  for s=1,4 do
       self.资源组[29+s]:显示(self.x+self.灵饰坐标.x[s]+1,self.y+self.灵饰坐标.y[s]-1)
      end

   for n=1,10 do
       xx=xx+1
       if xx==7 then
          xx,yy=1,yy+1
       elseif xx==5 and yy==2 then
          xx,yy=1,yy+1
       end
       --zts1:置颜色(n==14 and 0xFFFF6633 or 0xFFFFFFFF):显示(self.x+20+xx*53-53-(n==14 and 20 or 0),self.y+300+yy*76-76,(n<=6 and 人物装备[xx]) or (n<=10 and 人物灵饰[xx]) or (n<=14 and 人物法宝[xx]))
       --self.资源组[29+n]:显示(self.x+12+xx*53-53,self.y+323+yy*76-76)

       if n<=6 and self.装备数据 then
          --self.装备数据[n]:置坐标(self.x+11+xx*53-53,self.y+319+yy*76-76)    --原来
          self.装备数据[n]:置坐标(self.x+self.装备坐标.x[n]+1,self.y+self.装备坐标.y[n]-1,nil,nil,4,-1)
          self.装备数据[n]:显示(dt,x,y,self.鼠标)
          if self.装备数据[n].焦点 and self.装备数据[n].物品 then
             tp.提示:道具行囊(x,y,self.装备数据[n].物品)
             if 引擎.鼠标弹起(1) then
                --发送数据2(self.助战选中,12,677,"装备",n)
             end
          end
       elseif n<=10 and self.灵饰数据 then
          n=n-6
          self.灵饰数据[n]:置坐标(self.x + self.灵饰坐标.x[n]+1,self.y + self.灵饰坐标.y[n]-1,nil,nil,4,-1)
          --self.灵饰数据[n]:置坐标(self.x+11+xx*53-53,self.y+319+yy*76-76)
          self.灵饰数据[n]:显示(dt,x,y,self.鼠标)
          if self.灵饰数据[n].焦点 and self.灵饰数据[n].物品 then
             tp.提示:道具行囊(x,y,self.灵饰数据[n].物品)
             if 引擎.鼠标弹起(1) then
               --发送数据2(self.助战选中,12,677,"灵饰",n)
             end
          end
       end
   end
end

function 阿斌_助战系统:更换模型(dt,x,y)
   zts1:置颜色(0xFFFFFFFF):显示(self.x+500,self.y+30,"人族")
   self.资源组[90]:显示(self.x+375,self.y+50)
   zts1:置颜色(0xFFFFFFFF):显示(self.x+500,self.y+185,"魔族")
   self.资源组[90]:显示(self.x+375,self.y+205)
   zts1:置颜色(0xFFFFFFFF):显示(self.x+500,self.y+340,"人族")
   self.资源组[90]:显示(self.x+375,self.y+360)
   local xx,yy=0,1
   for n=1,6 do
       xx=xx+1
       if xx>=4 then
          xx=1
          yy=yy+1
       end
       tp.人物头像背景_:显示(self.x+420+xx*85-85,self.y+60+yy*55-55)
       self.模型头像[1][n]:更新(x,y)
       self.模型头像[1][n]:显示(self.x+423+xx*85-85,self.y+63+yy*55-55)
       tp.人物头像背景_:显示(self.x+420+xx*85-85,self.y+215+yy*55-55)
       self.模型头像[2][n]:更新(x,y)
       self.模型头像[2][n]:显示(self.x+423+xx*85-85,self.y+218+yy*55-55)
       tp.人物头像背景_:显示(self.x+420+xx*85-85,self.y+370+yy*55-55)
       self.模型头像[3][n]:更新(x,y)
       self.模型头像[3][n]:显示(self.x+423+xx*85-85,self.y+373+yy*55-55)
       if self.模型头像[3][n].事件 then
          tp.窗口.文本栏:载入("#W/确定要将伙伴造型更换为#R/"..self.模型头像[3][n].注释.."#W/吗?","助战更换造型",true,nil,nil,self.模型头像[3][n].注释)
       elseif self.模型头像[2][n].事件 then
          tp.窗口.文本栏:载入("#W/确定要将伙伴造型更换为#R/"..self.模型头像[2][n].注释.."#W/吗?","助战更换造型",true,nil,nil,self.模型头像[2][n].注释)
       elseif self.模型头像[1][n].事件 then
          tp.窗口.文本栏:载入("#W/确定要将伙伴造型更换为#R/"..self.模型头像[1][n].注释.."#W/吗?","助战更换造型",true,nil,nil,self.模型头像[1][n].注释)
       end
   end
end


function 阿斌_助战系统:助战头像(dt,x,y)
   local Data=self.数据.助战[self.助战选中]
   self.资源组[3]:显示(self.x+251,self.y+25)   -- 整体加了330
   for k,v in pairs(self.数据.助战) do
       local cjy=k*52-52
       zts1:置颜色(0xFF000000)
       bwh1:置坐标(self.x+254,self.y+33+cjy)
       if bwh1:检查点(x,y) then
          引擎.画矩形(self.x+254,self.y+32+cjy,self.x+439,self.y+33+cjy+51,0xFFFFFFCC)
          if 引擎.鼠标弹起(0) then
             self.助战选中=k
             self:置装备数据()
             self:置模型()
             self.改名输入:置文本(self.数据.助战[self.助战选中].名称)
          end
       end
       if self.助战选中==k then--某个对象被确认选中
          引擎.画矩形(self.x+254,self.y+32+cjy,self.x+439,self.y+33+cjy+51,0xFF3366FF)
       end
       tp.人物头像背景_:显示(self.x+258,self.y+33+cjy)
       self.头像组[k]:显示(self.x+261,self.y+36+cjy)
       zts1:显示(self.x+314,self.y+38+cjy,v.名称)
       zts1:显示(self.x+314,self.y+63+cjy,v.等级.."级")
       zts1:置颜色(0xFFFF0000):显示(self.x+374,self.y+63+cjy,((not v.门派 or v.门派=="无") and "无门派") or v.门派)
   end
   zts1:置颜色(0xFFFFFFFF):置字间距(0)--置回
   --self.资源组[4]:更新(x,y)
   --self.资源组[4]:显示(self.x+160,self.y+250)
   -- if self.资源组[4].事件 then
   --    发送数据2(1,51,677)
   -- end
   ----------------------------选中对象----------------------------
   self.资源组[5]:显示(self.x+67,self.y+24+100)    --整体左移122   下移70
   if self.助战选中~=0 then
      tp.影子:显示(self.x+67+(self.资源组[5].宽度/2)-3,self.y+155+100)
   end
   if self.足迹 then
      self.足迹:更新(dt)
      self.足迹:显示(self.x+67+(self.资源组[5].宽度/2)-3,self.y+155+100)
   end
   if self.光环 then
      self.光环:更新(dt)
      self.光环:显示(self.x+67+(self.资源组[5].宽度/2)-3,self.y+155+100)
   end
   if self.翅膀 then
      self.翅膀:更新(dt)
      self.翅膀:显示(self.x+67+(self.资源组[5].宽度/2)-3,self.y+155+100+30)
   end
   if self.锦衣111 then
      self.锦衣111:更新(dt)
      self.锦衣111:显示(self.x+67+(self.资源组[5].宽度/2)-3,self.y+155+100)
    else
      if self.人物 then
        self.人物:更新(dt)
       self.人物:显示(self.x+67+(self.资源组[5].宽度/2)-3,self.y+155+100)
      end
   end
   -- if self.人物 then
   --    self.人物:更新(dt)
   --    self.人物:显示(self.x+67+(self.资源组[5].宽度/2)-3,self.y+155+100)
   -- end
   if self.武器 then
      self.武器:更新(dt)
      self.武器:显示(self.x+67+(self.资源组[5].宽度/2)-3,self.y+155+100)
   end
   ----------------------------模型显示----------------------------
end

function 阿斌_助战系统:基础属性(dt,x,y)
   self.资源组[6]:置宽高1(100,18)
   local Data=self.数据.助战[self.助战选中]
   for k,v in pairs(基础属性) do
       local cjy=k*25-25
       zts:置颜色(0xFFFFFFFF)
       zts:显示(self.x+17,self.y+278+cjy,v)
       self.资源组[6]:显示(self.x+57,self.y+276+cjy,v)
       if Data then
          zts:置颜色(0xFF000000)
          if v=="气血" then
             zts:显示(self.x+63,self.y+278+cjy,Data.当前气血)
          elseif v=="魔法" then
             zts:显示(self.x+63,self.y+278+cjy,Data.当前魔法)
          elseif v=="法伤" then
             zts:显示(self.x+63,self.y+278+cjy,Data.灵力)
          elseif v=="法防" then
             zts:显示(self.x+63,self.y+278+cjy,Data.法防)

          else
             zts:显示(self.x+63,self.y+278+cjy,Data[v])
          end
       end
   end
   --------------经验条---------------
   zts:置颜色(0xFFFFFFFF):显示(self.x+17,self.y+479,"经验")
   引擎.画矩形(self.x+58,self.y+478,self.x+235,self.y+494,0xFFB9B1DA)
   if Data then
      local ts=(Data.当前经验 or "null").."/"..Data.最大经验
      zts:置颜色(0xFF000000):显示(self.x + ((177 - zts:取宽度(ts))/2)+58,self.y + 479,ts)
   end
   --------------经验条---------------
   for k,v in pairs(进阶属性) do
       local cjy=k*25-25
       if k~=1 then
          zts:置颜色((k==8 and 0xFFFF6633) or 0xFFFFFFFF):显示(self.x+167,self.y+278+cjy,v)
          self.资源组[6]:置宽高1((k==8 and 40) or 100 , 18):显示(self.x+207,self.y+276+cjy)
          if Data and v~="" then
             if v=="潜能" then
                if Data.临时潜能 and Data.临时潜能~=Data[v] then
                   zts:置颜色(0xFFFF0000):显示(self.x+213,self.y+278+cjy,Data.临时潜能)
                else
                   zts:置颜色(0xFF000000):显示(self.x+213,self.y+278+cjy,Data[v])
                end
             else
                if v~="名称" then
                   zts:置颜色(0xFF000000):显示(self.x+213,self.y+278+cjy,Data[v])
                end
                if Data.临时属性 and Data.临时属性[v] and Data.临时属性[v]>0 then
                   zts:置颜色(0xFFFF0000):显示(self.x+213+zts:取宽度(Data[v])+5,self.y+278+cjy,"+"..Data.临时属性[v])
                end
             end
          end
       end
   end
   local xx,yy=1,0
   for n=16,25 do
       yy=yy+1
       if yy==6 then
          xx=xx+1
          yy=1
       end
       self.资源组[n]:更新(x,y,nil,1)
       self.资源组[n]:显示(self.x+320+xx*22-22,self.y+327+yy*25-25)
       if Data and self.资源组[n].事件 then
          Data.临时属性 = Data.临时属性 or {}
          Data.临时潜能 = Data.临时潜能 or Data.潜能
          local 序列=n-20
          if n<=20 and Data.临时潜能>0 then
             序列=n-15
             Data.临时潜能=Data.临时潜能-1
             Data.临时属性[属性[序列]]=(Data.临时属性[属性[序列]] or 0)+1
          elseif Data.临时属性[属性[序列]] and Data.临时属性[属性[序列]]>0 then
             Data.临时属性[属性[序列]]=Data.临时属性[属性[序列]]-1
             Data.临时潜能=Data.临时潜能+1
          end
       end
       if self.资源组[n]:是否选中(x,y) then
          --tp.提示:自定义(x+65,y+20,"ALT+S打开系统设置,勾选连点模式,然后鼠标左键按下不松,即可快速加点")
       end
   end
   for n=26,29 do
       self.资源组[n]:更新(x,y)
   end
   self.资源组[26]:显示(self.x+250,self.y+450)
   self.资源组[27]:显示(self.x+320,self.y+450)
   self.资源组[28]:显示(self.x+250,self.y+475)
   self.资源组[29]:显示(self.x+295,self.y+475)
   if Data then
      if self.资源组[28].事件 then
         发送数据2(self.助战选中,3,677)
      elseif self.资源组[29].事件 then
         发送数据2(self.助战选中,4,677,table.tostring(Data.临时属性 or {}))
         Data.临时属性,Data.临时潜能=nil,nil
      elseif self.资源组[27].事件 then
         tp.窗口.幻化属性:打开(Data.等级,Data.装备属性,Data)
      elseif self.资源组[26].事件 then
         Data.临时属性={}
         Data.临时潜能=Data.潜能
         if Data.门派=="大唐官府" or Data.门派=="无" or Data.门派=="花果山" or Data.门派=="天宫" or Data.门派=="五庄观" or Data.门派=="凌波城" or Data.门派=="狮驼岭" then
            Data.临时属性.力量=(Data.临时属性.力量 or 0) + Data.临时潜能
         elseif Data.门派=="神木林" or Data.门派=="龙宫" or Data.门派=="女魃墓"or Data.门派=="魔王寨"  then
            Data.临时属性.魔力=(Data.临时属性.魔力 or 0) + Data.临时潜能
         elseif Data.门派=="盘丝洞" or Data.门派=="无底洞" or Data.门派=="方寸山" or Data.门派=="阴曹地府" or Data.门派=="女儿村"  then
            Data.临时属性.敏捷=(Data.临时属性.敏捷 or 0) + Data.临时潜能
         elseif Data.门派=="普陀山" or Data.门派=="无底洞" or Data.门派=="化生寺" or Data.门派=="天机城" then
            Data.临时属性.耐力=(Data.临时属性.耐力 or 0) + Data.临时潜能
         end
         Data.临时潜能=0
      end
   end
   self.控件类:更新(dt,x,y)
   self.控件类:显示(dt,x,y)
   --self.改名输入:置可视(true,true)
   --self.改名输入:置坐标(self.x+215,self.y+194+27*4)
   if self.改名输入._已碰撞 then
      self.焦点 = true
   end
   if self.资源组[14].事件 then
      if #self.改名输入:取文本()<=0 then
         tp.提示:写入("#Y/请先在左边的空栏处填写需要更改的名称")
      else
         --发送数据2(self.助战选中,26,677,self.改名输入:取文本())
      end
   end
end

function 阿斌_助战系统:置模型()
   local Data = self.数据.助战[self.助战选中]
   mx = Data.造型
   if Data.锦衣数据 and (Data.锦衣数据.锦衣 or Data.锦衣数据.定制 or Data.锦衣数据.战斗锦衣) then
      if Data.锦衣数据.定制 then
         mx = Data.锦衣数据.定制.."_"..Data.造型
      elseif Data.锦衣数据.战斗锦衣 then
         mx = Data.锦衣数据.战斗锦衣.."_"..Data.造型
      else
         mx = Data.锦衣数据.锦衣.."_"..Data.造型
      end
   end


   local 临时资源=引擎.取模型(mx)
   self.光环=nil
   self.人物=nil
   self.翅膀=nil
   self.锦衣111=nil
   self.足迹=nil
   self.武器=nil
   self.人物 = tp.资源:载入(临时资源[3],"网易WDF动画",临时资源[1])



   if Data.装备数据 and Data.装备数据.锦衣[3]  ~= nil  then
      local zy = 引擎.取光环(Data.装备数据.锦衣[3].名称)
      self.光环 = tp.资源:载入(zy[4],"网易WDF动画",zy[1])
    end
    if Data.装备数据 and Data.装备数据.锦衣[2]  ~= nil then
      local zy = 引擎.取翅膀(Data.装备数据.锦衣[2].名称)
      self.翅膀 = tp.资源:载入(zy[4],"网易WDF动画",zy[1])
    end
    if Data.装备数据 and Data.装备数据.锦衣[4] ~= nil  then
      local zy = 引擎.取足迹(Data.装备数据.锦衣[4].名称)
      self.足迹 = tp.资源:载入(zy[2],"网易WDF动画",zy[1])
    end
    if Data.装备数据 and Data.装备数据.锦衣[1] ~= nil  then
      local zy = 引擎.取普通锦衣模型(Data.装备数据.锦衣[1].名称,Data.模型)
      self.锦衣111 = tp.资源:载入(zy[3],"网易WDF动画",zy[1])
    end

    if Data.装备数据 and Data.装备数据.装备[3] ~= nil then
        local v = tp:取武器子类(Data.装备数据.装备[3].子类)
        if Data.装备数据.装备[3].名称 == "龙鸣寒水" or Data.装备数据.装备[3].名称 == "非攻" then
          v = "弓弩1"
        end
        local q = 引擎.取模型(Data.模型,v)
        self.人物 = tp.资源:载入(q[3],"网易WDF动画",q[1])
        local m = tp:取武器附加名称(Data.装备数据.装备[3].子类,Data.装备数据.装备[3].级别限制,Data.装备数据.装备[3].名称)
        local x = 引擎.取模型(m.."_"..Data.模型)
        self.武器  = tp.资源:载入(x[3],"网易WDF动画",x[1])
        self.武器 :置差异(self.武器 .帧数-self.人物.帧数)
        --self.人物组[2] = wq
    end


   -- if Data.锦衣数据 and Data.锦衣数据.光环 then
   --    临时资源=引擎.取光环(Data.锦衣数据.光环)
   --    self.光环 = tp.资源:载入(临时资源[4],"网易WDF动画",临时资源[1])
   -- end
   if Data.武器数据 and Data.武器数据.类别 ~= 0 and Data.武器数据.类别 ~= "" and Data.武器数据.类别 and not Data.锦衣数据.定制 then
      local m = tp:qfjmc(Data.武器数据.类别,Data.武器数据.等级,Data.武器数据.名称)
      local n = 取模型(m.."_"..Data.造型)
      self.武器 = tp.资源:载入(n.资源,"网易WDF动画",n.静立)
   end
   self.数据.助战[self.助战选中].临时属性=nil
   self.数据.助战[self.助战选中].临时潜能=nil
end

function 阿斌_助战系统:道具行囊(dt,x,y)
   local Data = self.数据.助战[self.助战选中]
   if self.子状态~="神器" then
      self.资源组[44]:显示(self.x+375,self.y+285)
   end
   self.装备状态=(self.子状态=="道具" and "装备") or
                (self.子状态=="法宝" and "法宝") or
                (self.子状态=="锦衣" and "锦衣") or
                (self.子状态=="灵宝" and "灵宝")

   local xx,yy=0,1
   for n=1,20 do
       xx=xx+1
       if xx>=6 then
          xx=1
          yy=yy+1
       end
       if Data and self.子状态~="神器" then
          self.物品[n]:置坐标(self.x+376+xx*51-51,self.y+284+yy*50-50)
          self.物品[n]:显示(dt,x,y,self.鼠标,背包类别[self.装备状态])
          if self.物品[n].焦点 and self.物品[n].物品 then
             tp.提示:道具行囊(x,y,self.物品[n].物品)
             if 引擎.鼠标弹起(1) and not self.物品[n].屏蔽 then
                if self.装备状态=="装备" and self.物品[n].物品.名称=="武器幻色丹" then
                   发送数据2(self.助战选中,55,677,n)
                elseif self.装备状态=="装备" and self.物品[n].物品.名称=="怪物卡片" then
                   发送数据2(self.助战选中,56,677,n)
                elseif self.装备状态=="装备" and self.物品[n].物品.名称=="月饼" then
                   发送数据2(self.助战选中,36,677,n)
                elseif self.装备状态=="装备" and self.物品[n].物品.名称=="新春限定礼包·称号" then
                   发送数据2(self.助战选中,43,677,n)
                else
                   发送数据2(self.助战选中,11,677,self.装备状态,n)
                end
             end
          end
      end
   end
   for n=1,5 do
       self.资源组[44+n]:更新(x,y)
       self.资源组[44+n]:显示(self.x+635,self.y+305+n*35-35)
       if self.资源组[44+n].事件 then
          self.子状态=(n~=4 and self.资源组[44+n].按钮文字) or self.子状态
          fc = (self.子状态=="道具" and "包裹") or self.子状态
          if n == 4 then
             if self.助战选中==0 then
                tp.提示:写入("#Y/请先选择一名助战角色。")
             else
                发送数据2(self.助战选中,38,677)
             end
          else
             发送数据2(n,53,677,fc)
          end
       end
   end
   fc=self[self.子状态] and self[self.子状态](tp.窗口.阿斌_助战系统,dt,x,y)
end





function 阿斌_助战系统:加入门派(dt,x,y)
   local Data=self.数据.助战[self.助战选中]
   self.资源组[150]:显示(self.x+370,self.y+40)
   local xx,yy=1,0
   if not self.子状态 then
      for n=1,18 do
          yy=yy+1
          if yy>=7 then
             xx=xx+1
             yy=1
          end
          --self.小门派组[n]:显示(self.x+393+xx*107-107,self.y+42+yy*72-70)
          if self.小门派组[n]:是否选中(x,y) and 引擎.鼠标弹起(0) then
             self.子状态=n
          end
      end
         self.资源组[151]:显示(self.x+385,self.y+100,"大唐官府")
         self.资源组[151]:显示(self.x+500,self.y+100,"狮驼岭")
         self.资源组[151]:显示(self.x+610,self.y+100,"龙  宫")

         self.资源组[151]:显示(self.x+385,self.y+170,"方寸山")
         self.资源组[151]:显示(self.x+500,self.y+170,"魔王寨")
         self.资源组[151]:显示(self.x+610,self.y+170,"天  宫")

         self.资源组[151]:显示(self.x+385,self.y+240,"化生寺")
         self.资源组[151]:显示(self.x+500,self.y+240,"地  府")
         self.资源组[151]:显示(self.x+610,self.y+240,"普陀山")

         self.资源组[151]:显示(self.x+385,self.y+315,"女儿村")
         self.资源组[151]:显示(self.x+500,self.y+315,"盘丝洞")
         self.资源组[151]:显示(self.x+610,self.y+315,"五庄观")

         self.资源组[151]:显示(self.x+385,self.y+385,"神木林")
         self.资源组[151]:显示(self.x+500,self.y+385,"无底洞")
         self.资源组[151]:显示(self.x+610,self.y+385,"凌波城")

         self.资源组[151]:显示(self.x+385,self.y+380+80,"天机城")
         self.资源组[151]:显示(self.x+500,self.y+380+80,"女魃墓")
         self.资源组[151]:显示(self.x+610,self.y+380+80,"花果山")
   else
      --self.大门派组[self.子状态]:显示(self.x+453,self.y+170)
      self.资源组[151]:显示(self.x+423,self.y+130,self.门派字[self.子状态])
      self.资源组[50]:更新(x,y)
      self.资源组[51]:更新(x,y)
      self.资源组[50]:显示(self.x+423,self.y+300)
      self.资源组[51]:显示(self.x+563,self.y+300)
      if self.资源组[51].事件 then
         self.子状态=nil
      elseif self.资源组[50].事件 then
         发送数据2(self.助战选中,5,677,self.门派组[self.子状态])
         self.子状态=nil
      end
   end

   --self.资源组[50]:显示(self.x+373,self.y+53)
   --self.资源组[50]:显示(self.x+480,self.y+53)
   --self.资源组[50]:显示(self.x+587,self.y+53)
end

function 阿斌_助战系统:刷新神器数据(数据)
   self.子状态="神器"
   self.神器数据=数据.数据
   self.资源组[114]:清空()
   self.资源组[114]:添加文本(助战神器介绍[self.神器数据.技能])
end

function 阿斌_助战系统:刷新龙魂数据(数据)
   for n=35,42 do
       self.龙魂[n]:置物品()
       self.龙魂[n]:置物品(数据[n])
   end
   self.状态="龙魂系统"
   self.龙魂套=数据.龙魂套
   self.龙魂数据=数据
end

function 阿斌_助战系统:刷新货币(数据)
    self.数据.银子=数据[1]
    self.数据.存银=数据[2]
    self.数据.储备=数据[3]
end

function 阿斌_助战系统:师门(dt,x,y)
   local Data=self.数据.助战[self.助战选中]
   --self.资源组[57]:显示(self.x+375,self.y+80)--163
   --self.资源组[57]:显示(self.x+538,self.y+80)
   zts1:显示(self.x+420,self.y+85,"师门技能")
   zts1:显示(self.x+583,self.y+85,"师门法术")
   zts1:显示(self.x+418,self.y+375,"可用经验")--410
   --self.资源组[58]:显示(self.x+375,self.y+395)--150
   zts1:显示(self.x+418,self.y+425,"可用金钱")
   --self.资源组[58]:显示(self.x+375,self.y+445)

   zts:置颜色(0xFF000000):显示(self.x+380,self.y+398,Data.当前经验)
   zts:置颜色(0xFF000000):显示(self.x+380,self.y+448,self.数据.银子)

   zts1:显示(self.x+581,self.y+375,"需求经验")--410
   --self.资源组[58]:显示(self.x+538,self.y+395)
   zts1:显示(self.x+581,self.y+425,"需求金钱")
   --self.资源组[58]:显示(self.x+538,self.y+445)

   if self.技能需求 then
      zts:置颜色(0xFF000000):显示(self.x+543,self.y+398,self.技能需求.经验)
      zts:置颜色(0xFF000000):显示(self.x+543,self.y+448,self.技能需求.金钱)
   end
   self.资源组[59]:更新(x,y,nil,1)
   self.资源组[59]:显示(self.x+500,self.y+475)
   if self.资源组[59]:是否选中(x,y) then
--      tp.提示:自定义(x+65,y+20,"#pALT+S打开系统设置,勾选连点模式,然后鼠标左键按下不松,即可快速学习")
   end
   if self.技能选中 and self.技能选中~=0 and self.资源组[59].事件 then

      发送数据2(self.助战选中,109,677,table.tostring({序列=self.技能选中}))
      --发送数据(3711,{序列=self.选中})
   end
   for k,v in pairs(self.临时技能 or {}) do
       bwh2:置坐标(self.x+375,self.y+115+k*35-35)
       if bwh2:检查点(x,y) then
          引擎.画矩形(self.x+375,self.y+114+k*35-35,self.x+524,self.y+147+k*35-35,0xFFFFFFCC)
          if 引擎.鼠标弹起(0) then
             self.技能选中=k
             self.技能需求=self:学习技能(v.等级+1)
          end
       end
       if self.技能选中==k then
          引擎.画矩形(self.x+375,self.y+114+k*35-35,self.x+524,self.y+147+k*35-35,0xFF3366FF)
          for i,n in pairs(v.包含技能) do
              if not n.学会 then
                 n.动画:灰度级()
              end
              n.动画:显示(self.x+548,self.y+115+i*35-35)
              if n.动画:是否选中(x,y) then
                 tp.提示:技能(x,y,n)
              end
              zts1:置颜色(0xFF000000):显示(self.x+582,self.y+119+i*35-35,n.名称)
          end
       end
       v.动画:显示(self.x+385,self.y+115+k*35-35)
       -- if v.动画:是否选中(x,y) then
       --   -- tp.提示:技能(x,y,v.技能.技能)
       -- end
       zts:显示(self.x+425,self.y+116+k*35-35,v.名称)
       zts:显示(self.x+425,self.y+131+k*35-35,v.等级.."级")
   end
end
function 阿斌_助战系统:置技能数据(off)
   self.临时技能={}
   if not self.技能加入 or not off then
      self.技能加入=0
   end
   self.技能选中=off or 0
   self.技能需求=nil
   local jn = (self.子状态=="师门" and "师门技能") or (self.子状态=="辅助" and "辅助技能") or "强化技能"
   local Data=self.数据.助战[self.助战选中]
   if jn=="辅助技能" then
      for k,v in pairs(Data[jn]) do
          if v.名称~="强身术" and v.名称~="冥想" and v.名称~="强壮" and v.名称~="暗器技巧" then
             Data[jn][k]=nil
          end
      end
   end
   for k,v in pairs(Data[jn] or {}) do
         local 临时技能=require("script/显示类/技能")()
         临时技能:置对象(v.名称,nil,Data.门派)
         临时技能.包含技能={}
         临时技能.学会=v.学会
         临时技能.等级=v.等级
         临时技能.动画=tp.资源:载入(临时技能.资源,"网易WDF动画",临时技能.小模型资源)
         if self.子状态=="师门" then
             local w = tp.队伍[1]:取包含技能(v.名称,Data.奇经八脉["当前流派"])
             for s=1,#w do
                   临时技能.包含技能[s] = require("script/显示类/技能")()
                   临时技能.包含技能[s]:置对象(w[s],nil,Data.门派)
                   临时技能.包含技能[s].学会=v.包含技能[s].学会
                   临时技能.包含技能[s].等级=v.包含技能[s].等级
                   临时技能.包含技能[s].动画=tp.资源:载入(临时技能.包含技能[s].资源,"网易WDF动画",临时技能.包含技能[s].小模型资源)
             end
         else

         end
         self.临时技能[k]=临时技能



       -- self.临时技能[k] = tp.资源:载入(lsjn.文件,"网易WDF动画",lsjn.小图标)
       -- self.临时技能[k].介绍=lsjn.介绍
       -- self.临时技能[k].名称=v.名称
       -- self.临时技能[k].等级=v.等级
       -- self.临时技能[k].包含技能={}
       -- self.临时技能[k].技能=tp._技能格子(0,0,0,"技能")
       -- self.临时技能[k].技能:置技能({名称=v.名称,lsjn.类型})
       -- for i,n in pairs(v.包含技能 or {}) do
       --     local lsjn = SkillData[n.名称]
       --     self.临时技能[k].包含技能[i]=tp.资源:载入(lsjn.文件,"网易WDF动画",lsjn.小图标)
       --     self.临时技能[k].包含技能[i].名称 = n.名称
       --     self.临时技能[k].包含技能[i].学会 = n.学会
       --     self.临时技能[k].包含技能[i].技能=tp._技能格子(0,0,0,"技能")
       --     self.临时技能[k].包含技能[i].技能:置技能({名称=n.名称,lsjn.类型})
       -- end
   end
   if off then
      self.技能需求 = self:学习技能(self.临时技能[self.技能选中].等级+1)
      self.资源组[60]:清空()
      local js = "#H/【介绍】"..self.临时技能[self.技能选中].介绍.."\n#H/【等级】"..self.临时技能[self.技能选中].等级.."\n#H/【需要帮贡】0"
      self.资源组[60]:添加文本(js)
   end
end
function 阿斌_助战系统:综合提升(dt,x,y)
   local Data=self.数据.助战[self.助战选中]
   if not Data then return end
   for n=1,1 do
          local 序列=51+((n==6 and 5) or (n==5 and 38) or n)
          self.资源组[序列]:更新(x,y)
          self.资源组[序列]:显示(self.x+375+n*55-55,self.y+45)
          if self.资源组[序列].事件 then
             self.上次状态=self.子状态
             self.子状态=self.资源组[序列].按钮文字
             fc=(
                (n==1 or n==2 or n==5) and self:置技能数据()) or
                (n==4 and ((self:sk(self.数据.助战[self.助战选中].门派) and self:刷新经脉数据()) or (tp.提示:写入("#Y/你还没有加入门派") and self:Esc())))
          end
   end
   fc=self[self.子状态] and self[self.子状态](tp.窗口.阿斌_助战系统,dt,x,y)
end

function 阿斌_助战系统:Esc()
   self.子状态=self.上次状态
end

function 阿斌_助战系统:强化(dt,x,y)
   local Data=self.数据.助战[self.助战选中]
   --self.资源组[57]:显示(self.x+375,self.y+80)--163
   --self.资源组[57]:显示(self.x+538,self.y+80)
   zts1:显示(self.x+420,self.y+85,"技能名称")
   zts1:显示(self.x+583,self.y+85,"技能描述")
   zts1:显示(self.x+418,self.y+375,"可用经验")--410
   --self.资源组[58]:显示(self.x+375,self.y+395)--150
   zts1:显示(self.x+418,self.y+425,"可用金钱")
   --self.资源组[58]:显示(self.x+375,self.y+445)
   zts:置颜色(0xFF000000):显示(self.x+380,self.y+398,Data.当前经验)
   zts:置颜色(0xFF000000):显示(self.x+380,self.y+448,self.数据.银子)
   zts1:显示(self.x+581,self.y+375,"需求经验")--410
   --self.资源组[58]:显示(self.x+538,self.y+395)
   zts1:显示(self.x+581,self.y+425,"需求金钱")
   --self.资源组[58]:显示(self.x+538,self.y+445)
   if self.技能需求 then
      zts:置颜色(0xFF000000):显示(self.x+543,self.y+398,self.技能需求.经验)
      zts:置颜色(0xFF000000):显示(self.x+543,self.y+448,self.技能需求.金钱)
   end
   self.资源组[59]:更新(x,y,nil,1)
   self.资源组[59]:显示(self.x+500,self.y+475)
   if self.技能选中 and self.技能选中~=0 and self.资源组[59].事件 then
      发送数据2(self.助战选中,6,677,table.tostring({"强化技能",self.技能选中}))
   end
   if 引擎.取鼠标滚轮()<0 then
      self.技能加入=(self.技能加入<=4 and self.技能加入+1) or self.技能加入
   elseif 引擎.取鼠标滚轮()>0 then
      self.技能加入=(self.技能加入>0 and self.技能加入-1) or self.技能加入
   end
   for n=1,7 do
       local k=n+self.技能加入
       local v=self.临时技能[k]
       bwh2:置坐标(self.x+375,self.y+115+n*35-35)
       if bwh2:检查点(x,y) then
          引擎.画矩形(self.x+375,self.y+114+n*35-35,self.x+524,self.y+147+n*35-35,0xFFFFFFCC)
          if 引擎.鼠标弹起(0) then
             self.技能选中=k
             self.技能需求=self:学习技能(v.等级+1)
             self.资源组[60]:清空()
             local js = "#H/【介绍】"..v.介绍.."\n#H/【等级】"..v.等级.."\n#H/【需要帮贡】0"
             self.资源组[60]:添加文本(js)
          end
       end
       if self.技能选中==k then
          引擎.画矩形(self.x+375,self.y+114+n*35-35,self.x+524,self.y+147+n*35-35,0xFF3366FF)
          self.资源组[60]:显示(self.x+542,self.y+119)
       end
       v:显示(self.x+378,self.y+115+n*35-35)
       if v:是否选中(x,y) then
          tp.提示:技能(x,y,v.技能.技能)
       end
       zts:显示(self.x+425,self.y+116+n*35-35,v.名称)
       zts:显示(self.x+425,self.y+131+n*35-35,v.等级.."/180")
   end
end

function 阿斌_助战系统:辅助(dt,x,y)
   local Data=self.数据.助战[self.助战选中]
   --self.资源组[57]:显示(self.x+375,self.y+80)--163
   --self.资源组[57]:显示(self.x+538,self.y+80)
   zts1:显示(self.x+420,self.y+85,"技能名称")
   zts1:显示(self.x+583,self.y+85,"技能描述")
   zts1:显示(self.x+418,self.y+375,"可用经验")--410
   --self.资源组[58]:显示(self.x+375,self.y+395)--150
   zts1:显示(self.x+418,self.y+425,"可用金钱")
   --self.资源组[58]:显示(self.x+375,self.y+445)
   zts:置颜色(0xFF000000):显示(self.x+380,self.y+398,Data.当前经验)
   zts:置颜色(0xFF000000):显示(self.x+380,self.y+448,self.数据.银子)
   zts1:显示(self.x+581,self.y+375,"需求经验")--410
   --self.资源组[58]:显示(self.x+538,self.y+395)
   zts1:显示(self.x+581,self.y+425,"需求金钱")
   --self.资源组[58]:显示(self.x+538,self.y+445)
   if self.技能需求 then
      zts:置颜色(0xFF000000):显示(self.x+543,self.y+398,self.技能需求.经验)
      zts:置颜色(0xFF000000):显示(self.x+543,self.y+448,self.技能需求.金钱)
   end
   self.资源组[59]:更新(x,y,nil,1)
   self.资源组[59]:显示(self.x+500,self.y+475)
   if self.技能选中 and self.技能选中~=0 and self.资源组[59].事件 then
      发送数据2(self.助战选中,6,677,table.tostring({"辅助技能",self.技能选中}))
   end
   for k,v in pairs(self.临时技能 or {}) do
       bwh2:置坐标(self.x+375,self.y+115+k*35-35)
       if bwh2:检查点(x,y) then
          引擎.画矩形(self.x+375,self.y+114+k*35-35,self.x+524,self.y+147+k*35-35,0xFFFFFFCC)
          if 引擎.鼠标弹起(0) then
             self.技能选中=k
             self.技能需求=self:学习技能(v.等级+1)
             self.资源组[60]:清空()
             local js = "#H/【介绍】"..v.介绍.."\n#H/【等级】"..v.等级.."\n#H/【需要帮贡】0"
             self.资源组[60]:添加文本(js)
          end
       end
       if self.技能选中==k then
          引擎.画矩形(self.x+375,self.y+114+k*35-35,self.x+524,self.y+147+k*35-35,0xFF3366FF)
          self.资源组[60]:显示(self.x+542,self.y+119)
       end
       v.动画:显示(self.x+385,self.y+115+k*35-35)
       -- if v:是否选中(x,y) then
       --    --tp.提示:技能(x,y,v.技能.技能)
       -- end
       zts:显示(self.x+425,self.y+116+k*35-35,v.名称)
       zts:显示(self.x+425,self.y+131+k*35-35,v.等级.."/180")
   end
end

function 阿斌_助战系统:刷新修炼经验(数据)
   for n=1,#数据 do
       for k=1,4 do
           self.数据.助战[n].人物修炼=数据[n].人物修炼
           self.数据.助战[n].召唤兽修炼=数据[n].召唤兽修炼
           self.数据.助战[n].人物修炼[修炼1[k]].修炼经验=数据[n].人物修炼经验[修炼1[k]] or "null"
           self.数据.助战[n].召唤兽修炼[修炼1[k]].修炼经验=数据[n].宝宝修炼经验[修炼1[k]] or "null"
       end
   end
   fc = self.数据.助战[self.助战选中] and self:刷新修炼数据()

end

function 阿斌_助战系统:刷新修炼数据()
   local Data = self.数据.助战[self.助战选中]
   for k,v in pairs(修炼1) do
       if Data.人物修炼.当前==v then
          self.人物修炼选中=k
       end
       if Data.召唤兽修炼.当前==v then
          self.宝宝修炼选中=k
       end
   end
end



function 阿斌_助战系统:修炼(dt,x,y)
   local Data = self.数据.助战[self.助战选中]
   zts1:置颜色(0xFFFFFFFF):显示(self.x+380,self.y+85,"角色:")
   zts1:置颜色(0xFF00FF00):显示(self.x+430,self.y+85,Data.人物修炼.当前)
   zts1:置颜色(0xFFFFFFFF):显示(self.x+490,self.y+85,"宠物:")
   zts1:置颜色(0xFF00FF00):显示(self.x+540,self.y+85,Data.召唤兽修炼.当前)
   self.资源组[61]:更新(x,y)
   self.资源组[61]:显示(self.x+600,self.y+82)

   self.资源组[62]:显示(self.x+380,self.y+115)
   --引擎.画矩形(self.x+385,self.y+128,self.x+676,self.y+148,0xFFFFFFCC)--291   20

   for k,v in pairs(修炼1) do
       bwh3:置坐标(self.x+385,self.y+128+k*30-30)
       if bwh3:检查点(x,y) then
          引擎.画矩形(self.x+385,self.y+128+k*30-30,self.x+676,self.y+148+k*30-30,0xFFFFFFCC)
          if 引擎.鼠标弹起(0) then
             self.人物修炼选中=k
          end
       end
       if self.人物修炼选中==k then
          引擎.画矩形(self.x+385,self.y+128+k*30-30,self.x+676,self.y+148+k*30-30,0xFF3366FF)
       end
       zts1:置颜色(0xFF000000):显示(self.x+395,self.y+130+k*30-30,v.."修炼  等级: "..Data.人物修炼[v].等级.."/"..Data.人物修炼[v].上限.."  经验: "..Data.人物修炼[v].经验.."/"..(Data.人物修炼[v].修炼经验 or "null"))
   end
   zts1:置颜色(0xFFFFFFFF):显示(self.x+390,self.y+265,"角 色 修 炼:")
   self.资源组[63]:更新(x,y)
   self.资源组[64]:更新(x,y)
   self.资源组[63]:显示(self.x+505,self.y+262)
   self.资源组[64]:显示(self.x+605,self.y+262)

   self.资源组[62]:显示(self.x+380,self.y+305)
   for k,v in pairs(修炼1) do
       bwh3:置坐标(self.x+385,self.y+318+k*30-30)
       if bwh3:检查点(x,y) then
          引擎.画矩形(self.x+385,self.y+318+k*30-30,self.x+676,self.y+338+k*30-30,0xFFFFFFCC)
          if 引擎.鼠标弹起(0) then
             self.宝宝修炼选中=k
          end
       end
       if self.宝宝修炼选中==k then
          引擎.画矩形(self.x+385,self.y+318+k*30-30,self.x+676,self.y+338+k*30-30,0xFF3366FF)
       end
       zts1:置颜色(0xFF000000):显示(self.x+395,self.y+320+k*30-30,v.."控制力 等级: "..Data.召唤兽修炼[v].等级.."/"..Data.召唤兽修炼[v].上限.." 经验: "..Data.召唤兽修炼[v].经验.."/"..(Data.召唤兽修炼[v].修炼经验 or "null"))
   end
   zts1:置颜色(0xFFFFFFFF):显示(self.x+390,self.y+455,"宠 物 修 炼:")
   self.资源组[65]:更新(x,y)
   self.资源组[66]:更新(x,y)
   self.资源组[65]:显示(self.x+505,self.y+452)
   self.资源组[66]:显示(self.x+605,self.y+452)
   if self.资源组[63].事件 then
      发送数据2(self.助战选中,8,677,self.人物修炼选中,1)
   elseif self.资源组[64].事件 then
      发送数据2(self.助战选中,8,677,self.人物修炼选中,10)
   elseif self.资源组[65].事件 then
      发送数据2(self.助战选中,9,677,self.宝宝修炼选中,1)
   elseif self.资源组[66].事件 then
      发送数据2(self.助战选中,9,677,self.宝宝修炼选中,10)
   elseif self.资源组[61].事件 then
      发送数据2(self.助战选中,52,677,self.人物修炼选中,self.宝宝修炼选中)
   end
end

function 阿斌_助战系统:潜能(dt,x,y)
   local Data=self.数据.助战[self.助战选中]
   self.资源组[67]:显示(self.x+375,self.y+130)
   zts2:置颜色(0xFF000000)
   zts2:显示(self.x+415,self.y+150,"【69级】可兑换潜能果：50")
   zts2:显示(self.x+415,self.y+195,"【89级】可兑换潜能果：100")
   zts2:显示(self.x+415,self.y+240,"【155级】可兑换潜能果：150")
   zts2:显示(self.x+415,self.y+285,"【168级】可兑换潜能果：200")
   zts1:置颜色(0xFFFFFFFF)
   zts1:显示(self.x+430,self.y+345,"已食用")
   --self.资源组[58]:显示(self.x+377,self.y+365)
   zts1:显示(self.x+588,self.y+345,"可食用")
   --self.资源组[58]:显示(self.x+535,self.y+365)
   zts1:显示(self.x+420,self.y+400,"当前经验")
   --self.资源组[58]:显示(self.x+377,self.y+420)--420
   zts1:显示(self.x+578,self.y+400,"需求经验")
   --self.资源组[58]:显示(self.x+535,self.y+420)
   local k=self.资源组[58].宽度
   zts1:置颜色(0xFF000000)
   zts1:显示(self.x+377+((k-zts1:取宽度(Data.潜能果.潜能果))/2),self.y+368,Data.潜能果.潜能果)
   zts1:显示(self.x+377+((k-zts1:取宽度(Data.当前经验))/2),self.y+423,Data.当前经验)
   zts1:显示(self.x+535+((k-zts1:取宽度(Data.潜能果.潜能果))/2),self.y+368,Data.潜能果.可换潜能果)
   local xq=100000000+Data.潜能果.潜能果*15000000
   zts1:显示(self.x+535+((k-zts1:取宽度(xq))/2),self.y+423,xq)
   self.资源组[68]:更新(x,y)
   self.资源组[68]:显示(self.x+500,self.y+460)
   if self.资源组[68].事件 then
      发送数据2(self.助战选中,10,677)
   end
end

function 阿斌_助战系统:显示(dt,x,y)
   self.焦点=false
   self:底图(dt,x,y)
   --self:基础属性(dt,x,y)
   fc=self[self.状态] and self[self.状态](tp.窗口.阿斌_助战系统,dt,x,y)
end

function 阿斌_助战系统:打开(数据)
    if self.可视 then
        self.可视 = false
        self.资源组=nil
        self.数据={}
    else
        self:加载资源(数据)
        table.insert(tp.窗口_,self)
        tp.运行时间 = tp.运行时间 + 1
        self.窗口时间 = tp.运行时间
        self.可视 = true
    end
    return 1
end

function 阿斌_助战系统:刷新经脉数据()
   local 临时经脉=self:sk(self.数据.助战[self.助战选中].门派)
   self.技能树=self.数据.助战[self.助战选中].奇经八脉.技能树
    for n=1,21 do
        self.经脉技能树[n]:置技能({名称=临时经脉[n],3})
        self.经脉技能树[n].技能树=nil
        self.经脉技能树[n].尝试=nil
        if (type(self.技能树)=="table" and (self.技能树[1]==n or self.技能树[2]==n or self.技能树[3]==n)) or self.经脉技能树[self.技能树] then
           self.经脉技能树[n].技能树=true
        end
    end
    self.经脉选中=0
    return 1
end

function 阿斌_助战系统:初始化(根)
    self.ID = 163
    self.x = 250   --原50
    self.y = 185   --原50
    self.xx = 0
    self.yy = 0
    self.注释 = "阿斌_助战系统"
    self.可视 = false
    self.鼠标 = false
    self.焦点 = false
    self.可移动 = true
    tp=根
    zts = tp.字体表.一般字体
    zts1 = tp.字体表.助战通用字体_
    zts2 = tp.字体表.助战通用字体_
    self.门派组={
         [1]="大唐官府",
         [2]="方寸山",
         [3]="化生寺",
         [4]="女儿村",
         [5]="神木林",
         [6]="天机城",
         [7]="狮驼岭",
         [8]="魔王寨",
         [9]="阴曹地府",
         [10]="盘丝洞",
         [11]="无底洞",
         [12]="女魃墓",
         [13]="龙宫",
         [14]="天宫",
         [15]="普陀山",
         [16]="五庄观",
         [17]="凌波城",
         [18]="花果山",
    }
    -- self.小门派组={
    --      [1]=require("gge图像类1")("Data/save/bj/dt-1.png"),
    --      [2]=require("gge图像类1")("Data/save/bj/fc-1.png"),
    --      [3]=require("gge图像类1")("Data/save/bj/hss-1.png"),
    --      [4]=require("gge图像类1")("Data/save/bj/ne-1.png"),
    --      [5]=require("gge图像类1")("Data/save/bj/sml-1.png"),
    --      [6]=require("gge图像类1")("Data/save/bj/tjc-1.png"),

    --      [7]=require("gge图像类1")("Data/save/bj/stl-1.png"),
    --      [8]=require("gge图像类1")("Data/save/bj/mw-1.png"),
    --      [9]=require("gge图像类1")("Data/save/bj/df-1.png"),
    --      [10]=require("gge图像类1")("Data/save/bj/psd-1.png"),
    --      [11]=require("gge图像类1")("Data/save/bj/wdd-1.png"),
    --      [12]=require("gge图像类1")("Data/save/bj/nbm-1.png"),

    --      [13]=require("gge图像类1")("Data/save/bj/lg-1.png"),
    --      [14]=require("gge图像类1")("Data/save/bj/tg-1.png"),
    --      [15]=require("gge图像类1")("Data/save/bj/pts-1.png"),
    --      [16]=require("gge图像类1")("Data/save/bj/wzg-1.png"),
    --      [17]=require("gge图像类1")("Data/save/bj/lbc-1.png"),
    --      [18]=require("gge图像类1")("Data/save/bj/hgs-1.png"),
    -- }
    -- self.大门派组={
    --      [1]=require("gge图像类1")("Data/save/bj/dt.png"),
    --      [2]=require("gge图像类1")("Data/save/bj/fc.png"),
    --      [3]=require("gge图像类1")("Data/save/bj/hss.png"),
    --      [4]=require("gge图像类1")("Data/save/bj/ne.png"),
    --      [5]=require("gge图像类1")("Data/save/bj/sml.png"),
    --      [6]=require("gge图像类1")("Data/save/bj/tjc.png"),

    --      [7]=require("gge图像类1")("Data/save/bj/stl.png"),
    --      [8]=require("gge图像类1")("Data/save/bj/mw.png"),
    --      [9]=require("gge图像类1")("Data/save/bj/df.png"),
    --      [10]=require("gge图像类1")("Data/save/bj/psd.png"),
    --      [11]=require("gge图像类1")("Data/save/bj/wdd.png"),
    --      [12]=require("gge图像类1")("Data/save/bj/nbm.png"),

    --      [13]=require("gge图像类1")("Data/save/bj/lg.png"),
    --      [14]=require("gge图像类1")("Data/save/bj/tg.png"),
    --      [15]=require("gge图像类1")("Data/save/bj/pts.png"),
    --      [16]=require("gge图像类1")("Data/save/bj/wzg.png"),
    --      [17]=require("gge图像类1")("Data/save/bj/lbc.png"),
    --      [18]=require("gge图像类1")("Data/save/bj/hgs.png"),
    -- }
    self.门派字={"大唐官府","方寸山","化生寺","女儿村","神木林","天机城","狮驼岭","魔王寨","阴曹地府","盘丝洞","无底洞","女魃墓","龙宫","天宫","普陀山","五庄观","凌波城","花果山"}
end
function 阿斌_助战系统:加载坐骑数据(内容)
   self.坐骑数据=内容
   self.坐骑选中=内容.选中 or 0
   self.坐骑模型=nil
   fc=self.坐骑选中~=0 and self:置坐骑模型()
end


function 阿斌_助战系统:置坐骑模型()
   local n = 引擎.坐骑库(self.数据.助战[self.助战选中].造型,self.坐骑数据[self.坐骑选中].类型,self.坐骑数据[self.坐骑选中].饰品 or "空")
   self.坐骑模型 = tp.资源:载入(n.坐骑资源,"网易WDF动画",n.坐骑站立)
   self.坐骑装备 = nil
   if self.坐骑数据[self.坐骑选中].饰品 then
      self.坐骑装备 = tp._物品格子(0,0,n,n)
      self.坐骑装备:置物品(self.坐骑数据[self.坐骑选中].装备)
   end
   self.资源组[168]=tp._按钮(tp.资源:载入("JM.dll","网易WDF动画",0xCEC838D7),0,0,1,true,true)
   self.资源组[169]=tp._按钮(tp.资源:载入("JM.dll","网易WDF动画",0xCEC838D7),0,0,1,true,true)
   local hh = require "Script/模型/召唤兽模型"
   if self.坐骑数据[self.坐骑选中].统御 then
      for k,v in pairs(self.坐骑数据[self.坐骑选中].统御) do
          if tonumber(v.序号)~=0 then
             local n=模型数据[v.模型]
             for j=1,#hh do
                 if hh[j] and hh[j].名称==v.模型 then
                    n=hh[j]
                 end
             end
             self.资源组[167+k]=tp._按钮(tp.资源:载入(n.头像资源,"网易WDF动画",n.小头像),0,0,1,true,true)
          end
      end
   end
end

function 阿斌_助战系统:坐骑骑乘(dt,x,y)
   local Data = self.数据.助战[self.助战选中]
   self.资源组[74]:显示(self.x+370,self.y+60)
   self.资源组[75]:显示(self.x+490,self.y+60)
   zts1:置颜色(0xFFFFFF00):显示(self.x+495,self.y+370,"祥瑞坐骑\n没有饰品")
   self.资源组[77]:显示(self.x+495,self.y+410)



   zts:置颜色(0xFFFFFFFF):显示(self.x+555,self.y+402,"统御的召唤兽")
   tp.宠物头像背景_:显示(self.x+555,self.y+422)
   tp.宠物头像背景_:显示(self.x+600,self.y+422)
   self.资源组[168]:更新(x,y)
   self.资源组[169]:更新(x,y)
   self.资源组[168]:显示(self.x+558,self.y+425)
   self.资源组[169]:显示(self.x+603,self.y+425)
   for k,v in pairs(self.坐骑数据 or {}) do
       if tonumber(k) then
          bwh4:置坐标(self.x+375,self.y+80+k*42-42)
          if bwh4:检查点(x,y) then
             引擎.画矩形(self.x+375,self.y+78+k*42-42,self.x+475,self.y+112+k*42-42,0xFFFFFFCC)
             if 引擎.鼠标弹起(0) then
                self.坐骑选中=k
                self:置坐骑模型()
             end
          end
          if self.坐骑选中==k then
             引擎.画矩形(self.x+375,self.y+78+k*42-42,self.x+475,self.y+112+k*42-42,0xFF3366FF)
          end
          zts1:置颜色(0xFF000000):显示(self.x+380,self.y+80+k*42-42,v.名称)
          zts1:置颜色(0xFF000000):显示(self.x+380,self.y+97+k*42-42,v.等级.."级")
          if v.坐骑者名称 then
             local 目标="主人"
             for t,l in pairs(self.数据.助战) do
                 if v.坐骑者名称==l.名称 then
                    目标=t.."号"
                    break
                 end
             end
             zts1:置颜色(0xFFFF0000):显示(self.x+420,self.y+97+k*42-42,目标)
          end
       end
   end
   self.资源组[76].按钮文字="骑  乘"
   if self.坐骑选中~=0 and self.坐骑模型 then
      self.坐骑模型:更新(dt)
      self.坐骑模型:显示(self.x+600,self.y+300)
      tp.影子:显示(self.x+600,self.y+300)
      zts1:置颜色(0xFF000000):显示(self.x+500,self.y+340,"骑乘者:")
      zts1:置颜色(0xFFFF0000):显示(self.x+555,self.y+340,self.坐骑数据[self.坐骑选中].坐骑者名称 or "无")
      if self.坐骑数据[self.坐骑选中].坐骑者id==Data.id then
         self.资源组[76].按钮文字="下  骑"
      end
      if self.坐骑装备 then
         self.坐骑装备:置坐标(self.x+494,self.y+407)
         self.坐骑装备:显示(dt,x,y,self.鼠标)
         if self.坐骑装备.焦点 and self.坐骑装备.物品 then
            tp.提示:道具行囊(x,y,self.坐骑装备.物品)
         end
      end
   end
   self.资源组[76]:更新(x,y)
   self.资源组[76]:显示(self.x+388,self.y+390)
   if self.资源组[76].事件 then
      if self.坐骑数据[self.坐骑选中] and self.坐骑数据[self.坐骑选中].坐骑者名称 and self.坐骑数据[self.坐骑选中].坐骑者名称~=Data.名称 then
         tp.提示:写入("#Y/当前选中的坐骑已有人骑乘,请换一只坐骑。")
      elseif self.坐骑数据[self.坐骑选中] then
         发送数据2(self.助战选中,16,677,nil,self.坐骑选中)
      end
   elseif self.资源组[168].事件 then
      if self.坐骑选中 == 0 then
         tp.提示:写入("#y/请先选择一只坐骑")
      else
         发送数据2(self.坐骑选中, 1, 581,1)
      end
   elseif self.资源组[169].事件 then
      if self.坐骑选中 == 0 then
         tp.提示:写入("#y/请先选择一只坐骑")
      else
         发送数据2(self.坐骑选中, 2, 581,1)
      end
   end
end


function 阿斌_助战系统:经脉(dt,x,y)
   local Data=self.数据.助战[self.助战选中]
   self.资源组[69]:显示(self.x+375,self.y+80)
   self.资源组[68]:更新(x,y)
   self.资源组[68]:显示(self.x+623,self.y+180)
   zts:置颜色(0xFFFFFFFF):显示(self.x+623,self.y+210,"已使用:")
   zts:置颜色(0xFFFF0000):显示(self.x+670,self.y+210,self.数据.助战[self.助战选中].奇经八脉.乾元丹.."+"..self.数据.助战[self.助战选中].奇经八脉.附加乾元丹)
   zts:置颜色(0xFFFFFFFF):显示(self.x+623,self.y+240,"可使用:")
   zts:置颜色(0xFFFF0000):显示(self.x+670,self.y+240,self.数据.助战[self.助战选中].奇经八脉.剩余乾元丹)
   zts:置颜色(0xFFFFFFFF):显示(self.x+623,self.y+270,"可兑换:")
   zts:置颜色(0xFFFF0000):显示(self.x+670,self.y+270,self.数据.助战[self.助战选中].奇经八脉.可换乾元丹)
   self.资源组[70]:更新(x,y)
   self.资源组[71]:更新(x,y)
   self.资源组[72]:更新(x,y)
   self.资源组[70]:显示(self.x+623,self.y+300)
   self.资源组[71]:显示(self.x+623,self.y+330)
   self.资源组[72]:显示(self.x+623,self.y+360)
   if self.资源组[70].事件 then
      if self.经脉选中==0 then
         tp.提示:写入("#Y/请先选择一个经脉")
      else
         发送数据2(self.助战选中,17,677,nil,self.经脉选中)
      end
   elseif self.资源组[71].事件 and self.经脉选中 and self.经脉选中~=0 then
          self.经脉技能树[self.经脉选中]:置技能({名称=self:sk(self.数据.助战[self.助战选中].门派)[self.经脉选中],3})
          self.经脉技能树[self.经脉选中].尝试=nil
          self.经脉选中=0
          for v=1,21 do
             if (type(self.技能树)=="table" and (self.技能树[1]==v or self.技能树[2]==v or self.技能树[3]==v)) or self.经脉技能树[self.技能树] then
                self.经脉技能树[v].技能树=true
             end
          end
   elseif self.资源组[72].事件 then
      发送数据2(self.助战选中,18,677)
   elseif self.资源组[68].事件 then
      发送数据2(self.助战选中,19,677)
   end
   self.资源组[73]:更新(dt)
   local xx,yy=0,1
   for n=1,21 do
       xx=xx+1
       if xx>=4 then
          xx=1
          yy=yy+1
       end
       self.经脉技能树[n]:置坐标(self.x+410+xx*65-65,self.y+95+yy*55-55)
       self.经脉技能树[n]:显示(x,y,self.鼠标)
       if not self.经脉技能树[n].尝试 and not Data.奇经八脉[self.经脉技能树[n].技能.名称] then
          self.经脉技能树[n].技能.模型:灰度级()
       end
       if self.经脉技能树[n].焦点 then
          tp.提示:技能(x,y,self.经脉技能树[n].技能)
          if 引擎.鼠标弹起(1) and self.经脉选中==n then
             self.经脉技能树[n]:置技能({名称=self:sk(self.数据.助战[self.助战选中].门派)[n],3})
             self.经脉技能树[n].尝试=nil
             self.经脉选中=0
             for v=1,21 do
                if (type(self.技能树)=="table" and (self.技能树[1]==v or self.技能树[2]==v or self.技能树[3]==v)) or self.经脉技能树[self.技能树] then
                   self.经脉技能树[v].技能树=true
                end
             end
          end
       end
       if self.经脉选中==0 and self.经脉技能树[n].技能树 then
          self.资源组[73]:显示(self.x+400+xx*65-65,self.y+85+yy*55-55)
       end
       if self.经脉技能树[n].技能树 and not self.经脉技能树[n].尝试 then
          if self.经脉技能树[n].焦点 and 引擎.鼠标弹起(0) then
             self.经脉技能树[n]:置技能({名称=self:sk(self.数据.助战[self.助战选中].门派)[n],3})
             self.经脉技能树[n].尝试=1
             self.经脉选中=n
             for v=1,21 do
                 self.经脉技能树[v].技能树=nil
             end
          end
       end
   end
end

function 阿斌_助战系统:刷新道具数据(数据)
   if self.状态=="道具行囊" then
      self.子状态=(数据.背包类型=="包裹" and "道具") or 数据.背包类型 or "道具"
   end
   for n=1,20 do
       self.物品[n]:置物品()
       self.物品[n]:置物品(数据[n])
   end
end

function 阿斌_助战系统:刷新装备数据(数据)
   if 数据.编号 then
      self.数据.助战[数据.编号].装备数据=数据
      self:置装备数据()
   else
      for n=1,#数据 do
          self.数据.助战[n].装备数据=数据[n]
      end
   end
end

function 阿斌_助战系统:置装备数据()
   local zbhj={"装备","灵饰","法宝","锦衣","灵宝"}
   local zbsl={6,4,4,6,2}
   local xx=0
   if self.助战选中==0 then return end
   self.数据.助战[self.助战选中].佩戴灵宝=self.数据.助战[self.助战选中].佩戴灵宝 or {}
   for k,v in pairs(zbhj) do
       local vv=v.."数据"
       self[vv]={}
       for n=1,zbsl[k] do
           self[vv][n]=tp._物品格子(0,0,n,n)
           self[vv][n]:置物品()
       end
       for i,n in pairs(self[vv]) do
           xx=xx+1
           self[vv][i]:置物品(self.数据.助战[self.助战选中].装备数据[v][i])
           if v=="灵宝" then
               self[vv][i]:置物品(self.数据.助战[self.助战选中].佩戴灵宝[i])
           end
       end
   end
end


function 阿斌_助战系统:道具(dt,x,y)

end


function 阿斌_助战系统:法宝(dt,x,y)
   local xx,yy=0,1
   for n=1,14 do--30,35
       xx=xx+1
       if xx==7 then
          xx,yy=1,yy+1
       elseif xx==5 and yy==2 then
          xx,yy=1,yy+1
       end
       zts1:置颜色(n==14 and 0xFFFF6633 or 0xFFFFFFFF):显示(self.x+383+xx*53-53-(n==14 and 20 or 0),self.y+30+yy*76-76,(n<=6 and 人物装备[xx]) or (n<=10 and 人物灵饰[xx]) or (n<=14 and 人物法宝[xx]))
       self.资源组[29+n]:显示(self.x+375+xx*53-53,self.y+53+yy*76-76)
       if n<=6 and self.装备数据 then
          self.装备数据[n]:置坐标(self.x+374+xx*53-53,self.y+49+yy*76-76)
          self.装备数据[n]:显示(dt,x,y,self.鼠标)
          if self.装备数据[n].焦点 and self.装备数据[n].物品 then
             tp.提示:道具行囊(x,y,self.装备数据[n].物品)
             if 引擎.鼠标弹起(1) then
                发送数据2(self.助战选中,12,677,"装备",n)
             end
          end
       elseif n<=10 and self.灵饰数据 then
          n=n-6
          self.灵饰数据[n]:置坐标(self.x+374+xx*53-53,self.y+49+yy*76-76)
          self.灵饰数据[n]:显示(dt,x,y,self.鼠标)
          if self.灵饰数据[n].焦点 and self.灵饰数据[n].物品 then
             tp.提示:道具行囊(x,y,self.灵饰数据[n].物品)
             if 引擎.鼠标弹起(1) then
                发送数据2(self.助战选中,12,677,"灵饰",n)
             end
          end
       elseif n<=14 and self.法宝数据 then
          n=n-10
          self.法宝数据[n]:置坐标(self.x+374+xx*53-53,self.y+49+yy*76-76)
          self.法宝数据[n]:显示(dt,x,y,self.鼠标)
          if self.法宝数据[n].焦点 and self.法宝数据[n].物品 then
             tp.提示:道具行囊(x,y,self.法宝数据[n].物品)
             if 引擎.鼠标弹起(1) then
                发送数据2(self.助战选中,12,677,"法宝",n)
             end
          end
       end
   end
end

function 阿斌_助战系统:锦衣(dt,x,y)
   local xx,yy=0,1
   for n=1,16 do--30,35
       xx=xx+1
       if xx==7 then
          xx,yy=1,yy+1
       elseif xx==5 and yy==2 then
          xx,yy=1,yy+1
       end
       local 序列=n
       if n>=13 then
          序列=13
       end
       zts1:置颜色(0xFFFFFFFF):显示(self.x+383+xx*53-53,self.y+30+yy*76-76,(n<=6 and 人物装备[xx]) or (n<=10 and 人物灵饰[xx]) or (n<=16 and 人物锦衣[xx]))
       self.资源组[29+序列]:显示(self.x+375+xx*53-53,self.y+53+yy*76-76)
       if n<=6 and self.装备数据 then
          self.装备数据[n]:置坐标(self.x+374+xx*53-53,self.y+49+yy*76-76)
          self.装备数据[n]:显示(dt,x,y,self.鼠标)
          if self.装备数据[n].焦点 and self.装备数据[n].物品 then
             tp.提示:道具行囊(x,y,self.装备数据[n].物品)
             if 引擎.鼠标弹起(1) then
                发送数据2(self.助战选中,12,677,"装备",n)
             end
          end
       elseif n<=10 and self.灵饰数据 then
          n=n-6
          self.灵饰数据[n]:置坐标(self.x+374+xx*53-53,self.y+49+yy*76-76)
          self.灵饰数据[n]:显示(dt,x,y,self.鼠标)
          if self.灵饰数据[n].焦点 and self.灵饰数据[n].物品 then
             tp.提示:道具行囊(x,y,self.灵饰数据[n].物品)
             if 引擎.鼠标弹起(1) then
                发送数据2(self.助战选中,12,677,"灵饰",n)
             end
          end
       elseif n<=16 and self.锦衣数据 then
          n=n-10
          self.锦衣数据[n]:置坐标(self.x+374+xx*53-53,self.y+49+yy*76-76)
          self.锦衣数据[n]:显示(dt,x,y,self.鼠标)
          if self.锦衣数据[n].焦点 and self.锦衣数据[n].物品 then
             tp.提示:道具行囊(x,y,self.锦衣数据[n].物品)
             if 引擎.鼠标弹起(1) then
                发送数据2(self.助战选中,12,677,"锦衣",n)
             end
          end
       end
   end
end


function 阿斌_助战系统:灵宝(dt,x,y)
   local xx,yy=0,1
   for n=1,12 do--30,35
       xx=xx+1
       if xx==7 then
          xx,yy=1,yy+1
       elseif xx==5 and yy==2 then
          xx,yy=1,yy+1
       end
       zts1:置颜色(n==14 and 0xFFFF6633 or 0xFFFFFFFF):显示(self.x+383+xx*53-53-(n==14 and 20 or 0),self.y+30+yy*76-76,(n<=6 and 人物装备[xx]) or (n<=10 and 人物灵饰[xx]) or (n<=12 and "灵宝"))
       self.资源组[29+n]:显示(self.x+375+xx*53-53,self.y+53+yy*76-76)
       if n<=6 and self.装备数据 then
          self.装备数据[n]:置坐标(self.x+374+xx*53-53,self.y+49+yy*76-76)
          self.装备数据[n]:显示(dt,x,y,self.鼠标)
          if self.装备数据[n].焦点 and self.装备数据[n].物品 then
             tp.提示:道具行囊(x,y,self.装备数据[n].物品)
             if 引擎.鼠标弹起(1) then
                发送数据2(self.助战选中,12,677,"装备",n)
             end
          end
       elseif n<=10 and self.灵饰数据 then
          n=n-6
          self.灵饰数据[n]:置坐标(self.x+374+xx*53-53,self.y+49+yy*76-76)
          self.灵饰数据[n]:显示(dt,x,y,self.鼠标)
          if self.灵饰数据[n].焦点 and self.灵饰数据[n].物品 then
             tp.提示:道具行囊(x,y,self.灵饰数据[n].物品)
             if 引擎.鼠标弹起(1) then
                发送数据2(self.助战选中,12,677,"灵饰",n)
             end
          end
       elseif n<=13 and self.灵宝数据 then
          n=n-10
          self.灵宝数据[n]:置坐标(self.x+374+xx*53-53,self.y+49+yy*76-76)
          self.灵宝数据[n]:显示(dt,x,y,self.鼠标)
          if self.灵宝数据[n].焦点 and self.灵宝数据[n].物品 then
             tp.提示:道具行囊(x,y,self.灵宝数据[n].物品)
             if 引擎.鼠标弹起(1) then
                发送数据2(self.助战选中,12,677,"灵宝",n)
             end
          end
       end
   end
end

function 阿斌_助战系统:sk(mp)
  if mp == "方寸山" then
    return {"苦缠","雷动","奔雷","黄粱","不舍","鬼怮","补缺","不倦","不灭","化身","调息","批亢","斗法","吐纳","飞符炼魂","鬼念","碎甲","摧心","顺势而为","额外能力","钟馗论道"}
  elseif mp == "女儿村" then
    return {"自矜","暗伤","杏花","花舞","倩影","淬芒","傲娇","花护","天香","国色","鸿影","轻霜","机巧","毒引","毒雾","嫣然","磐石","花殇","碎玉弄影","额外能力","鸿渐于陆"}
  elseif mp == "神木林" then
    return {"风灵","法身","咒术","灵压","咒法","蔓延","鞭挞","月影","不侵","风魂","冰锥","血契","滋养","纯净","破杀","追击","灵能","寄生","风卷残云","额外能力","凋零之歌"}
  elseif mp == "化生寺" then
    return {"止戈","销武","佛屠","佛誉","仁心","聚气","佛显","心韧","归气","天照","舍利","感念","慈针","佛性","妙悟","慈心","映法","流刚","渡劫金身","额外能力","诸天看护"}
  elseif mp == "大唐官府" then
    return {"目空","风刃","扶阵","翩鸿一击","勇武","长驱直入","杀意","念心","静岳","干将","勇念","神凝","狂狷","不惊","突进","破空","历战","连破","无敌","额外能力","破军"}
  elseif mp == "阴曹地府" then
    return {"判官","回旋","夜行","入骨","聚魂","六道无量","索魂","伤魂","百炼","黄泉","毒印","百爪狂杀","幽光","泉爆","鬼火","魂飞","汲魂","击破","魑魅缠身","额外能力","夜之王者"}
  elseif mp == "盘丝洞" then
    return {"鼓乐","妖气","怜心","迷瘴","粘附","意乱","绝殇","安抚","忘忧","忘川","迷梦","倾情","情劫","脱壳","迷意","结阵","媚态","利刃","落花成泥","额外能力","偷龙转凤"}
  elseif mp == "无底洞" then
    return {"金莲","追魂","回敬","由己渡人","自愈","陷阱","化莲","风墙","御兽","精进","救人","灵身","持戒","内伤","反先","忍辱","暗潮","噬魂","同舟共济","额外能力","妖风四起"}
  elseif mp == "魔王寨" then
    return {"充沛","炙烤","震怒","蚀天","敛恨","赤暖","火神","震天","神焰","神炎","返火","崩摧","魔冥","燃魂","狂月","威吼","连营","魔心","魔焰滔天","额外能力","烈焰真诀"}
  elseif mp == "狮驼岭" then
    return {"爪印","翼展","驭兽","化血","宁息","兽王","狮吼","怒象","鹰啸","乱破","魔息","威慑狂","狂袭","失心","羁绊","死地","乱击","悲恸","背水一战","额外能力","肝胆相照"}
  elseif mp == "天宫" then
    return {"威吓","疾雷","轰鸣","趁虚","余韵","驭意","震慑","神念","伏魔","苏醒","护佑","电芒","月桂","怒火","灵光","神律","洞察","雷波","画地为牢","额外能力","风雷韵动"}
  elseif mp == "普陀山" then
    return {"化戈","推衍","借灵","莲花心音","生克","默诵","劳心","普渡","化怨","甘露","法印","秘术","灵动","雨润","莲心剑意","玉帛","道衍","法咒","波澜不惊","额外能力","五行制化"}
  elseif mp == "凌波城" then
    return {"山破","战诀","抗击","聚气","神诀","魂聚","神躯","斩魔","不动","力战","破击凌","怒涛","海沸","怒火","煞气","强袭","雷附","再战","天神怒斩","额外能力","真君显灵"}
  elseif mp == "五庄观" then
    return {"体恤","锤炼","神附","心浪","养生","强击","归本","修心","混元","修身","同辉","雨杀","乾坤","意境","傲视","陌宝","心随意动","致命","清风望月","额外能力","天命剑法"}
  elseif mp == "龙宫" then
    return {"波涛","破浪","云霄","逐浪","踏涛","清吟","天龙","龙珠","龙骇","龙慑","傲翔","飞龙","戏珠","回魔","月魂","汹涌","龙魄","摧意","亢龙归海","额外能力","雷浪穿云"}
  elseif mp == "花果山" then
    return {"顽心","逐胜","愈勇","斗志","忘形","贪天","显圣","锤练","火眼","闹天","铁骨","搅海","伏妖","豪胆","压邪","战魄","不坏","荡魔","战神","额外能力","齐天神通"}
  elseif mp == "女魃墓" then
    return {"腐蚀","引魂","咒怨","不共戴天","灵诅","噬魔","异兆","不灭","灵威","余烬","遗世","奴火","凋零","血泉","俱灭","焕然","伤蚀","狂焰","觉醒","额外能力","无相诛戮"}
  elseif mp == "天机城" then
    return {"锐志","精奇","善工","玄机","催锋","奇肱","连枷","断矶","纯青","无痕","巧偃","功云","造化","天匠","穷变","鲸甲","神季","不厌","铜山铁壁","额外能力","所向披靡"}
  elseif mp == "东海渊" then
    return {"荒潮","隐藏","隐藏","灵神","隐藏","隐藏","神谕","隐藏","隐藏","余响","隐藏","隐藏","龟怒","隐藏","隐藏","如醉如狂","隐藏","隐藏","深渊","悔悟","隐藏"}
  end
end
function 阿斌_助战系统:检查点(x,y)
	if self.可视 and self.资源组[1]:是否选中(x,y) then
		return true
	else
		return false
	end
end

function 阿斌_助战系统:初始移动(x,y)
	tp.运行时间 = tp.运行时间 + 1
	if not tp.消息栏焦点 then
		self.窗口时间 = tp.运行时间
	end
	if not self.焦点 then
		tp.移动窗口 = true
	end
	if self.可视 and self.鼠标 and  not self.焦点 then
		self.xx = x - self.x
		self.yy = y - self.y
	end
end

function 阿斌_助战系统:开始移动(x,y)
	if self.可视 and self.鼠标 then
		self.x = x - self.xx
		self.y = y - self.yy
		-- if tp.窗口.锦衣.可视 then
		-- 	 tp.窗口.锦衣.x = x - self.xx+312
		-- 	 tp.窗口.锦衣.y = y - self.yy+206
		-- end
		-- if tp.窗口.灵饰.可视 then
		-- 	tp.窗口.灵饰.x = x - self.xx+312
		-- 	tp.窗口.灵饰.y = y - self.yy+1
		-- end
	end
end

function 阿斌_助战系统:学习技能(目标技能等级)
  if 目标技能等级 == 0 or 目标技能等级>=180 then
    目标技能等级= 1
  end
  local 技能消耗={}
  技能消耗[1]={经验=16,金钱=6}
  技能消耗[2]={经验=32,金钱=12}
  技能消耗[3]={经验=52,金钱=19}
  技能消耗[4]={经验=75,金钱=28}
  技能消耗[5]={经验=103,金钱=38}
  技能消耗[6]={经验=136,金钱=51}
  技能消耗[7]={经验=179,金钱=67}
  技能消耗[8]={经验=231,金钱=86}
  技能消耗[9]={经验=295,金钱=110}
  技能消耗[10]={经验=372,金钱=139}
  技能消耗[11]={经验=466,金钱=174}
  技能消耗[12]={经验=578,金钱=216}
  技能消耗[13]={经验=711,金钱=266}
  技能消耗[14]={经验=867,金钱=325}
  技能消耗[15]={经验=1049,金钱=393}
  技能消耗[16]={经验=1260,金钱=472}
  技能消耗[17]={经验=1503,金钱=563}
  技能消耗[18]={经验=1780,金钱=667}
  技能消耗[19]={经验=2096,金钱=786}
  技能消耗[20]={经验=2452,金钱=919}
  技能消耗[21]={经验=2854,金钱=1070}
  技能消耗[22]={经验=3304,金钱=1238}
  技能消耗[23]={经验=3807,金钱=1426}
  技能消耗[24]={经验=4364,金钱=1636}
  技能消耗[25]={经验=4983,金钱=1868}
  技能消耗[26]={经验=5664,金钱=2124}
  技能消耗[27]={经验=6415,金钱=2404}
  技能消耗[28]={经验=7238,金钱=2714}
  技能消耗[29]={经验=8138,金钱=3050}
  技能消耗[30]={经验=9120,金钱=3420}
  技能消耗[31]={经验=10188,金钱=3820}
  技能消耗[32]={经验=11347,金钱=4255}
  技能消耗[33]={经验=12602,金钱=4725}
  技能消耗[34]={经验=13959,金钱=5234}
  技能消耗[35]={经验=15423,金钱=5783}
  技能消耗[36]={经验=16998,金钱=6374}
  技能消耗[37]={经验=18692,金钱=7009}
  技能消耗[38]={经验=20508,金钱=7690}
  技能消耗[39]={经验=22452,金钱=8419}
  技能消耗[40]={经验=24532,金钱=9199}
  技能消耗[41]={经验=26753,金钱=10032}
  技能消耗[42]={经验=29121,金钱=10920}
  技能消耗[43]={经验=31642,金钱=11865}
  技能消耗[44]={经验=34323,金钱=12871}
  技能消耗[45]={经验=37169,金钱=13938}
  技能消耗[46]={经验=40188,金钱=15070}
  技能消耗[47]={经验=43388,金钱=16270}
  技能消耗[48]={经验=46773,金钱=17540}
  技能消耗[49]={经验=50352,金钱=18882}
  技能消耗[50]={经验=54132,金钱=20299}
  技能消耗[51]={经验=58120,金钱=21795}
  技能消耗[52]={经验=62324,金钱=23371}
  技能消耗[53]={经验=66750,金钱=25031}
  技能消耗[54]={经验=71407,金钱=26777}
  技能消耗[55]={经验=76303,金钱=28613}
  技能消耗[56]={经验=81444,金钱=30541}
  技能消耗[57]={经验=86840,金钱=32565}
  技能消耗[58]={经验=92500,金钱=34687}
  技能消耗[59]={经验=98430,金钱=36911}
  技能消耗[60]={经验=104640,金钱=39240}
  技能消耗[61]={经验=111136,金钱=41676}
  技能消耗[62]={经验=117931,金钱=44224}
  技能消耗[63]={经验=125031,金钱=46886}
  技能消耗[64]={经验=132444,金钱=49666}
  技能消耗[65]={经验=140183,金钱=52568}
  技能消耗[66]={经验=148253,金钱=55595}
  技能消耗[67]={经验=156666,金钱=58749}
  技能消耗[68]={经验=165430,金钱=62036}
  技能消耗[69]={经验=174556,金钱=65458}
  技能消耗[70]={经验=184052,金钱=69019}
  技能消耗[71]={经验=193930,金钱=72723}
  技能消耗[72]={经验=204198,金钱=76574}
  技能消耗[73]={经验=214868,金钱=80575}
  技能消耗[74]={经验=225948,金钱=84730}
  技能消耗[75]={经验=237449,金钱=89043}
  技能消耗[76]={经验=249383,金钱=93518}
  技能消耗[77]={经验=261760,金钱=98160}
  技能消耗[78]={经验=274589,金钱=102971}
  技能消耗[79]={经验=287884,金钱=107956}
  技能消耗[80]={经验=301652,金钱=113119}
  技能消耗[81]={经验=315908,金钱=118465}
  技能消耗[82]={经验=330662,金钱=123998}
  技能消耗[83]={经验=345924,金钱=129721}
  技能消耗[84]={经验=361708,金钱=135640}
  技能消耗[85]={经验=378023,金钱=141758}
  技能消耗[86]={经验=394882,金钱=148080}
  技能消耗[87]={经验=412297,金钱=154611}
  技能消耗[88]={经验=430280,金钱=161355}
  技能消耗[89]={经验=448844,金钱=168316}
  技能消耗[90]={经验=468000,金钱=175500}
  技能消耗[91]={经验=487760,金钱=182910}
  技能消耗[92]={经验=508137,金钱=190551}
  技能消耗[93]={经验=529145,金钱=198429}
  技能消耗[94]={经验=550796,金钱=206548}
  技能消耗[95]={经验=573103,金钱=214913}
  技能消耗[96]={经验=596078,金钱=223529}
  技能消耗[97]={经验=619735,金钱=232400}
  技能消耗[98]={经验=644088,金钱=241533}
  技能消耗[99]={经验=669149,金钱=250931}
  技能消耗[100]={经验=694932,金钱=260599}
  技能消耗[101]={经验=721452,金钱=270544}
  技能消耗[102]={经验=748722,金钱=280770}
  技能消耗[103]={经验=776755,金钱=291283}
  技能消耗[104]={经验=805566,金钱=302087}
  技能消耗[105]={经验=835169,金钱=313188}
  技能消耗[106]={经验=865579,金钱=324592}
  技能消耗[107]={经验=896809,金钱=336303}
  技能消耗[108]={经验=928876,金钱=348328}
  技能消耗[109]={经验=961792,金钱=360672}
  技能消耗[110]={经验=995572,金钱=373339}
  技能消耗[111]={经验=1030234,金钱=386337}
  技能消耗[112]={经验=1065190,金钱=399671}
  技能消耗[113]={经验=1102256,金钱=413346}
  技能消耗[114]={经验=1139649,金钱=427368}
  技能消耗[115]={经验=1177983,金钱=441743}
  技能消耗[116]={经验=1217273,金钱=456477}
  技能消耗[117]={经验=1256104,金钱=471576}
  技能消耗[118]={经验=1298787,金钱=487045}
  技能消耗[119]={经验=1341043,金钱=502891}
  技能消耗[120]={经验=1384320,金钱=519120}
  技能消耗[121]={经验=1428632,金钱=535737}
  技能消耗[122]={经验=1473999,金钱=552749}
  技能消耗[123]={经验=1520435,金钱=570163}
  技能消耗[124]={经验=1567957,金钱=587984}
  技能消耗[125]={经验=1616583,金钱=606218}
  技能消耗[126]={经验=1666328,金钱=624873}
  技能消耗[127]={经验=1717211,金钱=643954}
  技能消耗[128]={经验=1769248,金钱=663468}
  技能消耗[129]={经验=1822456,金钱=683421}
  技能消耗[130]={经验=1876852,金钱=703819}
  技能消耗[131]={经验=1932456,金钱=724671}
  技能消耗[132]={经验=1989284,金钱=745981}
  技能消耗[133]={经验=2047353,金钱=767757}
  技能消耗[134]={经验=2106682,金钱=790005}
  技能消耗[135]={经验=2167289,金钱=812733}
  技能消耗[136]={经验=2229192,金钱=835947}
  技能消耗[137]={经验=2292410,金钱=859653}
  技能消耗[138]={经验=2356960,金钱=883860}
  技能消耗[139]={经验=2422861,金钱=908573}
  技能消耗[140]={经验=2490132,金钱=933799}
  技能消耗[141]={经验=2558792,金钱=959547}
  技能消耗[142]={经验=2628860,金钱=985822}
  技能消耗[143]={经验=2700356,金钱=1012633}
  技能消耗[144]={经验=2773296,金钱=1039986}
  技能消耗[145]={经验=2847703,金钱=1067888}
  技能消耗[146]={经验=2923593,金钱=1096347}
  技能消耗[147]={经验=3000989,金钱=1125371}
  技能消耗[148]={经验=3079908,金钱=1154965}
  技能消耗[149]={经验=3160372,金钱=1185139}
  技能消耗[150]={经验=3242400,金钱=1215900}
  技能消耗[151]={经验=6652022,金钱=2494508}
  技能消耗[152]={经验=6822452,金钱=2558419}
  技能消耗[153]={经验=6996132,金钱=2623549}
  技能消耗[154]={经验=7173104,金钱=2689914}
  技能消耗[155]={经验=7353406,金钱=2757527}
  技能消耗[156]={经验=11305620,金钱=4239607}
  技能消耗[157]={经验=11586254,金钱=4344845}
  技能消耗[158]={经验=11872072,金钱=4452027}
  技能消耗[159]={经验=12163140,金钱=4561177}
  技能消耗[160]={经验=12459518,金钱=4672319}
  技能消耗[161]={经验=15033471,金钱=450041 }
  技能消耗[162]={经验=15315219,金钱=4594563}
  技能消耗[163]={经验=15600468,金钱=4680138}
  技能消耗[164]={经验=15889236,金钱=4766769}
  技能消耗[165]={经验=16181550,金钱=4854465}
  技能消耗[166]={经验=16477425,金钱=4943226}
  技能消耗[167]={经验=16776885,金钱=5033064}
  技能消耗[168]={经验=17079954,金钱=5123985}
  技能消耗[169]={经验=17386650,金钱=5215995}
  技能消耗[170]={经验=17697000,金钱=5309100}
  技能消耗[171]={经验=24014692,金钱=7204407}
  技能消耗[172]={经验=24438308,金钱=7331490}
  技能消耗[173]={经验=24866880,金钱=7460064}
  技能消耗[174]={经验=25300432,金钱=7590129}
  技能消耗[175]={经验=25739000,金钱=7721700}
  技能消耗[176]={经验=32728255,金钱=9818475}
  技能消耗[177]={经验=33289095,金钱=9986727}
  技能消耗[178]={经验=33856310,金钱=10156893}
  技能消耗[179]={经验=34492930,金钱=10328979}
  技能消耗[180]={经验=40842000,金钱=12252600}
  return {经验=技能消耗[目标技能等级].经验,金钱=技能消耗[目标技能等级].金钱}
end

return 阿斌_助战系统