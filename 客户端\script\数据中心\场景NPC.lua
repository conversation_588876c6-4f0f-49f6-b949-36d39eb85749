--======================================================================--
-- @作者: www.wnfj.com，联系QQ：79550111
-- @创建时间:   2018-03-03 02:34:19
-- @Last Modified time: 2024-12-05 17:26:07
--======================================================================--
function 场景取假人表(地图ID,假人ID,是否)
    if 是否~=nil and 是否==1 then
    else
        if 1==1 then return {} end
    end
	local 假人 = {}
	if 地图ID == 1003 then--桃园
		假人[1]={名称="夏大叔",模型="男人_苦力",X=35,方向=3,Y=85,执行事件="不执行",小地图名称颜色=0}
		假人[2]={名称="窑窑",模型="女人_绿儿",X=42,方向=2,Y=88,执行事件="不执行",小地图名称颜色=0}
		假人[3]={名称="彤彤",模型="女人_绿儿",X=71,方向=0,Y=87,执行事件="不执行",小地图名称颜色=0}
		假人[4]={名称="萍儿",模型="女人_赵姨娘",X=78,方向=1,Y=87,执行事件="不执行",小地图名称颜色=0}
		假人[5]={名称="桃源仙女",模型="普陀_接引仙女",X=115,方向=0,Y=63,执行事件="不执行",小地图名称颜色=0}
		假人[6]={名称="郭大哥",模型="男人_店小二",X=163,方向=0,Y=73,执行事件="不执行",小地图名称颜色=0}
		假人[7]={名称="狸",模型="狸",X=171,方向=1,Y=79,执行事件="不执行",小地图名称颜色=0}
		假人[8]={名称="狸",模型="狸",X=170,方向=1,Y=70,执行事件="不执行",小地图名称颜色=0}
		假人[9]={名称="狸",模型="狸",X=165,方向=0,Y=85,执行事件="不执行",小地图名称颜色=0}
		假人[10]={名称="谭村长",模型="男人_村长",X=163,方向=1,Y=48,执行事件="不执行",小地图名称颜色=0}
		假人[11]={名称="玄大夫",模型="男人_药店老板",X=136,方向=1,Y=47,执行事件="不执行",小地图名称颜色=0}
		假人[12]={名称="雨画师",模型="男人_老书生",X=122,方向=1,Y=47,执行事件="不执行",小地图名称颜色=0}
		假人[13]={名称="孙厨娘",模型="女人_染色师",X=108,方向=0,Y=40,执行事件="不执行",小地图名称颜色=0}
		假人[14]={名称="刘大婶",模型="女人_翠花",X=112,方向=0,Y=26,执行事件="不执行",小地图名称颜色=0}
		假人[15]={名称="小绿",模型="小毛头",X=123,方向=0,Y=16,执行事件="不执行",小地图名称颜色=0}
		假人[16]={名称="清清",模型="小丫丫",X=128,方向=1,Y=14,执行事件="不执行",小地图名称颜色=0}
		假人[17]={名称="孙猎户",模型="冯铁匠",X=163,方向=3,Y=20,执行事件="不执行",小地图名称颜色=0}
		假人[18]={名称="野猪",模型="野猪",X=168,方向=1,Y=18,执行事件="不执行",小地图名称颜色=0}
		假人[19]={名称="霞姑娘",模型="女人_丫鬟",X=78,方向=2,Y=25,执行事件="不执行",小地图名称颜色=0}
		-- 假人[20]={名称="新手使者",模型="普陀_接引仙女",X=19,方向=0,Y=25,执行事件="不执行",小地图名称颜色=0}
	-- elseif 地图ID == 1004  then -- 大雁塔一层
    elseif 地图ID == 1050 then--西凉女国
		假人[1] = {名称="梦之魅",模型="男人_师爷",X=15,Y=22,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1005  then -- 大雁塔二层
		假人[1]={名称="梦之魅",模型="僵尸",X=79,方向=0,Y=18,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1006  then -- 大雁塔三层


	-- 黑风山
	elseif 地图ID == 6018 then
		假人[1] = {名称="镇山太保",模型="男人_兰虎",X=58,Y=15,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="婆婆",模型="女人_孟婆",X=67,Y=16,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="唐玄奘",模型="唐僧",X=76,Y=17,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}


	elseif 地图ID == 2580 then
		假人[1]={名称="雨师妾",模型="雨师妾",X=57,方向=0,Y=107,执行事件="不执行",小地图名称颜色=0}
	             假人[2]={名称="刑天",称谓 = '师门任务',模型="刑天",X=95,方向=1,Y=82,执行事件="不执行",小地图名称颜色=0}
		假人[3]={名称="风祖飞廉",称谓 = '传送长安',模型="风祖飞廉",X=76,方向=1,Y=78,执行事件="不执行",小地图名称颜色=0}
		假人[4]={名称="食铁兽",模型="食铁兽",X=40,方向=0,Y=71,执行事件="不执行",小地图名称颜色=0}
		假人[5]={名称="蚩尤幻影",模型="蚩尤",X=68,方向=1,Y=26,执行事件="不执行",小地图名称颜色=0}




	elseif 地图ID == 1007  then -- 大雁塔四层
		假人[1]={名称="血之魅",模型="僵尸",X=11,方向=0,Y=40,执行事件="不执行",小地图名称颜色=0}
		假人[2]={名称="奔波儿灞",模型="蛤蟆精",X=128,方向=3,Y=40,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1008  then -- 大雁塔五层
		假人[1]={名称="森之魅",模型="僵尸",X=51,方向=0,Y=32,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1090  then -- 大雁塔六层

	elseif 地图ID == 1009  then--大雁塔七层
		假人[1]={名称="镇塔之神",模型="守门天将",X=31,方向=3,Y=29,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1107  then--傲来国民居
		假人[1]={名称="文老伯",模型="男人_老伯",X=18,方向=1,Y=21,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1501 then--建邺城
		假人[1] = {名称="宠物仙子",模型="普陀_接引仙女",X=62,Y=29,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="戏班班主",模型="男人_老伯",X=87,Y=29,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="吹牛王",模型="男人_苦力",X=91,Y=34,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[4] = {名称="游戏推广员",称谓="推广奖励领取员",模型="女人_赵姨娘",X=74,Y=44,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="飞儿",模型="小孩_飞儿",X=106,Y=37,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[6] = {名称="勾魂马面",模型="马面",X=101,Y=12,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[7] = {名称="王大嫂",模型="女人_王大嫂",X=139,Y=14,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[8] = {名称="海产收购商",模型="男人_钓鱼",X=230,Y=14,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[9] = {名称="老孙头",模型="男人_老孙头",X=223,Y=12,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[10] = {名称="陈长寿",模型="男人_药店老板",X=220,Y=21,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[11] = {名称="罗招弟",模型="小孩_飞儿",X=219,Y=28,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[12] = {名称="牛大胆",模型="男人_道士",X=231,Y=35,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[13] = {名称="装备收购商",模型="男人_苦力",X=242,Y=26,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[14] = {名称="装备鉴定商",模型="男人_苦力",X=247,Y=28,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[15] = {名称="超级巫医",模型="男人_巫医",X=211,Y=46,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[16] = {名称="赵元宝",模型="男人_老伯",X=227,Y=79,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[17] = {名称="小花",模型="普陀_接引仙女",X=207,Y=108,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[18] = {名称="赵捕头",模型="男人_衙役",称谓="新手任务",X=116,Y=75,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[19] = {名称="超级巫医",模型="男人_巫医",X=105,Y=89,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[20] = {名称="张来福",模型="男人_镖头",X=87,Y=71,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[21] = {名称="建邺特产商人",模型="男人_特产商人",X=84,Y=77,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[22] = {名称="马全有",模型="男人_武器店老板",X=49,Y=95,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[23] = {名称="迎客僧",模型="男人_胖和尚",X=10,Y=89,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[24] = {名称="管家",模型="男人_店小二",X=32,Y=58,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[25] = {名称="符全",模型="男人_兰虎",X=18,Y=54,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[26] = {名称="建邺守卫",称谓="传送江南野外",模型="男人_衙役",X=15.5,Y=138.5,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[27] = {名称="雷黑子",模型="小孩_雷黑子",X=70,Y=133,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[28] = {名称="教书先生",模型="男人_书生",称谓="强化技能学习",X=77,Y=54,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		--假人[29] = {名称="小白",模型="男人_财主",X=144,Y=64,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[29] = {名称="仓库管理员",模型="仓库管理员",X=52,Y=112,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[30] = {名称="梨园小贩",模型="小孩_飞儿",X=48,Y=12,方向=1,称谓="奇谈任务",事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		--假人[30] = {名称="董相公",模型="教书先生",称谓="衙门户籍员",X=66,Y=34,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[31] = {名称="癫散班主",模型="酒仙",X=15,Y=18,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[31] = {名称="酒剑仙",模型="酒仙",称谓="醉卧云端笑人间",X=70,Y=36,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[32] = {名称="迷茫的员外",模型="仓库管理员",称谓="建邺小酌",X=168,Y=131,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[33] = {名称="黑化雷黑子",模型="小孩_雷黑子",称谓="邪魔附魂",X=268,Y=65,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[34] = {名称="帅比老王",模型="剑侠客",称谓="最初的梦",X=41,Y=8,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[35] = {名称="老明月",模型="剑侠客",称谓="最初的梦",X=18,Y=18,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[36] = {名称="羊永信",模型="羊头怪",称谓="雷电法王",X=58,Y=50,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[36] = {名称="远古魔王",模型="地狱战神",称谓="经典的开端",X=196,Y=70,方向=3,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[37] = {名称="远古大海龟",模型="大海龟",称谓="经典的开端",X=202,Y=94,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1193 then--江南野外
		假人[1] = {名称="江湖奸商",模型="宝石商人",X=102,Y=22,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="樵夫",模型="樵夫",X=133,Y=96,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="卵二姐",模型="小桃红",X=25,Y=98,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="罗纤纤",模型="小桃红",X=20,Y=30,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1127  then
		假人[1] = {名称="幽冥鬼",模型="巡游天神",X=52,Y=26,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1128  then
		假人[1] = {名称="吊死鬼",模型="野鬼",X=33,Y=56,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1129  then
		假人[1] = {名称="无名野鬼",模型="野鬼",X=64,Y=26,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1130  then
		假人[1] = {名称="晶晶姑娘的鬼魂",模型="白晶晶",X=64,Y=26,方向=3,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1167  then
		假人[1] = {名称="文秀",模型="小桃红",X=9,Y=23,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1168  then
		假人[1] = {名称="刘洪",模型="男人_马副将",X=27,Y=21,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="衙役",模型="男人_衙役",X=19,Y=21,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="衙役",模型="男人_衙役",X=26,Y=25,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1103  then
		假人[1] = {名称="美猴王",模型="孙悟空",称谓="剧情技能",X=62,Y=34,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1505  then
		假人[1] = {名称="杂货店老板",模型="男人_巫医",X=36,Y=24,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	--朱紫国
	elseif 地图ID == 1208 then
		假人[1] = {名称="朱紫校尉",模型="校尉",X=138,Y=21,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="药店伙计",模型="男人_苦力",X=125,Y=97,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="紫阳药师",模型="男人_药店老板",X=144,Y=82,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="端木娘子",模型="女人_栗栗娘",X=97,Y=113,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="朱紫侍卫",模型="校尉",X=21,Y=115,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[6] = {名称="朱紫侍卫",模型="校尉",X=7,Y=109,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[7] = {名称="超级巫医",模型="男人_巫医",X=9,Y=31,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[8] = {名称="申太公",模型="男人_村长",X=74,Y=104,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[9] = {名称="彩虹大使",模型="男人_兰虎",X=104,Y=45,任务显示=true,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[9] = {名称="流光巡游",模型="巡游天神",X=132,Y=64,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[10] = {名称="流光灵魅",模型="蝴蝶仙子",X=96,Y=40,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[11] = {名称="流光仙卫",模型="进阶天兵",X=42,Y=37,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[12] = {名称="流光仙师",模型="风伯",X=13,Y=25,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[13] = {名称="酒店老板",模型="男人_酒店老板",X=41,Y=78,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[14] = {名称="豆蔻囡囡",模型="小桃红",X=150,Y=113,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[15] = {名称="小囝囝",模型="小孩_雷黑子",X=153,Y=115,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- elseif 地图ID == 1206 then
	-- 	假人[1] = {名称="门派使者",模型="男人_兰虎",称谓="武神坛报到处",X=115,Y=78,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 	假人[2] = {名称="袁天罡",模型="袁天罡",称谓="玄学大师",X=125,Y=77,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 	假人[3] = {名称="仙药",模型="男人_药店老板",称谓="药王孙思邈",X=135,Y=77,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 	假人[4] = {名称="变身卡",模型="孙悟空",称谓="变变变",X=145,Y=77,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 	假人[5] = {名称="符石商店",模型="仓库管理员",称谓="天才地宝",X=155,Y=77,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 	假人[6] = {名称="神器使者",模型="男人_太上老君",称谓="太上无极大道",X=165,Y=78,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 	假人[7] = {名称="五行大师",模型="五行大师",称谓="点化套装",X=115,Y=68,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 	假人[8] = {名称="染色师",模型="女人_染色师",称谓="炫彩使者",X=125,Y=68,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 	假人[9] = {名称="",模型="物件_打铁炉",X=135,Y=68,方向=0,事件ID=nil,执行事件="物件_打铁炉",小地图名称颜色=0}
	-- 	假人[10] = {名称="蚩尤",模型="蚩尤",称谓="上古魔神",X=145,Y=68,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1040 then--西凉女国
		假人[1] = {名称="西梁女兵",模型="西梁女兵",X=129,Y=104,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="西梁女兵",模型="西梁女兵",X=141,Y=99,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="西梁女兵",模型="西梁女兵",X=73,Y=107,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="西梁女兵",模型="西梁女兵",X=67,Y=110,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="鬼谷道士分身",模型="男人_道士",X=137,Y=104,任务显示=true,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[6] = {名称="驿站老板",称谓="传送朱紫国",模型="男人_驿站老板",X=18,Y=71,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[7] = {名称="驿站老板",称谓="传送丝绸之路",模型="男人_驿站老板",X=15,Y=114,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[8] = {名称="仓库管理员",模型="仓库管理员",X=101,Y=91,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[9] = {名称="慕容婆婆",模型="女人_孟婆",X=84,Y=33,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[9] = {名称="流光玉兔",模型="兔子怪",X=120,Y=21,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[10] = {名称="流光泡泡",模型="泡泡",X=68,Y=51,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[11] = {名称="流光玉镜",模型="镜妖",X=25,Y=69,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[12] = {名称="鲲鹏使",模型="简师爷",X=84,Y=62,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[13] = {名称="禁军卫士",模型="护卫",称谓="为了西凉",X=15,Y=25,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[14] = {名称="禁军卫士",模型="护卫",称谓="为了西凉",X=29,Y=19,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[15] = {名称="慕容复",模型="野鬼",称谓="西凉怪谈",X=58,Y=4,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[16] = {名称="马娘娘",模型="金圣宫",称谓="西凉怪谈",X=84,Y=31,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[17] = {名称="黑心掌柜",模型="当铺老板",称谓="西凉怪谈",X=135,Y=63,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[18] = {名称="黑夜暗影",模型="鲛人",称谓="西凉怪谈",X=41,Y=101,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		--假人[5] = {名称="测试专员",称谓="测试福利领取",模型="男人_武器店老板",X=67,Y=50,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		--假人[5] = {名称="朱紫侍卫",模型="校尉",X=21,Y=115,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		--假人[6] = {名称="朱紫侍卫",模型="校尉",X=7,Y=109,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		--假人[7] = {名称="超级巫医",模型="男人_巫医",X=9,Y=31,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1216 then
		假人[1] = {名称="御兽仙",称谓="召唤兽进阶",模型="进阶凤凰",X=151,Y=59,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="百兽王",称谓="坐骑任务",模型="大大王",X=24,Y=84,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="桃园仙翁",模型="南极仙翁",X=67,Y=94,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="仙缘染坊主",称谓="坐骑染色",模型="女人_赵姨娘",X=121,Y=61,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		--帮派地图
	elseif 地图ID == 1217 then
		假人[1] = {名称="帮派车夫",模型="男人_驿站老板",X=121,Y=102,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[2] = {名称="帮派车夫",模型="男人_驿站老板",X=28,Y=81,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="帮派土地公公",模型="男人_土地",X=126,Y=107,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[3] = {名称="修炼使",模型="九头精怪",X=239,Y=193,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1815 then-- 金库
		假人[1] = {名称="金库总管",模型="男人_兰虎",X=22,Y=22,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="紫色机关人",模型="帮派机关人",X=27,Y=20,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1825 then
		假人[1] = {名称="帮派师爷",模型="男人_师爷",X=15,Y=17,方向=0,事件ID=nil,执行事件="不执行"}
		假人[2] = {名称="蓝色机关人",模型="帮派机关人",X=33,Y=21,方向=0,事件ID=nil,执行事件="不执行"}
	elseif 地图ID == 1874 then
		假人[1] = {名称="帮派总管",模型="男人_兰虎",X=36,Y=23,方向=0,事件ID=nil,执行事件="不执行"}
		假人[2] = {名称="黑色机关人",模型="帮派机关人",X=25,Y=36,方向=1,事件ID=nil,执行事件="不执行"}
	elseif 地图ID == 1844 then
		假人[1] = {名称="厢房总管",模型="男人_兰虎",X=18,Y=18,方向=0,事件ID=nil,执行事件="不执行"}
		假人[2] = {名称="绿色机关人",模型="帮派机关人",X=16,Y=23,方向=0,事件ID=nil,执行事件="不执行"}
	elseif 地图ID == 1854 then
		假人[1] = {名称="修炼指导人",模型="男人_药店老板",X=20,方向=1,Y=16,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="红色机关人",模型="帮派机关人",X=15,Y=21,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1835 then-- 兽室
		假人[1] = {名称="帮派守护兽",模型="帮派妖兽",X=20,Y=20,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="青色机关人",模型="帮派机关人",X=35,Y=22,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1865 then
		假人[1] = {名称="青龙总管",模型="男人_兰虎",X=18,Y=19,方向=0,事件ID=nil,执行事件="不执行"}
		假人[2] = {名称="橙色机关人",模型="帮派机关人",X=30,Y=19,方向=1,事件ID=nil,执行事件="不执行"}
	elseif 地图ID == 1226 then
		假人[1] = {名称="驿站老板",称谓="传送长安城",模型="男人_驿站老板",X=143,Y=27,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="镖局学童",模型="男人_店小二",X=54,Y=14,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="土地公公",称谓="宝藏山传送员",模型="男人_土地",X=119,Y=16,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="炫彩大使",模型="女人_万圣公主",X=48,Y=81,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="武器大师",模型="男人_武器店老板",X=64,Y=71,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 5001 then
		假人[1] = {名称="土地公公",称谓="传送至宝象国",模型="男人_土地",X=76,Y=24,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1537 then--建邺城衙门
		-- 假人[1] = {名称="贸易车队总管",模型="男人_老财",X=12,Y=28,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="衙役",模型="男人_衙役",X=17,Y=24,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="衙役",模型="男人_衙役",X=26,Y=20,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="衙役",模型="男人_衙役",X=26,Y=29,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="衙役",模型="男人_衙役",X=33,Y=25,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[6] = {名称="简师爷",模型="男人_师爷",X=30,Y=19,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[7] = {名称="建邺县令",模型="男人_老书生",X=35,Y=20,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1523 then
		假人[1] = {名称="当铺老板",模型="男人_特产商人",X=22,Y=20,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1502 then
		假人[1] = {名称="武器店老板",模型="男人_武器店老板",X=22,Y=18,方向=1,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2]  = {名称="武器店掌柜",模型="男人_老孙头",X=15,Y=18,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3]  = {名称="打铁炉",模型="物件_打铁炉",X=19,Y=24,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1503 then
		假人[1] = {名称="服装店老板",模型="男人_服装店老板",X=17,Y=16,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="缝纫台",模型="物件_缝纫台",X=28,Y=17,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1504 then
		假人[1] = {名称="药店老板",模型="男人_药店老板",X=23,Y=17,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1526 then
		假人[1]  = {名称="周猎户",模型="男人_兰虎",X=22,Y=22,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1524 then
		假人[1] = {名称="钱庄老板",模型="男人_财主",X=33,Y=19,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1534 then
		假人[1]  = {名称="李善人",模型="男人_老财",X=21,Y=29,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 东海湾
	elseif 地图ID == 1506 then
		假人[1] = {名称="船夫",称谓="传送傲来国",模型="男人_驿站老板",X=64,Y=100,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="云游神医",模型="男人_村长",X=75,Y=109,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="超级巫医",模型="男人_巫医",X=95,Y=97,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="捕鱼人",称谓="传送东海岩洞",模型="男人_钓鱼",X=91,Y=81,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="老虾",称谓="传送龙宫",模型="虾兵",X=101,Y=41,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[6] = {名称="楚恋依",模型="普陀_接引仙女",X=57,Y=37,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[7] = {名称="游侠盗",模型="强盗",X=22,Y=90,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[8] = {名称="云中子",称谓="云上梦仙",模型="男人_诗中仙",X=25,Y=33,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[9] = {名称="红尘灵龟",称谓="传送东海渊",模型="大海龟",X=82,Y=86,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
    elseif 地图ID == 1126 then
		假人[1] = {名称="接引小仙",称谓="传送女魃墓",模型="女人_万圣公主",X=111,Y=19,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="六耳猕猴",模型="超级六耳猕猴",X=18,Y=14,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="吉祥",模型="小毛头",X=82,Y=69,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	--傲来圣殿
	elseif 地图ID == 1100 then
		假人[1] = {名称="小紫",称谓="迷茫",模型="少女",X=25,Y=28,方向=3,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 国境
	elseif 地图ID == 1110 then
		假人[1] = {名称="大唐国境土地",称谓="传送凌波城",模型="男人_土地",X=177,Y=74,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="普陀山接引仙女",称谓="传送普陀山",模型="普陀_接引仙女",X=229,Y=269,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="小二",模型="男人_店小二",X=68,Y=268,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="驿站老板",称谓="传送长安城",模型="男人_驿站老板",X=86,Y=86,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="超级巫医",模型="男人_巫医",X=54,Y=261,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[6] = {名称="者释和尚",模型="男人_胖和尚",X=174,Y=248,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[7] = {名称="业释和尚",模型="男人_胖和尚",X=186,Y=184,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[8] = {名称="海释和尚",模型="男人_胖和尚",X=139,Y=157,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[9] = {名称="白琉璃",模型="星灵仙子",X=27,Y=176,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[10] = {名称="山神",模型="雨师",X=43,Y=98,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[11] = {名称="虾兵",模型="虾兵",X=242,Y=74,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[12] = {名称="接引守卫",称谓="传送赤水州",模型="云游火",X=192,Y=36,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[13] = {名称="牢房守卫",模型="男人_衙役",X=331,Y=305,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[14] = {名称="黑山老妖",模型="黑山老妖",X=99,Y=52,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		--假人[15] = {名称="刘洪",模型="男人_马副将",X=317,Y=44,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[16] = {名称="虹姑娘",模型="女人_丫鬟",X=271,Y=86,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[17] = {名称="马大娘",模型="女人_孟婆",X=264,Y=81,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[18] = {名称="苏大娘",模型="女人_孟婆",X=22,Y=298,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[19] = {名称="刘老伯",模型="男人_胖和尚",X=65,Y=297,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[20] = {名称="公孙胜",模型="护卫",称谓="流浪义士",X=51,Y=320,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[21] = {名称="突厥奸细",模型="山贼",称谓="突厥军团",X=159,Y=313,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[22] = {名称="国境驿站",模型="男人_驿站老板",X=276,Y=253,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[23] = {名称="驿站小二",模型="男人_店小二",称谓="客官这边请",X=266,Y=259,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[24] = {名称="国境卫士",模型="护卫",称谓="誓守长安",X=312,Y=180,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[25] = {名称="突厥斥候",模型="进阶风伯",称谓="突厥军团",X=285,Y=132,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[26] = {名称="衙门守卫",模型="护卫",X=294,Y=48,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[27] = {名称="衙门守卫",模型="护卫",X=300,Y=51,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[28] = {名称="殷温娇",模型="女人_王大嫂",X=305,Y=35,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[29] = {名称="渔翁",模型="男人_钓鱼",X=148,Y=83,方向=3,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[30] = {名称="婆婆",模型="婆婆",X=31,Y=310,方向=3,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[31] = {名称="西域使者",模型="宝象国国王",X=22,Y=146,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}

	-- 凌波城
	elseif 地图ID == 1150 then
		假人[1] = {名称="二郎神",称谓="门派师傅",模型="二郎神",X=68,Y=32,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="哮天犬",模型="哮天犬",X=77,Y=36,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="守门天将",模型="天兵",X=6,Y=88,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="守门天将",模型="天兵",X=15,Y=93.5,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[12] = {名称="念奴娇",称谓="凌波城少主",模型="剑侠客",武器="擒龙",X=49,Y=65,方向=4,染色方案=2,染色组={3,3,4},事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[7] = {名称="荒芜星",模型="狂豹人形",X=23,Y=117,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[8] = {名称="刀砧星",模型="鲛人",X=29,Y=141,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[9] = {名称="反吟星",模型="羊头怪",X=54,Y=137,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[10] = {名称="天瘟星",模型="犀牛将军人形",X=93,Y=104,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[11] = {名称="伏断星",模型="野猪精",X=100,Y=48,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[6] = {名称="破碎星",模型="百足将军",X=26.5,Y=55.5,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="传送天将",称谓="传送长安",模型="天兵",X=44,Y=57,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 普陀山
	elseif 地图ID == 1140 then
		假人[2] = {名称="龙女宝宝",模型="小龙女",X=21,Y=32,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="黑熊怪",模型="黑熊精",X=24,Y=47,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[1] = {名称="接引仙女",称谓="传送长安",模型="普陀_接引仙女",X=19,Y=23,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 潮音洞
	elseif 地图ID == 1141 then
		假人[1] = {名称="观音姐姐",称谓="门派师傅",模型="观音姐姐",X=12.5,Y=11.5,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="青莲仙女",模型="普陀_接引仙女",X=25,Y=33,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 地府
	elseif 地图ID == 1122 then
		假人[1] = {名称="钟馗",称谓="捉鬼任务",模型="男人_钟馗",X=65,Y=70,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="马面",模型="马面",X=10,Y=87,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="追梦鬼",模型="兔子怪",X=10,Y=104,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="地遁鬼",称谓="传送长安",模型="僵尸",X=27.5,Y=63.5,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="孟婆",模型="女人_孟婆",X=99.5,Y=94.5,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[6] = {名称="地府货商",模型="奸商",X=87,Y=107,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[7] = {名称="往生使",模型="净瓶女娲",X=143,Y=16,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[8] = {名称="蚩尤魔魂",模型="蚩尤",X=101,Y=25,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 森罗 3.4 2
	elseif 地图ID == 1123 then
		假人[1] = {名称="判官",模型="男人_判官",X=18,Y=20,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="阎罗王",模型="阎罗王",X=22.2,Y=26.5,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="转轮王",模型="阎罗王",X=25.6,Y=28.5,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="秦广王",模型="阎罗王",X=29,Y=30.5,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="初江王",模型="阎罗王",X=32.4,Y=32.5,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[6] = {名称="宋帝王",模型="阎罗王",X=35.8,Y=34.5,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[7] = {名称="卞城王",模型="阎罗王",X=31.7,Y=21.7,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[8] = {名称="平等王",模型="阎罗王",X=35.1,Y=23.7,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[9] = {名称="泰山王",模型="阎罗王",X=38.5,Y=25.7,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[10] = {名称="都市王",模型="阎罗王",X=41.9,Y=27.7,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[11] = {名称="忤官王",模型="阎罗王",X=45.3,Y=29.7,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1125 then
		假人[1] = {名称="白无常",模型="白无常",称谓="传送长安",X=20,Y=23,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="黑无常",模型="黑无常",称谓="鬼王任务",X=37,Y=22,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 地藏王府
	elseif 地图ID == 1124 then
		假人[1] = {名称="地藏王",称谓="门派师傅",模型="地藏王",X=30,Y=24,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 龙宫
	elseif 地图ID == 1116 then
		假人[1] = {名称="虾兵",称谓="传送东海湾",模型="虾兵",X=24,Y=97,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="蟹将军",模型="蟹将",X=32,Y=100,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="虾兵",模型="虾兵",X=40,Y=104,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="龟千岁",模型="龟丞相",X=99,Y=57,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[6] = {名称="小龙女",模型="小龙女",X=95,Y=48,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[7] = {名称="虾兵",模型="虾兵",X=47,Y=21,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[8] = {名称="龟太尉",模型="龟丞相",X=53,Y=16,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[9] = {名称="虾兵",模型="虾兵",X=62,Y=14,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[10] = {名称="虾兵",模型="虾兵",X=188,Y=12,方向=3,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[11] = {名称="蛤蟆勇士",模型="蛤蟆精",X=195,Y=16,方向=3,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[12] = {名称="虾兵",模型="虾兵",X=203,Y=20,方向=3,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[13] = {名称="虾兵",模型="虾兵",X=188,Y=92,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[14] = {名称="虾将军",模型="虾兵",X=195,Y=88,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[15] = {名称="虾兵",模型="虾兵",X=203,Y=84,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[16] = {名称="万圣公主",模型="女人_万圣公主",X=18,Y=50,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="传送蟹将",称谓="传送长安",模型="蟹将",X=108,Y=60,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 水晶宫
	elseif 地图ID == 1117 then
		假人[1] = {名称="东海龙王",称谓="门派师傅",模型="男人_东海龙王",X=39,Y=25,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	--海底
	elseif 地图ID == 1507 then
		假人[1] = {名称="螃蟹精",称谓="传送建邺城",模型="蟹将",X=20,Y=21,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="蛤蟆精",模型="蛤蟆精",X=50,Y=29,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[3] = {名称="受伤的残月",称谓="怡红院剧情",模型="吸血鬼",X=105,Y=23,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1532 then
		假人[1] = {名称="小白龙",模型="男人_小白龙",X=52,Y=8,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
    elseif 地图ID == 1013 then--广源钱庄
	    假人[1] = {名称="钱庄老板",模型="宝石商人",X=29,Y=25,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
    elseif 地图ID == 1081 then--长寿村钱庄
	    假人[1] = {名称="钱庄老板",模型="宝石商人",X=21,Y=14,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1099 then--傲来钱庄
	    假人[1] = {名称="钱庄老板",模型="宝石商人",X=26,Y=24,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
    elseif 地图ID == 1524 then
		假人[1] = {名称="钱庄老板",模型="宝石商人",X=36,Y=23,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 花果山
	elseif 地图ID == 1514 then
		假人[1] = {名称="老马猴",称谓="法术认证",模型="马猴",X=34,Y=72,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="虾兵统领",模型="虾兵",X=111,Y=104,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="花果山猴统领",称谓="传送幻境花果山",模型="马猴",X=114,Y=78,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="侯医仙",模型="马猴",X=131,Y=41,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="老猕猴",模型="长眉灵猴",X=137,Y=11,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1251 then -- 幻境花果山
		假人[1] = {名称="齐天大圣",模型="孙悟空",X=89,Y=124-105,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="马元帅",模型="生肖猴",X=27,Y=124-89,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="流元帅",模型="进阶长眉灵猴",X=40,Y=124-50,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="崩将军",模型="长眉灵猴",X=82,Y=124-61,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="芭将军",模型="巨力神猿",X=106,Y=124-13,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[6] = {名称="幻境戍守",模型="进阶巨力神猿",X=38,Y=124-90,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[7] = {名称="幻境戍守",模型="进阶巨力神猿",X=44,Y=124-91,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 北俱芦洲
	elseif 地图ID == 1174 then
		假人[1] = {名称="地遁鬼",模型="兔子怪",称谓="传送长寿郊外",X=195,Y=163,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="女娲神迹传送人",模型="净瓶女娲",称谓="传送女娲神迹",X=13,Y=17,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="青琉璃",模型="星灵仙子",X=202,Y=35,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="超级巫医",模型="男人_巫医",X=29,Y=105,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="龙女妹妹",模型="小龙女",X=138,Y=71,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[6] = {名称="莽汉",模型="山贼",X=38,Y=129,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[7] = {名称="江湖奸商",模型="宝石商人",X=106,Y=58,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1178 then --龙窟二层
		假人[1] = {名称="龙神",模型="蛟龙",X=114,Y=43,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1183 then --龙窟七层
		假人[1] = {名称="青灵玄女",模型="如意仙子",X=67,Y=42,方向=3,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1186 then--凤巢一层
		假人[1] = {名称="辛发明",模型="男人_巫医",X=83,Y=28,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1192 then--凤巢七层
		假人[1] = {名称="无心",模型="男人_老和尚",X=87,Y=25,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 长寿郊外
	elseif 地图ID == 1091 then
		假人[1] = {名称="驿站老板",模型="男人_驿站老板",称谓="传送北俱芦洲",X=62,Y=104,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="传送天将",模型="男人_将军",称谓="传送天宫",X=22,Y=112,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="西牛贺洲土地",模型="男人_土地",称谓="传送大唐境外",X=90,Y=158,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="西域使者",模型="宝象国国王",X=22,Y=132,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[4] = {名称="流光逆光",模型="千年蛇魅",X=68,Y=6,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[5] = {名称="流光月灵",模型="净瓶女娲",X=42,Y=33,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[6] = {名称="流光灵珠",模型="泪妖",X=14,Y=40,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[7] = {名称="铃铛仙",模型="雾中仙",X=174,Y=116,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[8] = {名称="生死莫测",模型="男人_道童",X=173,Y=155,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[9] = {名称="北海公主",模型="小龙女",X=59,Y=140,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[10] = {名称="路人甲",模型="赌徒",X=44,Y=36,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 长寿村
	-- 方寸
	elseif 地图ID == 1135 then
		假人[2] = {名称="觉明",模型="男人_道士",X=62,Y=118,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="灵儿",模型="女人_丫鬟",X=50,Y=92,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="觉岸",模型="男人_道士",称谓="剧情技能",X=108,Y=43,方向=3,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[1] = {名称="接引道童",称谓="传送长安",模型="男人_道童",X=118,Y=30,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="只手摘星辰",模型="剑侠客",称谓="观星使",X=138,Y=144,方向=1,武器="冷月",染色方案=2,染色组={3,3,3},事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[6] = {名称="李逍遥",模型="剑侠客",称谓="仙剑传说",X=28,Y=31,方向=1,武器="四法青云",染色方案=2,染色组={3,3,3},事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[7] = {名称="流年",称谓="仙剑传说",模型="哮天犬",X=41,Y=32,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[8] = {名称="张天师",称谓="天师传",模型="逍遥生",X=137,Y=64,方向=1,武器="秋水人家",染色方案=2,染色组={3,3,3},事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[9] = {名称="酒剑仙",称谓="仙剑传说",模型="偃无师",X=127,Y=70,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 大唐境外
	elseif 地图ID == 1173 then
		假人[1] = {名称="南瞻部洲土地",模型="男人_土地",称谓="传送西牛贺洲",X=44,Y=102,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="驿站老板",模型="男人_驿站老板",称谓="传送碗子山",X=13,Y=95,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="超级巫医",模型="男人_巫医",X=50,Y=49,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="山贼头子",模型="山贼",X=96,Y=28,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="牛将军",模型="牛妖",X=79,Y=43,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[6] = {名称="驿站老板",模型="男人_驿站老板",称谓="传送长安城",X=205,Y=93,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[7] = {名称="白衣人",模型="逍遥生",X=234,Y=110,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[8] = {名称="阿紫",模型="星灵仙子",X=319,Y=47,方向=3,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[9] = {名称="姚太尉",模型="天兵",X=324,Y=44,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[10] = {名称="云游僧",模型="男人_老和尚",X=350,Y=89,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[11] = {名称="白鹿精",模型="赌徒",X=353,Y=116,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[12] = {名称="玉面狐狸",模型="狐狸精",X=348,Y=114,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[13] = {名称="超级巫医",模型="男人_巫医",X=409,Y=108,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[14] = {名称="野猪王",模型="野猪",X=480,Y=113,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[15] = {名称="偷尸鬼",模型="骷髅怪",X=579,Y=98,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[16] = {名称="李彪",模型="强盗",X=588,Y=101,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[17] = {名称="刘洪",模型="男人_马副将",X=591,Y=104,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[18] = {名称="强盗头子",模型="强盗",称谓="剧情技能",X=562,Y=33,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[19] = {名称="冤魂",模型="僵尸",X=603,Y=19,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[20] = {名称="至尊宝",模型="至尊宝",称谓="不渝之心",X=131,Y=56,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[21] = {名称="神秘客商",模型="奸商",X=166,Y=10,方向=1,事件ID=nil,执行事件="不执行",}
		假人[22] = {名称="高府守卫",模型="护卫",X=366,Y=83,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[23] = {名称="文老伯",模型="男人_老伯",X=374,Y=57,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[24] = {名称="胡总管",模型="男人_镖头",称谓="身份可疑",X=415,Y=77,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[25] = {名称="满天月",模型="混沌兽",称谓="天界巡查使",X=378,Y=19,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[26] = {名称="文秀",模型="女人_栗栗娘",X=399,Y=34,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[27] = {名称="土地公公",模型="男人_土地",称谓="不渝之心",X=315,Y=12,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[28] = {名称="卷帘大将",模型="沙和尚",X=243,Y=54,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[29] = {名称="张青",模型="进阶天将",称谓="菜园子",X=490,Y=56,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[30] = {名称="天蓬元帅",模型="猪八戒",X=615,Y=100,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[31] = {名称="食尸鬼",模型="骷髅怪",X=533,Y=39,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[32] = {名称="天兵飞剑",模型="天兵",X=235,Y=60,方向=3,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[33] = {名称="二帮主",模型="强盗",X=610,Y=60,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[34] = {名称="水云仙",模型="女人_万圣公主",X=322,Y=20,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[35] = {名称="轩辕",模型="剑侠客",称谓="神器任务",锦衣="从军行",武器="霜冷九州",X=176,Y=11,方向=4,事件ID=nil,执行事件="不执行",小地图名称颜色=6}
		假人[38] = {名称="飞廉幻影",模型="超级飞廉",称谓 = '传送九黎城',X=175,Y=30,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}

	elseif 地图ID == 1170 then -- 高老庄大厅
		假人[1] = {名称="高老先生",模型="男人_村长",X=32,Y=18,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1171 then -- 高小姐闺房
		假人[1] = {名称="高翠兰",模型="小桃红",X=12,Y=13,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 沉船
	elseif 地图ID == 1508 then
		假人[1] = {名称="虾精",称谓="传送建邺城",模型="虾兵",X=17,Y=16,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="妖风",模型="吸血鬼",X=55,Y=31,染色方案=96,染色组={1,1},方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="海霸王",称谓="海中霸王",模型="海毛虫",X=13,Y=58,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1509 then
		假人[1] = {名称="商人的鬼魂",模型="野鬼",X=24,Y=24,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}




	 elseif 地图ID == 1994 then
	 	 假人[1] = {名称="武神坛使者",模型="男人_兰虎",称谓="武神坛传送",X=431,Y=171,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0,商业分类1=0,商业分类2=0,商业分类3=0,商业分类4=0}
	 	 假人[2] = {名称="打铁炉",模型="物件_打铁炉",X=438,Y=184,方向=1,事件ID=nil,执行事件="物件_打铁炉",小地图名称颜色=0,商业分类1=0,商业分类2=0,商业分类3=0,商业分类4=0}

	 	 假人[3] = {名称="二郎神",称谓="凌波城",模型="二郎神",X=449,Y=183,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0,商业分类1=0,商业分类2=0,商业分类3=0,商业分类4=0}
		         假人[4] = {名称="观音姐姐",称谓="普陀山",模型="观音姐姐",X=453,Y=186,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0,商业分类1=0,商业分类2=0,商业分类3=0,商业分类4=0}
		         假人[5] = {名称="地藏王",称谓="地府",模型="地藏王",X=457,Y=187,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0,商业分类1=0,商业分类2=0,商业分类3=0,商业分类4=0}
		         假人[6] = {名称="东海龙王",称谓="龙宫",模型="东海龙王",X=461,Y=190,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0,商业分类1=0,商业分类2=0,商业分类3=0,商业分类4=0}
		         假人[7] = {名称="程咬金",称谓="大唐官府",模型="程咬金",X=465,Y=191,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0,商业分类1=0,商业分类2=0,商业分类3=0,商业分类4=0}
		         假人[8] = {名称="空度禅师",称谓="化生寺",模型="空度禅师",X=469,Y=194,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0,商业分类1=0,商业分类2=0,商业分类3=0,商业分类4=0}
		         假人[9] = {名称="孙婆婆",称谓="女儿村",模型="孙婆婆",X=473,Y=195,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0,商业分类1=0,商业分类2=0,商业分类3=0,商业分类4=0}
		         假人[10] = {名称="菩提老祖",称谓="方寸山",模型="菩提老祖",X=477,Y=198,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0,商业分类1=0,商业分类2=0,商业分类3=0,商业分类4=0}
		         假人[11] = {名称="李靖",称谓="天宫",模型="李靖",X=481,Y=199,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0,商业分类1=0,商业分类2=0,商业分类3=0,商业分类4=0}
		         假人[12] = {名称="牛魔王",称谓="魔王寨",模型="牛魔王",显示饰品=true,X=485,Y=202,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0,商业分类1=0,商业分类2=0,商业分类3=0,商业分类4=0}
		         假人[13] = {名称="镇元子",称谓="五庄观",模型="镇元子",X=489,Y=203,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0,商业分类1=0,商业分类2=0,商业分类3=0,商业分类4=0}
		         假人[14] = {名称="白晶晶",称谓="盘丝洞",模型="白晶晶",X=493,Y=206,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0,商业分类1=0,商业分类2=0,商业分类3=0,商业分类4=0}
		         假人[15] = {名称="巫奎虎",称谓="神木林",模型="巫奎虎",X=497,Y=207,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0,商业分类1=0,商业分类2=0,商业分类3=0,商业分类4=0}
		         假人[16] = {名称="地涌夫人",称谓="无底洞",模型="地涌夫人",X=501,Y=210,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0,商业分类1=0,商业分类2=0,商业分类3=0,商业分类4=0}
		         假人[17] = {名称="大大王",称谓="狮驼岭",模型="大大王",X=505,Y=211,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0,商业分类1=0,商业分类2=0,商业分类3=0,商业分类4=0}

		         假人[18] = {名称="袁天罡",模型="袁天罡",称谓="多功能使者",X=427,Y=188,方向=3,事件ID=nil,执行事件="不执行",小地图名称颜色=0,商业分类1=0,商业分类2=0,商业分类3=0,商业分类4=0}






	-- 长安
	elseif 地图ID == 1001 then
		假人[1] = {名称="PK申请人",模型="男人_马副将",X=250,Y=170,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[2] = {名称="王夫人",模型="女人_王大嫂",X=391,Y=231,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="商会总管",模型="仓库管理员",X=329,Y=265,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="陈员外",模型="仓库管理员",X=163,Y=244,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="花香香",模型="普陀_接引仙女",X=300,Y=253,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[6] = {名称="老花农",模型="男人_村长",X=289,Y=249,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[7] = {名称="罗道人",模型="男人_道士",X=254,Y=240,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[8] = {名称="染色师",模型="女人_染色师",X=294,Y=265,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[9] = {名称="驿站老板",称谓="传送大唐国境",模型="男人_驿站老板",X=274,Y=238,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[10] = {名称="张老财",模型="男人_老财",X=247,Y=253,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[11] = {名称="超级巫医",模型="男人_巫医",X=522,Y=95,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[12] = {名称="门派传送人",模型="男人_镖头",X=478,Y=246.8,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[13] = {名称="圣山传送人",模型="男人_太上老君",X=350.5,Y=205,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[14] = {名称="门派闯关使者",称谓="",模型="男人_马副将",X=128,Y=90,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[15] = {名称="刘副将",模型="男人_马副将",X=159,Y=190,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[16] = {名称="兰虎",模型="男人_兰虎",称谓="",X=434,Y=173,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[17] = {名称="马副将",模型="男人_马副将",X=193,Y=106,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[18] = {名称="御林军",模型="男人_马副将",X=201,Y=105,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[19] = {名称="皇宫护卫",称谓="赏金任务",模型="护卫",X=441,Y=32,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[20] = {名称="礼部侍郎",称谓="科举大赛",模型="考官2",X=280,Y=95,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[21] = {名称="袁天罡",模型="袁天罡",X=358,Y=35,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[22] = {名称="李将军",模型="男人_马副将",称谓="官职任务",X=152,Y=85,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[23] = {名称="五行大师",模型="五行大师",称谓="点化套装",X=358,Y=162,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[24] = {名称="杜少海",模型="男人_店小二",称谓="初出江湖",X=405,Y=213,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[25] = {名称="御林军左统领",模型="护卫",称谓="皇宫飞贼",X=102,Y=52,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[26] = {名称="御林军右统领",模型="护卫",X=110,Y=49,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[27] = {名称="长安珍品商人",模型="珍品商人",X=175,Y=251,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[28] = {名称="装备收购商",模型="男人_苦力",X=423,Y=32,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=5}
		假人[29] = {名称="装备鉴定商",模型="男人_苦力",X=428,Y=35,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=5}
		假人[30] = {名称="礼部侍郎嚄",模型="考官2",X=234,Y=168,方向=1,任务显示=true,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[30] = {名称="云中子",称谓="仙缘界",模型="进阶天兵",显示饰品=true,X=365,Y=37,武器染色方案=119,武器染色组={6,0,0},方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=5}
		假人[31] = {名称="仓库管理员",模型="仓库管理员",X=350,Y=36,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=5}
		-- 假人[32] = {名称="明月楼左护法",模型="剑侠客",称谓="纵使青山万重烟",武器="四法青云",X=393,Y=76,方向=0,染色方案=2,染色组={3,3,3},事件ID=nil,执行事件="不执行"}
		假人[33] = {名称="游奕灵官",模型="天兵",X=128,Y=97,任务显示=true,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
        假人[34] = {名称="超级大熊猫",模型="超级大熊猫",X=378,Y=50,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
        假人[35] = {名称="西域使者",模型="宝象国国王",X=362,Y=195,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
        		--  if 授权码 =="9xRVCmGmz33nf5dRwnhfYregfrrBz5"  then
        		-- else
        		假人[36] = {名称="超级鲲鹏",模型="进阶超级鲲鹏",称谓="高级神兽使者",X=398,Y=66,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
        		假人[37] = {名称="超级神虎（壬寅）",模型="进阶超级神虎（壬寅）",称谓="高级神兽",X=410,Y=67,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
        		假人[38] = {名称="超级飞天",模型="进阶超级飞天",称谓="高级神兽",X=400,Y=73,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
        		假人[39] = {名称="超级红孩儿",模型="超级红孩儿",称谓="高级神兽",X=414,Y=75,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
        		假人[40] = {名称="恶魔泡泡",模型="恶魔泡泡",称谓="高级神兽",X=420,Y=72,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
        		假人[41] = {名称="超级飞廉",模型="超级飞廉",称谓="高级神兽",X=409,Y=77,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
        		--end
		-- 假人[34] = {名称="超级灵鹿",模型="超级灵鹿",称谓="神兽",X=374,Y=55,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[35] = {名称="超级麒麟",模型="超级麒麟",称谓="神兽",X=378,Y=53,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[36] = {名称="超级筋斗云",模型="超级筋斗云",称谓="神兽",X=385,Y=50,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[37] = {名称="超级小白龙",模型="超级小白龙",称谓="神兽",X=390,Y=47,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[38] = {名称="超级大熊猫",模型="超级大熊猫",称谓="神兽",X=383,Y=60,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[39] = {名称="超级神羊",模型="超级神羊",称谓="神兽",X=393,Y=55,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[40] = {名称="超级大鹏",模型="超级大鹏",称谓="神兽",X=397,Y=63,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[41] = {名称="泡泡使者",模型="泡泡",称谓="神兽",X=393,Y=65,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[42] = {名称="超级神虎使者",模型="超级神虎",称谓="神兽",X=399,Y=69,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[43] = {名称="超级海豚使者",模型="超级海豚",称谓="神兽",X=405,Y=66,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[44] = {名称="超级人参娃娃使者",模型="超级人参娃娃",称谓="神兽",X=428,Y=51,方向=1,事件ID=nil,执行事件="不执行"}
		-- 假人[45] = {名称="超级大熊猫使者",模型="超级大熊猫",称谓="神兽",X=433,Y=53,方向=1,事件ID=nil,执行事件="不执行"}
		-- 假人[46] = {名称="超级金猴使者",模型="超级金猴",称谓="神兽",X=438,Y=56,方向=1,事件ID=nil,执行事件="不执行"}
		-- 假人[47] = {名称="超级大象使者",模型="超级大象",称谓="神兽",X=443,Y=58,方向=1,事件ID=nil,执行事件="不执行"}
		-- 假人[48] = {名称="超级灵鹿使者",模型="超级灵鹿",称谓="神兽",X=448,Y=61,方向=1,事件ID=nil,执行事件="不执行"}
		-- 假人[49] = {名称="超级大鹏使者",模型="超级大鹏",称谓="神兽",X=453,Y=63,方向=1,事件ID=nil,执行事件="不执行"}
		-- 假人[50] = {名称="超级筋斗云使者",模型="超级筋斗云",称谓="神兽",X=458,Y=66,方向=1,事件ID=nil,执行事件="不执行"}
		-- 假人[51] = {名称="超级神兔使者",模型="超级神兔",称谓="神兽",X=465,Y=65,方向=1,事件ID=nil,执行事件="不执行"}
		-- 假人[52] = {名称="超级灵狐使者",模型="超级灵狐",称谓="神兽",X=366,Y=56,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[53] = {名称="超级神蛇使者",模型="超级神蛇",称谓="神兽",X=390,Y=45,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[54] = {名称="超级神马使者",模型="超级神马",称谓="神兽",X=395,Y=42,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[55] = {名称="超级麒麟使者",模型="超级麒麟",称谓="神兽",X=385,Y=38,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[56] = {名称="超级神羊使者",模型="超级神羊",称谓="神兽",X=356,Y=53,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[57] = {名称="超级孔雀使者",模型="超级孔雀",称谓="神兽",X=362,Y=59.5,方向=0,事件ID=nil,执行事件="不执行"}
		假人[58] = {名称="车夫",模型="男人_兰虎",称谓="家园",X=520,Y=150,方向=0,事件ID=nil,执行事件="不执行"}
		假人[59] = {名称="长安衙役",称谓="建房史",模型="男人_衙役",X=35,Y=216,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[60] = {名称="符石道人",称谓="装备开运",模型="男人_道童",X=489,Y=201,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[67] = {名称="帮派竞赛主持人",称谓="帮战报名官员",模型="男人_将军",X=365,Y=159,方向=0,显示饰品=true,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[61] = {名称="贾有才",模型="仓库管理员",称谓="家具",X=280,Y=237,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[62] = {名称="贾有道",模型="仓库管理员",称谓="家具",X=282,Y=236,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[63] = {名称="贾有德",模型="仓库管理员",称谓="家具",X=284,Y=235,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[71] = {名称="贾有钱",模型="仓库管理员",称谓="符石",X=286,Y=234,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[72] = {名称="贾有品",模型="仓库管理员",称谓="符石",X=288,Y=233,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[73] = {名称="贾有鸣",模型="仓库管理员",称谓="符石",X=290,Y=232,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[64] = {名称="贾有钱",模型="仓库管理员",称谓="庭院",X=286,Y=234,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[65] = {名称="贾有品",模型="仓库管理员",称谓="庭院",X=288,Y=233,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[66] = {名称="贾有鸣",模型="仓库管理员",称谓="指南",X=290,Y=232,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[67] = {名称="贾富贵",模型="仓库管理员",称谓="精铁",X=292,Y=231,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[68] = {名称="贾百川",模型="仓库管理员",称谓="师门专卖",X=294,Y=230,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[69] = {名称="贾大朗",模型="仓库管理员",称谓="师门专卖",X=296,Y=229,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[70] = {名称="贾二郎",模型="仓库管理员",称谓="宝石专卖店",X=298,Y=228,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[71] = {名称="贾三郎",模型="仓库管理员",称谓="符石",X=300,Y=227,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[72] = {名称="贾四郎",模型="仓库管理员",称谓="符石",X=302,Y=226,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[73] = {名称="贾五郎",模型="仓库管理员",称谓="符石",X=304,Y=225,方向=0,事件ID=nil,执行事件="不执行"}
		假人[74] = {名称="帮派主管",模型="男人_兰虎",称谓="帮派总管",X=388,Y=13,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[75] = {名称="比武大会主持人",模型="男人_将军",X=111,Y=168,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=6}
		假人[76] = {名称="欧冶子之灵",模型="蜃气妖",X=145,Y=177,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=6}
		-- 假人[75] = {名称="超级神猴使者",模型="超级神猴",称谓="神兽",X=381,Y=60,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[76] = {名称="超级神鸡使者",模型="超级神鸡",称谓="神兽",X=392,Y=55,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[77] = {名称="鸿运鱼庄",模型="男人_店小二",称谓="鱼庄管理",X=450,Y=112,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[78] = {名称="元宵使者",模型="女人_灵鼠娃娃",X=229,Y=102,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[79] = {名称="四季使者",模型="女人_灵鼠娃娃",X=239,Y=107,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[80] = {名称="节日使者",模型="兔子怪",X=207,Y=152,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[81] = {名称="异兽使",模型="雾中仙",称谓="异兽使者",X=233,Y=161,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[82] = {名称="牛年牛逼",模型="踏云兽",X=375,Y=206,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[83] = {名称="护卫总管",模型="男人_马副将",X=178,Y=116,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[84] = {名称="月老",称谓="美满姻缘",模型="月老",X=417,Y=132,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[85] = {名称="顺丰快递",称谓="罗道人化身",模型="男人_道士",X=463,Y=239,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[86] = {名称="仙玉交易使者",称谓="包邮哦亲",模型="男人_老书生",X=46,Y=181,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[87] = {名称="怡红院接待",称谓="小桃红爱慕者",模型="男人_店小二",X=224,Y=245,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[88] = {名称="轩辕",称谓="轩辕剑冢",模型="剑侠客",武器="四法青云",X=10,Y=140,方向=0,染色方案=2,染色组={3,3,3},事件ID=nil,执行事件="不执行"}
		假人[89] = {名称="驯兽艺人",称谓="小龟赛跑主持人",模型="男人_兰虎",X=445,Y=62,方向=1,事件ID=nil,执行事件="不执行"}
		假人[90] = {名称="后裔英魂",模型="进阶狂豹人形",X=452,Y=48,方向=1,事件ID=nil,执行事件="不执行"}
		假人[32] = {名称="雁塔地宫守卫",模型="男人_马副将",X=445,Y=45,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[91] = {名称="礼部尚书",称谓="十一佳节活动",模型="考官2",X=236,Y=163,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0} --有对话
		-- 假人[92] = {名称="婵娟",称谓="中秋佳节活动",模型="女人_满天星",X=229,Y=158,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[93] = {名称="西门御林军",模型="男人_马副将",X=59,Y=129,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	    假人[94] = {名称="东门御林军",模型="男人_马副将",X=235,Y=40,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
  --   		假人[95] = {名称="超级神猪使者",模型="超级神猪",称谓="神兽",X=390,Y=50,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[96] = {名称="超级小白龙使者",模型="超级小白龙",称谓="神兽",X=382,Y=54,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[97] = {名称="超级神鼠使者",模型="超级神鼠",称谓="神兽",X=397,Y=58,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[98] = {名称="超级猪小戒使者",模型="超级猪小戒",称谓="神兽",X=385,Y=63,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[99] = {名称="超级神狗使者",模型="超级神狗",称谓="神兽",X=375,Y=56,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[100] = {名称="超级飞天使者",模型="超级飞天",称谓="神兽",X=382,Y=43,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[101] = {名称="超级土地公公使者",模型="超级土地公公",称谓="神兽",X=362,Y=52,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[102] = {名称="三圣化身",模型="进阶灵符女娲",称谓="寻梦三圣乡",X=325,Y=211,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[103] = {名称="威武大将军",模型="男人_程咬金",称谓="将军令",X=204,Y=116,方向=0,事件ID=nil,执行事件="不执行"}
		假人[104] = {名称="副本官员",模型="考官2",X=200,Y=119,方向=0,事件ID=nil,执行事件="不执行"}
--		假人[105] = {名称="配饰系统",模型="超级筋斗云",称谓="锦衣碎片兑换",X=213,Y=112,方向=0,事件ID=nil,执行事件="不执行"}	--定制修改
		假人[105.1] = {名称="活跃度使者",模型="考官2",称谓="活跃度兑换",X=223,Y=107,方向=0,事件ID=nil,执行事件="不执行"}	--定制修改
--		假人[105] = {名称="自动抓鬼",模型="兔子怪",X=213,Y=112,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[105] = {名称="要来一发吗",称谓="甜心小姐姐",模型="芙蓉仙子",染色方案=55,染色组={1,1,0},X=215,Y=101,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[106] = {名称="三界包打听",模型="男人_诗中仙",称谓="三界包打听",X=229,Y=109,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[107] = {名称="往生界",模型="毗舍童子",称谓="众生俱往生",X=213,Y=116,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[108] = {名称="福禄童子",模型="进阶小仙灵",称谓="新春活动",X=239,Y=164,方向=1,事件ID=nil,执行事件="不执行",显示饰品=true}
		假人[109] = {名称="师徒使者",模型="男人_诗中仙",称谓="",X=323,Y=76,方向=0,事件ID=nil,执行事件="不执行"}
		-- 假人[110] = {名称="打工魂打工人",称谓="天下皆是打工人",模型="逍遥生",武器="秋水人家",X=226,Y=129,方向=0,染色方案=2,染色组={3,3,3},事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[111] = {名称="神之化身",称谓="笑傲群英",模型="剑侠客",武器="四法青云",X=235,Y=124,方向=0,染色方案=2,染色组={3,3,3},事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[112] = {名称="南风♂知意♀",模型="狐狸精",称谓="怡红院博士",X=228,Y=259,方向=0,事件ID=nil,执行事件="不执行"}
   		-- 假人[113] = {名称="赌神发哥",称谓="西游周润发",模型="剑侠客",武器="冷月",X=291,Y=221,方向=0,染色方案=2,染色组={3,3,3},事件ID=nil,执行事件="不执行"}
		假人[114] = {名称="房都尉",模型="男人_马副将",X=375,Y=206,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[115] = {名称="超会超市",模型="男人_兰虎",称谓="超级会员藏宝阁",X=420,Y=98,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[116] = {名称="黑市老板",模型="男人_兰虎",称谓="黑市管理员",X=294,Y=265,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[117] = {名称="聚宝仙阁",模型="男人_兰虎",称谓="至尊聚宝阁",X=247,Y=110,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[118] = {名称="人家叫俺虎哥",称谓="威震八方",模型="虎头怪",武器="八卦",X=221,Y=141,方向=0,染色方案=2,染色组={3,3,3},事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[119] = {名称="牌中仙",模型="男人_诗中仙",称谓="梦幻棋牌使",X=458,Y=80,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[120] = {名称="宝石商人",模型="男人_财主",X=499,Y=138,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[121] = {名称="红线童子",模型="小丫丫",称谓="",X=431,Y=149,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[122] = {名称="骠骑大将军",模型="男人_将军",X=117,Y=61,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[123] = {名称="孙二娘",模型="女人_程夫人",称谓="茶颜悦色",X=335,Y=175,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[124] = {名称="地宫卫士",模型="护卫",称谓="雁塔地宫",X=445,Y=44,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[125] = {名称="丞相管家",模型="男人_师爷",称谓="突厥王牌",X=161,Y=153,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[126] = {名称="殷府护卫",模型="护卫",X=181,Y=171,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[127] = {名称="殷府护卫",模型="护卫",X=195,Y=164,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[128] = {名称="游方货商",模型="男人_特产商人",X=416,Y=215,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[129] = {名称="怜儿姑娘",模型="小桃红",X=277,Y=175,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[130] = {名称="赵美人",模型="普陀_接引仙女",X=4,Y=134,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[131] = {名称="袁守诚",模型="男人_太上老君",X=177,Y=265,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[132] = {名称="龙孙",模型="小白龙",X=169,Y=266,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		 假人[133] = {名称="二宝",模型="小丫丫",X=491,Y=89,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[134] = {名称="时事通",模型="和尚2",X=491,Y=89,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[135] = {名称="风云使者",模型="至尊宝",称谓="风云争霸赛",X=255,Y=113,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[136] = {名称="西游传说",模型="唐僧",称谓="传说副本",X=257,Y=167,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[137] = {名称="长安特产货商",模型="男人_特产商人",称谓="",X=381,Y=121,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[138] = {名称="武神之战",模型="律法女娲",称谓="武神坛之乱",X=420,Y=77,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[139] = {名称="卖烟花的兔子",模型="兔子怪",X=391,Y=206,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[140] = {名称="茶铺罗大爷",模型="男人_老孙头",称谓="自摸关三家",X=421,Y=255,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[141] = {名称="小飞天",模型="超级飞天",称谓="嘉年华",X=242,Y=166,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[144] = {名称="戴胄",模型="考官2",X=150,Y=96,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[142] = {名称="木桩",模型="木桩",称谓="伤害测试",X=375,Y=169,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[143] = {名称="云游仙人",模型="符石道人",X=268,Y=93,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}

    --书香斋
	elseif 地图ID == 1019 then
	    假人[1] = {名称="颜如羽",模型="男人_老书生",X=17,Y=19,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1025 then--冯记铁铺
		假人[1] = {名称="冯铁匠",称谓="",模型="冯铁匠",X=30,Y=22,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[2] = {名称="冯冯",称谓="合成能手",模型="男人_武器店老板",X=32,Y=20,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="打铁炉",模型="物件_打铁炉",X=25,Y=15,方向=0,事件ID=nil,执行事件="物件_打铁炉",小地图名称颜色=0}
	elseif 地图ID == 1033 then
		假人[1] = {名称="罗百万",模型="罗百万",X=37,Y=41,方向=3,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="小桃红",模型="少女",X=42,Y=39,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
   		假人[3] = {名称="陈妈妈",模型="陈妈妈",X=33,Y=33,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 金銮殿
	elseif 地图ID == 1044 then
		假人[1] = {名称="魏征",模型="考官2",X=70,Y=52.5,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="戴胄",模型="考官2",X=78.5,Y=57.5,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="房玄龄",模型="考官2",X=54.5,Y=61,方向=3,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="杜如晦",模型="考官2",X=64.5,Y=67,方向=3,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="李世民",模型="皇帝",X=48,Y=49,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 长安酒店
	elseif 地图ID == 1028 then
		假人[1] = {名称="店小二",称谓="挖宝图任务",模型="男人_店小二",X=24,Y=32,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="酒店老板",模型="男人_酒店老板",X=43,Y=26,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 大唐官府
	elseif 地图ID == 1198 then
		假人[2] = {名称="程夫人",模型="女人_程夫人",X=28,Y=26,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="丫鬟",模型="女人_丫鬟",X=24,Y=28,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="程府护卫",模型="护卫",X=89,Y=95,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="程府护卫",模型="护卫",X=121.5,Y=82.4,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[6] = {名称="程府护卫",模型="护卫",X=128,Y=79,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[7] = {名称="程府护卫",模型="护卫",X=155,Y=63,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[1] = {名称="传送护卫",称谓="传送长安",模型="护卫",X=72,Y=61,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1054 then
		假人[1] = {名称="程咬金",称谓="门派师傅",模型="男人_程咬金",X=21,Y=21,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	--南北杂货铺
	elseif 地图ID == 1015  then
		假人[1] = {名称="杂货店老板",模型="男人_巫医",X=20,Y=16,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 长安药店
	elseif 地图ID == 1016  then
		假人[1] = {名称="药店老板",模型="男人_药店老板",X=13.5,Y=18.7,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	--平安福寿
	elseif 地图ID == 1021 then
	    假人[1] = {名称="福寿店老板",模型="男人_老伯",X=27,Y=15,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1023 then
	    假人[1] = {名称="王武",模型="男人_兰虎",X=26,Y=20,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 长安镖局
	elseif 地图ID == 1024 then
		假人[1] = {名称="郑镖头",称谓="",模型="男人_镖头",X=31,Y=25,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 长安兵铁铺
	elseif 地图ID == 1020 then
		假人[1] = {名称="武器店掌柜",模型="男人_老孙头",X=13,Y=17,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="武器店老板",模型="男人_武器店老板",X=22,Y=14,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="打铁炉",模型="物件_打铁炉",X=28,Y=19,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 国子监
	elseif 地图ID == 1026 then
		假人[1] = {名称="吴举人",模型="男人_书生",X=26,Y=25,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[2] = {名称="夜行客",模型="吸血鬼",X=45,Y=19,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 云来酒店
	elseif 地图ID == 1030 then
		假人[1] = {名称="酒店老板",模型="男人_酒店老板",X=36,Y=20,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 化生寺
	elseif 地图ID == 1002 then
		假人[1] = {名称="慧静",模型="男人_胖和尚",X=113,Y=47,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="疥癞和尚",模型="男人_胖和尚",X=34,Y=78,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="接引僧",称谓="传送长安",模型="男人_胖和尚",X=58,Y=66,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="慧海",模型="男人_胖和尚",X=46,Y=60,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="慧悲",模型="男人_胖和尚",X=20,Y=51,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[6] = {名称="空慈方丈",模型="男人_方丈",X=120,Y=18,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1528 then
		假人[1] = {名称="慧明",模型="男人_胖和尚",X=10,Y=21,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="法明长老",模型="男人_老和尚",称谓="剧情技能",X=30,Y=21,方向=3,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1043 then
		假人[1] = {名称="空度禅师",称谓="门派师傅",模型="男人_老和尚",X=21,Y=16,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 丞相府
	elseif 地图ID == 1049 then
		假人[1] = {名称="殷丞相",模型="魏征",X=31,Y=29,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="殷夫人",模型="孟大娘",X=16,Y=40,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1056 then
		假人[1] = {名称="秦夫人",模型="女人_程夫人",X=24,Y=18,方向=1,事件ID=nil,执行事件="不执行"}
	elseif 地图ID == 1057 then
	    假人[1] = {名称="秦琼",模型="秦琼",X=31,Y=29,方向=0,事件ID=nil,执行事件="不执行"}
	-- 长安饰品店
	elseif 地图ID == 1017 then
		假人[1] = {名称="饰品店老板",模型="女人_赵姨娘",X=20,Y=18,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 长安衣服店
	elseif 地图ID == 1022 then
		假人[1] = {名称="服装店老板",模型="男人_服装店老板",X=32,Y=18,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="张裁缝",模型="男人_服装店老板",X=10,Y=23,方向=3,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="缝纫台",模型="物件_缝纫台",X=16,Y=27,方向=3,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 傲来国
	elseif 地图ID == 1092 then
		假人[1] = {名称="渔夫",称谓="钓鱼",模型="男人_钓鱼",X=150,Y=143,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="船夫",称谓="传送东海湾",模型="男人_驿站老板",X=172,Y=136,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="仙岛引路人",称谓="传送蓬莱仙岛",模型="男人_诗中仙",X=21.2,Y=53.8,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="驿站老板",称谓="传送长安城",模型="男人_驿站老板",X=50,Y=59,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="超级巫医",模型="男人_巫医",X=49,Y=119,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[6] = {名称="蝴蝶妹妹",模型="蝴蝶仙子",X=64,Y=101,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[7] = {名称="九头精怪",模型="九头精怪",X=34,Y=61,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[8] = {名称="游泳报名官",模型="雨师",称谓="游泳比赛",X=145,Y=62,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[9] = {名称="偷偷怪",模型="兔子怪",X=136,Y=19,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[10] = {名称="金毛猿",模型="马猴",称谓="幻域迷宫",X=198,Y=13,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[11] = {名称="傲来珍品商人",模型="珍品商人",X=56,Y=38,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[12] = {名称="云游道人",模型="男人_道士",X=106,Y=44,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[13] = {名称="仓库管理员",模型="仓库管理员",X=143,Y=50,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=5}
		假人[14] = {名称="傲来凶兽",模型="百足将军",X=186,Y=82,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=5}
		假人[15] = {名称="傲来货商",称谓="",模型="男人_特产商人",X=184,Y=46,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[16] = {名称="齐达内",称谓="傲来守门员",模型="天将",X=119,Y=88,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[17] = {名称="搁浅的玄武",模型="大海龟",X=107,Y=122,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[18] = {名称="傲来货商",称谓="",模型="男人_特产商人",X=91,Y=43,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[19] = {名称="傲来社区使者",模型="男人_驿站老板",X=77,Y=70,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1093 then
		假人[1] = {名称="王福来",模型="男人_酒店老板",X=21,Y=24,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="慕容先生",模型="考官1",X=44,Y=18,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1104 then
		假人[1] = {名称="沈妙衣",模型="男人_药店老板",X=14,Y=21,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
    elseif 地图ID == 1105 then
		假人[1] = {名称="杂货店老板",模型="男人_巫医",X=20,Y=13,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}

	elseif 地图ID == 1101 then
		假人[1] = {名称="杜天",模型="男人_兰虎",X=15,Y=15,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1095 then
		假人[1] = {名称="牛师傅",模型="男人_服装店老板",X=16,Y=19,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 女儿村
	elseif 地图ID == 1142 then
		假人[2] = {名称="翠花",模型="女人_翠花",X=73,Y=96,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="栗栗娘",模型="女人_栗栗娘",X=102,Y=56,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="柳飞絮",模型="普陀_接引仙女",X=23,Y=88,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="绿儿",模型="女人_绿儿",X=77,Y=68,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[1] = {名称="接引女使",称谓="传送长安",模型="女人_丫鬟",X=18.6,Y=18.8,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1143 then
		假人[1] = {名称="孙婆婆",称谓="门派师傅",模型="孙婆婆",X=26,Y=21,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 方寸
	elseif 地图ID == 1137 then
		假人[1] = {名称="菩提老祖",称谓="门派师傅",模型="菩提老祖",X=45,Y=30,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1230 then--秽气巢林
		假人[1] = {名称="灵木宝树",模型="灵木宝树",X=132,Y=83,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
    elseif 地图ID == 2002 then--无源之水
		假人[1] = {名称="灵木宝树树根",模型="灵木宝树",X=33,Y=25,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 比丘国
	elseif 地图ID == 1232 then
		假人[1] = {名称="彼岸",称谓="化劫成仙",模型="曼珠沙华",X=35,Y=48,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="鼎灵圣君",称谓="比丘国主",模型="男人_杨戬",X=131,Y=10,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="厄难魔君",称谓="比丘国师",模型="二大王",X=106,Y=54,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="秋月",称谓="女魔头",模型="进阶幽灵",X=142,Y=16,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 柳林坡
	elseif 地图ID == 1233 then
		假人[1] = {名称="柳林仙",称谓="万年精怪",模型="蜃气妖",X=71,Y=47,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="邪恶幻影",模型="狂豹人形",X=42,Y=61,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="寻仙客",称谓="柳林小妖",模型="千年蛇魅",X=24,Y=8,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="无名",称谓="接引仙踪林",模型="锦毛貂精",X=144,Y=18,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	--天宫
	elseif 地图ID == 1111 then
		假人[1] = {名称="守门天兵",称谓="传送长寿郊外",模型="天兵",X=224,Y=154,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="守门天兵",模型="天兵",X=231.5,Y=149.5,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="守门天兵",模型="天兵",X=238.8,Y=146,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="接引仙女",称谓="传送长安",模型="芙蓉仙子",X=148.5,Y=109.8,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[6] = {名称="守门道童",模型="男人_道童",X=25,Y=146.5,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[7] = {名称="守门道童",模型="男人_道童",X=32,Y=144,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="天牢守卫",模型="天兵",X=232,Y=22,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[8] = {名称="顺风耳",模型="天兵",X=104,Y=105,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[9] = {名称="马真人",称谓="宠物修炼",模型="男人_道士",X=229,Y=95,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[10] = {名称="天牢守卫",模型="天将",X=228,Y=25,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[11] = {名称="水军统领",模型="男人_将军",X=175,Y=28,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[12] = {名称="封天魔尊",称谓="封天浩劫",模型="皇帝",X=18,Y=39,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[13] = {名称="护天战神",称谓="天宫神卫",模型="大力金刚",X=43,Y=57,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[14] = {名称="流光月影",模型="雾中仙",X=210,Y=46,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[15] = {名称="流光右卫",模型="踏云兽",X=201,Y=84,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[16] = {名称="流光仙匪",模型="天将",X=140,Y=120,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[17] = {名称="流光仙王",模型="天兵",X=53,Y=149,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[18] = {名称="大力神灵",模型="天将",X=17,Y=38,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}

	-- 凌霄殿
	elseif 地图ID == 1112 then
		假人[1] = {名称="李靖",称谓="门派师傅",模型="李靖",X=26,Y=43,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="杨戬",称谓="降妖伏魔",模型="男人_杨戬",X=54.5,Y=53.4,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="玉皇大帝",模型="男人_玉帝",X=29.8,Y=34.8,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="王母娘娘",模型="女人_王母",X=35.0,Y=31.5,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="哪吒",模型="男人_哪吒",X=65.7,Y=46.8,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	--兜率宫
	elseif 地图ID == 1113 then
		假人[1] = {名称="太上老君",模型="男人_太上老君",X=22,Y=19,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="炼丹道士",模型="男人_道士",X=35,Y=23,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="金童子",模型="男人_道童",X=23,Y=23,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1114 then
		假人[1] = {名称="吴刚",模型="男人_打铁",X=12,Y=60,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="月香侍女",模型="普陀山_接引仙女",X=101,Y=33,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="康太尉",模型="天兵",X=97,Y=35,方向=3,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[4] = {名称="流光幽莹",模型="幽萤娃娃",X=107,Y=68	,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[5] = {名称="流光灵兽",模型="踏云兽",X=56,Y=12,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[6] = {名称="流光火鼠",模型="鼠先锋",X=25,Y=16,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 魔王寨
	elseif 地图ID == 1512 then
		假人[1] = {名称="守门牛妖",模型="牛妖",X=13,Y=75,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="守门牛妖",模型="牛妖",X=17,Y=78,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="传送牛妖",称谓="传送长安",模型="牛妖",X=87,Y=13,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1145 then
		假人[1] = {名称="牛魔王",称谓="门派师傅",模型="牛魔王",X=33,Y=21,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="九头精怪",称谓="种族任务",模型="九头精怪",X=17,Y=22,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 五庄
	elseif 地图ID == 1146 then
		假人[1] = {名称="接引道童",称谓="传送长安",模型="男人_道童",X=46.5,Y=37,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="青云子",称谓="缘秒不可言",模型="男人_道士",X=89,Y=15,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="清风",模型="男人_道童",X=49,Y=35,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="明月",模型="男人_道童",X=63,Y=42,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 乾坤殿
	elseif 地图ID == 1147 then
		假人[1] = {名称="镇元子",称谓="门派师傅",模型="镇元子",X=25.5,Y=20.5,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 大雄宝殿
	elseif 地图ID == 1153 then
		假人[1] = {名称="酒肉和尚",模型="雨师",X=34,Y=21,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="玄奘",模型="唐僧",X=13,Y=21,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 狮驼岭
	elseif 地图ID == 1131 then
		假人[1] = {名称="守山小妖",模型="雷鸟人",X=114,Y=8,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="守山小妖",模型="雷鸟人",X=119.2,Y=12.5,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="传送小妖",称谓="传送长安",模型="雷鸟人",X=111,Y=71,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="受伤的统领",模型="黑山老妖",X=29,Y=82,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="猪万能",模型="野猪",X=75,Y=54,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 狮王洞
	elseif 地图ID == 1132 then
		假人[1] = {名称="二大王",模型="二大王",X=25,Y=15,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1133 then
		假人[1] = {名称="三大王",模型="三大王",X=20,Y=17,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1134 then
		假人[1] = {名称="大大王",称谓="门派师父",模型="大大王",X=29.2,Y=19.2,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 盘丝岭
	elseif 地图ID == 1513 then
		假人[1] = {名称="女妖",模型="芙蓉仙子",X=110,Y=104,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="金琉璃",模型="如意仙子",X=64.5,Y=81,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="栗栗儿",模型="女人_丫鬟",X=73,Y=85,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="看门小妖",模型="树怪",X=171.8,Y=27.5,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="看门小妖",模型="树怪",X=185,Y=36,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[6] = {名称="引路小妖",称谓="传送长安",模型="蝴蝶仙子",X=180,Y=22,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1144 then --盘丝洞
		假人[1] = {名称="白晶晶",称谓="门派师傅",模型="白晶晶",X=32,Y=15,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="春十三娘",模型="春十三娘",X=25,Y=41,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 神木林
	elseif 地图ID == 1138 then
		假人[1] = {名称="引路族民",称谓="传送长安",模型="巫师",X=54,Y=92,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="云中月",模型="巫师",X=22,Y=68,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="云小奴",模型="女人_云小奴",X=17,Y=48,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="满天星",模型="女人_满天星",X=25,Y=37,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1154 then
		假人[1] = {名称="巫奎虎",称谓="门派师傅",模型="巫奎虎",X=33.5,Y=32,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 碗子山
	elseif 地图ID == 1228 then
		假人[1] = {名称="碗子山土地",模型="男人_土地",称谓="传送无底洞",X=25,Y=173,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="天尊星",模型="天兵",称谓="突厥秘卫",X=57,Y=130,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 无底洞
	elseif 地图ID == 1139 then
		假人[1] = {名称="璎珞",模型="幽萤娃娃",X=85,Y=49,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="墨衣",模型="修罗傀儡妖",X=4,Y=54,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="红莲",模型="修罗傀儡妖",X=29,Y=15,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="接引小妖",称谓="传送长安",模型="幽萤娃娃",X=47,Y=120,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1156 then
		假人[1] = {名称="地涌夫人",称谓="门派师傅",模型="地涌夫人",X=49.6,Y=27.6,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="灵鼠娃娃",模型="女人_灵鼠娃娃",X=55,Y=33,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
      --墨家村
	elseif 地图ID == 1218 then
		假人[1] = {名称="墨家驿站",称谓="传送长安城",模型="男人_驿站老板",X=21,Y=134,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="莫守",称谓="墨家机关",模型="机关人",X=51,Y=117,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="墨鱼",模型="男人_老孙头",X=74,Y=133,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="墨名",称谓="天机际遇",模型="男人_村长",X=21,Y=134,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="墨小二",模型="小丫丫",X=33,Y=82,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[6] = {名称="莫非",称谓="墨家机关",模型="机关鸟",X=73,Y=68,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[7] = {名称="行脚商",模型="男人_老财",X=38,Y=57,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[8] = {名称="墨家金刚",称谓="墨家护法",模型="大力金刚",X=36,Y=15,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[9] = {名称="天机城接引",称谓="传送天机城",模型="太白金星",X=89,Y=25,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[10] = {名称="墨子",称谓="墨家巨子",模型="巨子",X=30,Y=28,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	--墨家禁地
    elseif 地图ID == 1218 then
		假人[1] = {名称="禁地统领",模型="鬼将",X=33,Y=47,方向=3,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="禁地巨弩",模型="连弩车",X=107,Y=45,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="公输巨甲",模型="机关人人形",X=78,Y=101,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="超级巫医",模型="男人_巫医",X=30,Y=76,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="受损的墨家机关",模型="机关鸟",X=104,Y=89,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 西梁女国
	-- elseif 地图ID == 1040 then
	-- 	假人[1] = {名称="驿站老板",称谓="传送朱紫国",模型="男人_驿站老板",X=18,Y=71,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 	假人[2] = {名称="驿站老板",称谓="传送丝绸之路",模型="男人_驿站老板",X=15,Y=114,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 丝绸之路
	elseif 地图ID == 1235 then
		假人[1] = {名称="驿站老板",称谓="传送西梁女国",模型="男人_驿站老板",X=464,Y=92,方向=3,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="驿站老板",称谓="传送长安",模型="男人_驿站老板",X=351,Y=29,方向=3,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="曼陀罗",称谓="沙漠谜域",模型="花妖",X=314,Y=57,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="灵蛇",模型="超级腾蛇",X=177,Y=37,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="拦路大盗",模型="进阶夜罗刹",X=420,Y=76,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[6] = {名称="李樵夫",模型="樵夫",X=512,Y=63,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[7] = {名称="河老伯",模型="男人_老孙头",X=552,Y=63,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[8] = {名称="孙绣娘",模型="女人_云小奴",X=574,Y=72,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[9] = {名称="菜小坤",模型="男人_小白龙",X=616,Y=57,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[10] = {名称="菜虚坤",模型="男人_衙役",X=596,Y=49,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[11] = {名称="东盛",模型="男人_书生",X=557,Y=48,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[12] = {名称="严村长",模型="男人_村长",X=534,Y=50,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[13] = {名称="说书客",模型="男人_老书生",X=540,Y=90,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[14] = {名称="高丽游客",模型="巫师",X=484,Y=25,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[15] = {名称="土灵龙",模型="蛟龙",X=468,Y=35,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 麒麟山
	elseif 地图ID == 1210 then
		假人[1] = {名称="驿站老板",称谓="传送宝象国",模型="男人_驿站老板",X=70,Y=140,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		--假人[2] = {名称="红孩儿",称谓="圣婴大王",模型="小魔头",X=188,Y=110,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="红孩儿",称谓="圣婴大王",模型="小魔头",X=178,Y=106,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 蓬莱仙岛
	elseif 地图ID == 1207 then
		假人[1] = {名称="驿站老板",称谓="传送傲来国",模型="男人_驿站老板",X=9.2,Y=101.8,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 长寿村
	elseif 地图ID == 1070 then
		假人[1] = {名称="慧觉和尚",模型="男人_胖和尚",X=131.2,Y=143.4,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="陆萧然",称谓="任务链",模型="男人_老书生",X=20,Y=185,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="蝴蝶女",模型="蝴蝶仙子",X=47,Y=71,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="太白金星",模型="太白金星",称谓="仙族任务",X=53,Y=194,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="海老先生",模型="男人_村长",X=107,Y=175,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[6] = {名称="钟书生",模型="男人_书生",X=43,Y=51,方向=2,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[7] = {名称="许姑娘",模型="少女",X=76,Y=35,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[8] = {名称="仓库管理员",模型="仓库管理员",X=100,Y=149,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[9] = {名称="南极仙翁",模型="南极仙翁",称谓="剧情技能",X=107,Y=22,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[10] = {名称="长寿珍品商人",模型="珍品商人",X=109,Y=158,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[11] = {名称="马婆婆",模型="女人_孟婆",X=45,Y=143,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[12] = {名称="超级巫医",模型="男人_巫医",X=114,Y=99,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[13] = {名称="蕴子仙",模型="天兵",X=44,Y=134,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[14] = {名称="酒馆老板",模型="男人_酒店老板",X=95,Y=85,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[15] = {名称="许大娘",模型="许大娘",X=80,Y=144,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[16] = {名称="凤凰姑娘",模型="普陀山_接引仙女",X=24,Y=103,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[17] = {名称="毛驴张",模型="张老头",X=79,Y=81,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[19] = {名称="长寿货商",模型="男人_特产商人",X=145,Y=156,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[20] = {名称="长寿货商",模型="男人_特产商人",X=73,Y=186,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[21] = {名称="长寿社区使者",模型="男人_驿站老板",X=117,Y=148,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[22] = {名称="长寿渔夫",模型="海产收购商",X=80,Y=31,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[23] = {名称="勾魂索PK申请人",模型="男人_兰虎",X=63,Y=119,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[24] = {名称="黄真",模型="羽灵神",称谓="天罡星任务",锦衣="渡劫",武器="弦月",X=12,Y=106,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1083 then
		假人[1] = {名称="裁缝张",模型="男人_服装店老板",X=23.2,Y=20,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1085 then
		假人[1] = {名称="武器店老板",模型="男人_武器店老板",X=15,Y=21,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	-- 月宫
	-- elseif 地图ID == 1114 then
	-- 	假人[1] = {名称="吴刚",模型="吴刚",X=12,Y=63,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}

	-- 小雷音寺
	elseif 地图ID == 1204 then
		假人[1] = {名称="丹青生",模型="男人_书生",X=22,Y=48,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1205 then
		假人[1] = {名称="蚩尤元神",模型="蚩尤",X=61,Y=43,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="武神坛传送",模型="巫师",X=74,Y=47,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="巫师",模型="巫师",X=58,Y=54,方向=3,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1206 then
		假人[1] = {名称="长安传送人",模型="太白金星",X=186,Y=40,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[2] = {名称="歌泣メ",称谓="紫禁城代表队",模型="骨精灵",X=115+(4.2*1),Y=78,方向=4,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[3] = {名称="Tercel°倾寒",称谓="雄鹰岭代表队",模型="鬼潇潇",X=115+(4.2*2),Y=78,方向=4,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[4] = {名称="弑ㄨ魔法琳琳",称谓="蝴蝶泉代表队",模型="玄彩娥",X=115+(4.2*3),Y=77,方向=4,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[5] = {名称="江湖笑で",称谓="西栅老街代表队",模型="杀破狼",X=115+(4.2*4),Y=77,方向=4,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[6] = {名称="史竟呈来也",称谓="姑苏城代表队",模型="龙太子",X=115+(4.2*5),Y=77,方向=4,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[7] = {名称="ぃ☆十月．",称谓="钱塘江代表队",模型="神天兵",X=115+(4.2*6),Y=77,方向=4,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[8] = {名称="玲珑づ小小龙",称谓="太和殿代表队",模型="玄彩娥",X=115+(4.2*7),Y=78,方向=4,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[9] = {名称="然こ低调",称谓="星海湾代表队",模型="杀破狼",X=115+(4.2*8),Y=78,方向=4,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		-- 假人[10] = {名称="ぃ☆给力．",称谓="神兽="长屿硐天代表队",模型="神天兵",X=115+(4.2*9),Y=78,方向=4,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1020 then
		假人[1] = {名称="武器店掌柜",模型="男人_老孙头",X=13,Y=17,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="武器店老板",模型="男人_武器店老板",X=22,Y=14,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="打铁炉",模型="物件_打铁炉",X=28,Y=19,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1511 then --帮战地图
		假人[1] = {名称="帮派师爷藍",模型="男人_师爷",X=18,Y=32,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="帮派师爷紅",模型="男人_师爷",X=136,Y=81,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="帮派竞赛接引人",模型="男人_老伯",X=36,Y=34,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="帮派竞赛接引人",模型="男人_老伯",X=113,Y=82,方向=	1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}

	elseif 地图ID == 7018 then
		假人[1] ={名称="超级巫医",模型="男人_巫医",X=37,Y=19,方向=0,事件ID=nil,执行事件="不执行",执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 7019 then
		假人[1] ={名称="超级巫医",模型="男人_巫医",X=113,Y=69,方向=1,事件ID=nil,执行事件="不执行",执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 7020 then
		假人[1] ={名称="超级巫医",模型="男人_巫医",X=114,Y=47,方向=0,事件ID=nil,执行事件="不执行",执行事件="不执行",小地图名称颜色=0}

	elseif 地图ID == 1531 then --广寒宫
		假人[1] = {名称="嫦娥",模型="嫦娥",X=31,Y=20,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 10000 then --云影储备室
    	假人[1] = {名称="传送大使",模型="男人_马副将",X=17,Y=54,方向=0,显示饰品=true,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
    elseif 地图ID == 10001 then --虹光储备室
    	假人[1] = {名称="传送大使",模型="男人_马副将",X=17,Y=54,方向=0,显示饰品=true,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
    elseif 地图ID == 10002 or 地图ID == 10012 then --女儿村
        假人[1] ={名称="超级巫医",模型="男人_巫医",X=71,Y=89,方向=1,事件ID=nil,执行事件="不执行",执行事件="不执行",小地图名称颜色=0}
	    假人[2] ={名称="超级巫医",模型="男人_巫医",X=15,Y=46,方向=0,事件ID=nil,执行事件="不执行",执行事件="不执行",小地图名称颜色=0}
	    假人[3] ={名称="传送大使",模型="男人_马副将",X=60,Y=14,方向=1,事件ID=nil,执行事件="不执行",执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 10003 or 地图ID == 10013 then --狮驼岭
     --    假人[1] ={名称="超级巫医",模型="男人_巫医",X=71,Y=89,方向=1,事件ID=nil,执行事件="不执行",执行事件="不执行",小地图名称颜色=0}
	    -- 假人[2] ={名称="超级巫医",模型="男人_巫医",X=15,Y=46,方向=0,事件ID=nil,执行事件="不执行",执行事件="不执行",小地图名称颜色=0}
	    假人[1] ={名称="传送大使",模型="男人_马副将",X=112,Y=78,方向=1,事件ID=nil,执行事件="不执行",执行事件="不执行",小地图名称颜色=0}
    elseif 地图ID == 10004 or 地图ID == 10014 then --墨家村
        假人[1] ={名称="超级巫医",模型="男人_巫医",X=18,Y=127,方向=0,事件ID=nil,执行事件="不执行",执行事件="不执行",小地图名称颜色=0}
	    假人[2] ={名称="超级巫医",模型="男人_巫医",X=70,Y=77,方向=1,事件ID=nil,执行事件="不执行",执行事件="不执行",小地图名称颜色=0}
	    假人[3] ={名称="传送大使",模型="男人_马副将",X=42,Y=13,方向=0,事件ID=nil,执行事件="不执行",执行事件="不执行",小地图名称颜色=0}
    elseif 地图ID == 10005 or 地图ID == 10015 then --花果山
        假人[1] ={名称="超级巫医",模型="男人_巫医",X=100,Y=103,方向=1,事件ID=nil,执行事件="不执行",执行事件="不执行",小地图名称颜色=0}
	    假人[2] ={名称="超级巫医",模型="男人_巫医",X=100,Y=59,方向=0,事件ID=nil,执行事件="不执行",执行事件="不执行",小地图名称颜色=0}
	    假人[3] ={名称="传送大使",模型="男人_马副将",X=82,Y=13,方向=0,事件ID=nil,执行事件="不执行",执行事件="不执行",小地图名称颜色=0}
    elseif 地图ID == 10006 or 地图ID == 10016 then --龙宫
        假人[1] ={名称="超级巫医",模型="男人_巫医",X=102,Y=74,方向=1,事件ID=nil,执行事件="不执行",执行事件="不执行",小地图名称颜色=0}
	    假人[2] ={名称="超级巫医",模型="男人_巫医",X=162,Y=43,方向=1,事件ID=nil,执行事件="不执行",执行事件="不执行",小地图名称颜色=0}
	    假人[3] ={名称="传送大使",模型="男人_马副将",X=190,Y=20,方向=1,事件ID=nil,执行事件="不执行",执行事件="不执行",小地图名称颜色=0}
    elseif 地图ID == 10007 or 地图ID == 10017 then --太岁府
        假人[1] ={名称="超级巫医",模型="男人_巫医",X=17,Y=41,方向=1,事件ID=nil,执行事件="不执行",执行事件="不执行",小地图名称颜色=0}
	    假人[2] ={名称="传送大使",模型="男人_马副将",X=10,Y=16,方向=1,事件ID=nil,执行事件="不执行",执行事件="不执行",小地图名称颜色=0}
    elseif 地图ID == 10008 or 地图ID == 10018 then --月宫
        假人[1] ={名称="传送大使",模型="男人_马副将",X=75,Y=16,方向=1,事件ID=nil,执行事件="不执行",执行事件="不执行",小地图名称颜色=0}
    elseif 地图ID == 10009 then
    	假人[1] ={名称="超级巫医",模型="男人_巫医",X=89,Y=85,方向=1,事件ID=nil,执行事件="不执行",执行事件="不执行",小地图名称颜色=0}
    elseif 地图ID == 2003 then
		假人[1] = {名称="猴爷爷",模型="长眉灵猴",X=115,Y=80,方向=0,任务显示=true,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
    elseif 地图ID == 2009 then
    	假人[1] ={名称="超级巫医",模型="男人_巫医",X=30,Y=27,方向=1,事件ID=nil,执行事件="不执行",执行事件="不执行",小地图名称颜色=0}
    	假人[2] ={名称="超级治疗师",模型="男人_酒店老板",X=50,Y=28,方向=1,事件ID=nil,执行事件="不执行",执行事件="不执行",小地图名称颜色=0}
    	假人[3] ={名称="竞技馆主持人",模型="男人_马副将",X=29,Y=38,方向=1,事件ID=nil,执行事件="不执行",执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 5002 then
		假人[1] = {名称="超级神龙",模型="超级神龙",X=39,Y=29,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="超级腾蛇",模型="超级腾蛇",X=14,Y=48,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="超级土地公公",模型="超级土地公公",X=32,Y=54,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="超级六耳猕猴",模型="超级六耳猕猴",X=17,Y=67,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="超级神鸡",模型="超级神鸡",X=49,Y=65,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[6] = {名称="超级玉兔",模型="超级玉兔",X=43,Y=78,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[7] = {名称="超级神猴",模型="超级神猴",X=32,Y=82,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[8] = {名称="超级神马",模型="超级神马",X=57,Y=83,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[9] = {名称="超级神羊",模型="超级神羊",X=17,Y=83,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[10] = {名称="超级孔雀",模型="超级孔雀",X=128,Y=44,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[11] = {名称="超级灵狐",模型="超级灵狐",X=119,Y=54,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[12] = {名称="超级筋斗云",模型="超级筋斗云",X=129,Y=67,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[13] = {名称="超级麒麟",模型="超级麒麟",X=90,Y=45,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[14] = {名称="超级大鹏",模型="超级大鹏",X=101,Y=73,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[15] = {名称="超级赤焰兽",模型="超级赤焰兽",X=110,Y=81,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[16] = {名称="超级白泽",模型="超级白泽",X=84,Y=40,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[17] = {名称="超级灵鹿",模型="超级灵鹿",X=72,Y=55,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[18] = {名称="超级大象",模型="超级大象",X=31,Y=71,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[19] = {名称="超级金猴",模型="超级金猴",X=44,Y=86,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[20] = {名称="超级大熊猫",模型="超级大熊猫",X=74,Y=83,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[21] = {名称="超级泡泡",模型="超级泡泡",X=94,Y=83,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[22] = {名称="超级神兔",模型="超级神兔",X=114,Y=70,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[23] = {名称="超级神虎",模型="超级神虎",X=127,Y=31,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[24] = {名称="超级神牛",模型="超级神牛",X=144,Y=27,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[25] = {名称="超级海豚",模型="超级海豚",X=139,Y=41,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[26] = {名称="超级人参娃娃",模型="超级人参娃娃",X=27,Y=62,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[27] = {名称="超级青鸾",模型="超级青鸾",X=19,Y=55,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[28] = {名称="超级神蛇",模型="超级神蛇",X=27,Y=40,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[29] = {名称="超级神狗",模型="超级神狗",X=26,Y=29,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[30] = {名称="超级小白龙",模型="超级小白龙",X=25,Y=22,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[31] = {名称="超级猪小戒",模型="超级猪小戒",X=15,Y=16,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[32] = {名称="超级神猪",模型="超级神猪",X=89,Y=64,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[33] = {名称="超级神鼠",模型="超级神鼠",X=73,Y=45,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 2010 then --蓝方
		假人[1] = {名称="帮派竞赛接引人",模型="男人_将军",X=132,Y=6,方向=1,显示饰品=true,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	    假人[2] = {名称="帮派师爷",模型="男人_师爷",X=139,Y=10,方向=1,显示饰品=true,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	    假人[3] = {名称="传送人1号",模型="男人_老伯",X=13,Y=95,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 2011 then --红方
		假人[1] = {名称="帮派竞赛接引人",模型="男人_将军",X=132,Y=6,方向=1,显示饰品=true,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	    假人[2] = {名称="帮派师爷",模型="男人_师爷",X=139,Y=10,方向=1,显示饰品=true,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	    假人[3] = {名称="传送人2号",模型="男人_老伯",X=13,Y=95,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 2012 then
		假人[1] = {名称="传送人1号",模型="男人_老伯",X=22,Y=16,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="传送人2号",模型="男人_老伯",X=101,Y=105,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 1197 then --比武场
--		假人[1] = {名称="比武大会传送人",模型="男人_将军",X=14,Y=55,方向=3,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="传送人2号",模型="男人_老伯",X=101,Y=105,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 5005 then
		假人[1] = {名称="昆仑仙",模型="雾中仙",X=44,Y=24,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID == 2013 then
		假人[1] = {名称="欧冶子",模型="蜃气妖",X=138,Y=77,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=6}
		假人[2] = {名称="欧冶子",模型="蜃气妖",X=150,Y=30,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=6}
		假人[3] = {名称="欧冶子",模型="蜃气妖",X=77,Y=37,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=6}
		假人[4] = {名称="欧冶子",模型="蜃气妖",X=85,Y=122,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=6}
	elseif 地图ID == 5958 then
		假人[1] = {名称="钟馗分身",模型="男人_钟馗",X=102,Y=26,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
	elseif 地图ID >= 3000 and  地图ID <= 3150 then
		local 取怪物={
				[1]="白熊",
				[2]="白熊",
				[3]="白熊",
				[4]="锦毛貂精",
				[5]="白熊",
				[6]="白熊",
				[7]="雨师",
				[8]="锦毛貂精",
				[9]="锦毛貂精",
				[10]="白熊",
				[11]="天兵",
				[12]="蚌精",
				[13]="蚌精",
				[14]="蚌精",
				[15]="蚌精",
				[16]="蚌精",
				[17]="蚌精",
				[18]="狂豹人形",
				[19]="狂豹人形",
				[20]="进阶狂豹人形",
				[21]="狂豹人形",
				[22]="雷鸟人",
				[23]="狂豹人形",
				[24]="雷鸟人",
				[25]="狂豹人形",
				[26]="雷鸟人",
				[27]="狂豹人形",
				[28]="巨力神猿",
				[29]="巨力神猿",
				[30]="蚌精",
				[31]="狂豹人形",
				[32]="狂豹人形",
				[33]="巨力神猿",
				[34]="幽萤娃娃",
				[35]="狂豹人形",
				[36]="锦毛貂精",
				[37]="幽萤娃娃",
				[38]="画魂",
				[39 ]="画魂",
				[40]="画魂",
				[41]="幽萤娃娃",
				[42]="混沌兽",
				[43]="混沌兽",
				[44]="混沌兽",
				[45]="白熊",
				[46]="天兵",
				[47]="画魂",
				[48]="夜罗刹",
				[49]="夜罗刹",
				[50]="进阶幽萤娃娃",
				[51]="白熊",
				[52]="葫芦宝贝",
				[53]="夜罗刹",
				[54]="葫芦宝贝" ,
				[55]="狂豹人形",
				[56]="巨力神猿",
				[57]="混沌兽",
				[58]="夜罗刹",
				[59]="夜罗刹",
				[60]="夜罗刹",
				[61]="狂豹人形",
				[62]="巴蛇",
				[63]="白熊",
				[64]="曼珠沙华",
				[65]="混沌兽" ,
				[66]="葫芦宝贝",
				[67]="曼珠沙华",
				[68]="噬天虎" ,
				[69]="锦毛貂精",
				[70]="鬼将",
				[71]="巴蛇",
				[72]="曼珠沙华",
				[73]="律法女娲",
				[74]="夜罗刹",
				[75]="噬天虎",
				[76]="金身罗汉",
				[77]="巨力神猿",
				[78]="鬼将",
				[79]="巨力神猿",
				[80]="进阶混沌兽",
				[81]="狂豹人形",
				[82]="进阶巴蛇",
				[83]="进阶混沌兽",
				[84]="进阶曼珠沙华",
				[85]="进阶噬天虎",
				[86]="进阶金身罗汉",
				[87]="进阶夜罗刹",
				[88]="鬼将",
				[89]="巨力神猿",
				[90]="进阶狂豹人形",
				[91]="狂豹人形",
				[92]="进阶巴蛇",
				[93]="进阶混沌兽",
				[94]="进阶曼珠沙华",
				[95]="金身罗汉",
				[96]="进阶金身罗汉",
				[97]="鬼将",
				[98]="鬼将",
				[99]="进阶夜罗刹",
				[100]="进阶毗舍童子",



			}


                        local cs=地图ID-3000+1

		local mx=取怪物[cs]
		假人[1] = {名称=mx,模型=mx,称谓="地宫守护兽",X=60,Y=41,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=6}
		假人[2] = {名称="超级巫医",模型="男人_巫医",X=79,Y=40,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=6}


		elseif 地图ID == 6033 then
		假人[1] = {名称="锄树力士",模型="至尊宝",X=89,Y=41,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="浇水力士",模型="赌霸天",X=103,Y=40,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="修桃力士",模型="吴刚",X=94,Y=49,方向=3,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="一千年桃树",模型="桃树",X=74,Y=22,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[5] = {名称="两千年桃树",模型="桃树",X=22,Y=34,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[6] = {名称="三千年桃树",模型="桃树",X=21,Y=59,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[7] = {名称="四千年桃树",模型="桃树",X=72,Y=96,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[8] = {名称="五千年桃树",模型="桃树",X=125,Y=82,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[9] = {名称="六千年桃树",模型="桃树",X=141,Y=52,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[10] = {名称="七千年桃树",模型="桃树",X=130,Y=27,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[11] = {名称="八千年桃树",模型="桃树",X=63,Y=57,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		elseif 地图ID == 6034 then
		假人[1] = {名称="造酒仙官",模型="护卫",X=25,Y=34,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="运水道人",模型="雨师",X=81,Y=85,方向=1,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="烧火童子",模型="小魔头",X=90,Y=45,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="盘槽力士",模型="风伯",X=114,Y=70,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		elseif 地图ID == 6037 then
		假人[1] = {名称="杨戬",模型="男人_杨戬",X=154,Y=116,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[2] = {名称="李靖",模型="李靖",X=166,Y=110,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[3] = {名称="玉皇大帝",模型="男人_玉帝",X=151,Y=107,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}
		假人[4] = {名称="哪吒",模型="男人_哪吒",X=171,Y=121,方向=0,事件ID=nil,执行事件="不执行",小地图名称颜色=0}

	end
	if 假人ID==nil then
		return 假人
    else
		return 假人[假人ID]
	end
end