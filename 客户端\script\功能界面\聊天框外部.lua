-- @Date:   2018-06-05 07:42:01
-- @Last Modified time: 2022-12-17 09:20:43
local DLL模块 = require("gsub")
local 聊天框 = {}
local 渲染区
local 计次 = 0
local x0 = false
local x0s = 0
临时高度 = 0
local ltzt = 引擎.场景.字体表.标题字体
local ltzt2 = 引擎.场景.字体表.华康14
local ltzt3 = 引擎.场景.字体表.普通字体___
local function 初始化(根,标题)
	DLL模块.初始化建窗口(引擎.取窗口句柄(),引擎.宽度,900+3,外部窗口宽度," 聊天窗口",0)
	DLL模块.width_height(引擎.取窗口句柄(),外部窗口宽度,全局游戏高度+23)
	if not 渲染区 then
		渲染区   = require("gge纹理类")():渲染目标(引擎.宽度,900+3,6)--背景高度--########################################################?自己修改?##########################################
	end
	引擎.场景.窗口.消息框.按钮_移动.确定按下 = nil
	return 聊天框
end

function 聊天框.关闭()
	DLL模块.Release()
end



function 聊天框.渲染开始()
	if 临时高度~=0 then
		临时高度 = 0
		DLL模块.width_height(引擎.取窗口句柄(),外部窗口宽度,全局游戏高度+23)
		return 1
	end
	if 计次==9 then--减少帧数 刷新频率 5   2
		计次 = 0
	else
		计次 = 计次+1
		-- print(11)
		return false
	end
	-- print(11123)
	--=============
	local xx,yy =引擎.外部.取鼠标坐标()--鼠标.x,鼠标.y--x,y
	local xxk = 引擎.场景.窗口.消息框
	if (xxk.按钮_左拉:是否选中(xx,yy) and 聊天框.鼠标按下(0)) then--事件判断()
        wbgb按钮开关=1
    elseif  (xxk.按钮_下拉:是否选中(xx,yy) and 聊天框.鼠标按下(0)) then
        wbgb按钮开关=2
    elseif (xxk.按钮_上拉:是否选中(xx,yy) and 聊天框.鼠标按下(0)) then
        wbgb按钮开关=3
    elseif (xxk.按钮_禁止:是否选中(xx,yy) and 聊天框.鼠标按下(0)) then
        wbgb按钮开关=4
    elseif (xxk.按钮_清屏:是否选中(xx,yy) and 聊天框.鼠标按下(0)) then
        wbgb按钮开关=5
    end
	引擎.渲染开始(渲染区)
	if 聊天框.鼠标按下(0) then
		x0s = 1
	end
	if x0s == 1 then
		x0 = true
	end
	if 聊天框.鼠标弹起(0) then
		x0 = false
		x0s = 0
		聊天框.弹起事件 = true
	end
	引擎.渲染清除()
	return true
end

function 聊天框.渲染结束()
	引擎.渲染结束()
	local 宽度 = DLL模块.研究群342119466(渲染区:锁定(),外部窗口宽度,全局游戏高度+23)
	渲染区:解锁()
	DLL模块.Rende()
end

function 聊天框.显示(dt,x,y)
	local wbgb
	local wbgb2
	if 聊天框.渲染开始() then
		yq.场景.外部聊天框:显示(0,0)
			local xxx,xxy = 17,0
			local kg = false
			local xx,yy = yq.外部.取鼠标坐标()
			local xxk = yq.场景.窗口.消息框
			w=引擎.外部.取鼠标滚轮()
			xxk.按钮_左拉:更新(xx,yy)
			xxk.按钮_上拉:更新(xx,yy)
			xxk.按钮_下拉:更新(xx,yy)
			xxk.按钮_移动:更新(xx,yy)
			xxk.按钮_查询:更新(xx,yy)
			xxk.按钮_禁止:更新(xx,yy)
			xxk.按钮_锁定:更新(xx,yy)
			xxk.按钮_清屏:更新(xx,yy)
			xxk.丰富文本外框:更新(xx,yy)
			if (wbgb按钮开关~=nil and wbgb按钮开关==1)then
				wbgb2 = true
			elseif (wbgb按钮开关~=nil and wbgb按钮开关==2) or (w~= 0 and w< 0) then
				if xxk.丰富文本外框.滚动值 > 0 then
					xxk.丰富文本外框:滚动(-1)
				end
			elseif (wbgb按钮开关~=nil and wbgb按钮开关==3) or (w~= 0 and w> 0) then
				if xxk.丰富文本外框.滚动值 < #xxk.丰富文本外框.显示表 - 24 then
					xxk.丰富文本外框:滚动(1)
				end
			elseif (wbgb按钮开关~=nil and wbgb按钮开关==4)  then
				xxk.禁止 = xxk.禁止 == false
			elseif (wbgb按钮开关~=nil and wbgb按钮开关==5)  then
				xxk.丰富文本外框:清空()--xxk:清空内容()
			end
			w= 0
			wbgb按钮开关=nil
			xxk.丰富文本外框:显示(xxx -17,xxy + 2)
			xxk.按钮_左拉:显示(xxx + 13,xxy +全局游戏高度-30,true)
			xxk.按钮_上拉:显示(xxx + 36,xxy + 全局游戏高度-30,true)
			xxk.按钮_下拉:显示(xxx + 59,xxy + 全局游戏高度-30,true)
			xxk.按钮_移动:显示(xxx + 83,xxy + 全局游戏高度-30,true)
			xxk.按钮_查询:显示(xxx + 106,xxy +全局游戏高度-30,true)
			xxk.按钮_禁止:显示(xxx + 129,xxy + 全局游戏高度-30,true)
			xxk.按钮_锁定:显示(xxx + 153,xxy +全局游戏高度-30,true)
			xxk.按钮_清屏:显示(xxx + 175,xxy + 全局游戏高度-30,true)
		-- yq.场景.外部聊天框2:显示(0,0)
		-- yq.场景.外部聊天框:显示(0,全局游戏高度/2)
		-- local xxx,xxy = 0,0
		-- local kg = false
		-- local xx,yy =聊天框.取鼠标坐标()
		-- local xxk = yq.场景.窗口.消息框
		-- w=引擎.外部.取鼠标滚轮()
		-- xxk.按钮_左拉:更新(xx,yy)
		-- xxk.按钮_上拉:更新(xx,yy)
		-- xxk.按钮_下拉:更新(xx,yy)
		-- xxk.按钮_合并:更新(xx,yy)
		-- xxk.按钮_查询:更新(xx,yy)
		-- xxk.按钮_禁止:更新(xx,yy)
		-- xxk.按钮_滚动:更新(xx,yy)
		-- xxk.按钮_清屏:更新(xx,yy)
		-- xxk.按钮_收集:更新(xx,yy)
		-- xxk.按钮_隐藏:更新(xx,yy)
		-- xxk.按钮_换底:更新(xx,yy)
		-- xxk.按钮_世:更新(xx,yy)
		-- xxk.按钮_杂:更新(xx,yy)
		-- xxk.按钮_私:更新(xx,yy)
		-- xxk.按钮_活:更新(xx,yy)
		-- xxk.按钮_帮:更新(xx,yy)
		-- xxk.按钮_门:更新(xx,yy)
		-- xxk.按钮_家:更新(xx,yy)
		-- xxk.按钮_系:更新(xx,yy)
		-- xxk.按钮_全:更新(xx,yy)
		-- xxk.按钮_当:更新(xx,yy)
		-- xxk.按钮_闻:更新(xx,yy)
		-- xxk.按钮_组:更新(xx,yy)
		-- xxk.按钮_经:更新(xx,yy)
		-- xxk.按钮_队:更新(xx,yy)

		-- xxk.按钮_左拉2:更新(xx,yy)
		-- xxk.按钮_上拉2:更新(xx,yy)
		-- xxk.按钮_下拉2:更新(xx,yy)
		-- xxk.按钮_合并2:更新(xx,yy)
		-- xxk.按钮_查询2:更新(xx,yy)
		-- xxk.按钮_禁止2:更新(xx,yy)
		-- xxk.按钮_滚动2:更新(xx,yy)
		-- xxk.按钮_清屏2:更新(xx,yy)
		-- xxk.按钮_收集2:更新(xx,yy)
		-- xxk.按钮_隐藏2:更新(xx,yy)
		-- xxk.按钮_换底2:更新(xx,yy)
		-- xxk.按钮_世2:更新(xx,yy)
		-- xxk.按钮_杂2:更新(xx,yy)
		-- xxk.按钮_私2:更新(xx,yy)
		-- xxk.按钮_活2:更新(xx,yy)
		-- xxk.按钮_帮2:更新(xx,yy)
		-- xxk.按钮_门2:更新(xx,yy)
		-- xxk.按钮_家2:更新(xx,yy)
		-- xxk.按钮_系2:更新(xx,yy)
		-- xxk.按钮_全2:更新(xx,yy)
		-- xxk.按钮_当2:更新(xx,yy)
		-- xxk.按钮_闻2:更新(xx,yy)
		-- xxk.按钮_组2:更新(xx,yy)
		-- xxk.按钮_经2:更新(xx,yy)
		-- xxk.按钮_队2:更新(xx,yy)
		-- xxk.按钮_横条:更新(xx,yy)
		-- xxk.丰富文本外框:更新(xx,yy)
		-- xxk.丰富文本外框2:更新(xx,yy)
		-- --引擎.场景.鼠标:更新(dt,xx,yy)
		-- if (wbgb按钮开关~=nil and wbgb按钮开关==1)then
		-- 	wbgb2 = true
		-- elseif (wbgb按钮开关~=nil and wbgb按钮开关==2) or (w~= 0 and w< 0 and yy>全局游戏高度/2) then
		-- 	if xxk.丰富文本外框2.滚动值 > 0 then
		-- 		xxk.丰富文本外框2:滚动(-1)
		-- 	end
		-- elseif (wbgb按钮开关~=nil and wbgb按钮开关==3) or (w~= 0 and w> 0 and yy>全局游戏高度/2) then
		-- 	if xxk.丰富文本外框2.滚动值 < #xxk.丰富文本外框2.显示表 - 24 then
		-- 		xxk.丰富文本外框2:滚动(1)
		-- 	end
		-- elseif (wbgb按钮开关~=nil and wbgb按钮开关==4)  then
		-- 	xxk.禁止 = xxk.禁止 == false
		-- elseif (wbgb按钮开关~=nil and wbgb按钮开关==5)  then
		-- 	xxk.丰富文本外框:清空()
		-- elseif (wbgb按钮开关2~=nil and wbgb按钮开关==51)then
		-- 	wbgb2 = true
		-- elseif (wbgb按钮开关~=nil and wbgb按钮开关==52) or (w~= 0 and w< 0 and yy<全局游戏高度/2) then
		-- 	if xxk.丰富文本外框.滚动值 > 0 then
		-- 		xxk.丰富文本外框:滚动(-1)
		-- 	end
		-- elseif (wbgb按钮开关~=nil and wbgb按钮开关==53) or (w~= 0 and w> 0 and yy<全局游戏高度/2) then
		-- 	if xxk.丰富文本外框.滚动值 < #xxk.丰富文本外框.显示表 - 24 then
		-- 		xxk.丰富文本外框:滚动(1)
		-- 	end
		-- elseif (wbgb按钮开关~=nil and wbgb按钮开关==54)  then
		-- 	xxk.禁止 = xxk.禁止 == false
		-- elseif (wbgb按钮开关~=nil and wbgb按钮开关==55)  then
		-- 	xxk.丰富文本外框:清空()
		-- end
		-- w= 0
		-- wbgb按钮开关=nil
		-- 	xxk.丰富文本外框:显示(xxx,xxy + 2)
		-- if not 引擎.场景.战斗中  then
		-- 	xxk.丰富文本外框2:显示(xxx,全局游戏高度/2)
		-- end
		-- xxk.按钮_左拉:显示(xxx + 3,xxy +全局游戏高度-30,true)
		-- xxk.按钮_上拉:显示(xxx + 26,xxy + 全局游戏高度-30,true)
		-- xxk.按钮_下拉:显示(xxx + 49,xxy + 全局游戏高度-30,true)
		-- xxk.按钮_合并:显示(xxx + 73,xxy + 全局游戏高度-30,true)
		-- xxk.按钮_查询:显示(xxx + 96,xxy +全局游戏高度-30,true)
		-- xxk.按钮_禁止:显示(xxx + 119,xxy + 全局游戏高度-30,true)
		-- xxk.按钮_滚动:显示(xxx + 143,xxy +全局游戏高度-30,true)
		-- xxk.按钮_清屏:显示(xxx + 165,xxy + 全局游戏高度-30,true)
		-- xxk.按钮_收集:显示(xxx + 187,xxy + 全局游戏高度-30,true)
		-- xxk.按钮_隐藏:显示(xxx + 209,xxy + 全局游戏高度-30,true)
		-- xxk.按钮_换底:显示(xxx + 231,xxy + 全局游戏高度-30,true)

		-- xxk.按钮_全:显示(xxx + 3,xxy +全局游戏高度-55,true)
		-- xxk.按钮_当:显示(xxx + 26,xxy +全局游戏高度-55,nil,nil,nil,true,2)
		-- xxk.按钮_队:显示(xxx + 49,xxy +全局游戏高度-55,true,nil,nil,true,2)
		-- xxk.按钮_帮:显示(xxx + 73,xxy +全局游戏高度-55,true,nil,nil,true,2)
		-- xxk.按钮_组:显示(xxx + 96,xxy +全局游戏高度-55,true,nil,nil,true,2)
		-- xxk.按钮_家:显示(xxx + 119,xxy +全局游戏高度-55,true,nil,nil,true,2)
		-- xxk.按钮_私:显示(xxx + 143,xxy +全局游戏高度-55,true,nil,nil,true,2)
		-- xxk.按钮_世:显示(xxx + 165,xxy +全局游戏高度-55,true)
		-- xxk.按钮_门:显示(xxx + 187,xxy +全局游戏高度-55,true)
		-- xxk.按钮_闻:显示(xxx + 209,xxy +全局游戏高度-55,true)
		-- xxk.按钮_经:显示(xxx + 231,xxy +全局游戏高度-55,true)
		-- --
		-- xxk.按钮_左拉2:显示(xxx + 3,xxy +全局游戏高度/2-30,true)
		-- xxk.按钮_上拉2:显示(xxx + 26,xxy + 全局游戏高度/2-30,true)
		-- xxk.按钮_下拉2:显示(xxx + 49,xxy + 全局游戏高度/2-30,true)
		-- xxk.按钮_合并2:显示(xxx + 73,xxy + 全局游戏高度/2-30,true)
		-- xxk.按钮_查询2:显示(xxx + 96,xxy +全局游戏高度/2-30,true)
		-- xxk.按钮_禁止2:显示(xxx + 119,xxy + 全局游戏高度/2-30,true)
		-- xxk.按钮_滚动2:显示(xxx + 143,xxy +全局游戏高度/2-30,true)
		-- xxk.按钮_清屏2:显示(xxx + 165,xxy + 全局游戏高度/2-30,true)
		-- xxk.按钮_收集2:显示(xxx + 187,xxy + 全局游戏高度/2-30,true)
		-- xxk.按钮_隐藏2:显示(xxx + 209,xxy + 全局游戏高度/2-30,true)
		-- xxk.按钮_换底2:显示(xxx + 231,xxy + 全局游戏高度/2-30,true)

		-- xxk.按钮_全2:显示(xxx + 3,xxy +全局游戏高度/2-55,true)
		-- xxk.按钮_当2:显示(xxx + 26,xxy +全局游戏高度/2-55,true)
		-- xxk.按钮_队2:显示(xxx + 49,xxy +全局游戏高度/2-55,true)
		-- xxk.按钮_帮2:显示(xxx + 73,xxy +全局游戏高度/2-55,true)
		-- xxk.按钮_组2:显示(xxx + 96,xxy +全局游戏高度/2-55,true)
		-- xxk.按钮_家2:显示(xxx + 119,xxy +全局游戏高度/2-55,true)
		-- xxk.按钮_私2:显示(xxx + 143,xxy +全局游戏高度/2-55,true)
		-- xxk.按钮_世2:显示(xxx + 165,xxy +全局游戏高度/2-55,true,nil,nil,true,2)
		-- xxk.按钮_门2:显示(xxx + 187,xxy +全局游戏高度/2-55,true,nil,nil,true,2)
		-- xxk.按钮_闻2:显示(xxx + 209,xxy +全局游戏高度/2-55,true,nil,nil,true,2)
		-- xxk.按钮_经2:显示(xxx + 231,xxy +全局游戏高度/2-55,true,nil,nil,true,2)
		-- xxk.按钮_横条:显示(xxx,xxy-10 +全局游戏高度/2+5,true)
			--
			--引擎.场景.鼠标:显示(dt,xx,yy)
		-- local zdyy = 0
		-- if 引擎.场景.战斗中 then
		-- 	引擎.场景.战斗状态回合背景_:显示(xxx + 5,xxy +全局游戏高度/2+2)
		-- 	ltzt:置颜色(0xFFFFFFFF)
		-- 	ltzt:显示(xxx + 30,xxy +全局游戏高度/2+5,"第"..战斗类.回合数.."回合")
		-- 	for i=1,#战斗类.战斗单位[ljcs] do
		-- 		if 战斗类.战斗单位[ljcs][i].单位类型 == "角色" and 战斗类.战斗单位[ljcs][i].敌我 == 1 then
		-- 			引擎.场景.战斗状态名称背景_:显示(xxx + 5,xxy +全局游戏高度/2+24+(44*zdyy))
		-- 			ltzt2:置颜色(0xFF000000)
		-- 			ltzt2:显示(xxx + 30,xxy +全局游戏高度/2+40+(44*zdyy),战斗类.战斗单位[ljcs][i].名称)
		-- 			local zdxx = 0
		-- 			for k,v in pairs(战斗类.战斗单位[ljcs][i].状态特效) do
		-- 				if 战斗类.战斗单位[ljcs][i].状态特效[k].小图标 == nil then
		-- 					local qtb = 引擎.取技能(k)
		-- 					if qtb[1]~= nil and qtb[6]~= nil and qtb[7] ~= nil  then
		-- 						战斗类.战斗单位[ljcs][i].状态特效[k].小图标=引擎.场景.资源:载入(qtb[6],"网易WDF动画",qtb[8])
		-- 						-- 战斗类.战斗单位[ljcs][i].状态特效[k].介绍=qtb[1]
		-- 					else
		-- 					    战斗类.战斗单位[ljcs][i].状态特效[k].小图标=引擎.场景.资源:载入('wzife.wdf',"网易WDF动画",3143201775)
		-- 					    -- 战斗类.战斗单位[ljcs][i].状态特效[k].介绍=名称.."这个技能暂时没有添加提示请截图给管理员"
		-- 					end
		-- 				end
		-- 				引擎.场景.战斗状态状态背景_:显示(xxx + 110+(30*zdxx),xxy +全局游戏高度/2+24+(44*zdyy))
		-- 				战斗类.战斗单位[ljcs][i].状态特效[k].小图标:更新(xx,yy)
		-- 				if 战斗类.战斗单位[ljcs][i].状态特效[k].小图标.宽度 > 24 then
		-- 					local 偏移x,偏移y = 等比例缩放公式(24,24,战斗类.战斗单位[ljcs][i].状态特效[k].小图标.宽度,战斗类.战斗单位[ljcs][i].状态特效[k].小图标.高度)
		-- 					战斗类.战斗单位[ljcs][i].状态特效[k].小图标:显示(xxx + 113+(30*zdxx),xxy +全局游戏高度/2+28+(44*zdyy),偏移x,偏移y)
		-- 				else
		-- 				    战斗类.战斗单位[ljcs][i].状态特效[k].小图标:显示(xxx + 113+(30*zdxx),xxy +全局游戏高度/2+28+(44*zdyy))
		-- 				end
		-- 				ltzt3:置颜色(0xFF000000)
		-- 				ltzt3:显示(xxx + 124+(30*zdxx),xxy +全局游戏高度/2+53+(44*zdyy),(战斗类.战斗单位[ljcs][i].状态特效[k].回合 or "1"))
		-- 				-- if 战斗类.战斗单位[ljcs][i].状态特效[k].小图标:是否选中(xx,yy) then
		-- 					-- tp.外部提示:外置技能(xx,yy,k,"持续回合:"..(战斗类.战斗单位[ljcs][i].状态特效[k].回合 or "1"),(战斗类.战斗单位[ljcs][i].状态特效[k].介绍 or k.."这个技能暂时没有添加提示请截图给管理员"))
		-- 				-- end
		-- 				zdxx = zdxx +1
		-- 			end
		-- 			for k,v in pairs(战斗类.战斗单位[ljcs][i].奇经八脉) do
		-- 				if 战斗类.战斗单位[ljcs][i].奇经八脉[k].小图标 == nil then
		-- 					local qtb = 引擎.取技能(k)
		-- 					if qtb[1]~= nil and qtb[6]~= nil and qtb[7] ~= nil  then
		-- 						战斗类.战斗单位[ljcs][i].奇经八脉[k].小图标=引擎.场景.资源:载入(qtb[6],"网易WDF动画",qtb[8])
		-- 						-- 战斗类.战斗单位[ljcs][i].奇经八脉[k].介绍=qtb[1]
		-- 					else
		-- 					    战斗类.战斗单位[ljcs][i].奇经八脉[k].小图标=引擎.场景.资源:载入('wzife.wdf',"网易WDF动画",3143201775)
		-- 					    -- 战斗类.战斗单位[ljcs][i].奇经八脉[k].介绍=名称.."这个经脉暂时没有添加提示请截图给管理员"
		-- 					end
		-- 				end
		-- 				if 战斗类.战斗单位[ljcs][i].奇经八脉[k].层数 ~= 0 then
		-- 					引擎.场景.战斗状态状态背景_:显示(xxx + 110+(30*zdxx),xxy +全局游戏高度/2+24+(44*zdyy))
		-- 					战斗类.战斗单位[ljcs][i].奇经八脉[k].小图标:更新(xx,yy)
		-- 					if 战斗类.战斗单位[ljcs][i].奇经八脉[k].小图标.宽度 > 24 then
		-- 						local 偏移x,偏移y = 等比例缩放公式(24,24,战斗类.战斗单位[ljcs][i].奇经八脉[k].小图标.宽度,战斗类.战斗单位[ljcs][i].奇经八脉[k].小图标.高度)
		-- 						战斗类.战斗单位[ljcs][i].奇经八脉[k].小图标:显示(xxx + 113+(30*zdxx),xxy +全局游戏高度/2+28+(44*zdyy),偏移x,偏移y)
		-- 					else
		-- 					    战斗类.战斗单位[ljcs][i].奇经八脉[k].小图标:显示(xxx + 113+(30*zdxx),xxy +全局游戏高度/2+28+(44*zdyy))
		-- 					end
		-- 					ltzt3:置颜色(0xFF000000)
		-- 					ltzt3:显示(xxx + 124+(30*zdxx),xxy +全局游戏高度/2+53+(44*zdyy),(战斗类.战斗单位[ljcs][i].奇经八脉[k].层数 or "1"))
		-- 					-- if 战斗类.战斗单位[ljcs][i].奇经八脉[k].小图标:是否选中(xx,yy) then
		-- 					-- 	tp.外部提示:外置技能(xx,yy,k,"层数:"..(战斗类.战斗单位[ljcs][i].奇经八脉[k].层数 or "1"),(战斗类.战斗单位[ljcs][i].奇经八脉[k].介绍 or k.."这个经脉暂时没有添加提示请截图给管理员"))
		-- 					-- end
		-- 					zdxx = zdxx +1
		-- 				end
		-- 			end
		-- 			zdyy = zdyy +1
		-- 		end
		-- 	end
		-- end
		-- if #tp.外部提示.寄存内容 > 0 then
		-- 	if tp.外部提示.寄存内容.开启提示 then
		-- 		tp.提示框:置宽高(tp.外部提示.寄存内容.提示坐标[3]+15,tp.外部提示.寄存内容.提示坐标[4]+12)
		-- 		tp.提示框:显示(tp.外部提示.寄存内容.提示坐标[1],tp.外部提示.寄存内容.提示坐标[2])
		-- 	end
		-- 	for i=1,#tp.外部提示.寄存内容 do
		-- 		if tp.外部提示.寄存内容[i].内容 ~= nil then
		-- 				tp.外部提示.寄存内容[i].内容:显示(tp.外部提示.寄存内容[i].x,tp.外部提示.寄存内容[i].y)
		-- 		else
		-- 			if tp.外部提示.寄存内容[i].文字 ~= nil then
		-- 				tp.外部提示.寄存内容[i].文字:置颜色(tp.外部提示.寄存内容[i].颜色):显示(tp.外部提示.寄存内容[i].坐标[1],tp.外部提示.寄存内容[i].坐标[2],tp.外部提示.寄存内容[i].文本)
		-- 			end
		-- 		end
		-- 	end
		-- 	if tp.外部提示.显示方式==1 then
		-- 		tp.外部提示:清空寄存()
		-- 	elseif 引擎.鼠标按下(0) and tp.外部提示.显示方式==2 then
		-- 		tp.外部提示.显示方式=1
		-- 		tp.外部提示:清空寄存()
		-- 	end
		-- end
		聊天框.渲染结束()
		if 引擎.是否在窗口内() then
			聊天框.置鼠标按住(false)
		end
	end
	if wbgb then
		临时高度=1
	end
	if wbgb2 then
		聊天框.外部聊天框窗口关闭(wbgb2)
	end
end

function 聊天框.外部聊天框窗口关闭(wbgb)
	if yq.外部 ~= nil and wbgb then
		local xxk = yq.场景.窗口.消息框
		xxk.外部聊天 = xxk.外部聊天 == false
		聊天框.关闭()
		yq.外部 = nil
		xxk.丰富文本:置高度(xxk:取高度())
		if xxk.丰富文本.滚动值 > 0 then
			xxk.丰富文本:滚动(-xxk.丰富文本.滚动值)
		end
		if xxk.丰富文本.滚动值 < #xxk.丰富文本.显示表 - 24 then
			xxk.丰富文本:滚动(xxk.丰富文本.滚动值)
		end
		xxk.按钮_左拉.外部按钮 = nil
		xxk.按钮_上拉.外部按钮 = nil
		xxk.按钮_下拉.外部按钮 = nil
		xxk.按钮_移动.外部按钮 = nil
		xxk.按钮_查询.外部按钮 = nil
		xxk.按钮_禁止.外部按钮 = nil
		xxk.按钮_锁定.外部按钮 = nil
		xxk.按钮_清屏.外部按钮 = nil
		xxk.按钮_移动.确定按下 = false
		-- xxk.按钮_左拉.外部按钮 = nil
		-- xxk.按钮_上拉.外部按钮 = nil
		-- xxk.按钮_下拉.外部按钮 = nil
		-- xxk.按钮_合并.外部按钮 = nil
		-- xxk.按钮_查询.外部按钮 = nil
		-- xxk.按钮_禁止.外部按钮 = nil
		-- xxk.按钮_滚动.外部按钮 = nil
		-- xxk.按钮_清屏.外部按钮 = nil
		-- xxk.按钮_收集.外部按钮 = nil
		-- xxk.按钮_隐藏.外部按钮 = nil
		-- xxk.按钮_换底.外部按钮 = nil
		-- xxk.按钮_世.外部按钮 = nil
		-- xxk.按钮_私.外部按钮 = nil
		-- xxk.按钮_活.外部按钮 = nil
		-- xxk.按钮_帮.外部按钮 = nil
		-- xxk.按钮_门.外部按钮 = nil
		-- xxk.按钮_家.外部按钮 = nil
		-- xxk.按钮_系.外部按钮 = nil
		-- xxk.按钮_全.外部按钮 = nil
		-- xxk.按钮_当.外部按钮 = nil
		-- xxk.按钮_闻.外部按钮 = nil
		-- xxk.按钮_组.外部按钮 = nil
		-- xxk.按钮_经.外部按钮 = nil
		-- xxk.按钮_队.外部按钮 = nil

		-- xxk.按钮_左拉2.外部按钮 = nil
		-- xxk.按钮_上拉2.外部按钮 = nil
		-- xxk.按钮_下拉2.外部按钮 = nil
		-- xxk.按钮_合并2.外部按钮 = nil
		-- xxk.按钮_查询2.外部按钮 = nil
		-- xxk.按钮_禁止2.外部按钮 = nil
		-- xxk.按钮_滚动2.外部按钮 = nil
		-- xxk.按钮_清屏2.外部按钮 = nil
		-- xxk.按钮_收集2.外部按钮 = nil
		-- xxk.按钮_隐藏2.外部按钮 = nil
		-- xxk.按钮_换底2.外部按钮 = nil
		-- xxk.按钮_世2.外部按钮 = nil
		-- xxk.按钮_私2.外部按钮 = nil
		-- xxk.按钮_活2.外部按钮 = nil
		-- xxk.按钮_帮2.外部按钮 = nil
		-- xxk.按钮_门2.外部按钮 = nil
		-- xxk.按钮_家2.外部按钮 = nil
		-- xxk.按钮_系2.外部按钮 = nil
		-- xxk.按钮_全2.外部按钮 = nil
		-- xxk.按钮_当2.外部按钮 = nil
		-- xxk.按钮_闻2.外部按钮 = nil
		-- xxk.按钮_组2.外部按钮 = nil
		-- xxk.按钮_经2.外部按钮 = nil
		-- xxk.按钮_队2.外部按钮 = nil
		-- xxk.按钮_横条.外部按钮 = nil
		-- xxk.按钮_移动.确定按下 = false
		引擎.在外部 = nil
		collectgarbage("collect")
	end
end

function 聊天框.取鼠标坐标()
	return DLL模块.取鼠标坐标()
end

function 聊天框.取鼠标滚轮()
	return DLL模块.获取鼠标轮()
end

function 聊天框.窗口坐标()
	return DLL模块.窗口坐标()
end

function 聊天框.是否在窗口内()
	local cx,cy = 聊天框.窗口坐标()
	local mx,my = 聊天框.鼠标坐标()
	if mx+3 > cx and mx+3 < cx+外部窗口宽度 and my-23 > cy and my-23 < cy+全局游戏高度-1  then
		return true
	end
	return false
end

function 聊天框.鼠标坐标()
	return DLL模块.鼠标坐标()
end

function 聊天框.鼠标按下(key)
	return DLL模块.鼠标按下(key)
end

function 聊天框.鼠标弹起(key)
	return DLL模块.鼠标弹起(key)
end

function 聊天框.鼠标按住(key)
	return x0
end

function 聊天框.置鼠标按住(key)
	x0 = key
	x0s = 0
	if x0 then
		x0s = 1
	end
end

return 初始化
