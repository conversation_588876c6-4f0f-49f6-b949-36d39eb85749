local 自动抓鬼 = class()

local floor = math.floor
local min = math.min
local tp,zts1,zts2
local xxx = 0
local yyy = 0
local max = 1
local insert = table.insert
local mouseb = 引擎.鼠标弹起
local wz = require("gge文字类")

function 自动抓鬼:初始化(根)
	self.ID = 161
	--宽高 549 431
	self.文字类=wz
	self.x = 160
	self.y = 5
	self.xx = 0
	self.yy = 0
	self.注释 = "自动抓鬼"
	self.可视 = false
	self.鼠标 = false
	self.焦点 = false
	self.可移动 = true
	self.状态 = 1
	self.窗口时间 = 0
	tp = 根
	zts2 = tp.字体表.普通字体
	self.分类选中=""
	self.子类选中=""
	self.玩法介绍内容=""
	self.丰富文本说明 = 根._丰富文本(476,265)
	self.切换  = 0
	self.事件 = "自动抓鬼"
end

function 自动抓鬼:打开(数据)

	if self.可视 then
		self.可视 = false
		self.资源组 = nil
		return
	else
		insert(tp.窗口_,self)
		local 资源 = tp.资源
		local 自适应 = tp._自适应
		local 按钮 = tp._按钮
		self.资源组 = {

			[1] = 自适应.创建(0,1,200,100,3,9),
			[2] = 按钮.创建(自适应.创建(12,4,48,20,1,3),0,0,4,true,true,"关闭"),


			}


		self.状态 = 1
		self.加入 = 0
		tp.运行时间 = tp.运行时间 + 1
		self.窗口时间 = tp.运行时间
		self.可视 = true
		self.时间 = 0
		self.进程 = 数据.进程
		self.仙玉 = 数据.仙玉+0
		self.次数 = 数据.次数+0
		self.事件 = 数据.事件
		self.开启 = true
	end
end
function 自动抓鬼:刷新(数据)
		self.进程 = 数据.进程
		self.仙玉 = 数据.仙玉+0
		self.次数 = 数据.次数+0
		self.事件 = 数据.事件
		self.开启 = true


end

function 自动抓鬼:显示(dt,x,y)
    self.时间 = self.时间 + 1
	self.焦点 = false
	self.资源组[1]:显示(self.x,self.y)

	zts2:置字间距(2)
	zts2:置颜色(白色)
	zts2:显示(self.x+70,self.y+2,self.事件)
	zts2:置字间距(0)
	self.资源组[2]:显示(self.x+75,self.y+70)
	self.资源组[2]:更新(x,y)

	if self.资源组[2]:事件判断() then
		self.开启 = false
		self.可视 = false
		self.时间 = 0
		发送数据(107,{进程="关闭"})
	end

	local 字体 = tp.字体表.普通字体

    字体:显示(self.x+10,self.y+30,"每次消耗1次自动抓鬼")
	字体:显示(self.x+10,self.y+50,"当前自动抓鬼次数："..self.次数)

	local xx = 0
	local yy = 0
	if self.进程 == "第一进程"  and self.时间 > 150 and self.开启 == true then
	  发送数据(107,{进程="第二进程"})
	  self.时间 = 0
	  self.开启 =false
	end
	if self.进程 == "第二进程"  and self.时间 > 150 and self.开启 == true then
		发送数据(107,{进程="第三进程"})
		self.时间 = 0
		self.开启 =false
	end
	if self.进程 == "第三进程"  and self.时间 > 150 and self.开启 == true then
		发送数据(107,{进程="第四进程"})
		self.时间 = 0
		self.开启 =false
	end
	if self.进程 == "第四进程"  and self.时间 > 150 and self.开启 == true then
		发送数据(107,{进程="第五进程"})
		self.时间 = 0
		self.开启 =false
	end
	if self.进程 == "第五进程"  and self.时间 > 150 and self.开启 == true then
		发送数据(107,{进程="第六进程"})
		self.时间 = 0
		self.开启 =false
	end
	if self.进程 == "第六进程"  and self.时间 > 150 and self.开启 == true then
		发送数据(107,{进程="第一进程"})
		self.时间 = 0
		self.开启 =false
	end
	if self.进程 == "关闭"   and self.开启 == true then
		self.开启 = false
		self.可视 = false
		self.时间 = 0
		发送数据(107,{进程="关闭"})
	end
end












function 自动抓鬼:检查点(x,y)
	if self.资源组[1] ~= nil and  self.资源组[1]:是否选中(x,y) then
		return true
	end
end

function 自动抓鬼:初始移动(x,y)
	tp.运行时间 = tp.运行时间 + 1
	if not tp.消息栏焦点 then
		self.窗口时间 = tp.运行时间
	end
	if not self.焦点 then
		tp.移动窗口 = true
	end
	if self.鼠标 and  not self.焦点 then
		self.xx = x - self.x
		self.yy = y - self.y
	end
end

function 自动抓鬼:开始移动(x,y)
	if self.鼠标 then
		self.x = x - self.xx
		self.y = y - self.yy
	end
end

return 自动抓鬼