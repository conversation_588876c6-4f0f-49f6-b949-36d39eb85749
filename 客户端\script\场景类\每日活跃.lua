--======================================================================--

--======================================================================--
local 系统类_每日活跃 = class()
local floor = math.floor
local tp,zts,zt,zts2
local format = string.format
local insert = table.insert




function 系统类_每日活跃:初始化(根)
	self.x = 180
	self.y = 170
	self.xx = 0
	self.yy = 0
	self.注释 = "每 日 活 跃"
	self.可视 = false
	self.鼠标 = false
	self.焦点 = false
	self.可移动 = true
	tp = 根
	self.窗口时间 = 0
	zts = tp.字体表.描边自定
	zt = tp.字体表.汉仪字体4
	zts2 = tp.字体表.提示字体
	self.进程=1
	self.选中 = 0
	self.加入 = 0
	self.选中状态 = 0
	self.控件类 = require("ggeui/加载类")()
	local 总控件 = self.控件类:创建控件('序号控件')
	总控件:置可视(true,true)
	self.输入框 = 总控件:创建输入("卡号输入",0,0,180,14)
	self.输入框:置可视(false,false)
	self.输入框:置限制字数(30)
	self.输入框:屏蔽快捷键(true)
	self.输入框:置光标颜色(-16777216)
	self.输入框:置文字颜色(-16777216)

	-- self.数量框 = 总控件:创建输入("数量输入",0,0,180,14)
	-- self.数量框:置可视(false,false)
	-- self.数量框:置限制字数(10)
	-- self.数量框:置数字模式()
	-- self.数量框:屏蔽快捷键(true)
	-- self.数量框:置光标颜色(-16777216)
	-- self.数量框:置文字颜色(-16777216)

end

function 系统类_每日活跃:打开(内容)
	table.print(内容)
	if self.可视 then
		self.可视 = false
		self.输入框:置可视(false,false)
		--self.数量框:置可视(false,false)
		self.资源组 = nil
		self.物品组={}
		return
	else
		insert(tp.窗口_,self)
		local 资源 = tp.资源
		local 按钮 = tp._按钮
		local 自适应 = tp._自适应
		self.资源组 = {
			[1] = 自适应.创建(0,1,600,500,3,9),
			[2] = 资源:载入('wzife.wd3',"网易WDF动画",0x2436C9A1),
			[7] = 资源:载入('wzife.wdf',"网易WDF动画",0x2DA9D4EC),
			[8] = 资源:载入('wzife.wdf',"网易WDF动画",0x479E857C),
		}
		self.资源组[65] = 按钮.创建(自适应.创建(12,4,75,22,1,3),0,0,4,true,true," 下一页")
		self.资源组[66] = 按钮.创建(自适应.创建(12,4,75,22,1,3),0,0,4,true,true," 上一页")
		self.资源组[67] = 按钮.创建(自适应.创建(12,4,91,22,1,3),0,0,4,true,true,"自 动 抓 鬼")
		self.资源组[68] = 按钮.创建(自适应.创建(12,4,91,22,1,3),0,0,4,true,true,"每 日 领 奖")
		self.资源组[69] = 按钮.创建(自适应.创建(12,4,91,22,1,3),0,0,4,true,true,"在 线 购 卡")
		--self.资源组[65] = 按钮.创建(tp.资源:载入('wzife.wdf',"网易WDF动画",0x48D2590A),0,0,4,true,true,"确定充值")
		--self.资源组[66] = 按钮.创建(tp.资源:载入('wzife.wdf',"网易WDF动画",0x48D2590A),0,0,4,true,true,"兑换仙玉")
		self.线 = tp.资源:载入("wzife.wd1","网易WDF动画",999600305)
		self.线:置区域(0,0,350,2)
		self.数据=数据
		self.内容=内容
		tp.运行时间 = tp.运行时间 + 1
	    	self.窗口时间 = tp.运行时间
	    	self.可视 = true
	    	self.输入框:置可视(true,true)
	    	self.物品组={}
	    	 self.加入 = 0
	    	self.选中 = 0
	    	self.选中状态 = 0
	    	self.进程=1
	     	--self:加载物品(内容)


		-- self.数量框:置可视(true,true)
		-- self.数量框:置文本("输入兑换的仙玉数额")
	end
end



function 系统类_每日活跃:显示(dt,x,y)
	local 偏移x,偏移y = 等比例缩放公式(1250,1,self.线.宽度,self.线.高度)
	self.焦点 = false
	self.资源组[1]:显示(self.x,self.y)
	--self.资源组[2]:显示(self.x + 85+14,self.y+7-10)
	self.资源组[65]:更新(x,y)
	self.资源组[66]:更新(x,y)
	self.资源组[67]:更新(x,y)
	self.资源组[68]:更新(x,y)
	self.资源组[69]:更新(x,y)


	for i=0,4 do
		--self.线:显示(self.x+20,self.y+35+65+i*84)
		self.线:显示(self.x+25,self.y+100+i*84,偏移x,偏移y)
	end

	self.资源组[65]:显示(self.x + 500,self.y +465)
	self.资源组[66]:显示(self.x + 30,self.y +465)

	tp.窗口标题背景_:显示(self.x+self.资源组[1].宽度/2-88,self.y)
	引擎.场景.字体表.标题字体:显示(self.x+self.资源组[1].宽度/2,self.y+3,"每日活跃进程")




	zts:显示(self.x+230,self.y+465,"累计活跃度："..self.内容.总活跃度)


	if self.资源组[65]:事件判断() then
		self.进程=2
	elseif self.资源组[66]:事件判断() then
		self.进程=1
	-- elseif self.资源组[68]:事件判断() then
	-- 	发送数据(95.1)
	-- elseif self.资源组[69]:事件判断() then
	-- 	引擎.运行("https://shop.vvxxz.com")
	 end



	 if self.进程 == 1 then
	 	--zts:置颜色(黄色)
		zts:显示(self.x+25,self.y+70,"今日需完成抓鬼次数达到100次   当前已完成："..self.内容.活跃数据.抓鬼.." 次")
		zts:显示(self.x+25,self.y+154,"今日需完成押镖次数达到50次   当前已完成："..self.内容.活跃数据.押镖.." 次")
		zts:显示(self.x+25,self.y+238,"今日需完成押镖次数达到100次   当前已完成：0 次")
		zts:显示(self.x+25,self.y+322,"今日需完成押镖次数达到100次   当前已完成：0 次")
		zts:显示(self.x+25,self.y+406,"今日需完成押镖次数达到100次   当前已完成：0 次")
		zt:置颜色(绿色)
		zt:显示(self.x+380,self.y+70," 活跃度 10 点")
		zt:显示(self.x+380,self.y+154," 活跃度 10 点")
		zt:显示(self.x+380,self.y+238," 活跃度 10 点")
		zt:显示(self.x+380,self.y+322," 活跃度 10 点")
		zt:显示(self.x+380,self.y+406," 活跃度 10 点")
		--zts2:置颜色(红色)
		--zts:显示(self.x+525,self.y+70,"已完成")
		zts:显示(self.x+525,self.y+154,"未完成")
		zts:显示(self.x+525,self.y+238,"未完成")
		zts:显示(self.x+525,self.y+322,"未完成")
		zts:显示(self.x+525,self.y+406,"未完成")


		if self.内容.活跃数据.抓鬼 == 100 and  self.内容.活跃数据.抓鬼判断==true then
			zts:显示(self.x+525,self.y+70,"已完成")
		else
			zts:显示(self.x+525,self.y+70,"未完成")
		end

		if self.内容.活跃数据.押镖 == 50 and  self.内容.活跃数据.抓鬼判断==true then
			zts:显示(self.x+525,self.y+154,"已完成")
		else
			zts:显示(self.x+525,self.y+154,"未完成")
		end

	 elseif self.进程 == 2 then
	 	zts:显示(self.x+25,self.y+70,"今日需完成抓鬼次数达到100次   当前已完成：0 次")
		zts:显示(self.x+25,self.y+154,"今日需完成押镖次数达到100次   当前已完成：0 次")
		zts:显示(self.x+25,self.y+238,"今日需完成门派闯关达到100次   当前已完成：0 次")
		zts:显示(self.x+25,self.y+322,"今日需完成乌鸡国达到100次   当前已完成：0 次")
		zts:显示(self.x+25,self.y+406,"今日需完成水陆大会达到100次   当前已完成：0 次")
		zt:置颜色(绿色)
		zt:显示(self.x+380,self.y+70," 活跃度 10 点")
		zt:显示(self.x+380,self.y+154," 活跃度 10 点")
		zt:显示(self.x+380,self.y+238," 活跃度 10 点")
		zt:显示(self.x+380,self.y+322," 活跃度 10 点")
		zt:显示(self.x+380,self.y+406," 活跃度 10 点")
		--zts2:置颜色(红色)
		zts:显示(self.x+525,self.y+70,"已完成")
		zts:显示(self.x+525,self.y+154,"未完成")
		zts:显示(self.x+525,self.y+238,"未完成")
		zts:显示(self.x+525,self.y+322,"未完成")
		zts:显示(self.x+525,self.y+406,"未完成")
	 end




end





function 系统类_每日活跃:刷新(内容)

	self.内容=内容

end


function 系统类_每日活跃:检查点(x,y)
	if self.资源组 ~= nil and self.资源组[1]:是否选中(x,y)  then
		return true
	end
end

function 系统类_每日活跃:初始移动(x,y)
	tp.运行时间 = tp.运行时间 + 1
	if not tp.消息栏焦点 then
  		self.窗口时间 = tp.运行时间
 	end
	if not self.焦点 then
		tp.移动窗口 = true
	end
	if self.鼠标 and  not self.焦点 then
		self.xx = x - self.x
		self.yy = y - self.y
	end
end

function 系统类_每日活跃:开始移动(x,y)
	if self.鼠标 then
		self.x = x - self.xx
		self.y = y - self.yy
	end
end

return 系统类_每日活跃