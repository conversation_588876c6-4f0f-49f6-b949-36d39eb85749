--======================================================================--
--======================================================================--
local 场景类_充值 = class()
local tp
function 场景类_充值:初始化(根)
	local 资源 = 根.资源
	local 按钮 = 根._按钮
	local 滑块 = 根._滑块
	local 自适应 = 根._自适应
	self.标题背景 = 资源:载入('wzife.wd1',"网易WDF动画",0xCB41257C)
	self.进入游戏 = 按钮(资源:载入('wzife.wdf',"网易WDF动画",0x0A247197),0,0,3,true,true)
	self.注册账号 = 按钮(资源:载入('wzife.wdf',"网易WDF动画",0x072DD907),0,0,3,true,true)
	self.游戏充值 = 按钮(资源:载入('wzife.wdf',"网易WDF动画",0x499A35BB),0,0,3,true,true)
	self.上一步 = 按钮(资源:载入('wzife.wdf',"网易WDF动画",0x611107AA),0,0,3,true,true)
	self.退出游戏 = 按钮(资源:载入('wzife.wdf',"网易WDF动画",0xD139A8FE),0,0,3,true,true)
	self.确定游戏 = 按钮.创建(自适应.创建(12,4,72,22,1,3),0,0,4,true,true,"确定充值")
	tp = 根
end

function 场景类_充值:显示(dt,x,y)
	if tp.进程 == 6 then
		self.标题背景:显示(全局游戏宽度/2-250,全局游戏高度/2-180)
		-- 引擎.置新标题(全局游戏标题.."- 充值")
		self.进入游戏:更新(x,y)
		self.上一步:更新(x,y)
		self.注册账号:更新(x,y)
		self.游戏充值:更新(x,y)
		self.退出游戏:更新(x,y)
		self.确定游戏:更新(x,y)
		if self.进入游戏:事件判断() then
			-- 引擎.置新标题(全局游戏标题)
			tp.进程 = 2
			tp.选中窗口 = nil
		elseif self.注册账号:事件判断() then
			tp.进程 = 5
		elseif self.游戏充值:事件判断() then
			tp.进程 = 6
		elseif self.上一步:事件判断() then
			tp.进程 = 7
		elseif self.退出游戏:事件判断() then
			引擎关闭开始()
			return false
		end
		-- self.进入游戏:显示(全局游戏宽度-160,全局游戏高度-440)
		self.上一步:显示(全局游戏宽度-160,全局游戏高度-260)
		self.注册账号:显示(全局游戏宽度-160,全局游戏高度-320)
		-- self.游戏充值:显示(全局游戏宽度-160,全局游戏高度-320)
		self.退出游戏:显示(全局游戏宽度-160,全局游戏高度-200)
		--=============
		self.确定游戏:显示(全局游戏宽度/2-100,全局游戏高度/2-20)
	end
end

return 场景类_充值