--======================================================================--
-- @作者: www.wnfj.com，联系QQ：79550111
-- @创建时间:   2018-03-03 02:34:19
-- @Last Modified time: 2024-12-05 17:26:08
--======================================================================--
function 引擎.取技能(jn,lx)
	local jns = {}
	for k,v in pairs(技能信息) do
		if lx then
			if 技能信息[k].名称==jn and 技能信息[k].门派 == lx then
				jns[1] =技能信息[k].功效
				jns[2] =技能信息[k].门派
				jns[3] =技能信息[k].使用类型
				jns[4] =技能信息[k].消耗
				if 技能信息[k].技能 then
					jns[5] =技能信息[k].技能[1].."技能达到"..技能信息[k].技能[2]
					if 技能信息[k].飞升 then
						jns[5]=jns[5].."，人物飞升"
					end
					if 技能信息[k].角色等级 then
						jns[5]=jns[5].."，角色等级需求"..技能信息[k].角色等级
					end
				end
				jns[6] =技能信息[k].资源
				jns[7] =技能信息[k].模型大
				jns[8] =技能信息[k].模型小
				jns[12] =技能信息[k].冷却回合
			end
		else
			if 技能信息[k].名称==jn and 技能信息[k].门派 ~= "子女" then
				jns[1] =技能信息[k].功效
				jns[2] =技能信息[k].门派
				jns[3] =技能信息[k].使用类型
				jns[4] =技能信息[k].消耗
				if 技能信息[k].技能 then
					jns[5] =技能信息[k].技能[1].."技能达到"..技能信息[k].技能[2]
					if 技能信息[k].飞升 then
						jns[5]=jns[5].."，人物飞升"
					end
					if 技能信息[k].角色等级 then
						jns[5]=jns[5].."，角色等级需求"..技能信息[k].角色等级
					end
				end
				jns[6] =技能信息[k].资源
				jns[7] =技能信息[k].模型大
				jns[8] =技能信息[k].模型小
				jns[12] =技能信息[k].冷却回合
			end
		end
	end
	return jns
end

function 引擎.取技能调试(jn,lx)
	local jns = {}
	for k,v in pairs(技能信息) do
		if 技能信息[k].名称==jn and k== lx then
			jns[1] =技能信息[k].功效
			jns[2] =技能信息[k].门派
			jns[3] =技能信息[k].使用类型
			jns[4] =技能信息[k].消耗
			if 技能信息[k].技能 then
				jns[5] =技能信息[k].技能[1].."技能达到"..技能信息[k].技能[2]
				if 技能信息[k].飞升 then
					jns[5]=jns[5].."，人物飞升"
				end
				if 技能信息[k].角色等级 then
					jns[5]=jns[5].."，角色等级需求"..技能信息[k].角色等级
				end
			end
			jns[6] =技能信息[k].资源
			jns[7] =技能信息[k].模型大
			jns[8] =技能信息[k].模型小
			jns[12] =技能信息[k].冷却回合
		end
	end
	return jns
end
--2自己直接 3队友或自己 4敌人  7召唤兽被动技能 8辅助技能  108 非战斗技能