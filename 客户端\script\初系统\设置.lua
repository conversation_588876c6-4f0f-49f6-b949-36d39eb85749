--======================================================================--
--	☆ 作者：作者QQ：79550111
--======================================================================--
local 场景类_系统设置 = class()

local floor = math.floor
local ceil = math.ceil
local tp,zts
local insert = table.insert

function 场景类_系统设置:初始化(根)
	self.ID = 63
	self.x = 325
	self.y = 120
	self.xx = 0
	self.yy = 0
	self.注释 = "系统设置"
	self.可视 = false
	self.鼠标 = false
	self.焦点 = false
	self.可移动 = true
	local 资源 = 根.资源
	local 按钮 = 根._按钮
	local 自适应 = 根._自适应
	local 小型选项栏 = 根._小型选项栏
	tp = 根
	self.资源组 = {
		[1] = 自适应.创建(0,1,363,302,3,9),
		[2] = 按钮.创建(自适应.创建(18,4,16,16,4,3),0,0,4,true,true),--关闭
		[3] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
		[4] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
		[5] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
		[6] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
		[7] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
		[8] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
		[9] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
		[10] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
		[11] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
		[12] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
		[17] = 按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"保存设置"),
		[18] = 按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"系统设置"),
		[19] = 按钮.创建(自适应.创建(17,4,105,22,1,3),0,0,4,true,true,"切聊天框底图"),
		[20] = 按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"退出游戏"),
		[23] = tp._滑块.创建(资源:载入('wzife.wdf',"网易WDF动画",0x8D4BBC26),1,190,16,1),
		[24] = 自适应.创建(103,1,115,22,1,3,nil,18),--下拉框开始 --灰色
		[25] = 按钮.创建(自适应.创建(101,4,18,19,4,3),0,0,4,true,true),
		[26] = 小型选项栏.创建(自适应.创建(8,1,115,115,3,9),"游戏窗口设置"),--下拉框结束
		[27] = tp._滑块.创建(资源:载入('wzife.wdf',"网易WDF动画",0x8D4BBC26),1,190,16,1),
		[28] = 自适应.创建(78,1,337,230,3,9),
		[29] = 自适应.创建(103,1,190,22,1,3,nil,18),
		[30] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
	}
	for i=2,11 do
		self.资源组[i]:绑定窗口_(self.ID)
	end
	for i=17,20 do
		self.资源组[i]:绑定窗口_(self.ID)
		if i==19 then
		    self.资源组[i]:置偏移(2,0)
		else
			self.资源组[i]:置偏移(4,0)
		end

	end
	for i=3,11 do
		self.资源组[i]:置偏移(-3,2)
	end
	self.资源组[23]:绑定窗口_(self.ID)
	self.资源组[27]:绑定窗口_(self.ID)
	self.资源组[25]:绑定窗口_(self.ID)
	zts = tp.字体表.普通字体
	self.窗口时间 = 0
	local XXX=读配置("./config.ini","mhxy","宽度") or "1000"
	local YYY=读配置("./config.ini","mhxy","高度") or "620"
	self.游戏窗口设置= XXX.."*"..YYY
end

function 场景类_系统设置:打开()
	if self.可视 then
		self.可视 = false
	else
		insert(tp.窗口_,self)
		self.资源组[3]:置打勾框(tp.音乐开启)
		self.资源组[4]:置打勾框(tp.音效开启)
		self.资源组[5]:置打勾框(tp.地图特效)
		self.资源组[6]:置打勾框(tp.允许组队)
		self.资源组[7]:置打勾框(tp.允许加好友)
		self.资源组[8]:置打勾框(tp.显示锦衣)
		--self.资源组[8]:置打勾框(tp.允许查看装备)
		self.资源组[9]:置打勾框(tp.显示变身卡造型)
		self.资源组[10]:置打勾框(tp.显示坐骑)
		self.资源组[30]:置打勾框(tp.显示翅膀)
		self.资源组[11]:置打勾框(低配模式)
		self.资源组[12]:置打勾框(tp.连点模式)
		-- self.资源组[12]:置打勾框(true)
		-- self.资源组[13]:置打勾框(true)
		-- self.资源组[14]:置打勾框(true)
		-- self.资源组[15]:置打勾框(true)
		-- self.资源组[16]:置打勾框(true)
		self.资源组[23]:置起始点(self.资源组[23]:取百分比转换(tp.音乐音量,0,160))
		self.资源组[27]:置起始点(self.资源组[23]:取百分比转换(tp.音效音量,0,160))
	    tp.运行时间 = tp.运行时间 + 1
	  	self.窗口时间 = tp.运行时间
	    self.可视 = true
		if 引擎.是否全屏 then
			self.游戏窗口设置 = "全屏窗口"
		else
			self.游戏窗口设置 = 全局游戏宽度.."*"..全局游戏高度
		end
	end
end

function 场景类_系统设置:显示(dt,x,y)
	self.资源组[2]:更新(x,y)
	self.资源组[3]:更新(x,y)
	self.资源组[4]:更新(x,y)
	self.资源组[5]:更新(x,y)
	self.资源组[6]:更新(x,y)
	self.资源组[7]:更新(x,y)
	self.资源组[8]:更新(x,y)
	self.资源组[9]:更新(x,y)
	self.资源组[10]:更新(x,y)
	self.资源组[30]:更新(x,y)
	self.资源组[11]:更新(x,y)
	self.资源组[12]:更新(x,y)
	-- self.资源组[12]:更新(x,y)
	-- self.资源组[13]:更新(x,y)
	-- self.资源组[14]:更新(x,y)
	-- self.资源组[15]:更新(x,y)
	-- self.资源组[16]:更新(x,y)
	self.资源组[17]:更新(x,y)
	-- self.资源组[18]:更新(x,y)
	self.资源组[19]:更新(x,y)
	self.资源组[20]:更新(x,y)
	-- self.资源组[21]:更新(x,y)
	-- self.资源组[22]:更新(x,y)
	self.资源组[25]:更新(x,y)
	if 引擎.是否全屏 then
		self.游戏窗口设置 = "全屏窗口"
	else
		self.游戏窗口设置 = 全局游戏宽度.."*"..全局游戏高度
	end
	--########################################################?自己修改?##########################################
	if self.资源组[2]:事件判断() then
		self:打开()
		return
	elseif self.资源组[3]:事件判断() then
		-- print(tp.音乐开启)
		tp.音乐开启 = not self.资源组[3].打勾框
		if not tp.音乐开启 then
			tp.音乐:停止()
			self.资源组[3]:置打勾框(tp.音乐开启)
			发送数据(90,{选项="音乐开启",回调=tp.音乐开启})
		else
			tp.音乐:播放()
			引擎.场景.音乐:置音量(tp.音乐音量)
			self.资源组[3]:置打勾框(tp.音乐开启)
			发送数据(90,{选项="音乐开启",回调=tp.音乐开启})
		end
	elseif self.资源组[4]:事件判断() then
		tp.音效开启 = not self.资源组[4].打勾框
		self.资源组[4]:置打勾框(tp.音效开启)
		发送数据(90,{选项="音效开启",回调=tp.音效开启})
	elseif self.资源组[5]:事件判断() then
		tp.地图特效 = not self.资源组[5].打勾框
		self.资源组[5]:置打勾框(tp.地图特效)
		发送数据(90,{选项="地图特效",回调=tp.地图特效})
	elseif self.资源组[6]:事件判断() then
		tp.允许组队 = not self.资源组[6].打勾框
		self.资源组[6]:置打勾框(tp.允许组队)
		发送数据(90,{选项="允许组队",回调=tp.允许组队})
	elseif self.资源组[7]:事件判断() then
		tp.允许加好友 = not self.资源组[7].打勾框
		self.资源组[7]:置打勾框(tp.允许加好友)
		发送数据(90,{选项="允许加好友",回调=tp.允许加好友})
	elseif self.资源组[8]:事件判断() then
		-- tp.允许查看装备 = not self.资源组[8].打勾框
		-- self.资源组[8]:置打勾框(tp.允许查看装备)
		-- 发送数据(90,{选项="允许查看装备",回调=tp.允许查看装备})

		tp.显示锦衣 = not self.资源组[8].打勾框
		self.资源组[8]:置打勾框(tp.显示锦衣)
		tp.场景.人物:锦衣更换()
		if tp.队伍[1].装备[3] then
		tp.场景.人物:穿戴装备()
		end
		发送数据(90,{选项="显示锦衣",回调=tp.显示锦衣})
	elseif self.资源组[9]:事件判断() then
		tp.显示变身卡造型 = not self.资源组[9].打勾框
		self.资源组[9]:置打勾框(tp.显示变身卡造型)
		发送数据(90,{选项="显示变身卡造型",回调=tp.显示变身卡造型})
		tp.场景.人物:置模型()
		tp.场景.人物:锦衣更换()
		if tp.队伍[1].装备[3] then
		tp.场景.人物:穿戴装备()
		end
		--场景类_玩家:置模型()
	elseif self.资源组[10]:事件判断() then
		tp.显示坐骑 = not self.资源组[10].打勾框
		self.资源组[10]:置打勾框(tp.显示坐骑)
		--tp.场景.人物:置模型()
		tp.场景.人物:锦衣更换()
		if tp.队伍[1].装备[3] then
		tp.场景.人物:穿戴装备()
		end
		发送数据(90,{选项="显示坐骑",回调=tp.显示坐骑})
	elseif self.资源组[30]:事件判断() then
		tp.显示翅膀 = not self.资源组[30].打勾框
		self.资源组[30]:置打勾框(tp.显示翅膀)
		tp.场景.人物:置模型()
		发送数据(90,{选项="显示翅膀",回调=tp.显示翅膀})
	elseif self.资源组[11]:事件判断() then
		低配模式 = not self.资源组[11].打勾框
		self.资源组[11]:置打勾框(低配模式)
		写配置("./config.ini","mhxy","低配模式",低配模式)
		if 低配模式 then
			-- 资源缓存:清空缓存()
		    tp.常规提示:打开("#Y已为您开启精简模式，已屏蔽部分非必要的场景特效。")
		else
			tp.常规提示:打开("#Y已为您关闭精简模式。")
		end
	elseif self.资源组[17]:事件判断() then--保存
		发送数据(90,{选项="音乐音量",回调=ceil(160*self.资源组[23]:取百分比())})
		发送数据(90,{选项="音效音量",回调=ceil(160*self.资源组[27]:取百分比())})
		tp.音乐音量=ceil(160*self.资源组[23]:取百分比())
		tp.音效音量=ceil(160*self.资源组[27]:取百分比())
		tp.常规提示:打开("#Y保存成功！")
	-- elseif self.资源组[18]:事件判断() then--重选角色
	-- 	self:打开()
	-- 	引擎.场景:登陆恢复()
	-- 	tp.进程 = 1
	-- 	系统退出=true
	-- 	客户端:断开()
	-- 	客户端:连接处理(系统参数.服务器.ip,系统参数.服务器.端口)
	elseif self.资源组[20]:事件判断() then--退出
		引擎关闭开始()
	-- elseif self.资源组[21]:事件判断() then--到主界面
	-- 	self:打开()
	-- 	引擎.场景:登陆恢复()
	-- 	tp.进程 = 1
	-- 	系统退出=true
	-- 	客户端:断开()
	-- elseif self.资源组[22]:事件判断() then--游戏团队
	-- 	tp.常规提示:打开(tostring(全局msg制作团队))
		-- __gge.messagebox(tostring(全局msg制作团队),"游戏团队",16)


	elseif self.资源组[12]:事件判断() then
		tp.连点模式 = not self.资源组[12].打勾框
		self.资源组[12]:置打勾框(tp.连点模式)
		if self.资源组[12].打勾框 then
			连点模式=true
			tp.常规提示:打开("#Y你开启了连点模式，长按鼠标3秒后可持续给人物或召唤兽分配属性点")
		elseif not self.资源组[12].打勾框 then
			连点模式=false
			tp.常规提示:打开("#Y你关闭了连点模式")
		end
		-- if 连点模式 then
		-- 	连点模式=false
		-- 	tp.常规提示:打开("#Y你关闭了连点模式")
		-- else
		-- 	连点模式=true
		-- 	tp.常规提示:打开("#Y你开启了连点模式，长按鼠标3秒后可持续给人物或召唤兽分配属性点")
		-- end


	elseif self.资源组[19]:事件判断() then--游戏团队
		-- table.print(tp.聊天框底图)
		tp.聊天框底图=tp.聊天框底图+1
		if tp.聊天框底图>10 then
		    tp.聊天框底图=1
		end
		写配置("./config.ini","mhxy","界面",tp.聊天框底图)
		tp.外部聊天框= tp.资源:载入('zdy.rpk',"网易WDF动画",tp.底图库[tp.聊天框底图])
		tp.外部聊天框:置区域(0,0,外部窗口宽度,810)--可以充填
		-- tp.常规提示:打开("#Y切换底图成功！")
		-- if 连点模式 then
		-- 	连点模式=false
		-- 	tp.常规提示:打开("#Y你关闭了连点模式")
		-- else
		-- 	连点模式=true
		-- 	tp.常规提示:打开("#Y你开启了连点模式，长按鼠标3秒后可持续给人物或召唤兽分配属性点")
		-- end
	elseif self.资源组[25]:事件判断() then
		local tbt = {"800*600","1024*768","1280*800","全屏窗口"}
		self.资源组[26]:打开(tbt)
		self.资源组[26]:置选中(self.游戏窗口设置)
	end

	self.焦点 = false
	self.资源组[1]:显示(self.x,self.y)
	tp.窗口标题背景_:显示(self.x-86+self.资源组[1].宽度/2,self.y)
	引擎.场景.字体表.标题字体:显示(self.x+self.资源组[1].宽度/2,self.y+3,"系统设置")
	self.资源组[2]:显示(self.x-18+self.资源组[1].宽度,self.y+3)
	self.资源组[28]:显示(self.x+104-91,self.y+32)
	-- 新定义
	self.资源组[17]:显示(self.x+13,self.y+397-116-10,true)
	-- self.资源组[18]:显示(self.x+14,self.y+32,true)
	self.资源组[19]:显示(self.x+150-22,self.y+397-116-10,true)
	self.资源组[20]:显示(self.x+314-45,self.y+397-116-10,true)
	-- self.资源组[21]:显示(self.x+212,self.y+214)
	-- if 连点模式 then
	-- 	self.资源组[21]:更新(dt)
	--     self.资源组[21]:显示(self.x+151,self.y+303-10)
	-- end
	-- self.资源组[21]:显示(self.x+14,self.y+65,true)
	-- self.资源组[22]:显示(self.x+14,self.y+98,true)

	local XX,YY = 104-91,33--256,99
	zts:置颜色(0xFF000000)
	zts:显示(self.x+XX+56,self.y+YY+2+5,"地图特效")
	zts:显示(self.x+XX+56,self.y+YY+55,"游戏音乐")
	zts:显示(self.x+XX+56,self.y+YY+85,"游戏音效")
	zts:显示(self.x+XX+56+72,self.y+YY+2+5,"窗口大小")

	zts:显示(self.x+XX+56,self.y+YY+115+7,"允许组队")
	zts:显示(self.x+XX+56,self.y+YY+145+7,"允许加好友")
	zts:显示(self.x+XX+56,self.y+YY+175+7,"显示锦衣")
	--zts:显示(self.x+XX+56,self.y+YY+175+7,"允许查看装备")
	zts:显示(self.x+XX+56,self.y+YY+205+7,"连点模式")
	zts:显示(self.x+XX+206,self.y+YY+115+7,"显示变身卡造型")
	zts:显示(self.x+XX+206,self.y+YY+145+7,"显示坐骑")
	zts:显示(self.x+XX+206,self.y+YY+175+7,"显示翅膀")
	zts:显示(self.x+XX+206,self.y+YY+205+7,"精简模式（推荐）")
	self.资源组[6]:显示(self.x+XX+16,self.y+YY+117,true,nil,nil,tp.允许组队,2) --允许组队
	self.资源组[7]:显示(self.x+XX+16,self.y+YY+147,true,nil,nil,tp.允许加好友,2) --允许加好友
	self.资源组[8]:显示(self.x+XX+16,self.y+YY+177,true,nil,nil,tp.显示锦衣,2) --显示锦衣
	--self.资源组[8]:显示(self.x+XX+16,self.y+YY+177,true,nil,nil,tp.允许查看装备,2) --允许查看装备
	self.资源组[9]:显示(self.x+XX+166,self.y+YY+117,true,nil,nil,tp.显示变身卡造型,2) --显示变身卡造型
	self.资源组[10]:显示(self.x+XX+166,self.y+YY+147,true,nil,nil,tp.显示坐骑,2) --显示坐骑
	self.资源组[30]:显示(self.x+XX+166,self.y+YY+177,true,nil,nil,tp.显示翅膀,2) --显示翅膀
	self.资源组[11]:显示(self.x+XX+166,self.y+YY+207,true,nil,nil,低配模式,2) --显示祥瑞
	self.资源组[12]:显示(self.x+XX+16,self.y+YY+207,true,nil,nil,tp.连点模式,2) --连点模式

	--==================地图特效
	self.资源组[5]:显示(self.x+XX+16,self.y+YY+2,true,nil,nil,tp.地图特效,2) --地图特效
	---===================音效类
	-- self.资源组[3]:显示(self.x+XX+16,self.y+YY+50,true)  --游戏音乐
	self.资源组[3]:显示(self.x+XX+16,self.y+YY+50,true,nil,nil,tp.音乐开启,2)
	self.资源组[4]:显示(self.x+XX+16,self.y+YY+80,true,nil,nil,tp.音效开启,2) --游戏音效
	self.资源组[29]:显示(self.x+XX+129,self.y+YY+52)
	self.资源组[29]:显示(self.x+XX+129,self.y+YY+82)
	self.资源组[23]:显示(self.x+XX+129,self.y+YY+54,x,y,self.鼠标)
	self.资源组[27]:显示(self.x+XX+129,self.y+YY+84,x,y,self.鼠标)
	---===================窗口类
	self.资源组[24]:显示(self.x+XX+129+72,self.y+4+YY)
	self.资源组[25]:显示(self.x+XX+238+57,self.y+4+YY)
	self.资源组[26]:显示(self.x+XX+129+72,self.y+YY+18+4,x,y,self.鼠标)
	zts:置颜色(白色)
	zts:显示(self.x+XX+145+72,self.y+YY+2+5,self.游戏窗口设置)


	zts:置颜色(-16777216)

	--============
	-- zts:置颜色(-256)
	-- zts:显示(self.x+314,self.y+359,"游戏素材来源于网络，\n游戏免费勿商用！")
	if self.资源组[23].接触 then--音量"
		tp.音乐音量 = ceil(160*self.资源组[23]:取百分比())
		tp.音乐:置音量(tp.音乐音量)
		self.焦点 = true
	end
	if self.资源组[27].接触 then--音量"
		tp.音效音量 = ceil(160*self.资源组[27]:取百分比())
		self.焦点 = true
	end
	if self.资源组[26]:事件判断() then
		local 宽高s =self.资源组[26].弹出事件
		if 宽高s ~= "全屏窗口" then
			if 引擎.是否全屏 then
			    引擎.置全屏()
			end
			local 宽高XY =分割文本2(宽高s,"*")
			if not 判断是否数组(宽高XY) then
			    return false
			end
			local 宽高X,宽高Y=math.ceil(宽高XY[1]) or 800,math.ceil(宽高XY[2]) or 600
			引擎.置宽高(宽高X,宽高Y)
			全局游戏宽度 = 宽高X
			全局游戏高度 = 宽高Y
			withs = 全局游戏宽度
			hegts = 全局游戏高度
			with = 全局游戏宽度/2
			hegt = 全局游戏高度/2
			写配置("./config.ini","mhxy","宽度",全局游戏宽度)
			写配置("./config.ini","mhxy","高度",全局游戏高度)
			self.游戏窗口设置 = 宽高s
		else
			if (全局游戏宽度==1024 and 全局游戏高度==768 )or(全局游戏宽度==1280 and 全局游戏高度==720 )or(全局游戏宽度==1280 and 全局游戏高度==768 )or(全局游戏宽度==1280 and 全局游戏高度==800 )  then
			    引擎.置全屏()
			    self.游戏窗口设置 = 宽高s
				if not 引擎.是否全屏 then
				    self.游戏窗口设置 = 全局游戏宽度.."*"..全局游戏高度
				end
			end
		end
		引擎.场景.窗口.消息框:调整分辨率()
		if 全局游戏宽度==1024 or 全局游戏宽度==1280 then
			游戏传音.高度=150
		else
		    游戏传音.高度=0
		end
		self.资源组[26].弹出事件 = nil
	end
end

function 场景类_系统设置:检查点(x,y)
	if self.资源组[1]:是否选中(x,y)  then
		return true
	end
end

function 场景类_系统设置:初始移动(x,y)
	tp.运行时间 = tp.运行时间 + 1
	if not 引擎.消息栏焦点 then
  		self.窗口时间 = tp.运行时间
 	end
	if not self.焦点 then
		tp.移动窗口 = true
	end
	if self.鼠标 and  not self.焦点 then
		self.xx = x - self.x
		self.yy = y - self.y
	end
end

function 场景类_系统设置:开始移动(x,y)
	if self.鼠标 then
		self.x = x - self.xx
		self.y = y - self.yy
	end
end

return 场景类_系统设置