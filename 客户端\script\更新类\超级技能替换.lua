--======================================================================--

--======================================================================--
local 超级技能替换 = class()
local floor = math.floor
local tp,zts,zt
local format = string.format
local insert = table.insert



function 超级技能替换:初始化(根)
	self.ID = 20272.1
	self.x = 280
	self.y = 270
	self.xx = 0
	self.yy = 0
	self.注释 = "超 值 首 充"
	self.可视 = false
	self.鼠标 = false
	self.焦点 = false
	self.可移动 = true
	tp = 根
	self.窗口时间 = 0
	zts = tp.字体表.道具字体
	zts1= tp.字体表.描边字体
	zt = tp.字体表.描边字体
	self.进程=1
	self.主选择=nil
	self.控件类 = require("ggeui/加载类")()
	local 总控件 = self.控件类:创建控件('序号控件')
	总控件:置可视(true,true)
	self.输入框 = 总控件:创建输入("卡号输入",0,0,180,14)
	self.输入框:置可视(false,false)
	self.输入框:置限制字数(30)
	self.输入框:屏蔽快捷键(true)
	self.输入框:置光标颜色(-16777216)
	self.输入框:置文字颜色(-16777216)

	self.现在技能组 = {}
	local jn = tp._技能格子
	for i=1,4 do
	    self.现在技能组[i] = jn(0,0,i,"现在技能组")
	end

end

function 超级技能替换:打开(内容)

	if self.可视 then
		self.可视 = false
		self.输入框:置可视(false,false)

		self.资源组 = nil
		self.物品组={}
		return
	else
		insert(tp.窗口_,self)
		local 资源 = tp.资源
		local 按钮 = tp._按钮
		local 自适应 = tp._自适应
		self.资源组 = {
			[1] = 自适应.创建(0,1,300,200,3,9),
			[2] = 资源:载入('wzife.wd3',"网易WDF动画",0x2436C9A1),
			[7] = 按钮.创建(自适应.创建(12,4,55,22,1,3),0,0,4,true,true,"替换"),
		}


		self.线 = tp.资源:载入("wzife.wd1","网易WDF动画",999600305)
		self.线:置区域(0,0,350,2)
		self.数据=数据
		self.内容=内容
		tp.运行时间 = tp.运行时间 + 1
	    	self.窗口时间 = tp.运行时间
	    	self.可视 = true
	    	self.输入框:置可视(true,true)
	    	self.物品组={}
		self.主选择=内容.主选择
		self.现在技能组=内容.赐福技能
	end
end



function 超级技能替换:显示(dt,x,y)

	self.焦点 = false
	self.资源组[1]:显示(self.x+70,self.y)
	tp.窗口标题背景_:显示(self.x-15+self.资源组[1].宽度/2,self.y)
	引擎.场景.字体表.标题字体:显示(self.x+70+self.资源组[1].宽度/2,self.y+3,"赐福替换保存")

	  local 偏移x,偏移y = 等比例缩放公式(450,1,self.线.宽度,self.线.高度)
	self.线:显示(self.x+120,self.y+100,偏移x,偏移y)

	self.资源组[7]:更新(x,y)

	if self.主选择~=nil then
			for n=1,4 do
				if   #tp.队伍[1].宝宝列表[self.主选择].赐福列表==0   then
					self.现在技能组 = {}
					local jn = tp._技能格子
					for i=1,4 do
					    self.现在技能组[i] = jn(0,0,i,"现在技能组")
					end
					tp.物品格子背景_:显示(self.x+30+n*65,self.y+40)
				else
				tp.物品格子背景_:显示(self.x+30+n*65,self.y+40)
				self.现在技能组[n]:置坐标(self.x+36+n*65,self.y+46)
				self.现在技能组[n]:显示(x,y,self.鼠标)
				if self.现在技能组[n].焦点 and self.现在技能组[n].技能 ~= nil  and self.鼠标 then
					tp.提示:技能(x,y,self.现在技能组[n].技能)
				end

				end
			end

			for n=1,4 do
				if   #tp.队伍[1].宝宝列表[self.主选择].赐福列表==0   then
					self.现在技能组 = {}
					local jn = tp._技能格子
					for i=1,4 do
					    self.现在技能组[i] = jn(0,0,i,"现在技能组")
					end
					tp.物品格子背景_:显示(self.x+30+n*65,self.y+110)
				else
				tp.物品格子背景_:显示(self.x+30+n*65,self.y+110)
				self.现在技能组[n]:置坐标(self.x+36+n*65,self.y+116)
				self.现在技能组[n]:显示(x,y,self.鼠标)
				if self.现在技能组[n].焦点 and self.现在技能组[n].技能 ~= nil  and self.鼠标 then
					tp.提示:技能(x,y,self.现在技能组[n].技能)
				end

				end
			end
	end
	self.资源组[7]:显示(self.x +192, self.y + 170)


	if self.资源组[7]:事件判断() then

    		self:打开()
    		return
    	end



end



function 超级技能替换:刷新(内容)

	self.内容=内容

end

function 超级技能替换:检查点(x,y)
	if self.资源组 ~= nil and self.资源组[1]:是否选中(x,y)  then
		return true
	end
end

function 超级技能替换:初始移动(x,y)
	tp.运行时间 = tp.运行时间 + 1
	if not tp.消息栏焦点 then
  		self.窗口时间 = tp.运行时间
 	end
	if not self.焦点 then
		tp.移动窗口 = true
	end
	if self.鼠标 and  not self.焦点 then
		self.xx = x - self.x
		self.yy = y - self.y
	end
end

function 超级技能替换:开始移动(x,y)
	if self.鼠标 then
		self.x = x - self.xx
		self.y = y - self.yy
	end
end

return 超级技能替换