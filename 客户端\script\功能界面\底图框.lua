--======================================================================--
-- @作者: www.wnfj.com，联系QQ：79550111
-- @创建时间:   2018-03-03 02:34:19
-- @Last Modified time: 2025-07-22 15:05:03
--======================================================================--
local 系统类_底图框 = class()
local 矩阵 = require("gge包围盒")(650,587,350,35)
local tp;
local keyaz = 引擎.按键按住
local keyax = 引擎.按键按下
local keytq = 引擎.按键弹起
local KEY = KEY

function 系统类_底图框:初始化(根)
	local 按钮 = 根._按钮
	 local 自适应 = 根._自适应
	local 资源 = 根.资源
	self.UI_底图 = 资源:载入('wzife.wd2',"网易WDF动画",0x3D1FA249)
	self.UI_攻击 = 按钮.创建(资源:载入('wzife.wdf',"网易WDF动画",0x6BBC42FA),0,0,4,true)
	self.UI_道具 = 按钮.创建(资源:载入('wzife.wdf',"网易WDF动画",0x0E53F705),0,0,4,true)
	self.UI_给予 = 按钮.创建(资源:载入('wzife.wdf',"网易WDF动画",0x7E4DE3DE),0,0,4,true)
	self.UI_交易 = 按钮.创建(资源:载入('wzife.wdf',"网易WDF动画",0xCAB0B8B4),0,0,4,true)
	self.UI_队伍 = 按钮.创建(资源:载入('wzife.wdf',"网易WDF动画",0x0D3EA20B),0,0,4,true)
	self.UI_宠物 = 按钮.创建(资源:载入('wzife.wdf',"网易WDF动画",0x187ABFC8),0,0,4,true)
	self.UI_任务 = 按钮.创建(资源:载入('wzife.wdf',"网易WDF动画",0xA15292B2),0,0,4,true)
	self.UI_帮派 = 按钮.创建(资源:载入('wzife.wdf',"网易WDF动画",0xC35B2EC3),0,0,4,true)
	self.UI_快捷 = 按钮.创建(资源:载入('wzife.wdf',"网易WDF动画",0xBB6E607E),0,0,4,true)
	self.UI_好友 = 按钮.创建(资源:载入('wzife.wdf',"网易WDF动画",0x7C7A64D9),0,0,4,true)
	self.UI_成就 = 按钮.创建(资源:载入('wzife.wd2',"网易WDF动画",0x8B3AADDA),0,0,4,true)
	self.UI_动作 = 按钮.创建(资源:载入('wzife.wdf',"网易WDF动画",0x548156A0),0,0,4,true)
	self.UI_系统 = 按钮.创建(资源:载入('wzife.wdf',"网易WDF动画",0x5116F7DF),0,0,4,true)
	self.UI_体验状态 = 按钮.创建(资源:载入('wzife.wdf',"网易WDF动画",2161957867),0,0,4,true)
	self.好友闪烁 = 资源:载入('wzife.wdf',"网易WDF动画",0x6A062464) --723023243  1831469208
	self.申请闪烁 = 资源:载入('wzife.wdf',"网易WDF动画",0XD3EA20B)
	self.临时背包 = 按钮.创建(资源:载入('wzife.wdf',"网易WDF动画",0x237d38da),0,0,4,true)
	self.背包闪烁 = 资源:载入('wzife.wdf',"网易WDF动画",0x237d38da)
	self.充值 = 按钮.创建(资源:载入('bc.wdf',"网易WDF动画",0x00000019),0,0,1,true)
	self.首冲 = 按钮.创建(资源:载入('bc.wdf',"网易WDF动画",0x00000012),0,0,1,true)
	self.寻宝 = 按钮.创建(资源:载入('bc.wdf',"网易WDF动画",0x00000026),0,0,1,true)
	--self.收缩 = 按钮.创建(资源:载入('wzife.wdf',"网易WDF动画",0x6EDD4D71),0,0,4)
	--按钮.创建(资源:载入('wzife.wdf',"网易WDF动画",0x86D66B9A),0,0,4,true,true,"退出游戏"),
	self.藏宝阁= 按钮.创建(资源:载入('zdy.rpk',"网易WDF动画",16777504),0,0,4)
	self.抓鬼 = 按钮.创建(资源:载入('zdy.rpk',"网易WDF动画",16777746),0,0,4)
	self.图鉴 = 按钮.创建(资源:载入('zdy.rpk',"网易WDF动画",16777746),0,0,4)
	--self.抓鬼= 按钮.创建(自适应.创建(12,4,75,22,1,3),0,0,4,true,true,"自动抓鬼")
	-- self.商城按钮 = 按钮.创建(资源:载入('wzife.wd2',"网易WDF动画",3549461652),0,0,4,true)
	-- self.sc=资源:载入('common/wzife.wdf',"网易WDF动画",892834785)
	self.商城按钮=按钮.创建(资源:载入('common/wzife.wd1',"网易WDF动画",3344525719),0,0,4,true)--按钮.创建(资源:载入('common/wzife.wdf',"网易WDF动画",680315249),0,0,1,true)
	self.排列={
                             [1]=按钮.创建(自适应.创建(17,4,70,22,1,1),0,0,4,true,nil,"打开菜单"),
                             [2]=按钮.创建(自适应.创建(17,4,70,22,1,1),0,0,4,true,nil,"游戏排行"),
                             [3]=按钮.创建(自适应.创建(17,4,70,22,1,1),0,0,4,true,nil,"游戏充值"),
                             [4]=按钮.创建(自适应.创建(17,4,70,22,1,1),0,0,4,true,nil,"福利中心"),
                             [5]=按钮.创建(自适应.创建(17,4,70,22,1,1),0,0,4,true,nil,"藏 宝 阁"),
                             [6]=按钮.创建(自适应.创建(17,4,70,22,1,1),0,0,4,true,nil,"配饰系统"),
                             [7]=按钮.创建(自适应.创建(17,4,70,22,1,1),0,0,4,true,nil,"宝宝进化"),
                             [8]=按钮.创建(自适应.创建(17,4,70,22,1,1),0,0,4,true,nil,"角色多开"),
                             [9]=按钮.创建(自适应.创建(17,4,70,22,1,1),0,0,4,true,nil,"一键附魔"),
                             [10]=按钮.创建(自适应.创建(17,4,70,22,1,1),0,0,4,true,nil,"共享仓库"),
                             [11]=按钮.创建(自适应.创建(17,4,70,22,1,1),0,0,4,true,nil,"超级行囊"),
                             --[6]=按钮.创建(自适应.创建(17,4,70,22,1,1),0,0,4,true,nil,"月卡系统"),
                             --[7]=按钮.创建(自适应.创建(17,4,70,22,1,1),0,0,4,true,nil,"开启遇怪"),
                             --[8]=按钮.创建(自适应.创建(17,4,70,22,1,1),0,0,4,true,nil,"一键附魔"),
                            -- [9]=按钮.创建(自适应.创建(17,4,70,22,1,1),0,0,4,true,nil,"精灵系统"),
             }

             if   授权码 =="9xRVCmGmz33nf5dRwnhfYregfrrBz5" or 授权码 =="xx7r6TsUfrQadChQcfxWBHkwA" or 授权码 =="xx7r6TsUfrQadChQcfxWBHkwA02"   then
             	self.排列={
                             [1]=按钮.创建(自适应.创建(17,4,70,22,1,1),0,0,4,true,nil,"打开菜单"),
                             [2]=按钮.创建(自适应.创建(17,4,70,22,1,1),0,0,4,true,nil,"游戏排行"),
                             [3]=按钮.创建(自适应.创建(17,4,70,22,1,1),0,0,4,true,nil,"游戏充值"),
                             [4]=按钮.创建(自适应.创建(17,4,70,22,1,1),0,0,4,true,nil,"福利中心"),
                             [5]=按钮.创建(自适应.创建(17,4,70,22,1,1),0,0,4,true,nil,"藏 宝 阁"),
                             [6]=按钮.创建(自适应.创建(17,4,70,22,1,1),0,0,4,true,nil,"配饰系统"),
                             [7]=按钮.创建(自适应.创建(17,4,70,22,1,1),0,0,4,true,nil,"宝宝进化"),
                             [8]=按钮.创建(自适应.创建(17,4,70,22,1,1),0,0,4,true,nil,"角色多开"),
                             [9]=按钮.创建(自适应.创建(17,4,70,22,1,1),0,0,4,true,nil,"一键附魔"),
                             --[10]=按钮.创建(自适应.创建(17,4,70,22,1,1),0,0,4,true,nil,"共享仓库"),
                             --[6]=按钮.创建(自适应.创建(17,4,70,22,1,1),0,0,4,true,nil,"月卡系统"),
                             --[7]=按钮.创建(自适应.创建(17,4,70,22,1,1),0,0,4,true,nil,"开启遇怪"),
                             --[8]=按钮.创建(自适应.创建(17,4,70,22,1,1),0,0,4,true,nil,"一键附魔"),
                            -- [9]=按钮.创建(自适应.创建(17,4,70,22,1,1),0,0,4,true,nil,"精灵系统"),
             }
             end
	tp = 根
	self.临时背包闪烁=false

end

function 系统类_底图框:显示(dt,x,y)
	self.UI_快捷:更新(x,y)
	--self.UI_体验状态:更新(x,y,not tp.战斗中)
	-- self.商城按钮:更新(x,y,not tp.战斗中)
	if (keyaz(KEY.ALT) and keyax(KEY.F4)) then
		错误关闭数=100
		引擎关闭开始()
	end
	if not tp.战斗中 then
		self.UI_攻击:更新(x,y,not tp.战斗中)
		self.UI_道具:更新(x,y,not tp.战斗中)
		self.UI_给予:更新(x,y,not tp.战斗中)
		self.UI_交易:更新(x,y,not tp.战斗中)
		self.UI_队伍:更新(x,y,not tp.战斗中)
		self.UI_宠物:更新(x,y,not tp.战斗中)
		self.UI_任务:更新(x,y,not tp.战斗中)
		self.UI_帮派:更新(x,y,not tp.战斗中)
		self.UI_好友:更新(x,y,not tp.战斗中)
		self.UI_成就:更新(x,y,not tp.战斗中)
		self.UI_动作:更新(x,y,not tp.战斗中)
		self.UI_系统:更新(x,y,not tp.战斗中)

		--self.收缩:更新(x,y,not tp.战斗中)
		-- self.藏宝阁:更新(x,y,not tp.战斗中)
		-- self.图鉴:更新(x,y,not tp.战斗中)
		-- self.抓鬼:更新(x,y,not tp.战斗中)
		if keytq(KEY.F9) then
			玩家屏蔽 = not 玩家屏蔽
		end
		self.商城按钮:更新(x,y)
		self.商城按钮:显示(全局游戏宽度-59,全局游戏高度-116)
		--self.充值:更新(x,y)
		-- self.藏宝阁:更新(x,y)
		-- self.图鉴:更新(x,y)
		-- self.抓鬼:更新(x,y)
		if not 武神坛模式 then
		self.充值:更新(x,y,not tp.战斗中)
		self.首冲:更新(x,y)
		self.寻宝:更新(x,y)
			if 授权码~= "ugzaDy7b4wSHsNY" then
				self.充值:显示(130,20)
				self.寻宝:显示(175,20)
				-- if 授权码 ~="ukStGu7rt2DVdSRhwK5UbU87kjC8uR"  then
				-- 	if tp.队伍[1].首冲礼包 ~= true then
				-- 		self.首冲:更新(x,y)
				-- 		self.首冲:显示(220,25)
				-- 	 end
				-- end
			else
				if tp.队伍[1].首冲礼包 ~= true then
					self.首冲:更新(x,y)
					self.首冲:显示(130,25)
			 	end
			end
		end
		--self.收缩:更新(x,y)
		--print(全局游戏高度)


		--self.收缩:显示(10,100)
		-- self.抓鬼:显示(10,125)
		-- self.藏宝阁:显示(10,155)
		--self.图鉴:显示(10,185)
		--self.充值:显示(160,5+全局游戏高度-31,true)
		if 引擎.场景.队伍[1].体验状态 then
		    self.UI_体验状态:更新(x,y)
		    self.UI_体验状态:显示(全局游戏宽度/2,全局游戏高度-216)
		    if self.UI_体验状态:事件判断() then
		        tp.窗口.对话栏:文本("","体验状态","您的帐号目前处于体验状态，在以下的时间里，您将以“体验状态”进行游戏，在线消除“体验状态”，请点击“消除体验状态”，祝您游戏愉快！",{"什么是体验状态","消除体验状态","关闭"})
		    end
		end

        if (keyaz(KEY.ALT) and keyax(KEY.U)) then
			tp.窗口.坐骑属性栏:打开()
		elseif self.UI_攻击:事件判断() or (keyaz(KEY.ALT) and keytq(KEY.A))  then
			-- tp.窗口.装备开运:打开(0.2)
			-- 发送数据(33,{序列=0})
			tp.鼠标.置鼠标("平时攻击")
		elseif self.商城按钮:事件判断() or (keyaz(KEY.ALT) and keyax(KEY.D)) and not tp.消息栏焦点 then
			-- if tp.窗口.商城类.可视 then
			--     tp.窗口.商城类:打开()
			-- else
			-- 	发送数据(29)
			-- end
			发送数据(29.5)
			--tp.窗口.商城类:打开()
		elseif self.UI_动作:事件判断() then
			if not 武神坛模式 then
				发送数据(94.7)
			else
				tp.常规提示:打开("#Y武神坛模式无法使用该功能")
			end
		elseif self.抓鬼:事件判断() then
				发送数据(94.7)
			-- local x="{}"
			-- for i=1,30 do
			-- 	发送数据(10101,{x="{}",y={}})
			-- end
			-- print(11111)
			-- --tp.窗口.临时背包:打开()
			-- if 引擎.场景.飞行 == false then
			-- 	引擎.场景.飞行 = true
			-- else
			-- 	引擎.场景.飞行 = false
			-- end
		elseif self.UI_成就:事件判断() then  --排行榜
			if not 武神坛模式 then
			发送数据(95.3)
			else
				tp.常规提示:打开("#Y武神坛模式无法使用该功能")
			end
		elseif	self.藏宝阁:事件判断() then
			--tp.窗口.藏宝阁出售:打开(内容)
			发送数据(65,{文本="打开"})
				--发送数据(95.2)
		elseif self.充值:事件判断() then

			 if 内充系统开启==true then
		              	发送数据(115,{动作=1})
		            else
		                	发送数据(94.8)
		            end

			--tp.窗口.月卡系统:打开()
		elseif	self.图鉴:事件判断() then
			发送数据(191)

			--tp.窗口.图鉴:打开()
		elseif self.首冲:事件判断() then
			--if  授权码~= "ugzaDy7b4wSHsNY" then
			发送数据(61.6,{文本="打开"})
			--end
			--tp.窗口.首冲:打开()

		elseif self.寻宝:事件判断() then
			if  授权码~= "ugzaDy7b4wSHsNY" then
			发送数据(61.5,{文本="打开"})
			end

		elseif self.UI_道具:事件判断() or (keyaz(KEY.ALT) and keyax(KEY.E)) and not tp.消息栏焦点  then
			if tp.窗口.道具行囊.可视 then
				tp.窗口.道具行囊:打开()
			else
				发送数据(3699)
			end
		elseif (keyaz(KEY.ALT) and keytq(KEY.W)) and not tp.消息栏焦点  then
			if tp.窗口.人物状态栏.可视==false then
				发送数据(7)
			else
				tp.窗口.人物状态栏:打开()
			end
		elseif self.UI_宠物:事件判断() or ( keyaz(KEY.ALT) and keyax(KEY.P)) and not tp.消息栏焦点 then
			if tp.窗口.宠物状态栏.可视==false then
				发送数据(5006)
			else
				tp.窗口.宠物状态栏:打开()
			end
		elseif (keyaz(KEY.ALT) and keyax(KEY.O)) and not tp.消息栏焦点  then
			if tp.窗口.召唤兽属性栏.可视==false then
				发送数据(5001)
			else
				tp.窗口.召唤兽属性栏:打开()
			end
		elseif self.UI_帮派:事件判断() or (keyaz(KEY.ALT) and keyax(KEY.B)) and not tp.消息栏焦点  then
			if tp.窗口.帮派界面.可视==false then
				发送数据(36)
				--tp.窗口.帮派界面:打开()
			else
				tp.窗口.帮派界面:打开()
			end
		elseif self.UI_快捷:事件判断() or (keyaz(KEY.ALT) and keyax(KEY.C)) and not tp.消息栏焦点  then
			if tp.快捷技能显示 then
				tp.快捷技能显示 = not tp.快捷技能显示
			elseif not tp.快捷技能显示 then
				local xsf = false
				for i=1,8 do
					if tp.窗口.快捷技能栏.技能格子~=nil and tp.窗口.快捷技能栏.技能格子[i]~=nil and tp.窗口.快捷技能栏.技能格子[i].技能~=nil then
						xsf = true
					end
				end
				if not xsf then
					发送数据(28)
				end
				tp.快捷技能显示 = not tp.快捷技能显示
			end
		elseif self.UI_任务:事件判断() or (keyaz(KEY.ALT) and keyax(KEY.Q)) and not tp.消息栏焦点 and not 武神坛模式  then
			if tp.窗口.任务栏.可视==false then
				发送数据(10)
			else
				tp.窗口.任务栏:打开()
			end
		--elseif self.UI_体验状态:事件判断() and not tp.消息栏焦点  then
			--发送数据(56)
		elseif self.UI_给予:事件判断() or (keyaz(KEY.ALT) and keyax(KEY.G)) and not tp.消息栏焦点  then
			tp.鼠标.置鼠标("给予")
		elseif self.UI_交易:事件判断() or (keyaz(KEY.ALT) and keyax(KEY.X)) and not tp.消息栏焦点  then
			 tp.鼠标.置鼠标("交易")
		elseif (self.好友闪烁:是否选中(x,y) and self.UI_好友:是否选中(x,y) and 引擎.鼠标弹起(0)) or self.UI_好友:事件判断() or( keyaz(KEY.ALT) and keyax(KEY.F)) and not tp.消息栏焦点  then
			-- 引擎.场景.常规提示:打开("#Y/好友系统正在调试中，暂时关闭。")
			if tp.窗口.好友列表.可视 then
				tp.窗口.好友列表:打开()
			else
				发送数据(6956)
	   			-- 发送数据(16)
			end
		elseif (self.申请闪烁:是否选中(x,y) and self.UI_队伍:是否选中(x,y) and 引擎.鼠标弹起(0)) or self.UI_队伍:事件判断() or (keyaz(KEY.ALT) and keyax(KEY.T)) and not tp.消息栏焦点  then
			if tp.窗口.队伍栏.可视==false then
			   发送数据(4001)
			else
			  tp.窗口.队伍栏:打开()
			end
			申请队伍=false
		elseif self.UI_系统:事件判断() or (keyaz(KEY.ALT) and keyax(KEY.S)) and not tp.消息栏焦点  then
			tp.窗口.系统设置:打开()
			--tp.窗口.月卡系统:打开()
		end
	else
		if self.UI_快捷:事件判断() or (keyaz(KEY.ALT) and keyax(KEY.C)) and not tp.消息栏焦点  then
			发送数据(12)
		end
	end
	self.UI_底图:显示(全局游戏宽度-350,全局游戏高度-63)
	self.UI_攻击:显示(全局游戏宽度-346,全局游戏高度-34,true)
	self.UI_道具:显示(全局游戏宽度-320,全局游戏高度-34,true)
	self.UI_给予:显示(全局游戏宽度-294,全局游戏高度-34,true)
	self.UI_交易:显示(全局游戏宽度-269,全局游戏高度-34,true)
	self.UI_队伍:显示(全局游戏宽度-243,全局游戏高度-34,true)
	self.UI_宠物:显示(全局游戏宽度-218,全局游戏高度-34,true)
	self.UI_任务:显示(全局游戏宽度-191,全局游戏高度-34,true)
	self.UI_帮派:显示(全局游戏宽度-164,全局游戏高度-34,true)
	self.UI_快捷:显示(全局游戏宽度-137,全局游戏高度-34,true)
	self.UI_好友:显示(全局游戏宽度-110,全局游戏高度-34,true)
	-- self.sc:显示(全局游戏宽度-59,全局游戏高度-126)
local xx=0
             for n=1,self.排列[1].按钮文字=="打开菜单" and 1 or #self.排列 do
                   xx=xx+1
                   if self.排列[n] and not tp.战斗中 then
                      self.排列[n]:更新(x,y)
                      if not 武神坛模式 then          --武神坛
                      self.排列[n]:显示(10,95+xx*30-30)
                      end
                      if self.排列[n].事件 then
                         if n==1 then
                            self.排列[n].按钮文字=self.排列[1].按钮文字=="打开菜单" and "关闭菜单" or "打开菜单"
                         elseif n==2 then
			发送数据(95.3)
		elseif n==3 then
			 if 内充系统开启==true then
		              	发送数据(115,{动作=1})
		            else
		                	发送数据(94.8)
		            end
			--发送数据(94.8)
    --                      	  if tp.窗口.VIP系统.可视==false then
		  --    发送数据(45.1)
		  -- else
		  --    tp.窗口.VIP系统:打开()
		  -- end
		elseif n==4 then
			发送数据(94.7)
    --                      	  if tp.窗口.新充值界面.可视==false then
		  --    发送数据(115)
		  -- else
		  --    tp.窗口.新充值界面:打开()
		  -- end
		elseif n==5 then
			发送数据(65,{文本="打开"})
    --                      	  if tp.窗口.阿斌_助战系统.可视==false then
		  --    发送数据2(1,1,677)
		  -- else
		  --    tp.窗口.阿斌_助战系统:打开()
		  -- end
		elseif n==6 then
		  发送数据(7000)
		    -- tp.窗口.超级传送:打开()
		elseif n==7 then
			--tp.常规提示:打开("#Y/等待开放...")
			--tp.窗口.进化宝宝:打开()
			 发送数据(3823)
                            -- self.排列[n].按钮文字=self.排列[7].按钮文字=="开启遇怪" and "关闭遇怪" or "开启遇怪"
                            -- if self.排列[n].按钮文字=="关闭遇怪" then
                            --    自动遇怪=os.time()
                            -- else
                            --    自动遇怪=nil
                            -- end
		elseif n==8 then
                            --发送数据(263)
	                             if tp.窗口.阿斌_助战系统.可视==false then
			     	发送数据2(1,1,677)
			  else
			     tp.窗口.阿斌_助战系统:打开()
			  end
		elseif n==9 then
			发送数据(111)
                            --发送数据(249)
                        elseif n==10 then
                        	--if 授权码 ~="ugzaDy7b4wSHsNY" then
                        	--if 授权码 ~="9xRVCmGmz33nf5dRwnhfYregfrrBz5" then
				if tp.窗口.共享仓库类.可视 then
					tp.窗口.共享仓库类:打开()
				else
					发送数据(6751)
				end
			-- else
			-- 	引擎.场景.常规提示:打开("#Y暂时未开通，敬请期待！")
			-- end
		elseif n==11 then
			发送数据(3832,{序列=1})

                         end
                      end
                   end
             end
             if 自动遇怪 and not tp.战斗中 then
                if 自动遇怪+5<=os.time() then
                   发送数据(1100)
                   自动遇怪=os.time()
                end
             end
	if not tp.战斗中 and 消息闪烁 then
		self.好友闪烁:更新(dt)
	    self.好友闪烁:显示(全局游戏宽度-110,全局游戏高度-34)
	end
	if not tp.战斗中 and self.临时背包闪烁 then
		self.临时背包:更新(x,y,not tp.战斗中)
		self.临时背包:显示(全局游戏宽度-380,全局游戏高度-86)
		self.背包闪烁:更新(dt)
		self.背包闪烁:显示(全局游戏宽度-380,全局游戏高度-86)
		if self.临时背包:事件判断() then
			发送数据(3749,{方式="索取"})
		end
	end
	-- if not tp.战斗中 and self.临时背包闪烁 then
	-- 	self.临时背包:更新(x,y,not tp.战斗中)
	-- 	self.临时背包:显示(全局游戏宽度-380,全局游戏高度-86)
	-- 	if self.临时背包:事件判断() then
	-- 		发送数据(3749,{方式="索取"})
	-- 	end
	-- end
	if not tp.战斗中 and 申请队伍 then
		self.申请闪烁:更新(dt)
		self.申请闪烁:显示(全局游戏宽度-243,全局游戏高度-34)
	end
	self.UI_成就:显示(全局游戏宽度-81,全局游戏高度-31,true)
	self.UI_动作:显示(全局游戏宽度-53,全局游戏高度-34,true)
	self.UI_系统:显示(全局游戏宽度-26,全局游戏高度-34,true)

	if self:检查点(x,y) then
		tp.按钮焦点 = true
	end
	if self.UI_攻击:是否选中(x,y) then
		tp.提示:自定义(x-42,y+27,"快捷键:ALT+A")
	elseif self.商城按钮:是否选中(x,y)  then
		tp.提示:自定义(x-42,y+27,"快捷键:ALT+D")
	elseif self.UI_道具:是否选中(x,y)  then
		tp.提示:自定义(x-42,y+27,"快捷键:ALT+E")
	elseif self.UI_给予:是否选中(x,y)  then
		tp.提示:自定义(x-42,y+27,"快捷键:ALT+G")
	elseif self.UI_交易:是否选中(x,y)  then
		tp.提示:自定义(x-42,y+27,"快捷键:ALT+X")
	elseif self.UI_队伍:是否选中(x,y) then -- or self.申请闪烁:是否选中(x,y)
		tp.提示:自定义(x-42,y+27,"快捷键:ALT+T")
	elseif self.UI_宠物:是否选中(x,y)  then
		tp.提示:自定义(x-42,y+27,"快捷键:ALT+P")
	elseif self.UI_任务:是否选中(x,y)  then
		tp.提示:自定义(x-42,y+27,"快捷键:ALT+Q")
	elseif self.UI_帮派:是否选中(x,y)  then
		tp.提示:自定义(x-42,y+27,"快捷键:ALT+B")
	elseif self.UI_快捷:是否选中(x,y)  then
		tp.提示:自定义(x-42,y+27,"快捷键:ALT+C")
	elseif self.UI_好友:是否选中(x,y) then -- or self.好友闪烁:是否选中(x,y)
		tp.提示:自定义(x-42,y+27,"快捷键:ALT+F")
	elseif self.UI_成就:是否选中(x,y)  then
		tp.提示:自定义(x-42,y+27,"梦幻风云榜")
	-- elseif self.藏宝阁:是否选中(x,y) then
	-- 	tp.提示:自定义(x-42,y+27,"藏宝阁交易中心")
	-- elseif self.图鉴:是否选中(x,y) then
	-- 	tp.提示:自定义(x-42,y+27,"图鉴系统")
	elseif self.UI_动作:是否选中(x,y)  then   --or  self.抓鬼:是否选中(x,y)
		tp.提示:自定义(x-42,y+27,"月卡、赞助、抓鬼")
	elseif self.UI_系统:是否选中(x,y)  then
		tp.提示:自定义(x-42,y+27,"快捷键:ALT+S")
	end
end

function 系统类_底图框:检查点(x,y)
	return 矩阵:检查点(x,y)
end

return 系统类_底图框