--======================================================================--
-- @作者: www.wnfj.com，联系QQ：79550111
-- @创建时间:   2018-03-03 02:34:19
-- @Last Modified time: 2024-12-05 17:26:06
--======================================================================--
local 场景类_进化宝宝 = class()
local require = require
local tp,zts1,zts2
local floor = math.floor
local format = string.format
local insert = table.insert
local min = math.min
local ceil = math.ceil
local box = 引擎.画矩形
local bw = require("gge包围盒")(0,0,124,42)
local tx = 引擎.取头像
local mouseb = 引擎.鼠标弹起
local ani = 引擎.取战斗模型
local bds = {"攻击资质","防御资质","体力资质","法力资质","速度资质","躲闪资质"}
--local 增加资质= {取随机数(1,50),取随机数(1,50),取随机数(1,50),取随机数(1,50),取随机数(1,50),取随机数(1,50),}

function 场景类_进化宝宝:初始化(根)
	self.ID = 16.4
	self.x = 275
	self.y = 129
	self.xx = 0
	self.yy = 0
	self.注释 = "鉴定装备"
	self.可视 = false
	self.鼠标 = false
	self.焦点 = false
	self.可移动 = true
	tp = 根
	zts1 = tp.字体表.描边字体
	zts2 = tp.字体表.一般字体
	self.开始 = 1
	self.结束 = 20
	self.窗口时间 = 0
	self.选中物品 = nil
    	self.加入 = 0

end

function 场景类_进化宝宝:刷新资质(数据)
	local bbs=tp.队伍[1].宝宝列表
	local bbsa = #bbs
	if self.主召唤兽 ~= nil then
	bbs[self.主召唤兽].进化可加资质.攻资=数据.增加资质.攻资
	bbs[self.主召唤兽].进化可加资质.防资=数据.增加资质.防资
	bbs[self.主召唤兽].进化可加资质.体资=数据.增加资质.体资
	bbs[self.主召唤兽].进化可加资质.法资=数据.增加资质.法资
	bbs[self.主召唤兽].进化可加资质.速资=数据.增加资质.速资
	bbs[self.主召唤兽].进化可加资质.躲资=数据.增加资质.躲资
	end
end






function 场景类_进化宝宝:打开(数据)
	local 资源 = tp.资源
	self.数据 = 数据
	if self.可视 then
		self.可视 = false
		self.资源组=nil
		self.头像组 = nil
		return
	else
		insert(tp.窗口_,self)
		local 按钮 = require("script/系统类/按钮")
		local 自适应 = tp._自适应
		self.资源组 = {
			[1] = 自适应.创建(0,1,400,450,3,9),
			[2] = 按钮.创建(自适应.创建(18,4,16,16,4,3),0,0,4,true,true),
			[3] = tp.资源:载入("pic111/进化界面/进化宝宝.png", "图片"),
			[4] = 按钮.创建(自适应.创建(12,4,74,22,1,3),0,0,4,true,true,"刷新资质"),
			[5] = 按钮.创建(自适应.创建(12,4,74,22,1,3),0,0,4,true,true,"确认进化"),
			[13] = 自适应.创建(3,1,80,19,1,3),
			[20] = 按钮.创建(自适应.创建(20,4,18,19,4,3),0,0,4,true,true),
			[21] = 按钮.创建(自适应.创建(21,4,18,19,4,3),0,0,4,true,true),
			[29] = 自适应.创建(34,1,158,172,3,9),--召唤兽选择框，左边大框
			[30] = 自适应.创建(2,1,200,172,3,9),--宠物显示框
			[35] =  tp._滑块.创建(自适应.创建(11,4,15,40,2,3,nil),1,14,139,2),


--987F3539--加锁图标--wzife.wdf
		}
		self.锁定组={
			[1] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[2] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[3] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[4] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[5] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
			[6] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true),
		}

		self.锁定={}
		for i=1,6 do
			self.锁定组[i]:置打勾框(self.锁定[i])
		end

		self.头像组 = {}
		self.动画={}
		self.可视 = true
		tp.运行时间 = tp.运行时间 + 1
	  	self.窗口时间 = tp.运行时间
	   	 local wz = require("gge文字类")
	   	 self.字体=wz.创建(字体路径,14,false,false,字体抗锯齿)
	   	 self.字体2=wz.创建(字体路径,14,false,false,字体抗锯齿)
		self.状态=1
		self.资源组[35]:置起始点(0)
		self.仙玉基础消耗 = 数据.仙玉基础消耗
		self.神兜兜基础消耗=数据.神兜兜基础消耗

		local bbs=tp.队伍[1].宝宝列表
		if bbs[1]~= nil then
		self.主召唤兽=1
		self:主置形象()
		else
			self.主召唤兽=nil
		end


	end
end

function 场景类_进化宝宝:更新(dt,x,y)

 self.鼠标=self:检查点(x,y)
end
function 场景类_进化宝宝:刷新背包(数据)
	self.数据 = 数据
end

function 场景类_进化宝宝:显示(dt,x,y)

	-- if not self.可视 then
	-- 	return false
	-- end
	self.焦点 = false
    self.资源组[1]:显示(self.x,self.y)
    self.资源组[2]:更新(x,y)
    self.资源组[4]:更新(x,y)
    self.资源组[5]:更新(x,y)
    for i=1,6 do
	self.锁定组[i]:更新(x,y)
    end
    self.资源组[2]:显示(self.x + 384,self.y + 4)
    self.资源组[4]:显示(self.x + 70,   self.y+400)
     self.资源组[5]:显示(self.x + 250,self.y+400)
     self.资源组[29]:显示(self.x + 10,self.y+48)
     self.资源组[30]:显示(self.x + 182,self.y+47)

     tp.窗口标题背景_:显示(self.x-86+self.资源组[1].宽度/2,self.y)
    引擎.场景.字体表.标题字体:置颜色(白色):显示(self.x+self.资源组[1].宽度/2,self.y+3,"进化界面")
    zts2:置字间距(6)
    for i=0,5 do
		zts2:置颜色(白色):显示(self.x+11,self.y+234+i*23,bds[i+1])
		self.资源组[13]:显示(self.x+90,self.y+231+i*23)
     end

      for i=0,5 do
		--zts2:置颜色(绿色):显示(self.x+211,self.y+234+i*23,"→→→")
		--tp.字体表.普通字体:置颜色(绿色):显示(self.x+175,self.y+234+i*23,"+"..增加资质[i+1])
		tp.字体表.普通字体:置颜色(白色):显示(self.x+211,self.y+234+i*23,"→")
		self.资源组[13]:显示(self.x+245,self.y+231+i*23)
     end




		self.字体:置颜色(黄色)
		self.字体:显示(self.x+24,self.y+50-25,"请选择进化宝宝")
		self.字体:置颜色(白色)
	local bbs=tp.队伍[1].宝宝列表
	local bbsa = #bbs

	if bbsa > 4 then
		--self.资源组[35]:置高度(min(floor(180/(bbsa-4)),170))
		self.加入 = min(ceil((bbsa-4)*self.资源组[35]:取百分比()),bbsa-4)
	end
	if bbsa > 4 then
		    self.资源组[35]:显示(self.x+161,self.y+83-35,x,y,self.鼠标)
	end


		if(self.资源组[29]:是否选中(x,y) or  self.资源组[35]:是否选中(x,y)) and (引擎.取鼠标滚轮() ==1 and self.加入 > 0)  then--鼠标上滚动
		if self.加入-1 == bbsa-4 then
			self.加入=self.加入-1
		end
		self.资源组[35]:置起始点(self.资源组[35]:取百分比转换(self.加入-1,4,bbsa))

		elseif (self.资源组[29]:是否选中(x,y) or self.资源组[35]:是否选中(x,y)) and (引擎.取鼠标滚轮() ==-1 and self.加入 < bbsa - 4) then--鼠标下滚动
			self.资源组[35]:置起始点(self.资源组[35]:取百分比转换(self.加入+1,4,bbsa))
		end


		if self.主召唤兽 ~= nil then
		local  资质组= {bbs[self.主召唤兽].攻击资质,bbs[self.主召唤兽].防御资质,bbs[self.主召唤兽].体力资质,bbs[self.主召唤兽].法力资质,bbs[self.主召唤兽].速度资质,bbs[self.主召唤兽].躲闪资质}


		tp.影子:显示(self.x+280,self.y+195-30)
		if self.动画.主体~=nil then
			self.动画.主体:更新()
			self.动画.主体:显示(self.x+280,self.y+195-30)
			if self.动画.身体~=nil then
				self.动画.身体:更新()
				self.动画.身体:显示(self.x+280,self.y+195-30)
			end
			--self.资源组[36]:显示(self.x+244,self.y+40)
			if self.动画.饰品 ~= nil then
				self.动画.饰品:更新()
				self.动画.饰品:显示(self.x+280,self.y+195-30)
			end
		end


			local 锁定数量 = 0
			local  仙玉消耗 = 0
			local  神兜兜消耗= 0
			local  基础等级= 0
			local xx = 0
			local yy = 0
			  for i=1,6 do
				self.锁定组[i]:显示(self.x + 330,self.y+232+yy*23,true,nil,nil,self.锁定[i],2)
				if self.锁定[i] then
				 tp.字体表.普通字体:置颜色(红色):显示(self.x+355,self.y+234+1+yy*23,"已锁")
				else
				 tp.字体表.普通字体:置颜色(黄色):显示(self.x+355,self.y+234+1+yy*23,"未锁")
				end
			          		if  self.锁定[i] ==true then
			          			锁定数量 =锁定数量+1
			          		end
			          		if  bbs[self.主召唤兽].进化资质等级 ==nil then
			          			bbs[self.主召唤兽].进化资质等级=0
			          		end
			          		  仙玉消耗 = (1+锁定数量*2)*self.仙玉基础消耗
			          		  基础等级= bbs[self.主召唤兽].进化资质等级+1
			          		  神兜兜消耗= self.神兜兜基础消耗*基础等级
				xx = xx + 1
				if xx==1 then
				    xx=0
				    yy=yy+1
				end
			    end

			    tp.字体表.一般字体:置字间距(0)
			    tp.字体表.一般字体:置颜色(白色):显示(self.x+16,self.y+426,"说明：锁定资质消耗翻倍，进化消耗进化等级*"..self.神兜兜基础消耗.."的神兜兜")
			    tp.字体表.描边字体:置颜色(白色):显示(self.x+46,self.y+376,"刷新需消耗"..仙玉消耗.."仙玉")
			    tp.字体表.描边字体:置颜色(白色):显示(self.x+221,self.y+376,"进化需消耗"..神兜兜消耗.."神兜兜")



			              for i=0,5 do
			              tp.字体表.普通字体:置颜色(黑色):显示(self.x+100,self.y+234+1+i*23,资质组[i+1])   --现有资质
			              end

			              if  bbs[self.主召唤兽].进化资质等级==nil then
			              tp.字体表.普通字体:置颜色(红色):显示(self.x+220,self.y+200,"当前进化等级为0级")
			          else
			          	tp.字体表.普通字体:置颜色(红色):显示(self.x+220,self.y+200,"当前进化等级为"..bbs[self.主召唤兽].进化资质等级.."级")
			          	--tp.字体表.普通字体:置颜色(白色):显示(self.x+11,self.y+376,bbs[self.主召唤兽].进化资质等级)
			          	end

				if bbs[self.主召唤兽].进化可加资质 ==nil then
				    for i=0,5 do
					tp.字体表.普通字体:置颜色(绿色):显示(self.x+175,self.y+234+i*23,"+0")
					tp.字体表.普通字体:置颜色(黑色):显示(self.x+265,self.y+234+1+i*23,资质组[i+1])
			     	    end

				else
					local  进化可加资质组={bbs[self.主召唤兽].进化可加资质.攻资,bbs[self.主召唤兽].进化可加资质.防资,bbs[self.主召唤兽].进化可加资质.体资,bbs[self.主召唤兽].进化可加资质.法资,bbs[self.主召唤兽].进化可加资质.速资,bbs[self.主召唤兽].进化可加资质.躲资}
					for i=0,5 do
					 tp.字体表.普通字体:置颜色(绿色):显示(self.x+175,self.y+234+i*23,"+"..进化可加资质组[i+1])      -- 可加资质
						 if self.锁定[i+1] then
						 tp.字体表.普通字体:置颜色(红色):显示(self.x+265,self.y+234+1+i*23,资质组[i+1]+进化可加资质组[i+1])         --最终资质
						else
						tp.字体表.普通字体:置颜色(黑色):显示(self.x+265,self.y+234+1+i*23,资质组[i+1]+进化可加资质组[i+1])         --最终资质
						end
					end
				end
		    end

		     for i=1,4 do ---
		   if bbs[i+self.加入] ~= nil  then
			local jx = self.x+12
			local jy = self.y+(i*42)+44-35
			bw:置坐标(jx,jy+1)
			if self.头像组[i+self.加入] == nil or self.头像组[i+self.加入][1] ~= bbs[i+self.加入].模型 then
				self.头像组[i+self.加入] = {bbs[i+self.加入].模型}
				local n = tx(self.头像组[i+self.加入][1])
				self.头像组[i+self.加入][2] = tp.资源:载入(n[7],"网易WDF动画",n[1])
			end
			if bw:检查点(x,y) then
				if mouseb(0) and not self.资源组[35].接触 and self.鼠标 and not tp.消息栏焦点 then

					if self.主召唤兽 ~= i + self.加入  then
						if self.主召唤兽 == nil then
							self.主召唤兽 = i + self.加入
							self:主置形象()

						elseif self.主召唤兽 ~= nil  then
							self.主召唤兽 = i + self.加入
							self:主置形象()

						end

					else
						if self.主召唤兽 == i + self.加入 then
							self.主召唤兽 = nil
							self:主置形象()

						end
					end

				end
				self.焦点 = true
			end
			local 坐标={[1]=23,[2]=83,[3]=143}
			if self.主召唤兽 == i + self.加入 then
				box(jx-2,jy-3,jx+146,jy+40,-2097481216)
			end
			 tp.宠物头像背景_:显示(jx+1,jy)
			 --print(self.头像组[i+self.加入][1])
			 if self.头像组[i+self.加入][1] == "自在心猿" then
			    self.头像组[i+self.加入][2]:显示(jx+3,jy-2)
			 else
			     self.头像组[i+self.加入][2]:显示(jx+4,jy+4)
			 end
			if self.主召唤兽 == i + self.加入 then
				--self.资源组[19]:显示(jx+95,jy+3)
			end
			if tp.队伍[1].宝宝列表[i+self.加入].参战信息 ~= nil then
				self.字体2:置颜色(-65536)
			else
				self.字体2:置颜色(-16777216)
			end
			 if bbs[i+self.加入].加锁~=nil and bbs[i+self.加入].加锁 then
			    tp.进化宝宝图标1:显示(jx+81,jy+22)
		     end
			self.字体2:显示(jx+41,jy+4,bbs[i+self.加入].名称)
			self.字体2:显示(jx+41,jy+21,bbs[i+self.加入].等级.."级")
		end
	  end---

	if self.资源组[35].接触 then
		self.焦点 = true
	end


	  for i=1,6 do
	  	if self.锁定组[i]:事件判断()  then
	  		self.锁定[i] = not  self.锁定组[i].打勾框
			self.锁定组[i]:置打勾框(self.锁定[i])
			if self.锁定[i]==false then
			   self.锁定[i]=nil
			end
		end
	end



	 if self.资源组[2]:事件判断() then
    	--self:打开()
	    elseif self.资源组[5]:事件判断() then
	   	if self.主召唤兽~=nil and self.主召唤兽~=0 then
	           发送数据(3822,{序列=self.主召唤兽,操作="进化宝宝"})
		else
		tp.提示:写入("请选择要操作的召唤兽")
		end
	     elseif self.资源组[4]:事件判断() then
	   	if self.主召唤兽~=nil and self.主召唤兽~=0 then
	           发送数据(3821,{序列=self.主召唤兽,操作="刷新资质",锁定组 =self.锁定})
		else
		tp.提示:写入("请选择要操作的召唤兽")
		end
	    end



end
function 场景类_进化宝宝:主置形象()
	-- print(11)
	self.动画={}
	if tp.队伍[1].宝宝列表[self.主召唤兽]  ~= nil then
		local n = ani(tp.队伍[1].宝宝列表[self.主召唤兽].模型)
		self.动画.主体 = tp.资源:载入(n[10],"网易WDF动画",n[6])
		self.动画.主体:置方向(0)
		n = ani(tp.队伍[1].宝宝列表[self.主召唤兽].模型.."_身体")
		if n[6] ~= nil then
			self.动画.身体 = tp.资源:载入(n[10],"网易WDF动画",n[6])
			self.动画.身体:置方向(0)
		end
		if tp.队伍[1].宝宝列表[self.主召唤兽].饰品 ~= nil then
			n = ani(tp.队伍[1].宝宝列表[self.主召唤兽].模型.."_饰品")
			if n[6] ~= nil then
				self.动画.饰品 = tp.资源:载入(n[10],"网易WDF动画",n[6])
				if tp.队伍[1].宝宝列表[self.主召唤兽].饰品颜色 then
				    self.动画.饰品:置颜色(tp.队伍[1].宝宝列表[self.主召唤兽].饰品颜色,-1)
				end
				self.动画.饰品:置方向(0)
			end
			-- local wp = tp._物品
		 --    local qq = wp()
		 --    qq:置对象(tp.队伍[1].宝宝列表[self.选中].模型.."饰品")
		 --    self.饰品[1]:置物品(qq)
		 --    self.饰品[1].物品.总类=57
		 --    self.饰品[1].物品.玄天灵力=tp.队伍[1].宝宝列表[self.选中].饰品.玄天灵力 or 0
		    --local wp=引擎.取物品(tp.队伍[1].宝宝列表[self.主召唤兽].模型.."饰品")
			--self.饰品.小动画=tp.资源:载入(wp[11],"网易WDF动画",wp[12])
			--self.饰品.大动画=tp.资源:载入(wp[11],"网易WDF动画",wp[13])
			--self.饰品.名称=tp.队伍[1].宝宝列表[self.主召唤兽].模型.."饰品"
			-- self.饰品.玄天灵力=tp.队伍[1].宝宝列表[self.选中].饰品.玄天灵力 or 0
			--self.饰品.说明=wp[1]
		else
			n = ani(tp.队伍[1].宝宝列表[self.主召唤兽].模型.."_装饰")
			if n[6] ~= nil then
				self.动画.饰品 = tp.资源:载入(n[10],"网易WDF动画",n[6])
				self.动画.饰品:置方向(0)
			end
		end
		if tp.队伍[1].宝宝列表[self.主召唤兽].炫彩 ~= nil then
			self.动画.主体:炫彩染色(tp.队伍[1].宝宝列表[self.主召唤兽].炫彩,tp.队伍[1].宝宝列表[self.主召唤兽].炫彩组)
			--self.动画.主体:炫彩染色(tp.队伍[1].宝宝列表[self.选中].炫彩,tp.队伍[1].宝宝列表[self.主召唤兽].炫彩组)
			if self.动画.身体 ~= nil then
				self.动画.身体:炫彩染色(tp.队伍[1].宝宝列表[self.主召唤兽].炫彩,tp.队伍[1].宝宝列表[self.主召唤兽].炫彩组)
				--self.动画.身体:炫彩染色(tp.队伍[1].宝宝列表[self.选中].炫彩,tp.队伍[1].宝宝列表[self.主召唤兽].炫彩组)
			end
		else
			if tp.队伍[1].宝宝列表[self.主召唤兽].染色方案 ~= nil and tp.队伍[1].宝宝列表[self.主召唤兽].染色组~=nil then
				self.动画.主体:置染色(tp.队伍[1].宝宝列表[self.主召唤兽].染色方案,tp.队伍[1].宝宝列表[self.主召唤兽].染色组[1],tp.队伍[1].宝宝列表[self.主召唤兽].染色组[2],tp.队伍[1].宝宝列表[self.主召唤兽].染色组[3])
				self.动画.主体:置方向(0)
				if self.动画.身体 ~= nil then
					self.动画.身体:置染色(tp.队伍[1].宝宝列表[self.主召唤兽].染色方案,tp.队伍[1].宝宝列表[self.主召唤兽].染色组[1],tp.队伍[1].宝宝列表[self.主召唤兽].染色组[2],tp.队伍[1].宝宝列表[self.主召唤兽].染色组[3])
					self.动画.身体:置方向(0)
				end
			end
		end
	end
end
function 场景类_进化宝宝:检查点(x,y)
	if self.可视 and self.资源组[1]:是否选中(x,y)  then
		return true
	else
		return false
	end
end

function 场景类_进化宝宝:初始移动(x,y)
	tp.运行时间 = tp.运行时间 + 1
	if not tp.消息栏焦点 then
  		self.窗口时间 = tp.运行时间
 	end
	if not self.焦点 then
		tp.移动窗口 = true
	end
	if self.鼠标 and  not self.焦点 then
		self.xx = x - self.x
		self.yy = y - self.y
	end
end

function 场景类_进化宝宝:开始移动(x,y)
	if self.鼠标 then
		self.x = x - self.xx
		self.y = y - self.yy
	end
end

return 场景类_进化宝宝