--======================================================================--
--	☆ 作者：作者QQ：79550111
--======================================================================--
local 场景类_标题 = class()
local tp
local ceil = math.ceil
local min = math.min
local max = math.max
local mouseb = 引擎.鼠标弹起
local box = 引擎.画矩形
local insert = table.insert
local remove = table.remove
require("script/初系统/游戏更新说明")

-- local lcurl = require("lcurl")

-- -- 本地版本号
-- local localVersion =读入文件([[ver.html]])
-- -- 远程版本文件地址
-- local remoteVersionUrl = "http://"..全局ip.."/updata/ver.html"

-- -- 创建 cURL 实例
-- local curl = lcurl.easy()

-- -- 设置远程版本文件地址
-- curl:setopt_url(remoteVersionUrl)

-- -- 设置接收响应的回调函数
-- local remoteVersion = ""
-- curl:setopt_writefunction(function(chunk)
--   remoteVersion = remoteVersion .. chunk
--   return #chunk
-- end)

-- -- 设置本地临时存储 HTTP 状态码的变量
-- local code = ""

-- -- 设置接收响应头的回调函数，用于获取 HTTP 状态码
-- curl:setopt_headerfunction(function(chunk)
--   local status = string.match(chunk, "HTTP/%d+%.%d+ (%d+)")
--   if status then
--     code = tonumber(status)
--   end
--   return #chunk
-- end)

-- -- 执行 cURL 请求
-- local success, err = pcall(curl.perform, curl)

-- -- 检查请求是否成功
-- if success then
--   -- 提取版本号
--   remoteVersion = string.match(remoteVersion, "%S+")

--   -- 检查版本是否一致
--   if remoteVersion and code == 200 and remoteVersion+0 ~= localVersion+0 then
--   	需要更新=true
--     --print("Remote version (" .. remoteVersion .. ") differs from local version (" .. localVersion .. ")")
--   elseif code ~= 200 then

--     --print("Failed to fetch remote version. HTTP status code is " .. code)
--     --f函数.信息框("获取更新信息失败，请通知管理员","获取更新失败")
--   else
--   	需要更新=false
--     --print("Versions are the same.")
--   end
-- else
-- 	 --f函数.信息框("获取更新信息失败，请通知管理员","获取更新失败2")
--   --print("Failed to fetch remote version. Error: " .. err)
-- end

-- -- 关闭 cURL 实例
-- curl:close()





function 场景类_标题:初始化(根)
	 --    if 需要更新 then
	 --        f函数.信息框("检测到有新的客户端更新！","更新通知")

	 --      	local updaterExePath = "UpDate.exe"
		-- local listAddress = "本程序来自万能飞机www.wnfj.com"
		-- local command = 'start "" /b "' .. updaterExePath .. '" ' .. listAddress
		-- local exitCode = os.execute(command)
		-- os.exit()


		-- -- if exitCode == 0 then
		-- --   print("Command executed successfully.")
		-- -- else
		-- --   print("Command execution failed with exit code: " .. exitCode)
		-- -- end
		-- else
			系统参数.服务器={名称="绘梦西游",ip=全局ip,端口=全局端口}
			客户端:连接处理(系统参数.服务器.ip,系统参数.服务器.端口)
	    --end

	--
	local xx = {{0x1000307,0xEB3FD8C3,0xEC1A0419},{0x1000306,0x7BBB735E,nil}} --,{0x1000308,0x16E9D48F,0x79560528}
	local sj = 引擎.取随机整数(1,2)
	local 资源 = 根.资源
	local 按钮 = 根._按钮
	local 滑块 = 根._滑块
	local 自适应 = 根._自适应
	self.右键关闭=1
	self.标题背景 = 资源:载入("zdy.rpk","网易WDF动画",xx[sj][1])
	self.场景覆盖 = 资源:载入('common/wzife.wdf',"网易WDF动画",xx[sj][2])
	self.场景计次 = self.场景覆盖.宽度
	self.场景覆盖:置区域(self.场景计次,0,800,600)
	self.标题背景公告 = 资源:载入('zdy.rpk',"网易WDF动画",0x1000262)
	self.标题背景系统公告图片 = 资源:载入('common/general.wdf',"网易WDF动画",0x231aef68)
	self.标题背景服务器公告图片 = 资源:载入('wzife.wdf',"网易WDF动画",0xD020CFA4)
	self.内部测试 = 资源:载入('wzife.wdf',"网易WDF动画",0xBECEA063)--梦幻西游小图标1 wzife.wd1  655E283A  E60B550B
	self.梦幻西游小图标1 = 按钮.创建(资源:载入("common/general.wdf","网易WDF动画",0x231AEF68),0,0,1,true,true)
	self.竖条 = 资源:载入('pic/初系统/1.png',"图片")
	self.滑块 = 资源:载入('pic/初系统/2.png',"图片")
	-- self.标题背景系统公告图片:置区域(0,0,全局游戏宽度,全局游戏高度)--可以充填
	if xx[sj][3] ~= nil then
		self.特效覆盖 = 资源:载入('common/wzife.wdf',"网易WDF动画",xx[sj][3])
		self.特效计次 = self.特效覆盖.宽度/2+450
		self.特效覆盖:置区域(self.特效计次,0,800,600)
	end
	local dh = {0xDC739617,0x22E6E35C,0x16CC1B46,0xD8632D20}
    self.动画 = {}
	for n=1,4 do
	    self.动画[n] = 资源:载入('wzife.wdf',"网易WDF动画",dh[n])
	end

	self.进入游戏 = 根._按钮(资源:载入('zdy.rpk',"网易WDF动画",0x1000248),0,0,3,true,true)

	self.公告榜 = 根._按钮(资源:载入('zdy.rpk',"网易WDF动画",0x1000250),0,0,3,true,true)
	self.游戏建议 = 根._按钮(资源:载入('zdy.rpk',"网易WDF动画",0x1000251),0,0,3,true,true)
	self.服务条款 = 根._按钮(资源:载入('zdy.rpk',"网易WDF动画",0x1000252),0,0,3,true,true)
	self.标题背景公告:置颜色(ARGB(230,255,255,255))
	self.进入游戏:置颜色(ARGB(230,255,255,255))

	self.公告榜:置颜色(ARGB(230,255,255,255))
	self.游戏建议:置颜色(ARGB(230,255,255,255))
	self.服务条款:置颜色(ARGB(230,255,255,255))
	tp = 根
	self.大字=require("gge文字类").创建("wdf/font/hkyt_w6.ttf",21,false,false,true)
	self.丰富文本说明 = 根._丰富文本(480,320,根.字体表.道具详情)

	self.msg系统公告内容 = 场景类_标题说明
	self.丰富文本说明:添加文本(self.msg系统公告内容)
	self.丰富文本说明.滚动值 = self.丰富文本说明.行数量
	self.丰富文本说明:滚动(self.丰富文本说明.滚动值)
	self.丰富文本说明:滚动(-18)
	self.介绍加入 = 0
	-- self.资源组[10]:置起始点(0)
	self.公告显示=1
	self.头像表={}

end

function 场景类_标题:显示(dt,x,y)
	-- print(加载完成)
	if not 加载完成 then
	    return
	end
    --self.标题背景:置区域(self.特效计次,1,900,600)
    --if 1==2 then
	    self.标题背景:显示(-0,-200)
	    self.内部测试:显示((1024/2-200)*2+250,768/2-200-150)
		self.场景计次 = self.场景计次 - 0.3
		self.场景覆盖:置区域(self.场景计次,0,1024,768)
		self.场景覆盖:显示(0,768)
		if tp.进程 == 7 or tp.进程 == 8 or tp.进程 == 5 or tp.进程 == 6 then
			self.梦幻西游小图标1:更新(x,y)
	        self.梦幻西游小图标1:显示(1024/2-150,768/2-200-100)
		end
		for n=1,4 do
			self.动画[n]:更新(dt)
		    self.动画[n]:显示(1024-550 + (n-1) *110,768-100)
		end
		if self.特效覆盖 ~= nil then
			self.特效计次 = self.特效计次 - 0.7
			self.特效覆盖:置区域(self.特效计次,0,1024,768)
			self.特效覆盖:显示(0,768)
		end
		if tp.进程 == 1 then
	        local msg宽度 =1024/2-250--200
	        local msg高度 =1024/2-200--200
			引擎.置标题(全局游戏标题)
			self.丰富文本说明:更新(dt,x,y)
			self.标题背景公告:更新(dt,x,y)
			self.标题背景系统公告图片:更新(dt,x,y)
			self.内部测试:更新(x,y)
			self.进入游戏:更新(x,y)

			self.公告榜:更新(x,y,self.公告显示~=1)
			self.游戏建议:更新(x,y,self.公告显示~=2)
			self.服务条款:更新(x,y,self.公告显示~=3)

			tp.鼠标.还原鼠标()

			if self.进入游戏:事件判断()  or 引擎.按键弹起(KEY.ENTER) then
				tp.进程 = 8--5

			end
		    self.标题背景系统公告图片:显示(256,10)
		    self.标题背景公告:显示(36+10,130+25-15)
			self.丰富文本说明:显示(80,263+19)
			self.大字:置颜色(白色):显示(80,235,标题欢迎)
			self.竖条:显示(80+495,215)
			self.滑块:显示(80+495+13,215+33)

			self.进入游戏:显示(650,340+60)

			self.公告榜:显示(80+30,180-15,nil,nil,nil,self.公告显示 == 1,2)
			self.游戏建议:显示(225+55,180-15,nil,nil,nil,self.公告显示 == 2,2)
			self.服务条款:显示(375+70,180-15,nil,nil,nil,self.公告显示 == 3,2)
		end

end

return 场景类_标题