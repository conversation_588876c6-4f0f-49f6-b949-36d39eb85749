--======================================================================--

--======================================================================--
local 系统类_充值系统 = class()
local floor = math.floor
local tp,zts,zt
local format = string.format
local insert = table.insert

-- local wp={
--          [1]={名称="40装备礼包"},--技能书
--          [2]={名称="50W储备金"}, --特技书 新手宝宝
--          [3]={名称="新手宝宝"},
--          [4]={名称="地载阵"},-- 金砖 回梦丹
--          [5]={名称="红色合成旗"},
--         }



function 系统类_充值系统:初始化(根)
	self.x = 180
	self.y = 170
	self.xx = 0
	self.yy = 0
	self.注释 = "充 值 系 统"
	self.可视 = false
	self.鼠标 = false
	self.焦点 = false
	self.可移动 = true
	tp = 根
	self.窗口时间 = 0
	zts = tp.字体表.道具字体
	zt = tp.字体表.描边字体
	self.进程=1

	self.控件类 = require("ggeui/加载类")()
	local 总控件 = self.控件类:创建控件('序号控件')
	总控件:置可视(true,true)
	self.输入框 = 总控件:创建输入("卡号输入",0,0,180,14)
	self.输入框:置可视(false,false)
	self.输入框:置限制字数(30)
	self.输入框:屏蔽快捷键(true)
	self.输入框:置光标颜色(-16777216)
	self.输入框:置文字颜色(-16777216)

	-- self.数量框 = 总控件:创建输入("数量输入",0,0,180,14)
	-- self.数量框:置可视(false,false)
	-- self.数量框:置限制字数(10)
	-- self.数量框:置数字模式()
	-- self.数量框:屏蔽快捷键(true)
	-- self.数量框:置光标颜色(-16777216)
	-- self.数量框:置文字颜色(-16777216)

end

function 系统类_充值系统:打开(内容)
	 --self.月卡时间=内容.月卡时间
	 --self.月卡是否生效=内容.生效

	if self.可视 then
		self.可视 = false
		self.输入框:置可视(false,false)
		--self.数量框:置可视(false,false)
		self.资源组 = nil
		self.物品组={}
		return
	else
		insert(tp.窗口_,self)
		local 资源 = tp.资源
		local 按钮 = tp._按钮
		local 自适应 = tp._自适应
		self.资源组 = {
			[1] = 自适应.创建(0,1,360,120,3,9),
			[2] = 资源:载入('wzife.wd3',"网易WDF动画",0x2436C9A1),
			[7] = 资源:载入('wzife.wdf',"网易WDF动画",0x2DA9D4EC),
			[8] = 资源:载入('wzife.wdf',"网易WDF动画",0x479E857C),
		}
		self.资源组[65] = 按钮.创建(自适应.创建(12,4,51,22,1,3),0,0,4,true,true,"充 值")
		self.资源组[66] = 按钮.创建(自适应.创建(12,4,91,22,1,3),0,0,4,true,true,"累 充 领 奖")
		self.资源组[67] = 按钮.创建(自适应.创建(12,4,91,22,1,3),0,0,4,true,true,"自 动 抓 鬼")
		self.资源组[68] = 按钮.创建(自适应.创建(12,4,91,22,1,3),0,0,4,true,true,"每 日 领 奖")
		self.资源组[69] = 按钮.创建(自适应.创建(12,4,91,22,1,3),0,0,4,true,true,"在 线 购 卡")
		--self.资源组[65] = 按钮.创建(tp.资源:载入('wzife.wdf',"网易WDF动画",0x48D2590A),0,0,4,true,true,"确定充值")
		--self.资源组[66] = 按钮.创建(tp.资源:载入('wzife.wdf',"网易WDF动画",0x48D2590A),0,0,4,true,true,"兑换仙玉")
		self.线 = tp.资源:载入("wzife.wd1","网易WDF动画",999600305)
		self.线:置区域(0,0,350,2)
		self.数据=数据
		self.内容=内容
		tp.运行时间 = tp.运行时间 + 1
	    	self.窗口时间 = tp.运行时间
	    	self.可视 = true
	    	self.输入框:置可视(true,true)
	    	self.物品组={}
	     	--self:加载物品(内容)


		-- self.数量框:置可视(true,true)
		-- self.数量框:置文本("输入兑换的仙玉数额")
	end
end



function 系统类_充值系统:显示(dt,x,y)
	-- local 月卡时间=时间转换(self.月卡时间)
	-- local 月卡生效=self.月卡是否生效
	--cz = self.数据
	self.焦点 = false
	self.资源组[1]:显示(self.x+70,self.y)
	self.资源组[2]:显示(self.x + 85+14,self.y+7-10)
	self.资源组[65]:更新(x,y)
	self.资源组[66]:更新(x,y)
	-- self.资源组[67]:更新(x,y)
	-- self.资源组[68]:更新(x,y)
	-- self.资源组[69]:更新(x,y)
	self.资源组[65]:显示(self.x + 545-180+12-10,self.y +33+80-60)
	--self.资源组[66]:显示(self.x + 545-180+12-10-40,self.y +33+80)
	-- self.资源组[67]:显示(self.x + 545-180+12-10-40,self.y +310)
	-- self.资源组[68]:显示(self.x + 33+50,self.y +310)
	--self.资源组[69]:显示(self.x + 83,self.y +33+80)
	--self.线:显示(self.x+75,self.y+145)
	--tp.窗口标题背景_:显示(self.x+self.资源组[1].宽度/2-15,self.y)
	--tp.窗口标题背景_:显示(self.x+self.资源组[1].宽度/2-15,self.y+147)
	--引擎.场景.字体表.标题字体:显示(self.x+self.资源组[1].宽度/2+70,self.y+3,"充值系统")
	--引擎.场景.字体表.标题字体:显示(self.x+self.资源组[1].宽度/2+70,self.y+150," 月 卡 ")
	--self.资源组[66]:显示(self.x + 545-180+12,self.y +73+50)
	--self.资源组[8]:显示(self.x+315-180+12,self.y+73+50)
	self.资源组[8]:显示(self.x+315-180+12,self.y+33+80-60)
	self.控件类:更新(dt,x,y)
	self.控件类:显示(x,y)
	self.输入框:置坐标(self.x+317-180+12,self.y+36+80-60)

	--self.数量框:置坐标(self.x+317-180+12,self.y+76+50)
	zts:置颜色(黄色)
	zts:显示(self.x+210-180+12+45,self.y+30+80-60,"CDK")
	--zts:显示(self.x+210-180+12,self.y+70+50,"兑换仙玉")
	zts:显示(self.x+183,self.y+15,self.注释)
	zt:显示(self.x+220-180+40,self.y+100+50+20-80,"请输入正确的充值CDK，充值内容及比例请联系管理员！")
	--zt:显示(self.x+190,self.y+100+50+20-55,"当前累充："..self.内容.累充.."元！")
	--zt:显示(self.x+120,self.y+180,"到期时间:   "..时间转换(self.内容.月卡时间))
	--zt:显示(self.x+80,self.y+210,"     特权：每日礼包+100W银子+150次自动抓鬼！")


	-- if self.内容.月卡生效 == false then
	-- 	zt:显示(self.x+self.资源组[1].宽度/2+40,self.y+310,"[未生效]")
	-- else
	-- 		zt:显示(self.x+self.资源组[1].宽度/2+40,self.y+310,"[生效中]")
	-- end

	-- print(f函数.取剪贴板())
	if 引擎.按键按住(KEY.CTRL) and 引擎.按键按下(KEY.V) then
		self.输入框:置文本(f函数.取剪贴板())
	end

	-- local xx = 0
	-- local yy = 0
	-- for i=1,5 do
	-- 	tp.物品格子背景_:显示(self.x+68+57+25+(xx-1)*70,self.y+245+yy*84)
	-- 	xx = xx + 1
	-- 	if xx==5 then
	-- 	    xx=0
	-- 	    yy=yy+1
	-- 	end
	-- end


	-- for k,v in pairs(self.物品组) do
 --    	if k<=5 then
 --            local wx = k*70
	-- 		v.名称.小动画:显示(self.x+61+wx-50,self.y+247)
	-- 		if self.礼包序列.礼包一 then
	-- 			--v.名称.小动画:灰度级()
	-- 			--self.资源组[8]:显示(self.x+61+wx-50-146,self.y+237-380)
	-- 		end
	-- 		if v.名称.名称=="九转金丹" then
	-- 		   v.名称.品质=300
	-- 		end
	-- 		if v.名称.小动画:是否选中(x,y) then
	-- 	    	tp.提示:商城提示(self.x-150+wx,self.y+120,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
	-- 	    end
 --    	end

	--  end


	if self.资源组[65]:事件判断() then
		if self.输入框:取文本() == "" then
			tp.提示:写入("#Y/请输入正确的卡号")
			return
		end
		local function 事件()
			发送数据(47.1,{卡号=self.输入框:取文本(),代理=服务器名称数据})
			self.输入框:置文本("")
			self:打开()
			return
		end
		发送数据(47.1,{卡号=self.输入框:取文本(),代理=服务器名称数据})
		self.输入框:置文本("")

	elseif self.资源组[66]:事件判断() then
		发送数据(94.7)
	elseif self.资源组[67]:事件判断() then
		发送数据(119)
	elseif self.资源组[68]:事件判断() then
		发送数据(95.1)
	elseif self.资源组[69]:事件判断() then
		引擎.运行(充值网址)
	end
end


function 系统类_充值系统:加载物品(数据)
	local  wp = 数据.物品组
	self.礼包序列 =数据.是否领取
	--self.礼包序列 =tp.队伍[1].VIP礼包
	for k,v in pairs(wp) do
		if self.物品组[k]==nil then
		   self.物品组[k]={}
		end
		for i,n in pairs(v) do
			-- print(n)
			if self.物品组[k][i]== nil then
				self.物品组[k][i]={}
			end
			local 资源=引擎.取物品(n)
			self.物品组[k][i].小动画=tp.资源:载入(资源[11],"网易WDF动画",资源[12])
			self.物品组[k][i].大动画=tp.资源:载入(资源[11],"网易WDF动画",资源[13])
			self.物品组[k][i].名称=n
			self.物品组[k][i].说明=资源[1]
		end
	end
end


function 系统类_充值系统:更新礼包(sj)
	-- table.print(sj)
    self.礼包序列.礼包一=sj.内容.礼包一
    self.礼包序列.礼包二=sj.内容.礼包二
    self.礼包序列.礼包三=sj.内容.礼包三
    self.礼包序列.礼包四=sj.内容.礼包四
    self.礼包序列.礼包五=sj.内容.礼包五
end


function 系统类_充值系统:刷新(内容)

	self.内容=内容

end


function 系统类_充值系统:检查点(x,y)
	if self.资源组 ~= nil and self.资源组[1]:是否选中(x,y)  then
		return true
	end
end

function 系统类_充值系统:初始移动(x,y)
	tp.运行时间 = tp.运行时间 + 1
	if not tp.消息栏焦点 then
  		self.窗口时间 = tp.运行时间
 	end
	if not self.焦点 then
		tp.移动窗口 = true
	end
	if self.鼠标 and  not self.焦点 then
		self.xx = x - self.x
		self.yy = y - self.y
	end
end

function 系统类_充值系统:开始移动(x,y)
	if self.鼠标 then
		self.x = x - self.xx
		self.y = y - self.yy
	end
end

return 系统类_充值系统