--======================================================================--
-- @作者: www.wnfj.com，联系QQ：79550111
-- @创建时间:   2018-03-03 02:34:19
-- @Last Modified time: 2024-12-05 17:26:08
-- 更多游戏请访问万能飞机：www.wnfj.com,版本定制授权联系QQ：79550111
--======================================================================--
local 客户端类 = class()
local 回调 = require("script/网络/数据交换")
--回调:置缓冲区大小(10240)
--回调:置心跳检查间隔(0)
function 客户端类:初始化()
  self.连接账号=""
  self.连接密码=""
  self.连接结果=false
  fgf="*-*"
  fgc="@+@"
end

function 客户端类:更新(dt)

end

function 发送数据(id,内容)

  if 内容==nil then 内容={} end

  客户端:发送数据(id,内容,1)

end

function 客户端类:发送数据(序号,内容,数组转换)

  回调:发送数据(序号,内容,数组转换)
end

function 客户端类:连接处理(ip,端口,账号,密码)
  -- print(1111111)
  -- table.print(回调)
  self.连接结果=回调:连接(ip,端口)
  if self.连接结果==false then
    f函数.信息框("连接服务器失败，可能正在维护，请稍后再试","下线通知")
    os.exit()
    --tp.提示:写入("#R/连接服务器失败，请确认服务器是否在维护中或本机网络出现故障")
  end
end

function 客户端类:取状态(连接)
  return self:取状态()
end

function 客户端类:断开()
  回调:断开()
end

function 客户端类:数据到达(序号,内容,时间)


end

return 客户端类