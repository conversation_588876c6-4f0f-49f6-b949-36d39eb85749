
local 抽奖系统 = class()
local tp,zts1
local insert = table.insert

function 抽奖系统:初始化(根)
	tp = 根
    self.ID = 700
	self.x = 200
	self.y = 100
	self.xx = 0
	self.yy = 0
	self.注释 = "抽奖系统"
	self.可视 = false
	self.鼠标 = false
	self.焦点 = false
	self.可移动 = true

	local 按钮 = tp._按钮
		local 自适应 = tp._自适应
		self.资源组 = {
			--[1] = tp.资源:载入('登陆资源.wdf',"网易WDF动画",0xE5B48C60),
			[1] = 自适应.创建(0,1,480,480,3,9),
			[2] = 自适应.创建(1,1,476,18,1,3,nil,18),
	        [3] = tp.资源:载入('登陆资源.wdf',"网易WDF动画",0x01000052),
	        [4] = 按钮.创建(自适应.创建(12,4,72,20,1,3),0,0,4,true,true,"抽取一次"),
	        [5] = 按钮.创建(自适应.创建(12,4,72,20,1,3),0,0,4,true,true,"抽取十次"),
		}
	zts1 = 根.字体表.描边字体
	self.窗口时间 = 0
	self.剩余次数 = 0
	self.赞助字体= tp.字体表.描边字体
    self.赞助字体:置颜色(0xFFFFFFFF)
    self.赞助字体:置描边颜色(0xfff741940)--xFFaf140c)
     self.商品={}
end
function 抽奖系统:打开(内容)
if self.可视 then
		self.可视 = false
		return
	else

		insert(tp.窗口_,self)
     	tp.运行时间 = tp.运行时间 + 1
		self.窗口时间 = tp.运行时间
	    self.可视 = true
	    self.商品={}
	    self.剩余次数 = 内容.次数
	    for n=1,30 do
         	self.商品[n]={}
         	local 资源=引擎.取物品(内容.道具[n].名称)
		  	self.商品[n].小动画=tp.资源:载入(资源[11],"网易WDF动画",资源[12])
		  	self.商品[n].大动画=tp.资源:载入(资源[11],"网易WDF动画",资源[13])
		  	self.商品[n].名称=内容.道具[n].名称
		  	self.商品[n].说明=资源[1]
		  	if 内容.道具[n].说明~=nil and 内容.道具[n].说明~="普通" then
          	   self.商品[n].说明=内容.道具[n].说明
          	end
          	self.商品[n].备注=nil
          	if 内容.道具[n].备注~=nil and 内容.道具[n].备注~="无" then
          	   self.商品[n].备注=内容.道具[n].备注
          	end
	    end




    end
end




function 抽奖系统:刷新(内容)
	self.商品={}
    self.剩余次数 = 内容.次数
    for n=1,30 do
         self.商品[n]={}
         local 资源=引擎.取物品(内容.道具[n].名称)
		 self.商品[n].小动画=tp.资源:载入(资源[11],"网易WDF动画",资源[12])
		 self.商品[n].大动画=tp.资源:载入(资源[11],"网易WDF动画",资源[13])
		 self.商品[n].名称=内容.道具[n].名称
		 self.商品[n].说明=资源[1]
		if 内容.道具[n].说明~=nil and 内容.道具[n].说明~="普通" then
           self.商品[n].说明=内容.道具[n].说明
        end
        self.商品[n].备注=nil
       if 内容.道具[n].备注~=nil and 内容.道具[n].备注~="无" then
          self.商品[n].备注=内容.道具[n].备注
       end
	end
end





function 抽奖系统:显示(dt,x,y)
	self.焦点 = false
 	self.资源组[1]:显示(self.x,self.y)
 	self.资源组[2]:显示(self.x+2,self.y+2)
 	tp.窗口标题背景_:显示(self.x+self.资源组[1].宽度/2-90,self.y+2)
 	zts1:置字间距(3)
	zts1:置颜色(白色):显示(self.x+self.资源组[1].宽度/2-50,self.y+2,"抽 奖 系 统")
	zts1:置字间距(0)
	self.赞助字体:显示(self.x+120,self.y+40,"搏一搏,单车变摩托,剩余次数:"..self.剩余次数)
	self.资源组[4]:更新(x,y)
	self.资源组[4]:显示(self.x+130,self.y+435)
	self.资源组[5]:更新(x,y)
	self.资源组[5]:显示(self.x+280,self.y+435)
	if self.资源组[4]:事件判断() then
	    发送数据(61.5,{文本="抽奖一次"})
	elseif self.资源组[5]:事件判断() then
	    发送数据(61.5,{文本="抽奖十次"})
	end

 	zts1:置颜色(白色)
 	--zts1:显示(self.x+444,self.y+238,self.名称)
 	local xx=0
 	local yy=1
 	for n=1,30 do
     	local xx1=self.x + xx * 78+18
     	local yy1= self.y + yy * 73
     	tp.物品格子背景_:显示(xx1-2,yy1-2)
      	self.商品[n].小动画:显示(xx1,yy1)
      	--zts1:显示(xx1-10,yy1+53,self.商品[n].名称)
     	if self.位置==n then
          	self.资源组[3]:显示(xx1-3,yy1-2)
       	end
       	if self.商品[n].小动画:是否选中(x,y) then
            tp.提示:商城提示(x,y+20,self.商品[n].名称,self.商品[n].说明,self.商品[n].大动画,self.商品[n].备注)
        end
      	xx=xx+1
       	if xx==6 then
         	xx=0
         	yy=yy+1
     	end
 	end
end

function 抽奖系统:检查点(x,y)
	if self.可视 and self.资源组~=nil and self.资源组[1]:是否选中(x,y)  then
		return true
	end
end

function 抽奖系统:初始移动(x,y)
	tp.运行时间 = tp.运行时间 + 1
	if not tp.消息栏焦点 then
  		self.窗口时间 = tp.运行时间
 	end
	if not self.焦点 then
		tp.移动窗口 = true
	end
	if self.鼠标 and  not self.焦点 then
		self.xx = x - self.x
		self.yy = y - self.y
	end
end

function 抽奖系统:开始移动(x,y)
	if self.鼠标 then
		self.x = x - self.xx
		self.y = y - self.yy
	end
end





return 抽奖系统