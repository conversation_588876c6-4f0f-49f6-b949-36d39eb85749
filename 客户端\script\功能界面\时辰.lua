--======================================================================--
-- @作者: www.wnfj.com，联系QQ：79550111
-- @创建时间:   2018-03-03 02:34:19
-- @Last Modified time: 2024-12-05 17:26:06
-- @Last Modified time: 2023-12-05 10:08:40
-- @Last Modified time: 2023-12-06 15:18:45
--======================================================================--
local 系统类_时辰 = class()

local floor = math.floor
local ARGB = ARGB
local require = require
local 矩阵 = require("gge包围盒")(0,0,115,95)
local tp
local zt
local format = string.format
local keytq = 引擎.按键弹起

function 系统类_时辰:初始化(根)
	local 按钮 = 根._按钮
	local 自适应 = 根._自适应
	local 资源 = 根.资源
	self.背景 = 资源:载入('wzife.wdf',"网易WDF动画",0xDE3F48B7) --资源:载入('common/wzife.wdf',"网易WDF动画",2058817932)
	self.白昼 = 资源:载入('wzife.wdf',"网易WDF动画",0x9DF6DEBC).精灵
	self.黑昼 = 资源:载入('wzife.wdf',"网易WDF动画",0x99738f4c).精灵
	self.小人跑步 = 资源:载入('wzife.wdf',"网易WDF动画",0xAC307575)
	self.小人走路 = 资源:载入('wzife.wdf',"网易WDF动画",0xC7BEBF45)
	--self.时辰 = 资源:载入('wzife.wdf',"网易WDF动画",0xC9E2F072)
	self.序列=1
	self.梦幻指引 = 按钮.创建(资源:载入('wzife.wd2',"网易WDF动画",0xF102F42D),0,0,4)
	self.收缩 = 按钮.创建(资源:载入('wzife.wdf',"网易WDF动画",0x6EDD4D71),0,0,4)
	self.帮战报 = 按钮.创建(资源:载入('wzife.wdf',"网易WDF动画",1714563470),0,0,1,true)

	-- self.商城 = 按钮.创建(资源:载入('zdy.rpk',"网易WDF动画",16777504),0,0,4)
	-- self.签到 = 按钮.创建(资源:载入('zdy.rpk',"网易WDF动画",16777745),0,0,4)
	-- self.伙伴 = 按钮.创建(资源:载入('zdy.rpk',"网易WDF动画",16777746),0,0,4)
	-- self.博 = 按钮.创建(资源:载入('zdy.rpk',"网易WDF动画",16777737),0,0,4)
	-- self.盛夏祈福 = 按钮.创建(资源:载入('zdy.rpk',"网易WDF动画",16777744),0,0,4)
	             --self.月卡 = 按钮.创建(资源:载入('zdy.rpk',"网易WDF动画",16777737),0,0,4) --这个也还行
	-- self.点歌祝福 = 按钮.创建(资源:载入('wzife.wd2',"网易WDF动画",0x283aeeaa),0,0,4)
	-- self.屏蔽按钮 = 按钮(资源:载入('zdy3.rpk',"网易WDF动画",0x1000066),0,0,4)
	self.屏蔽 = false
	-- self.屏蔽按钮:置翻转(true)
	--self.点歌祝福 = 按钮.创建(资源:载入('common/wzife.wdf',"网易WDF动画",456022637),0,0,4)
	--common/wzife.wdf
	--日历 0x9c2f4872
	--指引 0xaf63a102
	self.灯笼 = {}
	for n=1,3 do
	   self.灯笼[n] = 按钮.创建(资源:载入('wzife.wdf',"网易WDF动画",0xBAF6A95D),0,0,2)
	end
	self.地图 = nil
	self.偏移 = nil
	self.计时 = 0
	self.计次 = 150
	self.白昼:置区域(self.计次,0,80,30)
	self.偏移坐标 = 0
	tp = 根
	zt = tp.字体表.描边字体
	local g = {0x361FA820,0xC0A66903,0xD1D11294,0xAA7DEB05,0x21274A87,0x09C4978D,0xC9E2F072,0x2ACB36B2,0xC26BF189,0x1AA170AE,0x7921D3A3,0xEA7CAB84}
	self.时辰={}
	for n=1,12 do
	  self.时辰[n]=资源:载入('wzife.wdf',"网易WDF动画",g[n])
	end
end

function 系统类_时辰:刷新()
	self.地图 = 取地图名称(tp.当前地图)
	self.偏移 = 60 - #self.地图*3.72
end

function 系统类_时辰:自动抓鬼刷新(内容)
	--print("收到的内容："..内容.战斗编号)
	self.内容 = 内容.战斗编号
	--print("self内容："..self.内容)

end


function 系统类_时辰:显示(dt,x,y)

	local 变量 = 0
	if 矩阵:检查点(x,y) then
		tp.选中UI = true
	end
	self.计时 = self.计时 + dt
	if self.计时 >= 1 then
		self.计次 = self.计次 - 1
		self.计时 = 0
	end
	if 昼夜==1 then
	    self.黑昼:置区域(self.计次,0,80,30)
		self.黑昼:显示(14,32 + self.偏移坐标)
	else
		self.白昼:置区域(self.计次,0,80,30)
		self.白昼:显示(14,32 + self.偏移坐标)
	end

	local  自动回收间隔时间 =300
	if  tp.自动回收 ==true and 自动回收开始~= nil then
		if 自动回收开始+自动回收间隔时间 <=os.time()  then
		自动回收开始= os.time()
	    	--发送数据(3785.4)
	    	发送数据(3785.4,{回收数据=物品回收数据[回收id]})
	    	end
	end
	if tp.窗口.回收系统.助战id then
		if tp.窗口.回收系统.助战id[1] then
			if  tp.助战自动回收 ==true and 助战自动回收开始~= nil then
				if 助战自动回收开始+自动回收间隔时间 <=os.time()  then
					助战自动回收开始= os.time()
					for k,v in pairs(tp.窗口.回收系统.助战id) do
					发送数据(3785.4,{回收数据=物品回收数据[回收id],助战回收id=v})
					end
				end
			end
		end
	end
	-- if tp.窗口.时辰.序列>=11 or tp.窗口.时辰.序列<=4 then --
	-- 	self.黑昼:置区域(self.计次,0,80,30)
	-- 	self.黑昼:显示(14,32 + self.偏移坐标)
	-- 	昼夜=1
	-- else
	-- 	self.白昼:置区域(self.计次,0,80,30)
	-- 	self.白昼:显示(14,32 + self.偏移坐标)
	-- 	昼夜=2
	-- end
	self.背景:显示(0,0 + self.偏移坐标)
	if 引擎.场景.场景.人物~=nil and 引擎.场景.场景.人物.行为 == "行走" then
		self.小人跑步:更新(dt)
		self.小人跑步:显示(58,60 + self.偏移坐标)
		i=1
	else
		self.小人走路:更新(dt)
		self.小人走路:显示(58,60 + self.偏移坐标)
	end
	self.收缩:更新(x,y)

	self.收缩:显示(3,64 + self.偏移坐标)

	if self.偏移坐标 == 0 then
		for n=1,3 do
		   self.灯笼[n]:更新(x,y)
		   --self.灯笼[n]:显示(93,17+((n-1)*18))
		end
                           --self.月卡:更新(x,y)
                           --self.月卡:显示(5,100)
		self.梦幻指引:更新(x,y)
		if self.梦幻指引:事件判断() and not tp.战斗中 then
			发送数据(66)
		end
		self.梦幻指引:显示(1,78 + self.偏移坐标)
		if 帮战开关 and not tp.战斗中 then --测试模式
            self.帮战报:更新(x,y)
			self.帮战报:显示(5,114)
        end
	end


	--print(self.内容)







	if self.灯笼[1]:事件判断() and not tp.战斗中 then
		if tp.窗口.任务栏.可视==false then
			发送数据(10)
		else
			tp.窗口.任务栏:打开()
		end
 	-- elseif self.月卡:事件判断() and not tp.战斗中 then
		--     发送数据(106.1)--月卡
	elseif self.灯笼[2]:事件判断() and not tp.战斗中 then
		tp.窗口.世界大地图:打开()
	elseif (self.灯笼[3]:事件判断() or keytq(KEY.TAB)) and not tp.战斗中 and not tp.消息栏焦点  then
		tp.窗口.小地图:打开(tp.当前地图)
    elseif 帮战开关 and not tp.战斗中 then
		if self.帮战报:事件判断() then
			if tp.窗口.帮战建设.可视 == false then
			    发送数据(6551)
			else
				tp.窗口.帮战建设:打开()
			end
	    end
	end
	self.时辰[self.序列]:显示(1.3,21.5 + self.偏移坐标)
	if self.偏移~=nil then
	   zt:显示(self.偏移-1,13.5 + self.偏移坐标,self.地图)
	end
	local xy = "X:"..floor(tp.角色坐标.x/20).." Y:"..floor(tp.角色坐标.y/20)
	zt:显示(floor(61 - zt:取宽度(xy) / 2)-1.5,62 + self.偏移坐标,xy)
	if self.灯笼[1]:是否选中(x,y) then
		tp.提示:自定义(x-42,y+27,"快捷键:ALT+Q")
	end
	if self.灯笼[2]:是否选中(x,y) then
		tp.提示:自定义(x-42,y+27,"快捷键:ALT+M")
	end
	if self.灯笼[3]:是否选中(x,y) then
		tp.提示:自定义(x-42,y+27,"快捷键:Tab")
	end
end

function 系统类_时辰:检查点(x,y)
	return 矩阵:检查点(x,y)
end

return 系统类_时辰