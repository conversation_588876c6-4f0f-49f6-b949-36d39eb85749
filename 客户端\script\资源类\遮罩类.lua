--======================================================================--
-- @作者: www.wnfj.com，联系QQ：79550111
-- @创建时间:   2018-03-03 02:34:19
-- @Last Modified time: 2024-12-05 17:26:08
-- 更多游戏请访问万能飞机：www.wnfj.com,版本定制授权联系QQ：79550111
--======================================================================--
local 遮罩类 = class()

local xys = 生成XY
local random = math.random

function 遮罩类:初始化(精灵)
    self.精灵 = 精灵
    self.xy     = xys(精灵.X,精灵.Y)
    self.排序点   = 精灵.排序点
    self.随机     = random(1,1200)/100 --随机排序值,防止相同排序点出现.
    self.是否遮罩 = true
    self.可见 = true
    self.坐标    = xys(精灵.X+10,self.排序点)
end

function 遮罩类:固定(v)
    self.是否固定   = v
    self.可见       = v
end

function 遮罩类:检查点(x,y)
    return self.精灵:检查点(x,y)
end

function 遮罩类:取排序点(xy)
    return self.排序点+self.随机
end

function 遮罩类:显示(dt,xx,yy,偏移)
   -- if self.可见  then
        self.精灵:显示(self.xy+偏移)
   --end
end

return 遮罩类