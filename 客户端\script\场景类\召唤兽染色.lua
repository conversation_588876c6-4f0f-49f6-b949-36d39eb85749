--======================================================================--

--======================================================================--
local 场景类_召唤兽染色 = class()
local floor = math.floor
local xslb,bb,lb,tp,fy,gz,方向
local format = string.format
local insert = table.insert

local 染色方案={
	[1]={方案={[1]=1,[2]=0},id=64},
	[2]={方案={[1]=1,[2]=0},id=0},
	[3]={方案={[1]=1,[2]=0},id=83},
	[4]={方案={[1]=1,[2]=0},id=106},
	[5]={方案={[1]=1,[2]=0},id=114},
	[6]={方案={[1]=1,[2]=0},id=77},
	[7]={方案={[1]=1,[2]=0},id=60},
	[8]={方案={[1]=1,[2]=0},id=2051},
	[9]={方案={[1]=1,[2]=0},id=2065},
	[10]={方案={[1]=1,[2]=1},id=56},
	[11]={方案={[1]=1,[2]=0},id=54},
	[12]={方案={[1]=1,[2]=0},id=67},
	[13]={方案={[1]=1,[2]=0},id=52},
	[14]={方案={[1]=1,[2]=0},id=82},
	[15]={方案={[1]=1,[2]=1},id=107},
	[16]={方案={[1]=1,[2]=0},id=76},
	[17]={方案={[1]=1,[2]=0},id=80},
	[18]={方案={[1]=1,[2]=0},id=2070},
	[19]={方案={[1]=0,[2]=1},id=2057},
	[20]={方案={[1]=1,[2]=0},id=53},
	[21]={方案={[1]=1,[2]=0},id=85},
	[22]={方案={[1]=1,[2]=0},id=69},
	[23]={方案={[1]=2},id=119},
	[24]={方案={[1]=0,[2]=1},id=2069},
	[25]={方案={[1]=1,[2]=0},id=101},
	[26]={方案={[1]=1,[2]=0},id=58},
	[27]={方案={[1]=2,[2]=0},id=2000},
	[28]={方案={[1]=5,[2]=0},id=2078},
	[29]={方案={[1]=5,[2]=0},id=2079},
	[30]={方案={[1]=1,[2]=0},id=2042},
	[31]={方案={[1]=1,[2]=1},id=90},
	[32]={方案={[1]=1,[2]=0},id=2071},
	[33]={方案={[1]=1,[2]=0},id=75},
	[34]={方案={[1]=1,[2]=0},id=95},
	[35]={方案={[1]=1,[2]=1},id=108},
	[36]={方案={[1]=1,[2]=0},id=81},
	[37]={方案={[1]=1,[2]=0},id=70},
	[38]={方案={[1]=1,[2]=1},id=55},
	[39]={方案={[1]=1,[2]=0},id=63},
	[40]={方案={[1]=1,[2]=0},id=89},
	[41]={方案={[1]=1,[2]=0},id=66},
	[42]={方案={[1]=4,[2]=0},id=20113},
	[43]={方案={[1]=1,[2]=0},id=74},
	[44]={方案={[1]=0,[2]=1},id=111},
	[45]={方案={[1]=1,[2]=0},id=2062},
	[46]={方案={[1]=0,[2]=1},id=112},
	[47]={方案={[1]=1,[2]=0},id=98},
	[48]={方案={[1]=1,[2]=1},id=94},
	[49]={方案={[1]=0,[2]=1},id=110},
	[50]={方案={[1]=0,[2]=1},id=102},
	[51]={方案={[1]=1,[2]=0},id=61},
	[52]={方案={[1]=1,[2]=1},id=59},
	[53]={方案={[1]=1,[2]=0},id=51},
	[54]={方案={[1]=1,[2]=0},id=68},
	[55]={方案={[1]=1,[2]=0},id=78},
	[56]={方案={[1]=1,[2]=1},id=93},
	[57]={方案={[1]=1,[2]=0},id=62},
	[58]={方案={[1]=1,[2]=0},id=103},
	[59]={方案={[1]=1,[2]=0},id=71},
	[60]={方案={[1]=5,[2]=0},id=20104},
	[61]={方案={[1]=1,[2]=1},id=92},
	[62]={方案={[1]=0,[2]=3},id=20103},
	[63]={方案={[1]=1,[2]=0},id=65},
	[64]={方案={[1]=1,[2]=1},id=91},
	[65]={方案={[1]=1,[2]=0},id=97},
	[66]={方案={[1]=1,[2]=0},id=96},
	[67]={方案={[1]=1,[2]=0},id=87},
	[68]={方案={[1]=1,[2]=0},id=105},
	[69]={方案={[1]=1,[2]=0},id=99},
	[70]={方案={[1]=1,[2]=0},id=104},
	[71]={方案={[1]=1,[2]=1},id=88},
	[72]={方案={[1]=1},id=20306},
	[73]={方案={[1]=1,[2]=0},id=100},
	[74]={方案={[1]=1,[2]=1},id=73},
	[75]={方案={[1]=1,[2]=0},id=79},
	[76]={方案={[1]=1,[2]=0},id=57},
	[77]={方案={[1]=1},id=2059},
	[78]={方案={[1]=1,[2]=0},id=84},
	[79]={方案={[1]=1,[2]=0},id=109},
	[80]={方案={[1]=3,[2]=0},id=20230},
	[81]={方案={[1]=1,[2]=2},id=3000},
}



function 场景类_召唤兽染色:初始化(根)
	self.ID = 35
	-- self.x = 370
	-- self.y = 40
	self.xx = 0
	self.yy = 0
	self.注释 = "召唤兽染色"
	self.可视 = false
	self.鼠标 = false
	self.焦点 = false
	self.可移动 = true
	self.窗口时间 = 0
	tp = 根
	self.选中染色=0
	self.原来颜色=0
	self.染色组1=0
	self.染色组2=0
	方向=0
end

function 场景类_召唤兽染色:打开(b,l,c)
	if b~=nil and self.可视 then self.可视=false end
	if self.可视 then
		if b ~= nil and bb ~= b then
			bb = b
			fy = 0
			self:置形象(方向)
			tp.运行时间 = tp.运行时间 + 1
		    self.窗口时间 = tp.运行时间
		    return false
		end
		fy = nil
		bb = nil
		xslb = nil
		self.可视 = false
		self.资源组=nil
		self.染色组=nil
		return
	else
		fy = 0
		bb = b
		self.技能={}
		insert(tp.窗口_,self)
		self:加载数据()
		self:置形象(方向)
		tp.运行时间 = tp.运行时间 + 1
	    self.窗口时间 = tp.运行时间
	    if bb.染色方案~=nil then
	        self.原来颜色=bb.染色方案
	    	self.染色组1=bb.染色组[1]
	    	self.染色组2=bb.染色组[2]
	    end
	    if bb.饰品染色方案~=nil then
	        self.饰品原来颜色=bb.饰品染色方案
	    	self.饰品染色组1=bb.饰品染色组[1]
	    	self.饰品染色组2=bb.饰品染色组[2]
	    end
	    self.x = tp.窗口.召唤兽查看栏.x+100
		self.y =tp.窗口.召唤兽查看栏.y-80
	    self.可视 = true
	end
end

function 场景类_召唤兽染色:加载数据()
	local 资源 = tp.资源
	local 按钮 = tp._按钮
	local 自适应 = tp._自适应
	self.资源组 = {
		[1] = 自适应.创建(0,1,573,342,3,9),--[1] = 自适应.创建(0,1,573,342,3,9),
		[2] = 按钮.创建(自适应.创建(18,4,16,16,4,3),0,0,4,true,true),
		[3] = 资源:载入('登陆资源.wdf',"网易WDF动画",0x5B52CB27),
		[4] = 按钮.创建(自适应.创建(12,4,64,22,1,3),0,0,4,true,true," 还 原"),
		[5] = 按钮.创建(自适应.创建(12,4,64,22,1,3),0,0,4,true,true," 染 色"),
		[8] = 自适应.创建(1,1,461,18,1,3,nil,18),
		[9] = 按钮.创建(资源:载入('wzife.wdf',"网易WDF动画",0x92ABEFD3),0,0,4,true,true),
		[14] = 资源:载入('pic/huancai/bj.png',"图片"),           --大白底框
		[15] = 按钮.创建(资源:载入('zdy3.rpk',"网易WDF动画",16777347),0,0,4,true,true),
		[16] = 按钮.创建(资源:载入('zdy3.rpk',"网易WDF动画",16777348),0,0,4,true,true),
	}
	self.染色组={}
	for n=1,80 do
		self.染色组[n]=按钮.创建(自适应.创建(17,4,28,22,1,3),0,0,4,true,true,n)
	end
end

function 场景类_召唤兽染色:置形象(方向)

	if bb ~= nil then
		self.资源组[6] = nil
		self.资源组[7] = nil
			local n = 引擎.取战斗模型(bb.模型)
			self.资源组[6] = tp.资源:载入(n[10],"网易WDF动画",n[6])
			if 引擎.取战斗模型(bb.模型.."_饰品") ~= nil and #引擎.取战斗模型(bb.模型.."_饰品") >0 then
				n = 引擎.取战斗模型(bb.模型.."_饰品")
				self.资源组[7] = tp.资源:载入(n[10],"网易WDF动画",n[6])
				self.资源组[7]:置方向(方向)
				self.资源组[6]:置方向(方向)
			end
		end
		if bb.染色方案 ~= nil then
			if bb.染色方案 == "黑白" then
				self.资源组[6]:置染色("黑白",ARGB(255,235,235,235))
				self.资源组[6]:置方向(方向)
			else
				if self.选中染色~=0 then
				    bb.染色方案=染色方案[self.选中染色].id
				    bb.染色组[1]=染色方案[self.选中染色].方案[1]
				    bb.染色组[2]=染色方案[self.选中染色].方案[2]
				    self.资源组[6]:置染色(bb.染色方案,bb.染色组[1],bb.染色组[2],bb.染色组[3])
					self.资源组[6]:置方向(方向)
				else
					bb.染色方案=nil
					bb.染色组[1]=nil
					bb.染色组[2]=nil
					--self.资源组[6]:置染色(bb.染色方案,bb.染色组[1],bb.染色组[2],bb.染色组[3])
					self.资源组[6]:置方向(方向)
				end
			end
		else
			if bb.染色方案==nil then
				bb.染色组={}
			    	bb.染色方案=0
			 	bb.染色组[1]=0
			 	bb.染色组[2]=0
			end
			 bb.染色方案=0
			 bb.染色组[1]=0
			 bb.染色组[2]=0
			if self.选中染色~=0 then
			    bb.染色方案=染色方案[self.选中染色].id
			    bb.染色组[1]=染色方案[self.选中染色].方案[1]
			    bb.染色组[2]=染色方案[self.选中染色].方案[2]
			    self.资源组[6]:置染色(bb.染色方案,bb.染色组[1],bb.染色组[2],bb.染色组[3])
			self.资源组[6]:置方向(方向)
			end
		end
		if bb.饰品染色方案 ~= nil then
			bb.饰品染色方案=self.饰品原来颜色
			bb.饰品染色组[1]=self.饰品染色组1
			bb.饰品染色组[2]=self.饰品染色组2
			self.资源组[7]:置染色(bb.饰品染色方案,bb.饰品染色组[1],bb.饰品染色组[2],bb.饰品染色组[3])
			self.资源组[6]:置方向(方向)
		end
	end

function 场景类_召唤兽染色:显示(dt,x,y)
	self.焦点 = false
	self.资源组[2]:更新(x,y)
	self.资源组[4]:更新(x,y)
	self.资源组[5]:更新(x,y)
	self.资源组[9]:更新(x,y)
	self.资源组[15]:更新(x,y)
	self.资源组[16]:更新(x,y)
	self.资源组[1]:显示(self.x,self.y)
	self.资源组[14]:显示(self.x+11,self.y+29)
	self.资源组[4]:显示(self.x + 325,self.y + 308)
	self.资源组[5]:显示(self.x + 440,self.y + 308)
	--self.资源组[3]:显示(self.x + 15,self.y + 30)
	--self.资源组[8]:显示(self.x+2,self.y+2)
	self.资源组[15]:显示(self.x+35+12,self.y+260)
	self.资源组[16]:显示(self.x+142+12,self.y+260)
	tp.窗口标题背景_:显示(self.x+self.资源组[1].宽度/2-83,self.y+2)
	zts1:置颜色(白色)
	zts1:置字间距(3)
	zts1:显示(self.x+self.资源组[1].宽度/2-38,self.y+2,self.注释)
	zts1:显示(self.x+280,self.y+230,"需求一")
	zts1:显示(self.x+280,self.y+264,"需求二")
	zts1:置字间距(0)
	zts1:置颜色(黄色)
	zts1:显示(self.x+75,self.y+238,"选中颜色为："..self.选中染色)
	zts:置颜色(黑色)
	zts:显示(self.x+289,self.y+39,"幻 兽")
	zts:显示(self.x+395,self.y+233,"彩果30个")
	zts:显示(self.x+390,self.y+267,"金钱3000W")
	--zts1:显示(self.x+320,self.y+300,"染色需消耗彩果30个，金钱3000W！")
	-- tp.宽竖排花纹背景_:置区域(0,0,37,166)
	-- tp.宽竖排花纹背景_:显示(self.x+153,self.y+31)
	-- tp.宽竖排花纹背景_:置区域(0,549,37,38)
	-- tp.宽竖排花纹背景_:显示(self.x+153,self.y+200)
	-- tp.物品界面背景_:显示(self.x+192,self.y+29)
	self.资源组[2]:显示(self.x-18+self.资源组[1].宽度,self.y+2)
	--self.资源组[9]:显示(self.x + 25,self.y +152)
	local xx = 0
	local yy = 0
	for n=1,80 do
		self.染色组[n]:更新(x,y,self.选中染色~=n)
		self.染色组[n]:显示(self.x+259+30*xx,self.y+65+yy*20)
		xx = xx + 1
		if xx > 9 then
			xx = 0
			yy = yy + 1
		end
		if self.鼠标 then
		    if self.染色组[n]:事件判断() then
		    	self.选中染色=n
		    	self:置形象(方向)
		    end
		end
	end
	if self.鼠标 then
		if self.资源组[2]:事件判断() then
			self:打开()
			return false
		elseif self.资源组[4]:事件判断() then
			self.选中染色=0
			self:置形象(方向)
		elseif self.资源组[5]:事件判断() then
			发送数据(5015,{序列=bb.认证码,序列1=bb.染色方案,序列2=bb.染色组[1],序列3=bb.染色组[2],序列4=bb.染色组[3]})
		elseif self.资源组[9]:事件判断() then
			方向 = 方向 - 1
			if 方向<0 then
			    方向=3
			end
			self:置形象(方向)
		elseif self.资源组[15]:事件判断() then
			方向 = 方向 + 1
			if 方向 >3 then
				方向 = 0
			end
			self:置形象(方向)
		elseif self.资源组[16]:事件判断() then
			方向 = 方向 - 1
			if 方向 <0 then
				方向 = 3
			end
			self:置形象(方向)
		end
	end
	if self.资源组[6] ~= nil then
		tp.影子:显示(self.x + 166-40,self.y+202-15)
		self.资源组[6]:更新(dt)
		self.资源组[6]:显示(self.x + 166-40,self.y+202-15)
		if self.资源组[7] ~= nil then
			self.资源组[7]:更新(dt)
			self.资源组[7]:显示(self.x + 166-40,self.y+202-15)
		end
	end
	local 字体 = tp.字体表.普通字体
	字体:置颜色(黑色)
	-- 字体:显示(self.x + 25,self.y + 38,bb.模型)

end

function 场景类_召唤兽染色:检查点(x,y)
	if self.资源组 ~= nil and self.资源组[1]:是否选中(x,y)  then
		return true
	end
end

function 场景类_召唤兽染色:初始移动(x,y)
	tp.运行时间 = tp.运行时间 + 1
	if not tp.消息栏焦点 then
  		self.窗口时间 = tp.运行时间
 	end
	if not self.焦点 then
		tp.移动窗口 = true
	end
	if self.鼠标 and  not self.焦点 then
		self.xx = x - self.x
		self.yy = y - self.y
	end
end

function 场景类_召唤兽染色:开始移动(x,y)
	if self.鼠标 then
		self.x = x - self.xx
		self.y = y - self.yy
	end
end

return 场景类_召唤兽染色