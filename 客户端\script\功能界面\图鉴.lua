--======================================================================--
--	☆ 作者：作者QQ：79550111
--======================================================================--
local 场景类_图鉴 = class()
local tx = 引擎.取头像
local bw = require("gge包围盒")(0,0,140,30)
local box = 引擎.画矩形
local ani = 引擎.取战斗模型
local format = string.format
local floor = math.floor
local min = math.min
local max = math.max
local tp,zts,zts1,zts2,zts3,zts4,ztstt3
local ceil = math.ceil
local tostring = tostring
local mousea = 引擎.鼠标按住
local mouseb = 引擎.鼠标弹起
local insert = table.insert
local zqj = 引擎.坐骑库
local tx = 引擎.取头像
local wp={}
local 锦衣wp ={}
local 足迹wp={}
local 光环wp={}
local 翅膀wp={}


local function 判断游戏名字(mz)
    if string.find(mz,"[%s%p%c%z%?\\!@#%$%%&%*%(%)%^,%.%+%-/<>;'\"%[%]{}]")~=nil then
        return 1
    elseif string.find(mz,"　")~=nil or string.find(mz, "GM") ~= nil or string.find(mz, "Gm") ~= nil or string.find(mz, "充值") ~= nil or string.find(mz, "gm") ~= nil or string.find(mz, "管理") ~= nil or string.find(mz, "老猫") ~= nil or string.find(mz, "国家") ~= nil or string.find(mz, "主席") ~= nil or string.find(mz, "近平") ~= nil then
        return 1
    end
end
function 场景类_图鉴:初始化(根)

	self.ID = 75.5
	self.x = 50
	self.y = 35
	self.xx = 0
	self.yy = 0
	self.注释 = "配饰系统"
	self.可视 = false
	self.鼠标 = false
	self.焦点 = false
	self.可移动 = true
	tp = 根
	local 资源 = 根.资源
	local 按钮 = 根._按钮
	local 自适应 = 根._自适应
	self.资源组 = {
		[1] = 自适应.创建(0,1,500,390,3,9),
		[2] = 按钮.创建(自适应.创建(18,4,16,16,4,3),0,0,4,true,true),
		[4] = 自适应.创建(34,1,173,142,3,9),--坐骑选择框171,182  --33
		[5] = 自适应.创建(2,1,158,165,3,9),
		[6] = 按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"  骑 乘"),
		[7] = 按钮.创建(自适应.创建(12,4,84,22,1,3),0,0,4,true,true,"  驯 养"),
		[8] = 按钮.创建(自适应.创建(12,4,84,22,1,3),0,0,4,true,true,"  放 生"),
		[9] = 按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"  属 性"),
		[10] = 按钮.创建(自适应.创建(12,4,70,22,1,3),0,0,4,true,true,"请选择"),
		[11] = 自适应.创建(3,1,87,19,1,3),--名称显示框
		[12] = 自适应.创建(3,1,66-11,19,1,3),--环境度
		[13] = 自适应.创建(3,1,87+40,19,1,3),--属性框1
		[14] = 按钮.创建(资源:载入('zdy.rpk',"网易WDF动画",0x1000379),0,0,4,true,true),--查看资质
		[15] = 按钮.创建(自适应.创建(12,4,55,22,1,3),0,0,4,true,true,"坐骑"),
		[16] = 按钮.创建(自适应.创建(12,4,55,22,1,3),0,0,4,true,true,"锦衣"),
		[17] = 根._滑块.创建(自适应.创建(11,4,15,40,2,3,nil),1,14,142,2),
		[18] = 资源:载入('zdy.rpk',"网易WDF动画",0x1000375),--经验
		[19] = 按钮.创建(根.资源:载入('common/wzife.wdf',"网易WDF动画",1518771720),0,0,1,true,true), --焕彩按钮
		[20] = 资源:载入('wzife.wd1',"网易WDF动画",0xE9063B56),--饰品  0x8561289C 空格子  8F00251E项链
		[21] = 按钮.创建(自适应.创建(12,4,55,22,1,3),0,0,4,true,true,"足迹"),
		[23] = 自适应.创建(3,1,87+40,19,1,3),--成长那个框框
		[22] = 按钮.创建(自适应.创建(12,4,55,22,1,3),0,0,4,true,true,"光环"),
		[28] = 按钮.创建(自适应.创建(10,4,18,20,1,3),0,0,4,true,true,"1"),
		[29] = 按钮.创建(自适应.创建(10,4,18,20,1,3),0,0,4,true,true,"2"),
		[30] = 按钮.创建(自适应.创建(12,4,75,22,1,3),0,0,4,true,true,"激活配饰"),
		[31] = 按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"  佩 戴"),
		[32] = 按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"  卸 下"),
		[33] = 按钮.创建(自适应.创建(12,4,55,22,1,3),0,0,4,true,true,"翅膀"),


	}
	self.宝宝头像背景 = 按钮(根.资源:载入("wzife.wdf","网易WDF动画",0xCEC838D7),0,0,1,true,true)
	self.宝宝头像背景2 = 按钮(根.资源:载入("wzife.wdf","网易WDF动画",0xCEC838D7),0,0,1,true,true)
	self.选中显示 = 资源:载入('xixige_newmall.gep',"内置png",0x10001065)
	for i=6,10 do
		self.资源组[i]:绑定窗口_(self.ID)
	end
	for i=14,16 do
		self.资源组[i]:绑定窗口_(self.ID)
	end
	self.资源组[19]:绑定窗口_(self.ID)
	self.资源组[21]:绑定窗口_(self.ID)


	self.资源组[10]:置偏移(5,0)

	self.控件类 = require("ggeui/加载类")()
	local 总控件 = self.控件类:创建控件('坐骑总控件')
	总控件:置可视(true,true)
	self.名称输入框 = 总控件:创建输入("名称输入",0,0,100,14,根,根.字体表.华康14)
	self.名称输入框:置可视(false,false)
	self.名称输入框:置限制字数(12)
	self.名称输入框:置光标颜色(-16777216)
	self.名称输入框:置文字颜色(-16777216)

	zts = tp.字体表.普通字体
	zts1 = tp.字体表.普通字体__
	zts2 = tp.字体表.汉仪字体4
	zts3 = tp.字体表.华康16粗
	zts4 = tp.字体表.华康16粗
	self.物品 = {}

	self.加入 = 0
	self.选中 = 0
	self.序号 = 0
	self.选中数值 = 0
	self.选中类型 = 0
	self.开始=1
	self.结束=25
	self.进程=1
	self.页面=1
	self.碎片消耗=0
	self.仙玉消耗=0
	self.记忆角色={}
	self.记忆角色[1]=nil
	self.记忆角色[2]=nil
	self.窗口时间 = 0
	self.服饰={}
	self.服饰数据={}
	self.图鉴={}
	self.服饰id={}
	self.图鉴数值={}

end
function 场景类_图鉴:加载物品(数据)
	-- self.礼包序列 =数据.是否领取

	for k,v in pairs(wp) do
		if self.物品组[k]==nil then
		   self.物品组[k]={}
		end
		if self.图鉴数值[k]==nil then
		   self.图鉴数值[k]={}
		end
		self.图鉴数值[k].数值=v.数值
		self.图鉴数值[k].类型=v.类型
		self.图鉴数值[k].碎片=v.碎片
		self.图鉴数值[k].仙玉=v.仙玉
		for i,n in pairs(v) do
			-- print(n)
			if self.物品组[k][i]== nil then
				self.物品组[k][i]={}
			end
			local 资源=引擎.取物品(n)
			self.物品组[k][i].小动画=tp.资源:载入(资源[11],"网易WDF动画",资源[12])
			self.物品组[k][i].大动画=tp.资源:载入(资源[11],"网易WDF动画",资源[13])
			self.物品组[k][i].名称=n
			self.物品组[k][i].说明=资源[1]
		end
	end
	--table.print(self.图鉴数值)
end

function 场景类_图鉴:加载锦衣物品(数据)
	-- self.礼包序列 =数据.是否领取
	for k,v in pairs(锦衣wp) do
		if self.锦衣物品组[k]==nil then
		   self.锦衣物品组[k]={}
		end
		if self.图鉴数值[k]==nil then
		   self.图鉴数值[k]={}
		end
		self.图鉴数值[k].数值=v.数值
		self.图鉴数值[k].类型=v.类型
		self.图鉴数值[k].碎片=v.碎片
		self.图鉴数值[k].仙玉=v.仙玉
		for i,n in pairs(v) do
			-- print(n)
			if self.锦衣物品组[k][i]== nil then
				self.锦衣物品组[k][i]={}
			end
			local 资源=引擎.取物品(n)
			self.锦衣物品组[k][i].小动画=tp.资源:载入(资源[11],"网易WDF动画",资源[12])
			self.锦衣物品组[k][i].大动画=tp.资源:载入(资源[11],"网易WDF动画",资源[13])
			self.锦衣物品组[k][i].名称=n
			self.锦衣物品组[k][i].说明=资源[1]
		end
	end
end

function 场景类_图鉴:加载翅膀物品(数据)
	-- self.礼包序列 =数据.是否领取

	for k,v in pairs(翅膀wp) do
		if self.翅膀物品组[k]==nil then
		   self.翅膀物品组[k]={}
		end
		if self.图鉴数值[k]==nil then
		   self.图鉴数值[k]={}
		end
		self.图鉴数值[k].数值=v.数值
		self.图鉴数值[k].类型=v.类型
		self.图鉴数值[k].碎片=v.碎片
		self.图鉴数值[k].仙玉=v.仙玉
		for i,n in pairs(v) do
			-- print(n)
			if self.翅膀物品组[k][i]== nil then
				self.翅膀物品组[k][i]={}
			end
			local 资源=引擎.取物品(n)
			self.翅膀物品组[k][i].小动画=tp.资源:载入(资源[11],"网易WDF动画",资源[12])
			self.翅膀物品组[k][i].大动画=tp.资源:载入(资源[11],"网易WDF动画",资源[13])
			self.翅膀物品组[k][i].名称=n
			self.翅膀物品组[k][i].说明=资源[1]
		end
	end
end


function 场景类_图鉴:加载光环物品(数据)
	-- self.礼包序列 =数据.是否领取

	for k,v in pairs(光环wp) do
		if self.光环物品组[k]==nil then
		   self.光环物品组[k]={}
		end
		if self.图鉴数值[k]==nil then
		   self.图鉴数值[k]={}
		end
		self.图鉴数值[k].数值=v.数值
		self.图鉴数值[k].类型=v.类型
		self.图鉴数值[k].碎片=v.碎片
		self.图鉴数值[k].仙玉=v.仙玉
		for i,n in pairs(v) do
			-- print(n)
			if self.光环物品组[k][i]== nil then
				self.光环物品组[k][i]={}
			end
			local 资源=引擎.取物品(n)
			self.光环物品组[k][i].小动画=tp.资源:载入(资源[11],"网易WDF动画",资源[12])
			self.光环物品组[k][i].大动画=tp.资源:载入(资源[11],"网易WDF动画",资源[13])
			self.光环物品组[k][i].名称=n
			self.光环物品组[k][i].说明=资源[1]
		end
	end
end

function 场景类_图鉴:加载足迹物品(数据)
	-- self.礼包序列 =数据.是否领取

	for k,v in pairs(足迹wp) do
		if self.足迹物品组[k]==nil then
		   self.足迹物品组[k]={}
		end
		if self.图鉴数值[k]==nil then
		   self.图鉴数值[k]={}
		end
		self.图鉴数值[k].数值=v.数值
		self.图鉴数值[k].类型=v.类型
		self.图鉴数值[k].碎片=v.碎片
		self.图鉴数值[k].仙玉=v.仙玉
		for i,n in pairs(v) do
			-- print(n)
			if self.足迹物品组[k][i]== nil then
				self.足迹物品组[k][i]={}
			end
			local 资源=引擎.取物品(n)
			self.足迹物品组[k][i].小动画=tp.资源:载入(资源[11],"网易WDF动画",资源[12])
			self.足迹物品组[k][i].大动画=tp.资源:载入(资源[11],"网易WDF动画",资源[13])
			self.足迹物品组[k][i].名称=n
			self.足迹物品组[k][i].说明=资源[1]
		end
	end
end

function 场景类_图鉴:激活刷新(内容)
	self.服饰=内容.内容
	self.服饰数据=内容.服饰数据
	for k, v in pairs(self.服饰) do
		if self.服饰[k]~= nil then
			for i,n in pairs(v) do
			if self.图鉴[n]== nil then
				self.图鉴[n]={}
			end
			if self.服饰id[n]== nil then
				self.服饰id[n]={}
			end
			 self.图鉴[n]=true
			 self.服饰id[n]=k
			end
		end
	end
end


function 场景类_图鉴:打开(内容)

	if self.可视 then
		self.加入 = 0
		self.选中 = 0
		self.序号 = 0
		self.可视 = false
		self.开始=1
		self.结束=25

	else
		insert(tp.窗口_,self)
		if wp==nil then
		    wp={}
		end
		for i=1,#wp do
			if wp ~= nil then
				if i > 4 then
					self.加入 = i-4
					self.资源组[17]:置起始点(self.资源组[17]:取百分比转换(self.加入,25,#wp))
				end
				self.选中 = i
				self.序号 = i
				self:置形象()
				break
			end
		end
		-- tp.窗口.坐骑属性选择:打开()
		tp.运行时间 = tp.运行时间 + 1
	    self.窗口时间 = tp.运行时间
	    self.可视 = true
	    self.选中 = 0
	    self.序号 = 0
	    self.开始=1
	    self.结束=25
	    -- self.坐骑图鉴=内容.图鉴数据.坐骑图鉴
	    -- self.图鉴=内容.图鉴数据.锦衣图鉴
	    -- self.足迹图鉴=内容.图鉴数据.足迹图鉴
	    -- self.光环图鉴=内容.图鉴数据.光环图鉴
	    self.物品组={}
	    self.锦衣物品组={}
	    self.光环物品组={}
	    self.足迹物品组={}
	    self.翅膀物品组={}

	    self.服饰=内容.内容
	    self.服饰数据=内容.服饰数据

		for k, v in pairs(self.服饰) do
			if self.服饰[k]~= nil then

				for i,n in pairs(v) do
					if self.图鉴[n]== nil then
						self.图鉴[n]={}
					end
					if self.服饰id[n]== nil then
						self.服饰id[n]={}
					end
					 self.图鉴[n]=true
					 self.服饰id[n]=k
				end
			end
		end

		for k, v in pairs(self.服饰数据) do
			if self.服饰数据[k]~= nil then
				if k > 1000   and  k < 2000 then
					if wp== nil then
						wp={}
					end
					wp[k] ={名称=self.服饰数据[k].名称,模型=self.服饰数据[k].名称,类型=self.服饰数据[k].类型,数值=self.服饰数据[k].数值,碎片=self.服饰数据[k].碎片,仙玉=self.服饰数据[k].仙玉}
				end
				if k > 2000   and  k < 3000 then
					if 锦衣wp== nil then
						锦衣wp={}
					end
					锦衣wp[k] ={名称=self.服饰数据[k].名称,模型=self.服饰数据[k].名称,类型=self.服饰数据[k].类型,数值=self.服饰数据[k].数值,碎片=self.服饰数据[k].碎片,仙玉=self.服饰数据[k].仙玉}
				end
				if k > 3000   and  k < 4000 then
					if 光环wp== nil then
						光环wp={}
					end
					光环wp[k] ={名称=self.服饰数据[k].名称,模型=self.服饰数据[k].名称,类型=self.服饰数据[k].类型,数值=self.服饰数据[k].数值,碎片=self.服饰数据[k].碎片,仙玉=self.服饰数据[k].仙玉}
				end
				if k > 4000   and  k < 5000 then
					if 足迹wp== nil then
						足迹wp={}
					end
					足迹wp[k] ={名称=self.服饰数据[k].名称,模型=self.服饰数据[k].名称,类型=self.服饰数据[k].类型,数值=self.服饰数据[k].数值,碎片=self.服饰数据[k].碎片,仙玉=self.服饰数据[k].仙玉}
				end
				if k > 5000   and  k < 6000 then
					if 翅膀wp== nil then
						翅膀wp={}
					end
					翅膀wp[k] ={名称=self.服饰数据[k].名称,模型=self.服饰数据[k].名称,类型=self.服饰数据[k].类型,数值=self.服饰数据[k].数值,碎片=self.服饰数据[k].碎片,仙玉=self.服饰数据[k].仙玉}
				end
			end
		end
		--table.print(锦衣wp)
		--table.print(self.图鉴)

	    self:加载物品()
	    self:加载锦衣物品()
	    self:加载光环物品()
	    self:加载足迹物品()
	    self:加载翅膀物品()
	end
end



function 场景类_图鉴:置形象()
	if wp[self.选中]  ~= nil then
		local n = {}
		if 引擎.新增坐骑(tp.队伍[1].模型,wp[self.选中].模型,"奔跑") ~= "" then
			n = {}
			n.人物资源 = "vvxxzcom/祥瑞坐骑.wdf"
			n.人物站立 = 引擎.新增坐骑(tp.队伍[1].模型,wp[self.选中].模型,"站立")
			n.坐骑资源 = "vvxxzcom/祥瑞坐骑.wdf"
			n.坐骑站立 = 引擎.新增坐骑(tp.队伍[1].模型,wp[self.选中].模型,"站立")
		else
			n = zqj(tp.队伍[1].模型,wp[self.选中].模型,wp[self.选中].饰品 or "空",wp[self.选中].饰品2 or "空")--ani(wp[self.选中].模型)
		end

		self.资源组[24] = tp.资源:载入(n.坐骑资源,"网易WDF动画",n.坐骑站立)--坐骑

		 self.资源组[24]:置方向(1)


	end
end

function 场景类_图鉴:置锦衣形象()
	if 锦衣wp[self.选中]  ~= nil then
		local xn = 引擎.取普通锦衣模型(锦衣wp[self.选中].名称,tp.队伍[1].模型)
		self.资源组[25] = tp.资源:载入(xn[3],"网易WDF动画",xn[1])
	end
end

function 场景类_图鉴:置翅膀形象()
	if 翅膀wp[self.选中]  ~= nil then
		local xn = 引擎.取翅膀(翅膀wp[self.选中].名称)
		self.资源组[34] = tp.资源:载入(xn[4],"网易WDF动画",xn[1])
	end
end

function 场景类_图鉴:置光环形象()
	if 光环wp[self.选中]  ~= nil then
		local xn = 引擎.取光环(光环wp[self.选中].名称)
		self.资源组[26] = tp.资源:载入(xn[4],"网易WDF动画",xn[1])
	end
end

function 场景类_图鉴:置足迹形象()
	if 足迹wp[self.选中]  ~= nil then
		local xn = 引擎.取足迹(足迹wp[self.选中].名称)
		self.资源组[27] = tp.资源:载入(xn[4],"网易WDF动画",xn[1])
	end
end

function 场景类_图鉴:显示(dt,x,y)
	if not self.可视 then return end
	if wp==nil then
		wp={}
		self.选中=0
	end
	local bbs = wp
	local bbsa = #bbs
	local bb
	local 灰色 = true
	if self.选中 ~= 0 then
		bb = bbs[self.选中]
	end


-- 更新
	self.焦点 = false
	self.资源组[2]:更新(x,y)
	self.资源组[6]:更新(x,y,bb ~= nil)
	self.资源组[7]:更新(x,y,bb ~= nil)
	self.资源组[8]:更新(x,y,bb ~= nil)
	self.资源组[9]:更新(x,y)
	self.资源组[10]:更新(x,y,bb ~= nil)
	self.资源组[14]:更新(x,y,bb ~= nil)
	self.资源组[15]:更新(x,y,self.进程 ~= 1)
	self.资源组[16]:更新(x,y,self.进程 ~= 2)
	self.资源组[19]:更新(x,y,bb ~= nil)
	self.资源组[21]:更新(x,y,self.进程 ~= 3)
	self.资源组[22]:更新(x,y,self.进程 ~= 4)
	self.资源组[33]:更新(x,y,self.进程 ~= 5)
	--self.资源组[23]:更新(x,y,bb ~= nil)
	--self.资源组[30]:更新(x,y，self.图鉴[self.序号] ==true)
	self.资源组[30]:更新(x,y, self.图鉴[self.序号] ~=true )


	-- 显示
	self.资源组[1]:显示(self.x,self.y)
	tp.窗口标题背景_:显示(self.x-76+self.资源组[1].宽度/2,self.y)
	引擎.场景.字体表.标题字体:显示(self.x+self.资源组[1].宽度/2+10,self.y+3,"配饰系统")
	self.资源组[2]:显示(self.x-18+self.资源组[1].宽度,self.y+2)
	self.资源组[5]:显示(self.x+12,self.y+29) --最早左边的大白框
	self.资源组[15]:显示(self.x+185,self.y+360,nil,nil,nil,self.进程 == 1,2)
	self.资源组[16]:显示(self.x+247,self.y+360,nil,nil,nil,self.进程 == 2,2)
	self.资源组[21]:显示(self.x+309,self.y+360,nil,nil,nil,self.进程 == 3,2)
	self.资源组[22]:显示(self.x+371,self.y+360,nil,nil,nil,self.进程 == 4,2)
	self.资源组[33]:显示(self.x+433,self.y+360,nil,nil,nil,self.进程 == 5,2)

	if self.资源组[15]:事件判断() then
		self.进程=1
		self.页面=1
	elseif self.资源组[16]:事件判断() then
		self.进程=2
		self.页面=1
	elseif self.资源组[21]:事件判断() then
		self.进程=3
		self.页面=1
	elseif self.资源组[22]:事件判断() then
		self.进程=4
		self.页面=1
	elseif self.资源组[33]:事件判断() then
		self.进程=5
		self.页面=1
	end

	zts:置颜色(-16777216)

	local xx = 0
	local yy = 0
	for i=1,25 do
		--local 偏移x,偏移y = 等比例缩放公式(45,45,tp.物品格子背景_.宽度,tp.物品格子背景_.高度)
		tp.物品格子背景_:显示(self.x+250+(xx-1)*62,self.y+30+yy*67)

		xx = xx + 1

		if xx==5 then
		    xx=0
		    yy=yy+1
		end
	end


      if self.进程 == 1 then           --坐骑

	for k,v in pairs(self.物品组) do
	    --if k<=5 then
	    if k<=1005 and k>1000 then
            	local wx = (k-1000)*62
            	local 找到 = 0
			v.模型.小动画:显示(self.x+220+wx-95,self.y+30)

				if  v.名称.名称 == "彩虹毛驴"  and  not self.图鉴.彩虹毛驴 or  v.名称.名称 == "蓝色狐狸"  and  not self.图鉴.蓝色狐狸 or  v.名称.名称 == "黄金狮子"  and  not self.图鉴.黄金狮子 or v.名称.名称 == "七彩祥云"  and  not self.图鉴.七彩祥云 or  v.名称.名称 == "粉红火鸡"  and  not self.图鉴.粉红火鸡  then
					v.模型.小动画:置颜色(0xFF808080)
					zts3:置颜色(红色)
					zts3:显示(self.x+220+wx-90,self.y+65,"未激活")
				elseif  v.名称.名称 == "彩虹毛驴"  and   self.图鉴.彩虹毛驴 or  v.名称.名称 == "蓝色狐狸"  and   self.图鉴.蓝色狐狸 or  v.名称.名称 == "黄金狮子"  and   self.图鉴.黄金狮子 or v.名称.名称 == "七彩祥云"  and   self.图鉴.七彩祥云 or  v.名称.名称 == "粉红火鸡"  and   self.图鉴.粉红火鸡  then
					v.模型.小动画:置颜色(白色)
					zts4:置颜色(绿色)
					zts4:显示(self.x+220+wx-90,self.y+65,"已激活")

				end

			if v.模型.小动画:是否选中(x,y,偏移x,偏移y) then
		    		tp.提示:商城提示(self.x-150+wx,self.y-90,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
		    	end
			if self.物品组[k] ~= nil and self.物品组[k].模型.小动画 ~= nil then
					if  self.选中 <  1000  or self.选中 > 2000 then
						self.选中 =  1001+self.加入
						self.序号 = "蓝色狐狸"
						tp.物品格子确定_:显示(self.x+225+62-95,self.y+32)
						if   self.图鉴数值[self.选中]~= nil  then
							self.选中数值=self.图鉴数值[self.选中].数值
							self.选中类型=self.图鉴数值[self.选中].类型
							self.碎片消耗=self.图鉴数值[self.选中].碎片
							self.仙玉消耗=self.图鉴数值[self.选中].仙玉
						end
						self:置形象()

					else
	   				if self.物品组[k].模型.小动画:是否选中(x,y)  then
	   					self.焦点 = true
	   					if 引擎.鼠标弹起(左键)  then
							self.选中 =  k+self.加入
							self.序号 = v.名称.名称

							if   self.图鉴数值[self.选中]~= nil  then
								self.选中数值=self.图鉴数值[self.选中].数值
								self.选中类型=self.图鉴数值[self.选中].类型
								self.碎片消耗=self.图鉴数值[self.选中].碎片
								self.仙玉消耗=self.图鉴数值[self.选中].仙玉
							end
							self:置形象()
						end
					end
					if self.选中~=0 then
						if  k+self.加入 ==self.选中  then
						  tp.物品格子确定_:显示(self.x+225+wx-95,self.y+32)
						end
					end
				end
			end
		end

	if k<=1010 and k>1005 then
            local wx = (k-1005)*62
			v.模型.小动画:显示(self.x+220+wx-95,self.y+30+1*67)
			if  v.名称.名称 == "粉嫩小猪"  and  not self.图鉴.粉嫩小猪 or  v.名称.名称 == "水晶芭蕉"  and  not self.图鉴.水晶芭蕉 or  v.名称.名称 == "深海狂鲨"  and  not self.图鉴.深海狂鲨 or v.名称.名称 == "雷霆黑豹"  and  not self.图鉴.雷霆黑豹 or  v.名称.名称 == "砖石小马"  and  not self.图鉴.砖石小马  then
					v.模型.小动画:置颜色(0xFF808080)
					zts3:置颜色(红色)
					zts3:显示(self.x+220+wx-90,self.y+65+1*67,"未激活")
				elseif  v.名称.名称 == "粉嫩小猪"  and   self.图鉴.粉嫩小猪 or  v.名称.名称 == "水晶芭蕉"  and   self.图鉴.水晶芭蕉 or  v.名称.名称 == "深海狂鲨"  and   self.图鉴.深海狂鲨 or v.名称.名称 == "雷霆黑豹"  and   self.图鉴.雷霆黑豹 or  v.名称.名称 == "砖石小马"  and   self.图鉴.砖石小马  then
					v.模型.小动画:置颜色(白色)
					zts4:置颜色(绿色)
					zts4:显示(self.x+220+wx-90,self.y+65+1*67,"已激活")
				end
			if v.模型.小动画:是否选中(x,y,偏移x,偏移y) then
		    	tp.提示:商城提示(self.x-150+wx,self.y-90+1*67,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
		    	end
			if self.物品组[k] ~= nil and self.物品组[k].模型.小动画 ~= nil then
	   				if self.物品组[k].模型.小动画:是否选中(x,y)  then
	   					self.焦点 = true
	   					if 引擎.鼠标弹起(左键)  then
							self.选中 =  k+self.加入
							self.序号 = v.名称.名称
							if   self.图鉴数值[self.选中]~= nil  then
								self.选中数值=self.图鉴数值[self.选中].数值
								self.选中类型=self.图鉴数值[self.选中].类型
								self.碎片消耗=self.图鉴数值[self.选中].碎片
								self.仙玉消耗=self.图鉴数值[self.选中].仙玉
							end
							self:置形象()
						end
					end
					if self.选中~=0 then
						if  k+self.加入 ==self.选中  then
						  tp.物品格子确定_:显示(self.x+225+wx-95,self.y+32+1*67)
						end
					end
			end
		end



	if k<=1015 and k>1010 then
            local wx = (k-1010)*62
			v.模型.小动画:显示(self.x+220+wx-95,self.y+30+2*67)
			if  v.名称.名称 == "九尾妖狐"  and  not self.图鉴.九尾妖狐  then
					v.模型.小动画:置颜色(0xFF808080)
					zts3:置颜色(红色)
					zts3:显示(self.x+220+wx-90,self.y+65+2*67,"未激活")
				elseif  v.名称.名称 == "九尾妖狐"  and   self.图鉴.九尾妖狐   then
					v.模型.小动画:置颜色(白色)
					zts4:置颜色(绿色)
					zts4:显示(self.x+220+wx-90,self.y+65+2*67,"已激活")
				end
			if v.模型.小动画:是否选中(x,y,偏移x,偏移y) then
		    	tp.提示:商城提示(self.x-150+wx,self.y-90+2*67,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
		    	end
			if self.物品组[k] ~= nil and self.物品组[k].模型.小动画 ~= nil then
	   				if self.物品组[k].模型.小动画:是否选中(x,y)  then
	   					self.焦点 = true
	   					if 引擎.鼠标弹起(左键)  then
							self.选中 =  k+self.加入
							self.序号 = v.名称.名称
							if   self.图鉴数值[self.选中]~= nil  then
								self.选中数值=self.图鉴数值[self.选中].数值
								self.选中类型=self.图鉴数值[self.选中].类型
								self.碎片消耗=self.图鉴数值[self.选中].碎片
								self.仙玉消耗=self.图鉴数值[self.选中].仙玉
							end
							self:置形象()
						end
					end
					if self.选中~=0 then
						if  k+self.加入 ==self.选中  then
						  tp.物品格子确定_:显示(self.x+225+wx-95,self.y+32+2*67)
						end
					end
			end
		end




    	end


    	self.资源组[6]:显示(self.x+10,self.y+203)--骑乘
	self.资源组[9]:显示(self.x+95,self.y+203,true)--属性
	self.资源组[30]:显示(self.x+55,self.y+353,true)--激活

	zts2:显示(self.x+15,self.y+250,"激活增加角色属性")
	if self.选中类型~=nil and self.选中类型=="最大气血" then
		self.选中类型="气血"
	end
	zts2:显示(self.x+55,self.y+295,self.选中数值.."点"..self.选中类型)
	--zts2:显示(self.x+95,self.y+295,self.选中类型)

	zts3:置颜色(绿色)
	zts3:显示(self.x+60,self.y+230,"["..self.序号.."]")
	zts4:置颜色(黄色)
	zts4:显示(self.x+5,self.y+335,"需拥有该坐骑+"..self.仙玉消耗.."仙玉激活")



------------------------坐骑显示框-----------------------------
	if bb ~= nil then
		local jx = self.x + 80
		local jy = self.y + 145--+25
		tp.影子:显示(jx,jy)
		if self.资源组[24] ~= nil then
			self.资源组[24]:更新(dt)
			self.资源组[24]:显示(jx,jy)
		end
	for z=1,10 do
		if self.选中~=0 and tp.队伍[1].坐骑~=nil and tp.坐骑列表 ~= nil and ( (tp.队伍[1].坐骑~=nil and tp.队伍[1].坐骑[z]~=nil and tp.坐骑列表[self.选中-1000].认证码==tp.队伍[1].坐骑[z].认证码)) then
			self.资源组[6]:置文字("  下 骑")
		else
			self.资源组[6]:置文字("  骑 乘")
		end
	end
------------------------坐骑显示框-----------------------------
	 end

	 if self.资源组[6]:事件判断() then --骑乘
			if self.资源组[6]:取文字()=="  骑 乘" then
				local 真实序列 = self.选中-1000
				发送数据(26.1,{序列=真实序列,坐骑名=self.序号})
				--发送数据(26.1,{序列=self.选中,坐骑名=self.序号})
			elseif self.资源组[6]:取文字()=="  下 骑" then
			    发送数据(27,{序列=0})
			end
	elseif self.资源组[9]:事件判断() then
			tp.窗口.坐骑属性栏:打开()
	elseif self.资源组[30]:事件判断() then
			local 真实序列 = self.选中-1000
			--发送数据(190,{序列=self.选中,图鉴名称=self.序号,文本="坐骑"})
			发送数据(7001,{序列=self.选中,激活id=真实序列,图鉴名称=self.序号,文本="坐骑",图鉴id=self.图鉴id})
		end


      elseif self.进程 == 2 then          --锦衣


      	if 锦衣wp==nil then
		锦衣wp={}
		self.选中=0
	end
	local bbs = 锦衣wp
	local bbsa = #bbs
	local 锦衣bb
	--local 置灰
	local 灰色 = true
	if self.选中 ~= 0 then
		锦衣bb = bbs[self.选中]
	end





	for k,v in pairs(self.锦衣物品组) do
		    --if k<=5 then
		    if k<=2005 and k>2000 then
	            	local wx = (k-2000)*62
	            	local 找到 = 0

				v.模型.小动画:显示(self.x+220+wx-95,self.y+30)


				if  v.名称.名称 == "青花瓷"  and  not self.图鉴.青花瓷 or  v.名称.名称 == "花间梦"  and  not self.图鉴.花间梦 or  v.名称.名称 == "鹿角弯弯"  and  not self.图鉴.鹿角弯弯 or v.名称.名称 == "水云归"  and  not self.图鉴.水云归 or  v.名称.名称 == "飞天舞"  and  not self.图鉴.飞天舞  then
					v.模型.小动画:置颜色(0xFF808080)
					zts3:置颜色(红色)
					zts3:显示(self.x+220+wx-90,self.y+65,"未激活")
				elseif  v.名称.名称 == "青花瓷"  and   self.图鉴.青花瓷 or  v.名称.名称 == "花间梦"  and   self.图鉴.花间梦 or  v.名称.名称 == "鹿角弯弯"  and   self.图鉴.鹿角弯弯 or v.名称.名称 == "水云归"  and   self.图鉴.水云归 or  v.名称.名称 == "飞天舞"  and   self.图鉴.飞天舞  then
					v.模型.小动画:置颜色(白色)
					zts4:置颜色(绿色)
					zts4:显示(self.x+220+wx-90,self.y+65,"已激活")
				end
				if v.模型.小动画:是否选中(x,y,偏移x,偏移y) then
			    		tp.提示:商城提示(self.x-150+wx,self.y-90,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
			    	end
				if self.锦衣物品组[k] ~= nil and self.锦衣物品组[k].模型.小动画 ~= nil then

						if  self.选中 <2000  or  self.选中 > 3000   then
							self.选中 =  2001+self.加入
							self.序号 = "青花瓷"
							tp.物品格子确定_:显示(self.x+225+62-95,self.y+32)
							if   self.图鉴数值[self.选中]~= nil  then
								self.选中数值=self.图鉴数值[self.选中].数值
								self.选中类型=self.图鉴数值[self.选中].类型
								self.碎片消耗=self.图鉴数值[self.选中].碎片
								self.仙玉消耗=self.图鉴数值[self.选中].仙玉

							end
						self:置锦衣形象()
					else

		   				if self.锦衣物品组[k].模型.小动画:是否选中(x,y)  then
		   					self.焦点 = true
		   					if 引擎.鼠标弹起(左键)  then
								self.选中 =  k+self.加入
								self.序号 = v.名称.名称
								if self.服饰id[self.序号] ~= nil  then
								    self.图鉴id=self.服饰id[self.序号]
								end
								if   self.图鉴数值[self.选中]~= nil  then
								self.选中数值=self.图鉴数值[self.选中].数值
								self.选中类型=self.图鉴数值[self.选中].类型
								self.碎片消耗=self.图鉴数值[self.选中].碎片
								self.仙玉消耗=self.图鉴数值[self.选中].仙玉
								end
								self:置锦衣形象()
							end
						end
						if self.选中~=0 then
							if  k+self.加入 ==self.选中  then
							  tp.物品格子确定_:显示(self.x+225+wx-95,self.y+32)

							end
						end
					end
				end
			end

			if k<=2010 and k>2005 then
            		local wx = (k-2005)*62
			v.模型.小动画:显示(self.x+220+wx-95,self.y+30+1*67)

			if  v.名称.名称 == "飞天舞朝露"  and  not self.图鉴.飞天舞朝露 or  v.名称.名称 == "羽仙歌"  and  not self.图鉴.羽仙歌 or  v.名称.名称 == "炽云缎墨黑"  and  not self.图鉴.炽云缎墨黑 or v.名称.名称 == "从军行"  and  not self.图鉴.从军行 or  v.名称.名称 == "从军行月曜"  and  not self.图鉴.从军行月曜  then
					v.模型.小动画:置颜色(0xFF808080)
					zts3:置颜色(红色)
					zts3:显示(self.x+220+wx-90,self.y+65+1*67,"未激活")
				elseif  v.名称.名称 == "飞天舞朝露"  and   self.图鉴.飞天舞朝露 or  v.名称.名称 == "羽仙歌"  and   self.图鉴.羽仙歌 or  v.名称.名称 == "炽云缎墨黑"  and   self.图鉴.炽云缎墨黑 or v.名称.名称 == "从军行"  and   self.图鉴.从军行 or  v.名称.名称 == "从军行月曜"  and   self.图鉴.从军行月曜  then
					v.模型.小动画:置颜色(白色)
					zts4:置颜色(绿色)
					zts4:显示(self.x+220+wx-90,self.y+65+1*67,"已激活")
				end

			if v.模型.小动画:是否选中(x,y,偏移x,偏移y) then
		    	tp.提示:商城提示(self.x-150+wx,self.y-90+1*67,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
		    	end
			if self.锦衣物品组[k] ~= nil and self.锦衣物品组[k].模型.小动画 ~= nil then
	   				if self.锦衣物品组[k].模型.小动画:是否选中(x,y)  then
	   					self.焦点 = true
	   					if 引擎.鼠标弹起(左键)  then
							self.选中 =  k+self.加入
							self.序号 = v.名称.名称
							if self.服饰id[self.序号] ~= nil  then
								    self.图鉴id=self.服饰id[self.序号]
							end
							if   self.图鉴数值[self.选中]~= nil  then
								self.选中数值=self.图鉴数值[self.选中].数值
								self.选中类型=self.图鉴数值[self.选中].类型
								self.碎片消耗=self.图鉴数值[self.选中].碎片
								self.仙玉消耗=self.图鉴数值[self.选中].仙玉
							end
							self:置锦衣形象()
						end
					end
					if self.选中~=0 then
						if  k+self.加入 ==self.选中  then
						  tp.物品格子确定_:显示(self.x+225+wx-95,self.y+32+1*67)
						end
					end
			end
		end

			if k<=2015 and k>2010 then
            		local wx = (k-2010)*62
			v.模型.小动画:显示(self.x+220+wx-95,self.y+30+2*67)

			if  v.名称.名称 == "绯雪织凝霜"  and  not self.图鉴.绯雪织凝霜 or  v.名称.名称 == "冰寒绡月白"  and  not self.图鉴.冰寒绡月白 or  v.名称.名称 == "蒹葭苍苍寒月"  and  not self.图鉴.蒹葭苍苍寒月 or v.名称.名称 == "浪淘纱"  and  not self.图鉴.浪淘纱 or  v.名称.名称 == "浪淘纱墨黑"  and  not self.图鉴.浪淘纱墨黑  then
					v.模型.小动画:置颜色(0xFF808080)
					zts3:置颜色(红色)
					zts3:显示(self.x+220+wx-90,self.y+65+2*67,"未激活")
				elseif  v.名称.名称 == "绯雪织凝霜"  and   self.图鉴.绯雪织凝霜 or  v.名称.名称 == "冰寒绡月白"  and   self.图鉴.冰寒绡月白 or  v.名称.名称 == "蒹葭苍苍寒月"  and   self.图鉴.蒹葭苍苍寒月 or v.名称.名称 == "浪淘纱"  and   self.图鉴.浪淘纱 or  v.名称.名称 == "浪淘纱墨黑"  and   self.图鉴.浪淘纱墨黑  then
					v.模型.小动画:置颜色(白色)
					zts4:置颜色(绿色)
					zts4:显示(self.x+220+wx-90,self.y+65+2*67,"已激活")
				end

			if v.模型.小动画:是否选中(x,y,偏移x,偏移y) then
		    	tp.提示:商城提示(self.x-150+wx,self.y-90+2*67,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
		    	end
			if self.锦衣物品组[k] ~= nil and self.锦衣物品组[k].模型.小动画 ~= nil then
	   				if self.锦衣物品组[k].模型.小动画:是否选中(x,y)  then
	   					self.焦点 = true
	   					if 引擎.鼠标弹起(左键)  then
							self.选中 =  k+self.加入
							self.序号 = v.名称.名称
							if self.服饰id[self.序号] ~= nil  then
								    self.图鉴id=self.服饰id[self.序号]
							end
							if   self.图鉴数值[self.选中]~= nil  then
								self.选中数值=self.图鉴数值[self.选中].数值
								self.选中类型=self.图鉴数值[self.选中].类型
								self.碎片消耗=self.图鉴数值[self.选中].碎片
								self.仙玉消耗=self.图鉴数值[self.选中].仙玉
							end
							self:置锦衣形象()
						end
					end
					if self.选中~=0 then
						if  k+self.加入 ==self.选中  then
						  tp.物品格子确定_:显示(self.x+225+wx-95,self.y+32+2*67)
						end
					end
			end
		end


			if k<=2020 and k>2015 then
            		local wx = (k-2015)*62
			v.模型.小动画:显示(self.x+220+wx-95,self.y+30+3*67)

			if  v.名称.名称 == "萌萌小厨"  and  not self.图鉴.萌萌小厨 or  v.名称.名称 == "霞姿月韵"  and  not self.图鉴.霞姿月韵 or  v.名称.名称 == "雪眸影夜烬"  and  not self.图鉴.雪眸影夜烬 or v.名称.名称 == "夜影"  and  not self.图鉴.夜影 or  v.名称.名称 == "冰雪玉兔"  and  not self.图鉴.冰雪玉兔  then
					v.模型.小动画:置颜色(0xFF808080)
					zts3:置颜色(红色)
					zts3:显示(self.x+220+wx-90,self.y+65+3*67,"未激活")
				elseif  v.名称.名称 == "萌萌小厨"  and   self.图鉴.萌萌小厨 or  v.名称.名称 == "霞姿月韵"  and   self.图鉴.霞姿月韵 or  v.名称.名称 == "雪眸影夜烬"  and   self.图鉴.雪眸影夜烬 or v.名称.名称 == "夜影"  and   self.图鉴.夜影 or  v.名称.名称 == "冰雪玉兔"  and   self.图鉴.冰雪玉兔  then
					v.模型.小动画:置颜色(白色)
					zts4:置颜色(绿色)
					zts4:显示(self.x+220+wx-90,self.y+65+3*67,"已激活")
				end

			if v.模型.小动画:是否选中(x,y,偏移x,偏移y) then
		    	tp.提示:商城提示(self.x-150+wx,self.y-90+3*67,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
		    	end
			if self.锦衣物品组[k] ~= nil and self.锦衣物品组[k].模型.小动画 ~= nil then
	   				if self.锦衣物品组[k].模型.小动画:是否选中(x,y)  then
	   					self.焦点 = true
	   					if 引擎.鼠标弹起(左键)  then
							self.选中 =  k+self.加入
							self.序号 = v.名称.名称
							if self.服饰id[self.序号] ~= nil  then
								    self.图鉴id=self.服饰id[self.序号]
							end
							if   self.图鉴数值[self.选中]~= nil  then
								self.选中数值=self.图鉴数值[self.选中].数值
								self.选中类型=self.图鉴数值[self.选中].类型
								self.碎片消耗=self.图鉴数值[self.选中].碎片
								self.仙玉消耗=self.图鉴数值[self.选中].仙玉
							end
							self:置锦衣形象()
						end
					end
					if self.选中~=0 then
						if  k+self.加入 ==self.选中  then
						  tp.物品格子确定_:显示(self.x+225+wx-95,self.y+32+3*67)
						end
					end
			end
		end


			if k<=2025 and k>2020 then
		local 锦衣激活 = 0
            		local wx = (k-2020)*62
			v.模型.小动画:显示(self.x+220+wx-95,self.y+30+4*67)

			if  v.名称.名称 == "官服"  and  not self.图鉴.官服 or  v.名称.名称 == "闲云野鹤"  and  not self.图鉴.闲云野鹤 or  v.名称.名称 == "明光宝甲"  and  not self.图鉴.明光宝甲 or v.名称.名称 == "化圣"  and  not self.图鉴.化圣 or  v.名称.名称 == "渡劫"  and  not self.图鉴.渡劫  then
					v.模型.小动画:置颜色(0xFF808080)
					zts3:置颜色(红色)
					zts3:显示(self.x+220+wx-90,self.y+65+4*67,"未激活")
					 锦衣激活 = 0
				elseif  v.名称.名称 == "官服"  and   self.图鉴.官服 or  v.名称.名称 == "闲云野鹤"  and   self.图鉴.闲云野鹤 or  v.名称.名称 == "明光宝甲"  and   self.图鉴.明光宝甲 or v.名称.名称 == "化圣"  and   self.图鉴.化圣 or  v.名称.名称 == "渡劫"  and   self.图鉴.渡劫  then
					v.模型.小动画:置颜色(白色)
					zts4:置颜色(绿色)
					zts4:显示(self.x+220+wx-90,self.y+65+4*67,"已激活")
					 锦衣激活 = 1
				end

			if v.模型.小动画:是否选中(x,y,偏移x,偏移y) then
		    	tp.提示:商城提示(self.x-150+wx,self.y-90+4*67,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
		    	end
			if self.锦衣物品组[k] ~= nil and self.锦衣物品组[k].模型.小动画 ~= nil then
	   				if self.锦衣物品组[k].模型.小动画:是否选中(x,y)  then
	   					self.焦点 = true
	   					if 引擎.鼠标弹起(左键)  then
							self.选中 =  k+self.加入
							self.序号 = v.名称.名称
							if self.服饰id[self.序号] ~= nil  then
								    self.图鉴id=self.服饰id[self.序号]
							end
							if   self.图鉴数值[self.选中]~= nil  then
								self.选中数值=self.图鉴数值[self.选中].数值
								self.选中类型=self.图鉴数值[self.选中].类型
								self.碎片消耗=self.图鉴数值[self.选中].碎片
								self.仙玉消耗=self.图鉴数值[self.选中].仙玉
							end
							self:置锦衣形象()
						end
					end
					if self.选中~=0 then
						if  k+self.加入 ==self.选中  then
						  tp.物品格子确定_:显示(self.x+225+wx-95,self.y+32+4*67)
						end
					end
			end
		end


		end
		-- if   self.选中~=0 and 置灰 == true    then
		-- 	self.资源组[31]:更新(x,y, 锦衣bb ~= nil and #锦衣bb>1000)
		-- else
		-- 	self.资源组[31]:更新(x,y,锦衣bb ~= nil )
		-- end



		zts2:显示(self.x+15,self.y+250,"激活增加角色属性")
		if self.选中类型~=nil and self.选中类型=="最大气血" then
			self.选中类型="气血"
		end
		zts2:显示(self.x+55,self.y+295,self.选中数值.."点"..self.选中类型)

		zts3:置颜色(绿色)
		zts3:显示(self.x+60,self.y+230,"["..self.序号.."]")
		zts4:置颜色(黄色)
		zts4:显示(self.x+5,self.y+335,"锦衣碎片"..self.碎片消耗.."个+"..self.仙玉消耗.."仙玉激活")


		self.资源组[31]:更新(x,y,锦衣bb ~= nil  and self.图鉴[self.序号] ==true )
      		self.资源组[32]:更新(x,y,锦衣bb ~= nil  and tp.队伍[1].锦衣[1] ~=nil   )
      		self.资源组[30]:显示(self.x+55,self.y+353,true)--激活
      		self.资源组[31]:显示(self.x+10,self.y+203,true)--激活
      		self.资源组[32]:显示(self.x+95,self.y+203,true)--激活

		if 锦衣bb ~= nil then
		local jx = self.x + 80
		local jy = self.y + 145--+25
		tp.影子:显示(jx,jy)
		if self.资源组[25] ~= nil then
			self.资源组[25]:更新(dt)
			self.资源组[25]:显示(jx,jy)
		end
		end


		-- for z=1,10 do
		-- if self.选中~=0 and tp.队伍[1].锦衣[1]~=nil  then
		-- 	self.资源组[31]:置文字("  卸 下")
		-- else
		-- 	self.资源组[31]:置文字("  穿 戴")
		-- end
		-- end


		if self.资源组[30]:事件判断() then
			发送数据(7001,{序列=self.选中,激活id=self.选中,名称=self.序号,文本="锦衣",佩戴文本="锦衣",图鉴id=self.图鉴id})
		 elseif self.资源组[31]:事件判断() then --骑乘
		 	发送数据(7002,{序列=self.选中,名称=self.序号})
			-- if self.资源组[31]:取文字()=="  穿 戴" then
			-- 	发送数据(7002,{序列=self.选中,名称=self.序号})
			-- elseif self.资源组[31]:取文字()=="  卸 下" then
			-- 	发送数据(7003,{序列=self.选中,名称=self.序号})
			--     --发送数据(27,{序列=0})
			-- end
		elseif self.资源组[32]:事件判断() then --骑乘
		 	发送数据(7003,{序列=self.选中,名称=self.序号})
		end



      elseif self.进程 == 3 then       -- 足迹

      	if 足迹wp==nil then
		足迹wp={}
		self.选中=0
	end
	local bbs = 足迹wp
	local bbsa = #bbs
	local 足迹bb
	--local 置灰
	local 灰色 = true
	if self.选中 ~= 0 then
		足迹bb = bbs[self.选中]
	end


      		self.资源组[28]:更新(x,y)
      		self.资源组[29]:更新(x,y)
      		self.资源组[31]:更新(x,y,足迹bb ~= nil  and self.图鉴[self.序号] ==true )
      		self.资源组[32]:更新(x,y,足迹bb ~= nil  and tp.队伍[1].锦衣[4] ~=nil   )
      		self.资源组[31]:显示(self.x+10,self.y+203,true)--激活
      		self.资源组[32]:显示(self.x+95,self.y+203,true)--激活
      		self.资源组[30]:显示(self.x+55,self.y+353,true)--激活
      		self.资源组[28]:显示(self.x+168,self.y+25,nil,nil,nil,self.页面 == 1,2)--第一页
      		self.资源组[29]:显示(self.x+168,self.y+55,nil,nil,nil,self.页面 == 2,2)--第一页


	if self.页面==1 then
		for k,v in pairs(self.足迹物品组) do
		    --if k<=5 then
		    if k<=4005 and k>4000 then
	            	local wx = (k-4000)*62
				v.模型.小动画:显示(self.x+220+wx-95,self.y+30)
				if  v.名称.名称 == "心享气球"  and  not self.图鉴.心享气球 or  v.名称.名称 == "皮球足迹"  and  not self.图鉴.皮球足迹 or  v.名称.名称 == "龙卷风足迹"  and  not self.图鉴.龙卷风足迹 or v.名称.名称 == "星光"  and  not self.图鉴.星光 or  v.名称.名称 == "小心机"  and  not self.图鉴.小心机  then
					v.模型.小动画:置颜色(0xFF808080)
					zts3:置颜色(红色)
					zts3:显示(self.x+220+wx-90,self.y+65,"未激活")
				elseif  v.名称.名称 == "心享气球"  and   self.图鉴.心享气球 or  v.名称.名称 == "皮球足迹"  and   self.图鉴.皮球足迹 or  v.名称.名称 == "龙卷风足迹"  and   self.图鉴.龙卷风足迹 or v.名称.名称 == "星光"  and   self.图鉴.星光 or  v.名称.名称 == "小心机"  and   self.图鉴.小心机  then
					v.模型.小动画:置颜色(白色)
					zts4:置颜色(绿色)
					zts4:显示(self.x+220+wx-90,self.y+65,"已激活")
				end
				if v.模型.小动画:是否选中(x,y,偏移x,偏移y) then
			    		tp.提示:商城提示(self.x-150+wx,self.y-90,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
			    	end

				if self.足迹物品组[k] ~= nil and self.足迹物品组[k].模型.小动画 ~= nil then
					--print(self.选中)
						if  self.选中 <4000    then
						self.选中 =  4001+self.加入
						self.序号 = "心享气球"
						tp.物品格子确定_:显示(self.x+225+62-95,self.y+32)
							if   self.图鉴数值[self.选中]~= nil  then
								self.选中数值=self.图鉴数值[self.选中].数值
								self.选中类型=self.图鉴数值[self.选中].类型
								self.碎片消耗=self.图鉴数值[self.选中].碎片
								self.仙玉消耗=self.图鉴数值[self.选中].仙玉
							end
						self:置足迹形象()

						else


		   				if self.足迹物品组[k].模型.小动画:是否选中(x,y)  then
		   					self.焦点 = true
		   					if 引擎.鼠标弹起(左键)  then
								self.选中 =  k+self.加入
								print(k)
								self.序号 = v.名称.名称
								self:置足迹形象()
							end
						end
						if self.选中~=0 then
							if  k+self.加入 ==self.选中  then
							  tp.物品格子确定_:显示(self.x+225+wx-95,self.y+32)

							end
						end
					end
				end
			end

			if k<=4010 and k>4005 then
            		local wx = (k-4005)*62
			v.模型.小动画:显示(self.x+220+wx-95,self.y+30+1*67)
			if  v.名称.名称 == "浮游水母"  and  not self.图鉴.浮游水母 or  v.名称.名称 == "闪光足迹"  and  not self.图鉴.闪光足迹 or  v.名称.名称 == "跃动喷泉"  and  not self.图鉴.跃动喷泉 or v.名称.名称 == "彩蝶飞"  and  not self.图鉴.彩蝶飞 or  v.名称.名称 == "锦鲤游"  and  not self.图鉴.锦鲤游  then
					v.模型.小动画:置颜色(0xFF808080)
					zts3:置颜色(红色)
					zts3:显示(self.x+220+wx-90,self.y+65+1*67,"未激活")
				elseif  v.名称.名称 == "浮游水母"  and   self.图鉴.浮游水母 or  v.名称.名称 == "闪光足迹"  and   self.图鉴.闪光足迹 or  v.名称.名称 == "跃动喷泉"  and   self.图鉴.跃动喷泉 or v.名称.名称 == "彩蝶飞"  and   self.图鉴.彩蝶飞 or  v.名称.名称 == "锦鲤游"  and   self.图鉴.锦鲤游  then
					v.模型.小动画:置颜色(白色)
					zts4:置颜色(绿色)
					zts4:显示(self.x+220+wx-90,self.y+65+1*67,"已激活")
				end
			if v.模型.小动画:是否选中(x,y,偏移x,偏移y) then
		    	tp.提示:商城提示(self.x-150+wx,self.y-90+1*67,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
		    	end
			if self.足迹物品组[k] ~= nil and self.足迹物品组[k].模型.小动画 ~= nil then
		   				if self.足迹物品组[k].模型.小动画:是否选中(x,y)  then
		   					self.焦点 = true
		   					if 引擎.鼠标弹起(左键)  then
								self.选中 =  k+self.加入
								self.序号 = v.名称.名称
								if   self.图鉴数值[self.选中]~= nil  then
								self.选中数值=self.图鉴数值[self.选中].数值
								self.选中类型=self.图鉴数值[self.选中].类型
								self.碎片消耗=self.图鉴数值[self.选中].碎片
								self.仙玉消耗=self.图鉴数值[self.选中].仙玉
							end
								self:置足迹形象()
							end
						end
						if self.选中~=0 then
							if  k+self.加入 ==self.选中  then
							  tp.物品格子确定_:显示(self.x+225+wx-95,self.y+32+1*67)
							end
						end
				end
			end


			if k<=4015 and k>4010 then
            		local wx = (k-4010)*62
			v.模型.小动画:显示(self.x+220+wx-95,self.y+30+2*67)
			if  v.名称.名称 == "雷电足迹"  and  not self.图鉴.雷电足迹 or  v.名称.名称 == "旋律足迹"  and  not self.图鉴.旋律足迹 or  v.名称.名称 == "踩浪花"  and  not self.图鉴.踩浪花 or v.名称.名称 == "飞天足迹"  and  not self.图鉴.飞天足迹 or  v.名称.名称 == "枫叶足迹"  and  not self.图鉴.枫叶足迹  then
					v.模型.小动画:置颜色(0xFF808080)
					zts3:置颜色(红色)
					zts3:显示(self.x+220+wx-90,self.y+65+2*67,"未激活")
				elseif  v.名称.名称 == "雷电足迹"  and   self.图鉴.雷电足迹 or  v.名称.名称 == "旋律足迹"  and   self.图鉴.旋律足迹 or  v.名称.名称 == "踩浪花"  and   self.图鉴.踩浪花 or v.名称.名称 == "飞天足迹"  and   self.图鉴.飞天足迹 or  v.名称.名称 == "枫叶足迹"  and   self.图鉴.枫叶足迹  then
					v.模型.小动画:置颜色(白色)
					zts4:置颜色(绿色)
					zts4:显示(self.x+220+wx-90,self.y+65+2*67,"已激活")
				end
			if v.模型.小动画:是否选中(x,y,偏移x,偏移y) then
		    	tp.提示:商城提示(self.x-150+wx,self.y-90+2*67,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
		    	end
			if self.足迹物品组[k] ~= nil and self.足迹物品组[k].模型.小动画 ~= nil then
		   				if self.足迹物品组[k].模型.小动画:是否选中(x,y)  then
		   					self.焦点 = true
		   					if 引擎.鼠标弹起(左键)  then
								self.选中 =  k+self.加入
								self.序号 = v.名称.名称
								if   self.图鉴数值[self.选中]~= nil  then
								self.选中数值=self.图鉴数值[self.选中].数值
								self.选中类型=self.图鉴数值[self.选中].类型
								self.碎片消耗=self.图鉴数值[self.选中].碎片
								self.仙玉消耗=self.图鉴数值[self.选中].仙玉
							end
								self:置足迹形象()
							end
						end
						if self.选中~=0 then
							if  k+self.加入 ==self.选中  then
							  tp.物品格子确定_:显示(self.x+225+wx-95,self.y+32+2*67)
							end
						end
				end
			end


			if k<=4020 and k>4015 then
            		local wx = (k-4015)*62
			v.模型.小动画:显示(self.x+220+wx-95,self.y+30+3*67)
			if  v.名称.名称 == "随风舞"  and  not self.图鉴.随风舞 or  v.名称.名称 == "鬼脸南瓜"  and  not self.图鉴.鬼脸南瓜 or  v.名称.名称 == "爱的风暴"  and  not self.图鉴.爱的风暴 or v.名称.名称 == "落羽"  and  not self.图鉴.落羽 or  v.名称.名称 == "星如雨"  and  not self.图鉴.星如雨  then
					v.模型.小动画:置颜色(0xFF808080)
					zts3:置颜色(红色)
					zts3:显示(self.x+220+wx-90,self.y+65+3*67,"未激活")
				elseif  v.名称.名称 == "随风舞"  and   self.图鉴.随风舞 or  v.名称.名称 == "鬼脸南瓜"  and   self.图鉴.鬼脸南瓜 or  v.名称.名称 == "爱的风暴"  and   self.图鉴.爱的风暴 or v.名称.名称 == "落羽"  and   self.图鉴.落羽 or  v.名称.名称 == "星如雨"  and   self.图鉴.星如雨  then
					v.模型.小动画:置颜色(白色)
					zts4:置颜色(绿色)
					zts4:显示(self.x+220+wx-90,self.y+65+3*67,"已激活")
				end
			if v.模型.小动画:是否选中(x,y,偏移x,偏移y) then
		    	tp.提示:商城提示(self.x-150+wx,self.y-90+3*67,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
		    	end
			if self.足迹物品组[k] ~= nil and self.足迹物品组[k].模型.小动画 ~= nil then
		   				if self.足迹物品组[k].模型.小动画:是否选中(x,y)  then
		   					self.焦点 = true
		   					if 引擎.鼠标弹起(左键)  then
								self.选中 =  k+self.加入
								self.序号 = v.名称.名称
								if   self.图鉴数值[self.选中]~= nil  then
								self.选中数值=self.图鉴数值[self.选中].数值
								self.选中类型=self.图鉴数值[self.选中].类型
								self.碎片消耗=self.图鉴数值[self.选中].碎片
								self.仙玉消耗=self.图鉴数值[self.选中].仙玉
							end
								self:置足迹形象()
							end
						end
						if self.选中~=0 then
							if  k+self.加入 ==self.选中  then
							  tp.物品格子确定_:显示(self.x+225+wx-95,self.y+32+3*67)
							end
						end
				end
			end


			if k<=4025 and k>4020 then
            		local wx = (k-4020)*62
			v.模型.小动画:显示(self.x+220+wx-95,self.y+30+4*67)
			if  v.名称.名称 == "璀璨足迹"  and  not self.图鉴.璀璨足迹 or  v.名称.名称 == "璀璨烟花"  and  not self.图鉴.璀璨烟花 or  v.名称.名称 == "蝴蝶足迹"  and  not self.图鉴.蝴蝶足迹 or v.名称.名称 == "星光泡泡"  and  not self.图鉴.星光泡泡 or  v.名称.名称 == "鱼群足迹"  and  not self.图鉴.鱼群足迹  then
					v.模型.小动画:置颜色(0xFF808080)
					zts3:置颜色(红色)
					zts3:显示(self.x+220+wx-90,self.y+65+4*67,"未激活")
				elseif  v.名称.名称 == "璀璨足迹"  and   self.图鉴.璀璨足迹 or  v.名称.名称 == "璀璨烟花"  and   self.图鉴.璀璨烟花 or  v.名称.名称 == "蝴蝶足迹"  and   self.图鉴.蝴蝶足迹 or v.名称.名称 == "星光泡泡"  and   self.图鉴.星光泡泡 or  v.名称.名称 == "鱼群足迹"  and   self.图鉴.鱼群足迹  then
					v.模型.小动画:置颜色(白色)
					zts4:置颜色(绿色)
					zts4:显示(self.x+220+wx-90,self.y+65+4*67,"已激活")
				end
			if v.模型.小动画:是否选中(x,y,偏移x,偏移y) then
		    	tp.提示:商城提示(self.x-150+wx,self.y-90+4*67,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
		    	end
			if self.足迹物品组[k] ~= nil and self.足迹物品组[k].模型.小动画 ~= nil then
		   				if self.足迹物品组[k].模型.小动画:是否选中(x,y)  then
		   					self.焦点 = true
		   					if 引擎.鼠标弹起(左键)  then
								self.选中 =  k+self.加入
								self.序号 = v.名称.名称
								if   self.图鉴数值[self.选中]~= nil  then
								self.选中数值=self.图鉴数值[self.选中].数值
								self.选中类型=self.图鉴数值[self.选中].类型
								self.碎片消耗=self.图鉴数值[self.选中].碎片
								self.仙玉消耗=self.图鉴数值[self.选中].仙玉
							end
								self:置足迹形象()
							end
						end
						if self.选中~=0 then
							if  k+self.加入 ==self.选中  then
							  tp.物品格子确定_:显示(self.x+225+wx-95,self.y+32+4*67)
							end
						end
				end
			end

		end

	 elseif self.页面==2 then
		for k,v in pairs(self.足迹物品组) do
		    if k<=4030 and k>4025 then
	            	local wx =  (k-4025)*62
	            		if  v.名称.名称 =="甜蜜糖果" then
	            			v.模型.小动画:显示(self.x+220+wx-95+24,self.y+30+27)
	            			--self.物品.小模型:显示(self.x+24,self.y+27)
	            		else
				v.模型.小动画:显示(self.x+220+wx-95,self.y+30)
				end
				if  v.名称.名称 == "甜蜜糖果"  and  not self.图鉴.甜蜜糖果  then
					v.模型.小动画:置颜色(0xFF808080)
					zts3:置颜色(红色)
					zts3:显示(self.x+220+wx-90,self.y+65,"未激活")
				elseif  v.名称.名称 == "甜蜜糖果"  and   self.图鉴.甜蜜糖果   then
					v.模型.小动画:置颜色(白色)
					zts4:置颜色(绿色)
					zts4:显示(self.x+220+wx-90,self.y+65,"已激活")
				end
				if v.模型.小动画:是否选中(x,y,偏移x,偏移y) then
			    		tp.提示:商城提示(self.x-150+wx,self.y-90,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
			    	end
				if self.足迹物品组[k] ~= nil and self.足迹物品组[k].模型.小动画 ~= nil then
		   				if self.足迹物品组[k].模型.小动画:是否选中(x,y)  then
		   					self.焦点 = true
		   					if 引擎.鼠标弹起(左键)  then
								self.选中 =  k+self.加入
								self.序号 = v.名称.名称
								if   self.图鉴数值[self.选中]~= nil  then
								self.选中数值=self.图鉴数值[self.选中].数值
								self.选中类型=self.图鉴数值[self.选中].类型
								self.碎片消耗=self.图鉴数值[self.选中].碎片
								self.仙玉消耗=self.图鉴数值[self.选中].仙玉
							end
								self:置足迹形象()
							end
						end
						if self.选中~=0 then
							if  k+self.加入 ==self.选中  then
							  tp.物品格子确定_:显示(self.x+225+wx-95,self.y+32)

							end
						end
				end
			end
		end
	end

		zts2:显示(self.x+15,self.y+250,"激活增加角色属性")
		if self.选中类型~=nil and self.选中类型=="最大气血" then
			self.选中类型="气血"
		end
		zts2:显示(self.x+55,self.y+295,self.选中数值.."点"..self.选中类型)

		zts3:置颜色(绿色)
		zts3:显示(self.x+60,self.y+230,"["..self.序号.."]")
		zts4:置颜色(黄色)
		zts4:显示(self.x+5,self.y+335,"足迹碎片"..self.碎片消耗.."个+"..self.仙玉消耗.."仙玉激活")



		local jx = self.x + 80+35
		local jy = self.y + 145+15
		--tp.影子:显示(jx,jy)
		if self.资源组[27] ~= nil then
			self.资源组[27]:更新(dt)
			self.资源组[27]:显示(jx,jy)
		end

		if self.资源组[30]:事件判断() then
			发送数据(7001,{序列=self.选中,激活id=self.选中,名称=self.序号,文本="锦衣",佩戴文本="足迹",图鉴id=self.图鉴id})
		elseif self.资源组[28]:事件判断() then
			self.页面=1
		elseif self.资源组[29]:事件判断() then
			self.页面=2
		 elseif self.资源组[31]:事件判断() then --骑乘
		 	发送数据(7002,{序列=self.选中,名称=self.序号})
		elseif self.资源组[32]:事件判断() then --骑乘
		 	发送数据(7003,{序列=self.选中,名称=self.序号})
		end


      elseif self.进程 == 4 then         --光环
      	if 光环wp==nil then
		光环wp={}
		self.选中=0
	end
	local bbs = 光环wp
	local bbsa = #bbs
	local 光环bb
	--local 置灰
	local 灰色 = true
	if self.选中 ~= 0 then
		光环bb = bbs[self.选中]
	end



      		self.资源组[28]:更新(x,y)
      		self.资源组[29]:更新(x,y)
      		self.资源组[31]:更新(x,y,光环bb ~= nil  and self.图鉴[self.序号] ==true )
      		self.资源组[32]:更新(x,y,光环bb ~= nil  and tp.队伍[1].锦衣[3] ~=nil   )
      		self.资源组[31]:显示(self.x+10,self.y+203,true)--激活
      		self.资源组[32]:显示(self.x+95,self.y+203,true)--激活
      		self.资源组[28]:显示(self.x+168,self.y+25,nil,nil,nil,self.页面 == 1,2)--第一页
      		self.资源组[29]:显示(self.x+168,self.y+55,nil,nil,nil,self.页面 == 2,2)--第一页
      		self.资源组[30]:显示(self.x+55,self.y+353)--激活


	if self.页面==1 then
		for k,v in pairs(self.光环物品组) do
		    --if k<=5 then
		    if k<=3005 and k>3000 then
	            	local wx = (k-3000)*62
	            	local 找到 = 0
				v.模型.小动画:显示(self.x+220+wx-95,self.y+30)
				if  v.名称.名称 == "雪花飘落"  and  not self.图鉴.雪花飘落 or  v.名称.名称 == "星光熠熠"  and  not self.图鉴.星光熠熠 or  v.名称.名称 == "祥云瑞气"  and  not self.图鉴.祥云瑞气 or v.名称.名称 == "荷塘涟漪"  and  not self.图鉴.荷塘涟漪 or  v.名称.名称 == "浩瀚星河"  and  not self.图鉴.浩瀚星河  then
					v.模型.小动画:置颜色(0xFF808080)
					zts3:置颜色(红色)
					zts3:显示(self.x+220+wx-90,self.y+65,"未激活")
				elseif  v.名称.名称 == "雪花飘落"  and   self.图鉴.雪花飘落 or  v.名称.名称 == "星光熠熠"  and   self.图鉴.星光熠熠 or  v.名称.名称 == "祥云瑞气"  and   self.图鉴.祥云瑞气 or v.名称.名称 == "荷塘涟漪"  and   self.图鉴.荷塘涟漪 or  v.名称.名称 == "浩瀚星河"  and   self.图鉴.浩瀚星河  then
					v.模型.小动画:置颜色(白色)
					zts4:置颜色(绿色)
					zts4:显示(self.x+220+wx-90,self.y+65,"已激活")
				end
				if v.模型.小动画:是否选中(x,y,偏移x,偏移y) then
			    		tp.提示:商城提示(self.x-150+wx,self.y-90,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
			    	end
				if self.光环物品组[k] ~= nil and self.光环物品组[k].模型.小动画 ~= nil then
						if  self.选中 <3000  or  self.选中 > 4000   then
							self.选中 =  3001+self.加入
							self.序号 = "雪花飘落"
							tp.物品格子确定_:显示(self.x+225+62-95,self.y+32)
							if   self.图鉴数值[self.选中]~= nil  then
								self.选中数值=self.图鉴数值[self.选中].数值
								self.选中类型=self.图鉴数值[self.选中].类型
								self.碎片消耗=self.图鉴数值[self.选中].碎片
								self.仙玉消耗=self.图鉴数值[self.选中].仙玉
							end
						self:置光环形象()
					else

		   				if self.光环物品组[k].模型.小动画:是否选中(x,y)  then
		   					self.焦点 = true
		   					if 引擎.鼠标弹起(左键)  then
								self.选中 =  k+self.加入
								self.序号 = v.名称.名称
								self:置光环形象()
							end
						end
						if self.选中~=0 then
							if  k+self.加入 ==self.选中  then
							  tp.物品格子确定_:显示(self.x+225+wx-95,self.y+32)

							end
						end
					end
				end

			end

			if k<=3010 and k>3005 then
            		local wx = (k-3005)*62
	            	local 找到 = 0
				v.模型.小动画:显示(self.x+220+wx-95,self.y+30+1*67)
				if  v.名称.名称 == "凌波微步"  and  not self.图鉴.凌波微步 or  v.名称.名称 == "珠落玉盘"  and  not self.图鉴.珠落玉盘 or  v.名称.名称 == "金沙海滩"  and  not self.图鉴.金沙海滩 or v.名称.名称 == "双鲤寄情"  and  not self.图鉴.双鲤寄情 or  v.名称.名称 == "花的海洋"  and  not self.图鉴.花的海洋  then
					v.模型.小动画:置颜色(0xFF808080)
					zts3:置颜色(红色)
					zts3:显示(self.x+220+wx-90,self.y+65+1*67,"未激活")
				elseif  v.名称.名称 == "凌波微步"  and   self.图鉴.凌波微步 or  v.名称.名称 == "珠落玉盘"  and   self.图鉴.珠落玉盘 or  v.名称.名称 == "金沙海滩"  and   self.图鉴.金沙海滩 or v.名称.名称 == "双鲤寄情"  and   self.图鉴.双鲤寄情 or  v.名称.名称 == "花的海洋"  and   self.图鉴.花的海洋  then
					v.模型.小动画:置颜色(白色)
					zts4:置颜色(绿色)
					zts4:显示(self.x+220+wx-90,self.y+65+1*67,"已激活")
				end
				if v.模型.小动画:是否选中(x,y,偏移x,偏移y) then
			    		tp.提示:商城提示(self.x-150+wx,self.y-90+1*67,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
			    	end
				if self.光环物品组[k] ~= nil and self.光环物品组[k].模型.小动画 ~= nil then
		   				if self.光环物品组[k].模型.小动画:是否选中(x,y)  then
		   					self.焦点 = true
		   					if 引擎.鼠标弹起(左键)  then
								self.选中 =  k+self.加入
								self.序号 = v.名称.名称
								if   self.图鉴数值[self.选中]~= nil  then
								self.选中数值=self.图鉴数值[self.选中].数值
								self.选中类型=self.图鉴数值[self.选中].类型
								self.碎片消耗=self.图鉴数值[self.选中].碎片
								self.仙玉消耗=self.图鉴数值[self.选中].仙玉
							end
								self:置光环形象()
							end
						end
						if self.选中~=0 then
							if  k+self.加入 ==self.选中  then
							  tp.物品格子确定_:显示(self.x+225+wx-95,self.y+32+1*67)

							end
						end
				end

			end


			if k<=3015 and k>3010 then
            		local wx = (k-3010)*62
	            	local 找到 = 0
				v.模型.小动画:显示(self.x+220+wx-95,self.y+30+2*67)
				if  v.名称.名称 == "红叶随风"  and  not self.图鉴.红叶随风 or  v.名称.名称 == "水墨游龙"  and  not self.图鉴.水墨游龙 or  v.名称.名称 == "烈焰澜翻"  and  not self.图鉴.烈焰澜翻 or v.名称.名称 == "桃花飞舞"  and  not self.图鉴.桃花飞舞 or  v.名称.名称 == "月影婆娑"  and  not self.图鉴.月影婆娑  then
					v.模型.小动画:置颜色(0xFF808080)
					zts3:置颜色(红色)
					zts3:显示(self.x+220+wx-90,self.y+65+2*67,"未激活")
				elseif  v.名称.名称 == "红叶随风"  and   self.图鉴.红叶随风 or  v.名称.名称 == "水墨游龙"  and   self.图鉴.水墨游龙 or  v.名称.名称 == "烈焰澜翻"  and   self.图鉴.烈焰澜翻 or v.名称.名称 == "桃花飞舞"  and   self.图鉴.桃花飞舞 or  v.名称.名称 == "月影婆娑"  and   self.图鉴.月影婆娑  then
					v.模型.小动画:置颜色(白色)
					zts4:置颜色(绿色)
					zts4:显示(self.x+220+wx-90,self.y+65+2*67,"已激活")
				end
				if v.模型.小动画:是否选中(x,y,偏移x,偏移y) then
			    		tp.提示:商城提示(self.x-150+wx,self.y-90+2*67,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
			    	end
				if self.光环物品组[k] ~= nil and self.光环物品组[k].模型.小动画 ~= nil then
		   				if self.光环物品组[k].模型.小动画:是否选中(x,y)  then
		   					self.焦点 = true
		   					if 引擎.鼠标弹起(左键)  then
								self.选中 =  k+self.加入
								self.序号 = v.名称.名称
								if   self.图鉴数值[self.选中]~= nil  then
								self.选中数值=self.图鉴数值[self.选中].数值
								self.选中类型=self.图鉴数值[self.选中].类型
								self.碎片消耗=self.图鉴数值[self.选中].碎片
								self.仙玉消耗=self.图鉴数值[self.选中].仙玉
							end
								self:置光环形象()
							end
						end
						if self.选中~=0 then
							if  k+self.加入 ==self.选中  then
							  tp.物品格子确定_:显示(self.x+225+wx-95,self.y+32+2*67)

							end
						end
				end

			end


			if k<=3020 and k>3015 then
            		local wx = (k-3015)*62
	            	local 找到 = 0
				v.模型.小动画:显示(self.x+220+wx-95,self.y+30+3*67)
				if  v.名称.名称 == "九尾庞庞"  and  not self.图鉴.九尾庞庞 or  v.名称.名称 == "财源滚滚"  and  not self.图鉴.财源滚滚 or  v.名称.名称 == "寒霜冰凌"  and  not self.图鉴.寒霜冰凌 or v.名称.名称 == "金枝春绽"  and  not self.图鉴.金枝春绽 or  v.名称.名称 == "九霄云图"  and  not self.图鉴.九霄云图  then
					v.模型.小动画:置颜色(0xFF808080)
					zts3:置颜色(红色)
					zts3:显示(self.x+220+wx-90,self.y+65+3*67,"未激活")
				elseif  v.名称.名称 == "九尾庞庞"  and   self.图鉴.九尾庞庞 or  v.名称.名称 == "财源滚滚"  and   self.图鉴.财源滚滚 or  v.名称.名称 == "寒霜冰凌"  and   self.图鉴.寒霜冰凌 or v.名称.名称 == "金枝春绽"  and   self.图鉴.金枝春绽 or  v.名称.名称 == "九霄云图"  and   self.图鉴.九霄云图  then
					v.模型.小动画:置颜色(白色)
					zts4:置颜色(绿色)
					zts4:显示(self.x+220+wx-90,self.y+65+3*67,"已激活")
				end
				if v.模型.小动画:是否选中(x,y,偏移x,偏移y) then
			    		tp.提示:商城提示(self.x-150+wx,self.y-90+3*67,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
			    	end
				if self.光环物品组[k] ~= nil and self.光环物品组[k].模型.小动画 ~= nil then
		   				if self.光环物品组[k].模型.小动画:是否选中(x,y)  then
		   					self.焦点 = true
		   					if 引擎.鼠标弹起(左键)  then
								self.选中 =  k+self.加入
								self.序号 = v.名称.名称
								if   self.图鉴数值[self.选中]~= nil  then
								self.选中数值=self.图鉴数值[self.选中].数值
								self.选中类型=self.图鉴数值[self.选中].类型
								self.碎片消耗=self.图鉴数值[self.选中].碎片
								self.仙玉消耗=self.图鉴数值[self.选中].仙玉
							end
								self:置光环形象()
							end
						end
						if self.选中~=0 then
							if  k+self.加入 ==self.选中  then
							  tp.物品格子确定_:显示(self.x+225+wx-95,self.y+32+3*67)

							end
						end
				end

			end


			if k<=3025 and k>3020 then
            		local wx = (k-3020)*62
	            	local 找到 = 0
				v.模型.小动画:显示(self.x+220+wx-95,self.y+30+4*67)
				if  v.名称.名称 == "浪淘沙墨黑"  and  not self.图鉴.浪淘沙墨黑 or  v.名称.名称 == "浪淘沙月白"  and  not self.图鉴.浪淘沙月白 or  v.名称.名称 == "凌霄天宫墨黑"  and  not self.图鉴.凌霄天宫墨黑 or v.名称.名称 == "凌霄天宫"  and  not self.图鉴.凌霄天宫 or  v.名称.名称 == "凌霄天宫月白"  and  not self.图鉴.凌霄天宫月白  then
					v.模型.小动画:置颜色(0xFF808080)
					zts3:置颜色(红色)
					zts3:显示(self.x+220+wx-90,self.y+65+4*67,"未激活")
				elseif  v.名称.名称 == "浪淘沙墨黑"  and   self.图鉴.浪淘沙墨黑 or  v.名称.名称 == "浪淘沙月白"  and   self.图鉴.浪淘沙月白 or  v.名称.名称 == "凌霄天宫墨黑"  and   self.图鉴.凌霄天宫墨黑 or v.名称.名称 == "凌霄天宫"  and   self.图鉴.凌霄天宫 or  v.名称.名称 == "凌霄天宫月白"  and   self.图鉴.凌霄天宫月白  then
					v.模型.小动画:置颜色(白色)
					zts4:置颜色(绿色)
					zts4:显示(self.x+220+wx-90,self.y+65+4*67,"已激活")
				end
				if v.模型.小动画:是否选中(x,y,偏移x,偏移y) then
			    		tp.提示:商城提示(self.x-150+wx,self.y-90+4*67,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
			    	end
				if self.光环物品组[k] ~= nil and self.光环物品组[k].模型.小动画 ~= nil then
		   				if self.光环物品组[k].模型.小动画:是否选中(x,y)  then
		   					self.焦点 = true
		   					if 引擎.鼠标弹起(左键)  then
								self.选中 =  k+self.加入
								self.序号 = v.名称.名称
								if   self.图鉴数值[self.选中]~= nil  then
								self.选中数值=self.图鉴数值[self.选中].数值
								self.选中类型=self.图鉴数值[self.选中].类型
								self.碎片消耗=self.图鉴数值[self.选中].碎片
								self.仙玉消耗=self.图鉴数值[self.选中].仙玉
							end
								self:置光环形象()
							end
						end
						if self.选中~=0 then
							if  k+self.加入 ==self.选中  then
							  tp.物品格子确定_:显示(self.x+225+wx-95,self.y+32+4*67)

							end
						end
				end

			end

		end
	elseif self.页面==2 then
	     for k,v in pairs(self.光环物品组) do
		if k<=3030 and k>3025 then
	            	local wx =  (k-3025)*62
	            	local 找到 = 0
				v.模型.小动画:显示(self.x+220+wx-95,self.y+30)
				if  v.名称.名称 == "流云蝶影"  and  not self.图鉴.流云蝶影 or  v.名称.名称 == "琪草瑶花"  and  not self.图鉴.琪草瑶花 or  v.名称.名称 == "琪花瑶草"  and  not self.图鉴.琪花瑶草 or v.名称.名称 == "沙骨王座"  and  not self.图鉴.沙骨王座 or  v.名称.名称 == "午夜魔蝠"  and  not self.图鉴.午夜魔蝠  then
					v.模型.小动画:置颜色(0xFF808080)
					zts3:置颜色(红色)
					zts3:显示(self.x+220+wx-90,self.y+65,"未激活")
				elseif  v.名称.名称 == "流云蝶影"  and   self.图鉴.流云蝶影 or  v.名称.名称 == "琪草瑶花"  and   self.图鉴.琪草瑶花 or  v.名称.名称 == "琪花瑶草"  and   self.图鉴.琪花瑶草 or v.名称.名称 == "沙骨王座"  and   self.图鉴.沙骨王座 or  v.名称.名称 == "午夜魔蝠"  and   self.图鉴.午夜魔蝠  then
					v.模型.小动画:置颜色(白色)
					zts4:置颜色(绿色)
					zts4:显示(self.x+220+wx-90,self.y+65,"已激活")
				end
				if v.模型.小动画:是否选中(x,y,偏移x,偏移y) then
			    		tp.提示:商城提示(self.x-150+wx,self.y-90,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
			    	end
				if self.光环物品组[k] ~= nil and self.光环物品组[k].模型.小动画 ~= nil then
		   				if self.光环物品组[k].模型.小动画:是否选中(x,y)  then
		   					self.焦点 = true
		   					if 引擎.鼠标弹起(左键)  then
								self.选中 =  k+self.加入
								self.序号 = v.名称.名称
								if   self.图鉴数值[self.选中]~= nil  then
								self.选中数值=self.图鉴数值[self.选中].数值
								self.选中类型=self.图鉴数值[self.选中].类型
								self.碎片消耗=self.图鉴数值[self.选中].碎片
								self.仙玉消耗=self.图鉴数值[self.选中].仙玉
							end
								self:置光环形象()
							end
						end
						if self.选中~=0 then
							if  k+self.加入 ==self.选中  then
							  tp.物品格子确定_:显示(self.x+225+wx-95,self.y+32)

							end
						end
				end

			end


			if k<=3035 and k>3030 then
	            	local wx =  (k-3030)*62
	            	local 找到 = 0
				v.模型.小动画:显示(self.x+220+wx-95,self.y+30+1*67)
				if  v.名称.名称 == "星驰剑阵染"  and  not self.图鉴.星驰剑阵染 or  v.名称.名称 == "星驰剑阵"  and  not self.图鉴.星驰剑阵 or  v.名称.名称 == "月半潮墨黑"  and  not self.图鉴.月半潮墨黑 or v.名称.名称 == "月半潮"  and  not self.图鉴.月半潮 or  v.名称.名称 == "月半潮月白"  and  not self.图鉴.月半潮月白  then
					v.模型.小动画:置颜色(0xFF808080)
					zts3:置颜色(红色)
					zts3:显示(self.x+220+wx-90,self.y+65+1*67,"未激活")
				elseif  v.名称.名称 == "星驰剑阵染"  and   self.图鉴.星驰剑阵染 or  v.名称.名称 == "星驰剑阵"  and   self.图鉴.星驰剑阵 or  v.名称.名称 == "月半潮墨黑"  and   self.图鉴.月半潮墨黑 or v.名称.名称 == "月半潮"  and   self.图鉴.月半潮 or  v.名称.名称 == "月半潮月白"  and   self.图鉴.月半潮月白  then
					v.模型.小动画:置颜色(白色)
					zts4:置颜色(绿色)
					zts4:显示(self.x+220+wx-90,self.y+65+1*67,"已激活")
				end
				if v.模型.小动画:是否选中(x,y,偏移x,偏移y) then
			    		tp.提示:商城提示(self.x-150+wx,self.y-90+1*67,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
			    	end
				if self.光环物品组[k] ~= nil and self.光环物品组[k].模型.小动画 ~= nil then
		   				if self.光环物品组[k].模型.小动画:是否选中(x,y)  then
		   					self.焦点 = true
		   					if 引擎.鼠标弹起(左键)  then
								self.选中 =  k+self.加入
								self.序号 = v.名称.名称
								if   self.图鉴数值[self.选中]~= nil  then
								self.选中数值=self.图鉴数值[self.选中].数值
								self.选中类型=self.图鉴数值[self.选中].类型
								self.碎片消耗=self.图鉴数值[self.选中].碎片
								self.仙玉消耗=self.图鉴数值[self.选中].仙玉
							end
								self:置光环形象()
							end
						end
						if self.选中~=0 then
							if  k+self.加入 ==self.选中  then
							  tp.物品格子确定_:显示(self.x+225+wx-95,self.y+32+1*67)

							end
						end
				end

			end

			if k<=3040 and k>3035 then
	            	local wx =  (k-3035)*62
	            	local 找到 = 0
				v.模型.小动画:显示(self.x+220+wx-95,self.y+30+2*67)
				if  v.名称.名称 == "云龙梦墨黑"  and  not self.图鉴.云龙梦墨黑 or  v.名称.名称 == "云龙梦"  and  not self.图鉴.云龙梦 or  v.名称.名称 == "云龙梦月白"  and  not self.图鉴.云龙梦月白  then
					v.模型.小动画:置颜色(0xFF808080)
					zts3:置颜色(红色)
					zts3:显示(self.x+220+wx-90,self.y+65+2*67,"未激活")
				elseif  v.名称.名称 == "云龙梦墨黑"  and   self.图鉴.云龙梦墨黑 or  v.名称.名称 == "云龙梦"  and   self.图鉴.云龙梦 or  v.名称.名称 == "云龙梦月白"  and   self.图鉴.云龙梦月白  then
					v.模型.小动画:置颜色(白色)
					zts4:置颜色(绿色)
					zts4:显示(self.x+220+wx-90,self.y+65+2*67,"已激活")
				end
				if v.模型.小动画:是否选中(x,y,偏移x,偏移y) then
			    		tp.提示:商城提示(self.x-150+wx,self.y-90+2*67,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
			    	end
				if self.光环物品组[k] ~= nil and self.光环物品组[k].模型.小动画 ~= nil then
		   				if self.光环物品组[k].模型.小动画:是否选中(x,y)  then
		   					self.焦点 = true
		   					if 引擎.鼠标弹起(左键)  then
								self.选中 =  k+self.加入
								self.序号 = v.名称.名称
								if   self.图鉴数值[self.选中]~= nil  then
								self.选中数值=self.图鉴数值[self.选中].数值
								self.选中类型=self.图鉴数值[self.选中].类型
								self.碎片消耗=self.图鉴数值[self.选中].碎片
								self.仙玉消耗=self.图鉴数值[self.选中].仙玉
							end
								self:置光环形象()
							end
						end
						if self.选中~=0 then
							if  k+self.加入 ==self.选中  then
							  tp.物品格子确定_:显示(self.x+225+wx-95,self.y+32+2*67)

							end
						end
				end

			end


		end
	end

		zts2:显示(self.x+15,self.y+250,"激活增加角色属性")
		if self.选中类型~=nil and self.选中类型=="最大气血" then
			self.选中类型="气血"
		end
		zts2:显示(self.x+55,self.y+295,self.选中数值.."点"..self.选中类型)

		zts3:置颜色(绿色)
		zts3:显示(self.x+60,self.y+230,"["..self.序号.."]")
		zts4:置颜色(黄色)
		zts4:显示(self.x+5,self.y+335,"光环碎片"..self.碎片消耗.."个+"..self.仙玉消耗.."仙玉激活")



		local jx = self.x + 80 +11
		local jy = self.y + 145--+25
		--tp.影子:显示(jx,jy)
		if self.资源组[26] ~= nil then
			self.资源组[26]:更新(dt)
			self.资源组[26]:显示(jx,jy)
		end

		if self.资源组[30]:事件判断() then
			发送数据(7001,{序列=self.选中,激活id=self.选中,名称=self.序号,文本="锦衣",佩戴文本="光环",图鉴id=self.图鉴id})
			--发送数据(190,{序列=self.选中,图鉴名称=self.序号,文本="光环"})
		elseif self.资源组[28]:事件判断() then
			self.页面=1
		elseif self.资源组[29]:事件判断() then
			self.页面=2
		elseif self.资源组[31]:事件判断() then --骑乘
		 	发送数据(7002,{序列=self.选中,名称=self.序号})
		elseif self.资源组[32]:事件判断() then --骑乘
		 	发送数据(7003,{序列=self.选中,名称=self.序号})
		end



	elseif self.进程 == 5 then          --翅膀
      	if 翅膀wp==nil then
		翅膀wp={}
		self.选中=0
	end
	local bbs = 翅膀wp
	local bbsa = #bbs
	local 翅膀bb
	--local 置灰
	local 灰色 = true
	if self.选中 ~= 0 then
		翅膀bb = bbs[self.选中]
	end





	for k,v in pairs(self.翅膀物品组) do
		    --if k<=5 then
		    if k<=5005 and k>5000 then
	            	local wx = (k-5000)*62
	            	local 找到 = 0

				v.模型.小动画:显示(self.x+220+wx-95,self.y+30)
				if  v.名称.名称 == "定制翅膀一"  and  not self.图鉴.定制翅膀一 or  v.名称.名称 == "定制翅膀二"  and  not self.图鉴.定制翅膀二 or  v.名称.名称 == "定制翅膀三"  and  not self.图鉴.定制翅膀三 or v.名称.名称 == "定制翅膀四"  and  not self.图鉴.定制翅膀四 or  v.名称.名称 == "定制翅膀五"  and  not self.图鉴.定制翅膀五  then
					v.模型.小动画:置颜色(0xFF808080)
					zts3:置颜色(红色)
					zts3:显示(self.x+220+wx-90,self.y+65,"未激活")
				elseif  v.名称.名称 == "定制翅膀一"  and   self.图鉴.定制翅膀一 or  v.名称.名称 == "定制翅膀二"  and   self.图鉴.定制翅膀二 or  v.名称.名称 == "定制翅膀三"  and   self.图鉴.定制翅膀三 or v.名称.名称 == "定制翅膀四"  and   self.图鉴.定制翅膀四 or  v.名称.名称 == "定制翅膀五"  and   self.图鉴.定制翅膀五  then
					v.模型.小动画:置颜色(白色)
					zts4:置颜色(绿色)
					zts4:显示(self.x+220+wx-90,self.y+65,"已激活")
				end
				if v.模型.小动画:是否选中(x,y,偏移x,偏移y) then
			    		tp.提示:商城提示(self.x-150+wx,self.y-90,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
			    	end
				if self.翅膀物品组[k] ~= nil and self.翅膀物品组[k].模型.小动画 ~= nil then
						if  self.选中 <5000  or  self.选中 > 6000   then
							self.选中 =  5001+self.加入
							self.序号 = "定制翅膀一"
							tp.物品格子确定_:显示(self.x+225+62-95,self.y+32)
							if   self.图鉴数值[self.选中]~= nil  then
								self.选中数值=self.图鉴数值[self.选中].数值
								self.选中类型=self.图鉴数值[self.选中].类型
								self.碎片消耗=self.图鉴数值[self.选中].碎片
								self.仙玉消耗=self.图鉴数值[self.选中].仙玉

							end
						self:置翅膀形象()
					else

		   				if self.翅膀物品组[k].模型.小动画:是否选中(x,y)  then
		   					self.焦点 = true
		   					if 引擎.鼠标弹起(左键)  then
								self.选中 =  k+self.加入
								self.序号 = v.名称.名称
								if self.服饰id[self.序号] ~= nil  then
								    self.图鉴id=self.服饰id[self.序号]
								end
								if   self.图鉴数值[self.选中]~= nil  then
								self.选中数值=self.图鉴数值[self.选中].数值
								self.选中类型=self.图鉴数值[self.选中].类型
								self.碎片消耗=self.图鉴数值[self.选中].碎片
								self.仙玉消耗=self.图鉴数值[self.选中].仙玉
								end
								self:置翅膀形象()
							end
						end
						if self.选中~=0 then
							if  k+self.加入 ==self.选中  then
							  tp.物品格子确定_:显示(self.x+225+wx-95,self.y+32)

							end
						end
					end
				end
			end

			if k<=5010 and k>5005 then
            		local wx = (k-5005)*62
			v.模型.小动画:显示(self.x+220+wx-95,self.y+30+1*67)

			if  v.名称.名称 == "定制翅膀六"  and  not self.图鉴.定制翅膀六 or  v.名称.名称 == "定制翅膀七"  and  not self.图鉴.定制翅膀七 or  v.名称.名称 == "定制翅膀八"  and  not self.图鉴.定制翅膀八 or v.名称.名称 == "定制翅膀九"  and  not self.图鉴.定制翅膀九 or  v.名称.名称 == "定制翅膀十"  and  not self.图鉴.定制翅膀十  then
					v.模型.小动画:置颜色(0xFF808080)
					zts3:置颜色(红色)
					zts3:显示(self.x+220+wx-90,self.y+65+1*67,"未激活")
				elseif  v.名称.名称 == "定制翅膀六"  and   self.图鉴.定制翅膀六 or  v.名称.名称 == "定制翅膀七"  and   self.图鉴.定制翅膀七 or  v.名称.名称 == "定制翅膀八"  and   self.图鉴.定制翅膀八 or v.名称.名称 == "定制翅膀九"  and   self.图鉴.定制翅膀九 or  v.名称.名称 == "定制翅膀十"  and   self.图鉴.定制翅膀十  then
					v.模型.小动画:置颜色(白色)
					zts4:置颜色(绿色)
					zts4:显示(self.x+220+wx-90,self.y+65+1*67,"已激活")
				end

			if v.模型.小动画:是否选中(x,y,偏移x,偏移y) then
		    	tp.提示:商城提示(self.x-150+wx,self.y-90+1*67,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
		    	end
			if self.翅膀物品组[k] ~= nil and self.翅膀物品组[k].模型.小动画 ~= nil then
	   				if self.翅膀物品组[k].模型.小动画:是否选中(x,y)  then
	   					self.焦点 = true
	   					if 引擎.鼠标弹起(左键)  then
							self.选中 =  k+self.加入
							self.序号 = v.名称.名称
							if self.服饰id[self.序号] ~= nil  then
								    self.图鉴id=self.服饰id[self.序号]
							end
							if   self.图鉴数值[self.选中]~= nil  then
								self.选中数值=self.图鉴数值[self.选中].数值
								self.选中类型=self.图鉴数值[self.选中].类型
								self.碎片消耗=self.图鉴数值[self.选中].碎片
								self.仙玉消耗=self.图鉴数值[self.选中].仙玉
							end
							self:置翅膀形象()
						end
					end
					if self.选中~=0 then
						if  k+self.加入 ==self.选中  then
						  tp.物品格子确定_:显示(self.x+225+wx-95,self.y+32+1*67)
						end
					end
			end
		end

			if k<=5015 and k>5010 then
            		local wx = (k-5010)*62
			v.模型.小动画:显示(self.x+220+wx-95,self.y+30+2*67)

			if  v.名称.名称 == "定制翅膀十一"  and  not self.图鉴.定制翅膀十一 or  v.名称.名称 == "定制翅膀十二"  and  not self.图鉴.定制翅膀十二 or  v.名称.名称 == "定制翅膀十三"  and  not self.图鉴.定制翅膀十三 or v.名称.名称 == "定制翅膀十四"  and  not self.图鉴.定制翅膀十四 or  v.名称.名称 == "定制翅膀十五"  and  not self.图鉴.定制翅膀十五  then
					v.模型.小动画:置颜色(0xFF808080)
					zts3:置颜色(红色)
					zts3:显示(self.x+220+wx-90,self.y+65+2*67,"未激活")
				elseif  v.名称.名称 == "定制翅膀十一"  and   self.图鉴.定制翅膀十一 or  v.名称.名称 == "定制翅膀十二"  and   self.图鉴.定制翅膀十二 or  v.名称.名称 == "定制翅膀十三"  and   self.图鉴.定制翅膀十三 or v.名称.名称 == "定制翅膀十四"  and   self.图鉴.定制翅膀十四 or  v.名称.名称 == "定制翅膀十五"  and   self.图鉴.定制翅膀十五  then
					v.模型.小动画:置颜色(白色)
					zts4:置颜色(绿色)
					zts4:显示(self.x+220+wx-90,self.y+65+2*67,"已激活")
				end

			if v.模型.小动画:是否选中(x,y,偏移x,偏移y) then
		    	tp.提示:商城提示(self.x-150+wx,self.y-90+2*67,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
		    	end
			if self.翅膀物品组[k] ~= nil and self.翅膀物品组[k].模型.小动画 ~= nil then
	   				if self.翅膀物品组[k].模型.小动画:是否选中(x,y)  then
	   					self.焦点 = true
	   					if 引擎.鼠标弹起(左键)  then
							self.选中 =  k+self.加入
							self.序号 = v.名称.名称
							if self.服饰id[self.序号] ~= nil  then
								    self.图鉴id=self.服饰id[self.序号]
							end
							if   self.图鉴数值[self.选中]~= nil  then
								self.选中数值=self.图鉴数值[self.选中].数值
								self.选中类型=self.图鉴数值[self.选中].类型
								self.碎片消耗=self.图鉴数值[self.选中].碎片
								self.仙玉消耗=self.图鉴数值[self.选中].仙玉
							end
							self:置翅膀形象()
						end
					end
					if self.选中~=0 then
						if  k+self.加入 ==self.选中  then
						  tp.物品格子确定_:显示(self.x+225+wx-95,self.y+32+2*67)
						end
					end
			end
		end


			if k<=5020 and k>5015 then
            		local wx = (k-5015)*62
			v.模型.小动画:显示(self.x+220+wx-95,self.y+30+3*67)

			if  v.名称.名称 == "定制翅膀十六"  and  not self.图鉴.定制翅膀十六 or  v.名称.名称 == "定制翅膀十七"  and  not self.图鉴.定制翅膀十七 or  v.名称.名称 == "定制翅膀十八"  and  not self.图鉴.定制翅膀十八 or v.名称.名称 == "定制翅膀十九"  and  not self.图鉴.定制翅膀十九 or  v.名称.名称 == "定制翅膀二十"  and  not self.图鉴.定制翅膀二十  then
					v.模型.小动画:置颜色(0xFF808080)
					zts3:置颜色(红色)
					zts3:显示(self.x+220+wx-90,self.y+65+3*67,"未激活")
				elseif  v.名称.名称 == "定制翅膀十六"  and   self.图鉴.定制翅膀十六 or  v.名称.名称 == "定制翅膀十七"  and   self.图鉴.定制翅膀十七 or  v.名称.名称 == "定制翅膀十八"  and   self.图鉴.定制翅膀十八 or v.名称.名称 == "定制翅膀十九"  and   self.图鉴.定制翅膀十九 or  v.名称.名称 == "定制翅膀二十"  and   self.图鉴.定制翅膀二十  then
					v.模型.小动画:置颜色(白色)
					zts4:置颜色(绿色)
					zts4:显示(self.x+220+wx-90,self.y+65+3*67,"已激活")
				end

			if v.模型.小动画:是否选中(x,y,偏移x,偏移y) then
		    	tp.提示:商城提示(self.x-150+wx,self.y-90+3*67,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
		    	end
			if self.翅膀物品组[k] ~= nil and self.翅膀物品组[k].模型.小动画 ~= nil then
	   				if self.翅膀物品组[k].模型.小动画:是否选中(x,y)  then
	   					self.焦点 = true
	   					if 引擎.鼠标弹起(左键)  then
							self.选中 =  k+self.加入
							self.序号 = v.名称.名称
							if self.服饰id[self.序号] ~= nil  then
								    self.图鉴id=self.服饰id[self.序号]
							end
							if   self.图鉴数值[self.选中]~= nil  then
								self.选中数值=self.图鉴数值[self.选中].数值
								self.选中类型=self.图鉴数值[self.选中].类型
								self.碎片消耗=self.图鉴数值[self.选中].碎片
								self.仙玉消耗=self.图鉴数值[self.选中].仙玉
							end
							self:置翅膀形象()
						end
					end
					if self.选中~=0 then
						if  k+self.加入 ==self.选中  then
						  tp.物品格子确定_:显示(self.x+225+wx-95,self.y+32+3*67)
						end
					end
			end
		end


			if k<=5025 and k>5020 then
		local 锦衣激活 = 0
            		local wx = (k-5020)*62
			v.模型.小动画:显示(self.x+220+wx-95,self.y+30+4*67)

			if  v.名称.名称 == "定制翅膀二十一"  and  not self.图鉴.定制翅膀二十一 or  v.名称.名称 == "定制翅膀二十二"  and  not self.图鉴.定制翅膀二十二 or  v.名称.名称 == "定制翅膀二十三"  and  not self.图鉴.定制翅膀二十三   then
					v.模型.小动画:置颜色(0xFF808080)
					zts3:置颜色(红色)
					zts3:显示(self.x+220+wx-90,self.y+65+4*67,"未激活")
					 锦衣激活 = 0
				elseif  v.名称.名称 == "定制翅膀二十一"  and   self.图鉴.定制翅膀二十一 or  v.名称.名称 == "定制翅膀二十二"  and   self.图鉴.定制翅膀二十二 or  v.名称.名称 == "定制翅膀二十三"  and   self.图鉴.定制翅膀二十三 then
					v.模型.小动画:置颜色(白色)
					zts4:置颜色(绿色)
					zts4:显示(self.x+220+wx-90,self.y+65+4*67,"已激活")
					 锦衣激活 = 1
				end

			if v.模型.小动画:是否选中(x,y,偏移x,偏移y) then
		    	tp.提示:商城提示(self.x-150+wx,self.y-90+4*67,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
		    	end
			if self.翅膀物品组[k] ~= nil and self.翅膀物品组[k].模型.小动画 ~= nil then
	   				if self.翅膀物品组[k].模型.小动画:是否选中(x,y)  then
	   					self.焦点 = true
	   					if 引擎.鼠标弹起(左键)  then
							self.选中 =  k+self.加入
							self.序号 = v.名称.名称
							if self.服饰id[self.序号] ~= nil  then
								    self.图鉴id=self.服饰id[self.序号]
							end
							if   self.图鉴数值[self.选中]~= nil  then
								self.选中数值=self.图鉴数值[self.选中].数值
								self.选中类型=self.图鉴数值[self.选中].类型
								self.碎片消耗=self.图鉴数值[self.选中].碎片
								self.仙玉消耗=self.图鉴数值[self.选中].仙玉
							end
							self:置翅膀形象()
						end
					end
					if self.选中~=0 then
						if  k+self.加入 ==self.选中  then
						  tp.物品格子确定_:显示(self.x+225+wx-95,self.y+32+4*67)
						end
					end
			end
		end


		end

		zts2:显示(self.x+15,self.y+250,"激活增加角色属性")
		if self.选中类型~=nil and self.选中类型=="最大气血" then
			self.选中类型="气血"
		end
		zts2:显示(self.x+55,self.y+295,self.选中数值.."点"..self.选中类型)

		zts3:置颜色(绿色)
		zts3:显示(self.x+52,self.y+230,"["..self.序号.."]")
		zts4:置颜色(黄色)
		zts4:显示(self.x+5,self.y+335,"翅膀碎片"..self.碎片消耗.."个+"..self.仙玉消耗.."仙玉激活")

		self.资源组[31]:更新(x,y,翅膀bb ~= nil  and self.图鉴[self.序号] ==true )
      		self.资源组[32]:更新(x,y,翅膀bb ~= nil  and tp.队伍[1].锦衣[2] ~=nil   )
      		self.资源组[30]:显示(self.x+55,self.y+353,true)--激活
      		self.资源组[31]:显示(self.x+10,self.y+203,true)--激活
      		self.资源组[32]:显示(self.x+95,self.y+203,true)--激活

		if 翅膀bb ~= nil then
		local jx = self.x + 80
		local jy = self.y + 145--+25
		--tp.影子:显示(jx,jy)
		if self.资源组[34] ~= nil then
			self.资源组[34]:更新(dt)
			self.资源组[34]:显示(jx+30,jy+50)
		end
		end

		if self.资源组[30]:事件判断() then
			发送数据(7001,{序列=self.选中,激活id=self.选中,名称=self.序号,文本="锦衣",佩戴文本="翅膀",图鉴id=self.图鉴id})
		 elseif self.资源组[31]:事件判断() then --骑乘
		 	发送数据(7002,{序列=self.选中,名称=self.序号})
		elseif self.资源组[32]:事件判断() then --骑乘
		 	发送数据(7003,{序列=self.选中,名称=self.序号})
		end



	end
end



function 场景类_图鉴:刷新经验(内容)
	-- table.print(内容)
    if self.选中~=0 then
        wp[self.选中]=内容
    end
end

function 场景类_图鉴:刷新(f)
	-- if tp.窗口.坐骑资质栏.可视 then
	-- 	tp.窗口.坐骑资质栏:打开()
	-- end
	if tp.窗口.坐骑技能栏.可视 then
		tp.窗口.坐骑技能栏:打开()
	end
	self.加入 = 0
	self.选中 = 0
	self.拽拖计次 = 0
	self.拽拖对象 = 0
	self.拽拖事件 = 0
	self.插入选区 = 0
	self.头像组 = {}
	self.名称输入框:置可视(false,false)
end


function 场景类_图鉴:检查点(x,y)
	if self.资源组[1]:是否选中(x,y)  then
		return true
	end
end

function 场景类_图鉴:初始移动(x,y)
	tp.运行时间 = tp.运行时间 + 1
	if not tp.消息栏焦点 then
  		self.窗口时间 = tp.运行时间
 	end
	if not self.焦点 then
		tp.移动窗口 = true
	end
	if self.鼠标 and  not self.焦点 then
		self.xx = x - self.x
		self.yy = y - self.y
	end
end

function 场景类_图鉴:开始移动(x,y)
	if self.鼠标 then
		self.x = x - self.xx
		self.y = y - self.yy
	end
end

return 场景类_图鉴