--======================================================================--
--  ☆ 作者：作者QQ：79550111
--======================================================================--
local 场景类_创建 = class()

local tp
local mouseb = 引擎.鼠标按下
local qtx = 引擎.取头像
local qmx = 引擎.取模型
local qzd = 引擎.取战斗模型
local random = 引擎.取随机整数
local mousea = 引擎.鼠标弹起
--########################################################?自己修改?##########################################
local function 判断游戏名字(mz)
    if string.find(mz,"[%s%p%c%z%?\\!@#%$%%&%*%(%)%^,%.%+%-/<>;'\"%[%]{}]")~=nil then
        return 1
    elseif string.find(mz,"　")~=nil or string.find(mz, "GM") ~= nil or string.find(mz, "Gm") ~= nil or string.find(mz, "充值") ~= nil or string.find(mz, "gm") ~= nil or string.find(mz, "管理") ~= nil or string.find(mz, "老猫") ~= nil or string.find(mz, "国家") ~= nil or string.find(mz, "主席") ~= nil or string.find(mz, "近平") ~= nil then
        return 1
    end
end
local 姓库 = {
    '赵',
    '钱',
    '孙',
    '李',
    '周',
    '吴',
    '郑',
    '王',
    '冯',
    '陈',
    '褚',
    '卫',
    '蒋',
    '沈',
    '韩',
    '杨',
    '朱',
    '秦',
    '尤',
    '许',
    '何',
    '吕',
    '施',
    '张',
    '孔',
    '曹',
    '严',
    '华',
    '金',
    '魏',
    '陶',
    '姜',
    '戚',
    '谢',
    '邹',
    '喻',
    '柏',
    '水',
    '窦',
    '章',
    '云',
    '苏',
    '潘',
    '葛',
    '奚',
    '范',
    '彭',
    '郎',
    '鲁',
    '韦',
    '昌',
    '马',
    '苗',
    '凤',
    '花',
    '方',
    '俞',
    '任',
    '袁',
    '柳',
    '邓',
    '鲍',
    '史',
    '唐',
    '费',
    '廉',
    '岑',
    '薛',
    '雷',
    '贺',
    '倪',
    '汤',
    '藤',
    '殷',
    '罗',
    '毕',
    '郝',
    '邬',
    '安',
    '常',
    '乐',
    '于',
    '时',
    '付',
    '皮',
    '卞',
    '齐',
    '康',
    '伍',
    '余',
    '元',
    '卜',
    '顾',
    '孟',
    '平',
    '黄',
    '和',
    '穆',
    '肖',
    '尹',
    '姚',
    '邵',
    '湛',
    '汪',
    '祁',
    '毛',
    '禹',
    '狄',
    '米',
    '贝',
    '明',
    '藏',
    '计',
    '伏',
    '成',
    '戴',
    '谈',
    '宋',
    '茅',
    '庞',
    '熊',
    '纪',
    '舒',
    '屈',
    '项',
    '祝',
    '董',
    '梁',
    '杜',
    '阮',
    '伏',
    '蓝',
    '闵',
    '席',
    '季',
    '麻',
    '强',
    '贾',
    '路',
    '娄',
    '危',
    '江',
    '童',
    '颜',
    '郭',
    '梅',
    '盛',
    '林',
    '刁',
    '钟',
    '徐',
    '邱',
    '骆',
    '高',
    '夏',
    '蔡',
    '田',
    '樊',
    '胡',
    '凌',
    '霍',
    '虞',
    '万',
    '支',
    '柯',
    '昝',
    '管',
    '卢',
    '莫',
    '经',
    '房',
    '裘',
    '缪',
    '干',
    '解',
    '应',
    '宗',
    '丁',
    '宣',
    '贲',
    '郁',
    '单',
    '杭',
    '洪',
    '包',
    '诸',
    '左',
    '石',
    '崔',
    '吉',
    '钮',
    '龚',
    '程',
    '嵇',
    '邢',
    '滑',
    '裴',
    '陆',
    '荣',
    '翁',
    '荀',
    '羊',
    '惠',
    '甄',
    '上官',
    '欧阳',
    '夏候',
    '诸葛',
    '闻人',
    '东方',
    '赫连',
    '皇甫',
    '尉迟',
    '公羊',
    '澹台',
    '公冶',
    '宗政',
    '濮阳',
    '淳于',
    '单于',
    '太叔',
    '申屠',
    '公孙',
    '仲孙',
    '轩辕',
    '令狐',
    '钟离',
    '宇文',
    '长孙',
    '慕容',
    '鲜于',
    '闾丘',
    '司徒',
    '司空',
    '亓官',
    '司寇',
    '仉督',
    '子车',
    '颛孙',
    '端木',
    '巫马',
    '公西',
    '漆雕',
    '乐正',
    '壤驷',
    '公良',
    '拓拔',
    '夹谷',
    '宰父',
    '谷梁',
    '南宫',
    '百里',
    '段干',
    '东郭',
    '南门',
    '呼延',
    '归海',
    '羊舌',
    '微生',
    '梁丘',
    '左丘',
    '东门',
    '西门',
    '楚',
    '晋',
    '闫',
    '法',
    '汝',
    '鄢',
    '涂',
    '钦',
    '岳',
    '帅',
    '缑',
    '亢',
    '况',
    '有',
    '琴',
    '商',
    '牟',
    '佘',
    '耳',
    '伯',
    '赏',
    '墨',
    '哈',
    '年',
    '爱',
    '阳',
    '佟',
    '言',
    '福',
    '曲',
    '家',
    '封',
    '芮',
    '羿',
    '储',
    '靳',
    '汲',
    '邴',
    '糜',
    '松',
    '祖',
    '井',
    '刘',
    '段',
    '富',
    '巫',
    '叶',
    '乌',
    '焦',
    '巴',
    '弓',
    '牧',
    '隗',
    '山',
    '谷',
    '车',
    '侯',
    '全',
    '蓬',
    '景',
    '郗',
    '班',
    '仰',
    '秋',
    '仲',
    '伊',
    '宫',
    '宁',
    '仇',
    '栾',
    '暴',
    '甘',
    '钭',
    '厉',
    '戎',
    '武',
    '符',
    '从',
    '詹',
    '束',
    '龙',
    '幸',
    '司',
    '韶',
    '郜',
    '黎',
    '蓟',
    '薄',
    '印',
    '宿',
    '白',
    '怀',
    '武',
    '蒲',
    '邰',
    '鄂',
    '索',
    '咸',
    '籍',
    '赖',
    '卓',
    '蔺',
    '屠',
    '蒙',
    '池',
    '乔',
    '阴',
    '胥',
    '能',
    '苍',
    '双',
    '闻',
    '莘',
    '党',
    '翟',
    '谭',
    '贡',
    '劳',
    '逄',
    '姬',
    '申',
    '扶',
    '堵',
    '冉',
    '宰',
    '郦',
    '雍',
    '隙',
    '璩',
    '桑',
    '桂',
    '濮',
    '牛',
    '寿',
    '通',
    '边',
    '扈',
    '燕',
    '冀',
    '郏',
    '浦',
    '尚',
    '农',
    '温',
    '别',
    '庄',
    '晏',
    '柴',
    '瞿',
    '阎',
    '充',
    '慕',
    '连',
    '茹',
    '习',
    '宦',
    '艾',
    '鱼',
    '容',
    '向',
    '古',
    '易',
    '慎',
    '戈',
    '廖',
    '庾',
    '终',
    '暨',
    '居',
    '衡',
    '步',
    '都',
    '耿',
    '满',
    '弘',
    '匡',
    '文',
    '国',
    '寇',
    '广',
    '禄',
    '东',
    '欧',
    '殳',
    '沃',
    '利',
    '蔚',
    '越',
    '隆',
    '师',
    '巩',
    '聂',
    '晁',
    '勾',
    '敖',
    '融',
    '訾',
    '冷',
    '辛',
    '那',
    '简',
    '饶',
    '空',
    '曾',
    '沙',
    '养',
    '鞠',
    '须',
    '丰',
    '关',
    '相',
    '查',
    '后',
    '荆',
    '红',
    '游',
    '竺',
    '权',
    '盖',
    '益',
    '桓',
    '公',
}

local 单字 = {
    '天',
    '夜',
    '晴',
    '瑜',
    '飞',
    '文',
    '弘',
    '松',
    '晓',
    '智',
    '云',
    '易',
    '远',
    '航',
    '笑',
    '白',
    '映',
    '波',
    '代',
    '桃',
    '泽',
    '啸',
    '宸',
    '博',
    '靖',
    '琪',
    '十',
    '君',
    '浩',
    '绍',
    '辉',
    '冷',
    '安',
    '盼',
    '旋',
    '秋',
    '瑾',
    '宇',
    '黎',
    '杰',
    '辉',
    '德',
    '邪',
    '默',
    '磊',
    '豪',
    '寒',
    '瀚',
    '哲',
    '阳',
    '风',
    '皓',
    '世',
    '轩',
    '思',
    '鸿',
    '涛',
    '煜',
    '雄',
    '英',
    '诗',
    '展',
    '聪',
    '俊',
    '海',
    '彤',
    '珍',
    '雨',
    '琴',
    '玉',
    '鹏',
    '祺',
    '命',
    '成',
    '先',
    '忘',
    '幽',
    '威',
    '秀',
    '凡',
    '渊',
    '熙',
    '胜',
    '鸣',
    '姿',
    '芷',
    '芝',
    '筝',
    '真',
    '贞',
    '婴',
    '雯',
    '纹',
    '菀',
    '莞',
    '宛',
    '桐',
    '愫',
    '素',
    '涑',
    '姝',
    '弱',
    '若',
    '蓉',
    '清',
    '青',
    '茗',
    '敏',
    '萍',
    '蓝',
    '兰',
    '莺',
    '萤',
    '弱',
    '怡',
    '紫',
    '芯',
    '雁',
    '嫣',
    '荠',
    '嵩',
    '卿',
    '裘',
    '阁',
    '康',
    '城',
    '焱',
    '穆',
    '枫',
    '翼',
    '鹤',
    '乾'
}

local 双字 = {
    '之玉',
    '越泽',
    '锦程',
    '修杰',
    '烨伟',
    '尔曼',
    '立辉',
    '致远',
    '天思',
    '友绿',
    '聪健',
    '修洁',
    '访琴',
    '初彤',
    '谷雪',
    '平灵',
    '源智',
    '申屠',
    '振家',
    '越彬',
    '子轩',
    '伟宸',
    '晋鹏',
    '觅松',
    '海亦',
    '雨珍',
    '浩宇',
    '嘉熙',
    '志泽',
    '苑博',
    '念波',
    '峻熙',
    '俊驰',
    '子车',
    '南松',
    '聪展',
    '问旋',
    '黎昕',
    '谷波',
    '凝海',
    '靖易',
    '芷烟',
    '渊思',
    '煜祺',
    '乐驹',
    '风华',
    '睿渊',
    '博超',
    '天磊',
    '夜白',
    '初晴',
    '瑾瑜',
    '鹏飞',
    '弘文',
    '伟泽',
    '迎松',
    '雨泽',
    '鹏笑',
    '诗云',
    '大官人',
    '大诗人',
    '白易'

}

function 场景类_创建:初始化(根)
  tp = 根
  local require = require
  local 资源 = 根.资源
  local 按钮 = tp._按钮
  self.右键关闭=1
  -- 公用
   self.上一步 = 按钮.创建(资源:载入('zdy.rpk',"网易WDF动画",0x1000253),0,0,3,true,true)

  self.下一步 = 按钮.创建(资源:载入('zdy.rpk',"网易WDF动画",0x1000258),0,0,3,true,true)

  self.角色图片组 = {
    [1] = {模型="飞燕女",种族="人",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010070),0,0,3,true,true),染色方案=nil,介绍="    深山有佳人,灼灼芙蓉姿,飞燕女轻盈飘逸,灵慧动人,自幼怜爱弱小,嫉恶如仇,一生自由自在,是大自然骄纵的宠儿",兵器="可用兵器为：双剑、环圈",门派="可选择门派：大唐官府、方寸山、女儿村、神木林",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010049),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010084),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010043)},
    [2] = {模型="英女侠",种族="人",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010012),0,0,3,true,true),染色方案=nil,介绍="    兰心惠质出名门,英姿飒爽自芳华,英女侠天资聪颖,精通琴棋书画,心怀仁爱,行善不落人后,是位侠骨柔情的奇女子",兵器="可用兵器为：双剑、长鞭",门派="可选择门派：大唐官府、方寸山、女儿村、神木林",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010054),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010085),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010048)},
    [3] = {模型="巫蛮儿",种族="人",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010010),0,0,3,true,true),染色方案=nil,介绍="    嫣然巧笑踏绿萝,一路银铃一路歌,巫蛮儿质朴单纯,灵动可人,生性善良,活泼可爱,花盈翠影出神木,环佩婉转披香来",兵器="可用兵器为：宝珠、法杖",门派="可选择门派：大唐官府、方寸山、女儿村、神木",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010051),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010086),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010045)},
    [4] = {模型="偃无师",种族="人",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010013),0,0,3,true,true),染色方案=nil,介绍="    铁手隐机枢，巧夺天工，猛力执巨剑，志敌万均。偃无师性情冷厉，疏狂不羁，亦有奇谋满腹，铮铮傲骨。",兵器="可用兵器为：巨剑、剑",门派="可选择门派：大唐官府、化生寺、方寸山、神木林",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010053),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010087),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010047)},
    [5] = {模型="逍遥生",种族="人",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010014),0,0,3,true,true),染色方案=nil,介绍="    快意恩仇事,把酒踏歌行,一袭白衫,一纸折扇,逍遥生风流倜傥,潇洒自如,行事光明磊落,是世人乐于结交的谦谦君子",兵器="可用兵器为：剑、扇",门派="可选择门派：大唐官府、化生寺、方寸山、神木林",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010052),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010088),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010046)},
    [6] = {模型="剑侠客",种族="人",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010011),0,0,3,true,true),染色方案=nil,介绍="    霜刃露锋芒,飒沓如流星,剑侠客率情任性,狂放不羁,一生淡泊名利,嗜武如痴,英雄意,儿女情,独闯江湖半生醉,举杯邀月最销魂",兵器="可用兵器为：刀、剑",门派="可选择门派：大唐官府、化生寺、方寸山、神木林",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010050),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010089),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010044)},
    [7] = {模型="狐美人",种族="魔",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010015),0,0,3,true,true),染色方案=nil,介绍="    修眉连娟,斜挑入眉,媚眼如丝,含娇含笑,狐美人柔情绰态,胜似海棠醉日,风情万种,颠倒众生",兵器="可用兵器为：爪刺、鞭",门派="可选择门派：盘丝洞、阴曹地府、魔王寨、无底洞",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010026),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010090),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010025)},
    [8] = {模型="骨精灵",种族="魔",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010019),0,0,3,true,true),染色方案=nil,介绍="    眉黛春山秀,横波剪秋水,骨精灵娇妍俏皮,顾盼神飞,机敏聪慧,好打不平,对世间万物充满好奇",兵器="可用兵器为：爪刺、魔棒",门派="可选择门派：盘丝洞、阴曹地府、魔王寨、无底洞",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010022),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010091),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010021)},
    [9] = {模型="鬼潇潇",种族="魔",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010071),0,0,3,true,true),染色方案=nil,介绍="    寒眸印秋水，魂隐三生途，素手执竹伞，影馀幽冥路。鬼潇潇青丝如墨，红杉如火，一对异色瞳里似乎藏着无尽的忧愁和神秘。",兵器="可用兵器为：爪刺、伞",门派="可选择门派：狮驼岭、魔王寨、阴曹地府、无底洞",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010024),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010092),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010023)},
    [10] = {模型="杀破狼",种族="魔",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010016),0,0,3,true,true),染色方案=nil,介绍="    一啸生风雪,长歌动寒霜,杀破狼飘逸潇洒,气宇轩昂,能文能武,卓尔不群,身具的神秘天狼血统,纵横骄天下,傲立三界间.",兵器="可用兵器为：弓弩、宝珠",门派="可选择门派：狮驼岭、魔王寨、阴曹地府、无底洞",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010032),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010093),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010031)},
    [11] = {模型="巨魔王",种族="魔",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010017),0,0,3,true,true),染色方案=nil,介绍="    一怒震乾坤,杀气凝如山,巨魔王力拔山气兮盖世,肩负魔族神秘使命,叱咤风云,威风凛凛",兵器="可用兵器为：斧钺、刀",门派="可选择门派：狮驼岭、魔王寨、阴曹地府、无底洞",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010030),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010094),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010029)},
    [12] = {模型="虎头怪",种族="魔",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010018),0,0,3,true,true),染色方案=nil,介绍="    戏谑犹可爱,虽有神力不欺人,虎头怪弯弧五百步,长戟八十斤,勇武过人,生性耿直豁达,对朋友忠肝义胆,是顶天立地的大丈夫",兵器="可用兵器为：斧钺、锤",门派="可选择门派：狮驼岭、魔王寨、阴曹地府、无底洞",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010028),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010095),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010027)},
    [13] = {模型="舞天姬",种族="仙",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010004),0,0,3,true,true),染色方案=nil,介绍="    霓裳曳广带,飘拂升天行,舞天姬明眸珠辉,瑰姿艳逸,生性善解人意,令人如沐春风.一舞绡丝动四方,观之心魂俱醉",兵器="可用兵器为：飘带、环圈",门派="可选择门派：天宫、普陀山、龙宫、凌波城",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010064),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010096),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010058)},
    [14] = {模型="玄彩娥",种族="仙",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010009),0,0,3,true,true),染色方案=nil,介绍="    桂露对仙娥,星星下云逗,玄彩娥在花从中蝶翼翩翩,婀娜曼妙,犹聚晨露新聚,奇花初蕊,是集天地灵气于一身的百花仙子",兵器="可用兵器为：飘带、魔棒",门派="可选择门派：龙宫、普陀山、天宫、凌波城",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010065),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010097),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010059)},
    [15] = {模型="桃夭夭",种族="仙",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010005),0,0,3,true,true),染色方案=nil,介绍="    桃夭柳媚梦酣眠，笑语嫣然化春风。一朝春近晴光好，清波潋滟映芳菲，桃夭夭是蟠桃园含花吐蕊的花苞，历经三千毓秀钟灵，化身一个机灵爽朗，骄憨顽皮的少女。",兵器="飘带、灯笼",门派="可选择门派：天宫、龙宫、普陀山、凌波城",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010063),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010101),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010057)},
    [16] = {模型="羽灵神",种族="仙",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010008),0,0,3,true,true),染色方案=nil,介绍="    游侠红尘里,豪情动九天.羽灵神热情正直,率性豁达,游侠三界间,交友遍天下;乐见四海尽升平,愿引凤鸣遍九州",兵器="可用兵器为：弓弩、法杖",门派="可选择门派：天宫、龙宫、普陀山、凌波城",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010066),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010098),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010060)},
    [17] = {模型="神天兵",种族="仙",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010006),0,0,3,true,true),染色方案=nil,介绍="    金甲腾云受天命,神枪破逆卫灵霄,神天兵风采鹰扬,锋芒毕露,守护天庭立天威,所向披靡,妖魔皆闻风丧胆",兵器="可用兵器为：枪矛、锤",门派="可选择门派：龙宫、天宫、五庄观、凌波城",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010062),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010099),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010056)},
    [18] = {模型="龙太子",种族="仙",头像=根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010007),0,0,3,true,true),染色方案=nil,介绍="    乘风破浪翔碧海,腾云架雾上青天,龙太子凭借天生的优势领悟仙法精髓,是当之无愧的龙族骄子,身经百战的天界战将",兵器="可用兵器为：枪矛、扇",门派="可选择门派：龙宫、天宫、五庄观、凌波城",图片=资源:载入('lg.rpk',"网易WDF动画",0x00010061),头像圆图=资源:载入('lg.rpk',"网易WDF动画",0x00010100),图片介绍=资源:载入('lg.rpk',"网易WDF动画",0x00010055)},
    [19] = {模型="影精灵",种族="魔",头像=根._按钮(资源:载入('vvxxzcom/yjl/cwmx.wdf',"网易WDF动画",0x00001271),0,0,3,true,true),染色方案=nil,介绍="    酷似骨精灵的魔族少女，冷酷，理智，不近人情。她曾经是蚩尤残党的领袖，意图复活蚩尤。",兵器="可用兵器为：爪、棒、双斧",门派="可选择门派：狮驼岭、魔王寨、阴曹地府、无底洞、九黎城", 图片=资源:载入('wdf/vvxxzcom/yjl/影精灵.png',"图片"),头像圆图=资源:载入('wdf/vvxxzcom/yjl/影圆.png',"图片"),图片介绍=资源:载入('wdf/vvxxzcom/yjl/文字3.png',"图片")},
  }
  self.创建人物精灵 = {}
  for n=1,19 do
    local s = qtx(self.角色图片组[n].模型)
    local q = qmx(self.角色图片组[n].模型,nil,根)
    local w = qzd(self.角色图片组[n].模型,nil,根)
    self.创建人物精灵[n] = {}
    self.创建人物精灵[n]["静立"] = 资源:载入(q[3],"网易WDF动画",q[1])
    self.创建人物精灵[n]["行走"] = 资源:载入(q[3],"网易WDF动画",q[2])
    self.创建人物精灵[n]["攻击"] = 资源:载入(w[10],"网易WDF动画",w[1])
    self.创建人物精灵[n]["施法"] = 资源:载入(w[10],"网易WDF动画",w[7])
  end
  self.介绍文本 = 根._丰富文本(220,100)
  self.介绍文本:置行度(3)
  self.人族背景 = 资源:载入('lg.rpk',"网易WDF动画",0x00010035)
  self.仙族背景 = 资源:载入('lg.rpk',"网易WDF动画",0x00010033)
  self.魔族背景 = 资源:载入('lg.rpk',"网易WDF动画",0x00010034)
  self.创建 = 按钮.创建(资源:载入('lg.rpk',"网易WDF动画",0X00010111),0,0,3,true,true)
  self.创建种族底框 = 资源:载入('lg.rpk',"网易WDF动画",0x00010042)
  self.创建名字 = 资源:载入('lg.rpk',"网易WDF动画",0x00010069)
  self.创建选中光环 = 资源:载入('lg.rpk',"网易WDF动画",0x00010078)
  self.名字骰子 = 根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010068),0,0,3,true,true)
  self.创建人物背景 = 资源:载入('lg.rpk',"网易WDF动画",0x00010072)
  self.站立按钮 = 根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010073,9393),0,0,3,true,true)
  self.奔跑按钮 = 根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010079,9393),0,0,3,true,true)
  self.攻击按钮 = 根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010080,9393),0,0,3,true,true)
  self.施法按钮 = 根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010081,9393),0,0,3,true,true)
  self.左边旋转按钮 = 根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010074),0,0,3,true,true)
  self.右边旋转按钮 = 根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010075),0,0,3,true,true)
  self.站台 = 资源:载入('lg.rpk',"网易WDF动画",0x00010076)
  self.人物介绍按钮 = 根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010077),0,0,3,true,true)
  self.可用兵器按钮 = 根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010082),0,0,3,true,true)
  self.可选门派按钮 = 根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010083),0,0,3,true,true)
  self.头像底框 = 资源:载入('lg.rpk',"网易WDF动画",0x00010067)
  self.魔族头像底框 = 资源:载入('wdf/vvxxzcom/yjl/创建头像.png',"图片")

  self.魔族未选中 = 根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010036),0,0,3,true,true)
  self.仙族未选中 = 根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010037),0,0,3,true,true)
  self.人族未选中 = 根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010038),0,0,3,true,true)
  self.魔族选中 = 根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010039),0,0,3,true,true)
  self.仙族选中 = 根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010040),0,0,3,true,true)
  self.人族选中 = 根._按钮(资源:载入('lg.rpk',"网易WDF动画",0x00010041),0,0,3,true,true)
  self.影子 = 资源:载入('shape.wdf',"网易WDF动画",0xDCE4B562)
  self.种族选中 = "人"
  self.种族背景 = ""
  self.选中人物 = 1
  self.方向 = 4
  self.动作 = "静立"
  self.控件类 = require("ggeui/加载类")()
    local 总控件 = self.控件类:创建控件('创建控件')
    总控件:置可视(true,true)
    self.名称输入框 = 总控件:创建输入("创建输入",253,543,120,20,0xFFFFFFFF)
    self.名称输入框:置可视(false,false)
    self.名称输入框:置限制字数(10)
  self.人物列表 = {}
  self.人物列表[1] = {模型="飞燕女",种族="人",x=230,y=65,染色方案=3,介绍="深山有佳人,灼灼芙蓉姿,飞燕女轻盈飘逸,灵慧动人,自幼怜爱弱小,嫉恶如仇,一生自由自在,是大自然骄纵的宠儿",兵器="可用兵器为：双剑、环圈",门派="可选择门派：大唐官府、方寸山、女儿村、神木林"}
  self.人物列表[2] = {模型="英女侠",种族="人",x=303,y=65,染色方案=4,介绍="兰心惠质出名门,英姿飒爽自芳华,英女侠天资聪颖,精通琴棋书画,心怀仁爱,行善不落人后,是位侠骨柔情的奇女子",兵器="可用兵器为：双剑、长鞭",门派="可选择门派：大唐官府、方寸山、女儿村、神木林"}
  self.人物列表[3] = {模型="巫蛮儿",种族="人",x=376,y=65,染色方案=nil,介绍="嫣然巧笑踏绿萝,一路银铃一路歌,巫蛮儿质朴单纯,灵动可人,生性善良,活泼可爱,花盈翠影出神木,环佩婉转披香来",兵器="可用兵器为：宝珠、法杖",门派="可选择门派：大唐官府、方寸山、女儿村、神木"}
  self.人物列表[4] = {模型="逍遥生",种族="人",x=522,y=65,染色方案=1,介绍="快意恩仇事,把酒踏歌行,一袭白衫,一纸折扇,逍遥生风流倜傥,潇洒自如,行事光明磊落,是世人乐于结交的谦谦君子",兵器="可用兵器为：剑、扇",门派="可选择门派：大唐官府、化生寺、方寸山、神木林"}
  self.人物列表[5] = {模型="剑侠客",种族="人",x=595,y=65,染色方案=2,介绍="霜刃露锋芒,飒沓如流星,剑侠客率情任性,狂放不羁,一生淡泊名利,嗜武如痴,英雄意,儿女情,独闯江湖半生醉,举杯邀月最销魂",兵器="可用兵器为：刀、剑",门派="可选择门派：大唐官府、化生寺、方寸山、神木林"}
  self.人物列表[6] = {模型="狐美人",种族="魔",x=230,y=142,染色方案=7,介绍="修眉连娟,斜挑入眉,媚眼如丝,含娇含笑,狐美人柔情绰态,胜似海棠醉日,风情万种,颠倒众生",兵器="可用兵器为：爪刺、鞭",门派="可选择门派：盘丝洞、阴曹地府、魔王寨、无底洞"}
  self.人物列表[7] = {模型="骨精灵",种族="魔",x=303,y=142,染色方案=8,介绍="眉黛春山秀,横波剪秋水,骨精灵娇妍俏皮,顾盼神飞,机敏聪慧,好打不平,对世间万物充满好奇",兵器="可用兵器为：爪刺、魔棒",门派="可选择门派：盘丝洞、阴曹地府、魔王寨、无底洞"}
  self.人物列表[8] = {模型="杀破狼",种族="魔",x=449,y=142,染色方案=nil,介绍="一啸生风雪,长歌动寒霜,杀破狼飘逸潇洒,气宇轩昂,能文能武,卓尔不群,身具的神秘天狼血统,纵横骄天下,傲立三界间.",兵器="可用兵器为：弓弩、宝珠",门派="可选择门派：狮驼岭、魔王寨、阴曹地府、无底洞"}
  self.人物列表[9] = {模型="巨魔王",种族="魔",x=522,y=142,染色方案=5,介绍="一怒震乾坤,杀气凝如山,巨魔王力拔山气兮盖世,肩负魔族神秘使命,叱咤风云,威风凛凛",兵器="可用兵器为：斧钺、刀",门派="可选择门派：狮驼岭、魔王寨、阴曹地府、无底洞"}
  self.人物列表[10] = {模型="虎头怪",种族="魔",x=595,y=142,染色方案=6,介绍="戏谑犹可爱,虽有神力不欺人,虎头怪弯弧五百步,长戟八十斤,勇武过人,生性耿直豁达,对朋友忠肝义胆,是顶天立地的大丈夫",兵器="可用兵器为：斧钺、锤",门派="可选择门派：狮驼岭、魔王寨、阴曹地府、无底洞"}
  self.人物列表[11] = {模型="舞天姬",种族="仙",x=230,y=219,染色方案=11,介绍="霓裳曳广带,飘拂升天行,舞天姬明眸珠辉,瑰姿艳逸,生性善解人意,令人如沐春风.一舞绡丝动四方,观之心魂俱醉",兵器="可用兵器为：飘带、环圈",门派="可选择门派：天宫、普陀山、龙宫、凌波城"}
  self.人物列表[12] = {模型="玄彩娥",种族="仙",x=303,y=219,染色方案=12,介绍="桂露对仙娥,星星下云逗,玄彩娥在花从中蝶翼翩翩,婀娜曼妙,犹聚晨露新聚,奇花初蕊,是集天地灵气于一身的百花仙子",兵器="可用兵器为：飘带、魔棒",门派="可选择门派：龙宫、普陀山、天宫、凌波城"}
  self.人物列表[13] = {模型="羽灵神",种族="仙",x=449,y=219,染色方案=nil,介绍="游侠红尘里,豪情动九天.羽灵神热情正直,率性豁达,游侠三界间,交友遍天下;乐见四海尽升平,愿引凤鸣遍九州",兵器="可用兵器为：弓弩、法杖",门派="可选择门派：天宫、龙宫、普陀山、凌波城"}
  self.人物列表[14] = {模型="神天兵",种族="仙",x=522,y=219,染色方案=9,介绍="金甲腾云受天命,神枪破逆卫灵霄,神天兵风采鹰扬,锋芒毕露,守护天庭立天威,所向披靡,妖魔皆闻风丧胆",兵器="可用兵器为：枪矛、锤",门派="可选择门派：龙宫、天宫、五庄观、凌波城"}
  self.人物列表[15] = {模型="龙太子",种族="仙",x=595,y=219,染色方案=10,介绍="乘风破浪翔碧海,腾云架雾上青天,龙太子凭借天生的优势领悟仙法精髓,是当之无愧的龙族骄子,身经百战的天界战将",兵器="可用兵器为：枪矛、扇",门派="可选择门派：龙宫、天宫、五庄观、凌波城"}
  self.人物列表[17] = {模型="偃无师",种族="人",x=449,y=65,染色方案=nil,介绍="铁手隐机枢，巧夺天工，猛力执巨剑，志敌万均。偃无师性情冷厉，疏狂不羁，亦有奇谋满腹，铮铮傲骨。",兵器="可用兵器为：巨剑、剑",门派="可选择门派：大唐官府、化生寺、方寸山、神木林"}
  self.人物列表[18] = {模型="鬼潇潇",种族="魔",x=376,y=142,染色方案=nil,介绍="寒眸印秋水，魂隐三生途，素手执竹伞，影馀幽冥路。鬼潇潇青丝如墨，红杉如火，一对异色瞳里似乎藏着无尽的忧愁和神秘。",兵器="可用兵器为：爪刺、伞",门派="可选择门派：狮驼岭、魔王寨、阴曹地府、无底洞"}
  self.人物列表[16] = {模型="桃夭夭",种族="仙",x=376,y=219,染色方案=nil,介绍="桃夭柳媚梦酣眠，笑语嫣然化春风。一朝春近晴光好，清波潋滟映芳菲，桃夭夭是蟠桃园含花吐蕊的花苞，历经三千毓秀钟灵，化身一个机灵爽朗，骄憨顽皮的少女。",兵器="可用兵器为：飘带、灯笼",门派="可选择门派：天宫、龙宫、普陀山、凌波城"}
  self.人物列表[19] = {模型="影精灵",种族="魔",x=376,y=142,染色方案=2,介绍="寒眸印秋水，魂隐三生途，素手执竹伞，影馀幽冥路。鬼潇潇青丝如墨，红杉如火，一对异色瞳里似乎藏着无尽的忧愁和神秘。",兵器="可用兵器为：斧头",门派="可选择门派：狮驼岭、魔王寨、阴曹地府、无底洞、九黎城"}
end

function 场景类_创建:置方向(方向,n)
  self.创建人物精灵[n]["静立"]:置方向(方向)
  self.创建人物精灵[n]["行走"]:置方向(方向)
  self.创建人物精灵[n]["攻击"]:置方向(取四至八方向(方向))
  self.创建人物精灵[n]["施法"]:置方向(取四至八方向(方向))
end

function 场景类_创建:刷新位置()
  local 资源 = tp.资源
  self.局部游戏高度 = 全局游戏高度/2
  self.局部游戏宽度 = 800/2
  self.人物列表 = {}
  self.人物列表[1] = {模型="飞燕女",种族="人",x=230,y=65,染色方案=3,介绍="深山有佳人,灼灼芙蓉姿,飞燕女轻盈飘逸,灵慧动人,自幼怜爱弱小,嫉恶如仇,一生自由自在,是大自然骄纵的宠儿",兵器="可用兵器为：双剑、环圈",门派="可选择门派：大唐官府、方寸山、女儿村、神木林"}
  self.人物列表[2] = {模型="英女侠",种族="人",x=303,y=65,染色方案=4,介绍="兰心惠质出名门,英姿飒爽自芳华,英女侠天资聪颖,精通琴棋书画,心怀仁爱,行善不落人后,是位侠骨柔情的奇女子",兵器="可用兵器为：双剑、长鞭",门派="可选择门派：大唐官府、方寸山、女儿村、神木林"}
  self.人物列表[3] = {模型="巫蛮儿",种族="人",x=376,y=65,染色方案=nil,介绍="嫣然巧笑踏绿萝,一路银铃一路歌,巫蛮儿质朴单纯,灵动可人,生性善良,活泼可爱,花盈翠影出神木,环佩婉转披香来",兵器="可用兵器为：宝珠、法杖",门派="可选择门派：大唐官府、方寸山、女儿村、神木"}
  self.人物列表[4] = {模型="逍遥生",种族="人",x=522,y=65,染色方案=1,介绍="快意恩仇事,把酒踏歌行,一袭白衫,一纸折扇,逍遥生风流倜傥,潇洒自如,行事光明磊落,是世人乐于结交的谦谦君子",兵器="可用兵器为：剑、扇",门派="可选择门派：大唐官府、化生寺、方寸山、神木林"}
  self.人物列表[5] = {模型="剑侠客",种族="人",x=595,y=65,染色方案=2,介绍="霜刃露锋芒,飒沓如流星,剑侠客率情任性,狂放不羁,一生淡泊名利,嗜武如痴,英雄意,儿女情,独闯江湖半生醉,举杯邀月最销魂",兵器="可用兵器为：刀、剑",门派="可选择门派：大唐官府、化生寺、方寸山、神木林"}
  self.人物列表[6] = {模型="狐美人",种族="魔",x=230,y=142,染色方案=7,介绍="修眉连娟,斜挑入眉,媚眼如丝,含娇含笑,狐美人柔情绰态,胜似海棠醉日,风情万种,颠倒众生",兵器="可用兵器为：爪刺、鞭",门派="可选择门派：盘丝洞、阴曹地府、魔王寨、无底洞"}
  self.人物列表[7] = {模型="骨精灵",种族="魔",x=303,y=142,染色方案=8,介绍="眉黛春山秀,横波剪秋水,骨精灵娇妍俏皮,顾盼神飞,机敏聪慧,好打不平,对世间万物充满好奇",兵器="可用兵器为：爪刺、魔棒",门派="可选择门派：盘丝洞、阴曹地府、魔王寨、无底洞"}
  self.人物列表[8] = {模型="杀破狼",种族="魔",x=449,y=142,染色方案=nil,介绍="一啸生风雪,长歌动寒霜,杀破狼飘逸潇洒,气宇轩昂,能文能武,卓尔不群,身具的神秘天狼血统,纵横骄天下,傲立三界间.",兵器="可用兵器为：弓弩、宝珠",门派="可选择门派：狮驼岭、魔王寨、阴曹地府、无底洞"}
  self.人物列表[9] = {模型="巨魔王",种族="魔",x=522,y=142,染色方案=5,介绍="一怒震乾坤,杀气凝如山,巨魔王力拔山气兮盖世,肩负魔族神秘使命,叱咤风云,威风凛凛",兵器="可用兵器为：斧钺、刀",门派="可选择门派：狮驼岭、魔王寨、阴曹地府、无底洞"}
  self.人物列表[10] = {模型="虎头怪",种族="魔",x=595,y=142,染色方案=6,介绍="戏谑犹可爱,虽有神力不欺人,虎头怪弯弧五百步,长戟八十斤,勇武过人,生性耿直豁达,对朋友忠肝义胆,是顶天立地的大丈夫",兵器="可用兵器为：斧钺、锤",门派="可选择门派：狮驼岭、魔王寨、阴曹地府、无底洞"}
  self.人物列表[11] = {模型="舞天姬",种族="仙",x=230,y=219,染色方案=11,介绍="霓裳曳广带,飘拂升天行,舞天姬明眸珠辉,瑰姿艳逸,生性善解人意,令人如沐春风.一舞绡丝动四方,观之心魂俱醉",兵器="可用兵器为：飘带、环圈",门派="可选择门派：天宫、普陀山、龙宫、凌波城"}
  self.人物列表[12] = {模型="玄彩娥",种族="仙",x=303,y=219,染色方案=12,介绍="桂露对仙娥,星星下云逗,玄彩娥在花从中蝶翼翩翩,婀娜曼妙,犹聚晨露新聚,奇花初蕊,是集天地灵气于一身的百花仙子",兵器="可用兵器为：飘带、魔棒",门派="可选择门派：龙宫、普陀山、天宫、凌波城"}
  self.人物列表[13] = {模型="羽灵神",种族="仙",x=449,y=219,染色方案=nil,介绍="游侠红尘里,豪情动九天.羽灵神热情正直,率性豁达,游侠三界间,交友遍天下;乐见四海尽升平,愿引凤鸣遍九州",兵器="可用兵器为：弓弩、法杖",门派="可选择门派：天宫、龙宫、普陀山、凌波城"}
  self.人物列表[14] = {模型="神天兵",种族="仙",x=522,y=219,染色方案=9,介绍="金甲腾云受天命,神枪破逆卫灵霄,神天兵风采鹰扬,锋芒毕露,守护天庭立天威,所向披靡,妖魔皆闻风丧胆",兵器="可用兵器为：枪矛、锤",门派="可选择门派：龙宫、天宫、五庄观、凌波城"}
  self.人物列表[15] = {模型="龙太子",种族="仙",x=595,y=219,染色方案=10,介绍="乘风破浪翔碧海,腾云架雾上青天,龙太子凭借天生的优势领悟仙法精髓,是当之无愧的龙族骄子,身经百战的天界战将",兵器="可用兵器为：枪矛、扇",门派="可选择门派：龙宫、天宫、五庄观、凌波城"}
  self.人物列表[17] = {模型="偃无师",种族="人",x=449,y=65,染色方案=nil,介绍="铁手隐机枢，巧夺天工，猛力执巨剑，志敌万均。偃无师性情冷厉，疏狂不羁，亦有奇谋满腹，铮铮傲骨。",兵器="可用兵器为：巨剑、剑",门派="可选择门派：大唐官府、化生寺、方寸山、神木林"}
  self.人物列表[18] = {模型="鬼潇潇",种族="魔",x=376,y=142,染色方案=nil,介绍="寒眸印秋水，魂隐三生途，素手执竹伞，影馀幽冥路。鬼潇潇青丝如墨，红杉如火，一对异色瞳里似乎藏着无尽的忧愁和神秘。",兵器="可用兵器为：爪刺、伞",门派="可选择门派：狮驼岭、魔王寨、阴曹地府、无底洞"}
  self.人物列表[16] = {模型="桃夭夭",种族="仙",x=376,y=219,染色方案=nil,介绍="桃夭柳媚梦酣眠，笑语嫣然化春风。一朝春近晴光好，清波潋滟映芳菲，桃夭夭是蟠桃园含花吐蕊的花苞，历经三千毓秀钟灵，化身一个机灵爽朗，骄憨顽皮的少女。",兵器="可用兵器为：飘带、灯笼",门派="可选择门派：天宫、龙宫、普陀山、凌波城"}
  self.人物列表[19] = {模型="影精灵",种族="魔",x=376,y=142,染色方案=nil,介绍="寒眸印秋水，魂隐三生途，素手执竹伞，影馀幽冥路。鬼潇潇青丝如墨，红杉如火，一对异色瞳里似乎藏着无尽的忧愁和神秘。",兵器="可用兵器为：斧头",门派="可选择门派：狮驼岭、魔王寨、阴曹地府、无底洞、九黎城"}
  self.创建中头像 = {}
  self.创建大头像 = {}
  self.创建人物精灵 = {}

  for n=1,19 do
    local s = qtx(self.角色图片组[n].模型)
    local q = qmx(self.角色图片组[n].模型,nil,根)
    local w = qzd(self.角色图片组[n].模型,nil,根)
    self.创建中头像[n] = 资源:载入(s[7],"网易WDF动画",s[5])
    self.创建大头像[n] = 资源:载入(s[7],"网易WDF动画",s[6])
    self.创建人物精灵[n] = {}
    self.创建人物精灵[n]["静立"] = 资源:载入(q[3],"网易WDF动画",q[1])
    self.创建人物精灵[n]["行走"] = 资源:载入(q[3],"网易WDF动画",q[2])
    self.创建人物精灵[n]["攻击"] = 资源:载入(w[10],"网易WDF动画",w[1])
    self.创建人物精灵[n]["施法"] = 资源:载入(w[10],"网易WDF动画",w[7])
    self.角色图片组[n].x = self.角色图片组[n].x
    self.角色图片组[n].y = self.角色图片组[n].y
  end

  self.控件类 = require("ggeui/加载类")()
    local 总控件 = self.控件类:创建控件('创建控件')
    总控件:置可视(true,true)
    self.名称输入框 = 总控件:创建输入("创建输入",253,543,120,20,0xFFFFFFFF)
    self.名称输入框:置可视(false,false)
    self.名称输入框:置限制字数(10)
end

function 场景类_创建:置染色(人物ID,染色方案,染色ID,方向)
  self.创建人物精灵[人物ID]["静立"]:置染色(染色方案,染色ID,染色ID,染色ID)
  self.创建人物精灵[人物ID]["行走"]:置染色(染色方案,染色ID,染色ID,染色ID)
  self.创建人物精灵[人物ID]["攻击"]:置染色(染色方案,染色ID,染色ID,染色ID)
  self.创建人物精灵[人物ID]["施法"]:置染色(染色方案,染色ID,染色ID,染色ID)
  self:置方向(方向,人物ID)
end

function 场景类_创建:后退方向()
  if self.方向 ~= 7 then
  self.方向 = self.方向 + 1
  self:置方向(self.方向,self.选中人物)
  end
end

function 场景类_创建:前进方向()
  if self.方向 ~= 0 then
  self.方向 = self.方向 - 1
  self:置方向(self.方向,self.选中人物)
  end
end

function 场景类_创建:显示(dt,x,y)
  --引擎.置标题(全局游戏标题.." - ("..全局大分区.."["..全局小分区.."] - ".."创建角色")
	if self.选中人物 ~= 0 then
		self.创建人物精灵[self.选中人物][self.动作]:更新(dt)
	end
	self.人族选中:更新(x,y)
	self.魔族选中:更新(x,y)
	self.仙族选中:更新(x,y)
	self.人族未选中:更新(x,y)
	self.魔族未选中:更新(x,y)
	self.仙族未选中:更新(x,y)
	self.创建选中光环:更新(dt)
	self.站立按钮:更新(x,y)
	self.奔跑按钮:更新(x,y)
	self.攻击按钮:更新(x,y)
	self.施法按钮:更新(x,y)
	self.左边旋转按钮:更新(x,y)
	self.右边旋转按钮:更新(x,y)
	self.人物介绍按钮:更新(x,y)
	self.可用兵器按钮:更新(x,y)
	self.可选门派按钮:更新(x,y)
	if self.种族选中 == "人" then
                        local 偏移x,偏移y = 等比例缩放公式(1024,768,self.人族背景.宽度,self.人族背景.高度)
                        self.人族背景:显示(0,0,偏移x,偏移y)
		--self.人族背景:显示(0,0)
		self.创建种族底框:显示(20,20)
		self.人族选中:显示(70,25)
		self.魔族未选中:显示(5,70)
		self.仙族未选中:显示(5,0)
		self.头像底框:显示(220,20)
		if self.人族选中:事件判断() then
			self.种族选中 = "人"
			self.选中人物=1
			self.说明框状态 = "人物介绍"
		elseif self.魔族未选中:事件判断() then
			self.种族选中 = "魔"
			self.选中人物=7
			self.说明框状态 = "人物介绍"
		elseif self.仙族未选中:事件判断() then
			self.种族选中 = "仙"
			self.选中人物=13
			self.说明框状态 = "人物介绍"
		end
	elseif self.种族选中 == "魔" then
                        local 偏移x,偏移y = 等比例缩放公式(1024,768,self.魔族背景.宽度,self.魔族背景.高度)
                        self.魔族背景:显示(0,0,偏移x,偏移y)
		--self.魔族背景:显示(0,0)
		self.创建种族底框:显示(20,20)
		self.魔族选中:显示(70,25)
		self.仙族未选中:显示(5,70)
		self.人族未选中:显示(5,0)
		--self.头像底框:显示(220,20)
                        self.魔族头像底框:显示(175,20)
		if self.魔族选中:事件判断() then
			self.种族选中 = "魔"
			self.选中人物=7
			self.说明框状态 = "人物介绍"
		elseif self.仙族未选中:事件判断() then
			self.种族选中 = "仙"
			self.选中人物=13
			self.说明框状态 = "人物介绍"
		elseif self.人族未选中:事件判断() then
			self.种族选中 = "人"
			self.选中人物=1
			self.说明框状态 = "人物介绍"
		end
	elseif self.种族选中 == "仙" then
                        local 偏移x,偏移y = 等比例缩放公式(1024,768,self.仙族背景.宽度,self.仙族背景.高度)
                        self.仙族背景:显示(0,0,偏移x,偏移y)
		--self.仙族背景:显示(0,0)
		self.创建种族底框:显示(20,20)
		self.仙族选中:显示(70,25)
		self.人族未选中:显示(5,70)
		self.魔族未选中:显示(5,0)
		self.头像底框:显示(220,20)
		if self.仙族选中:事件判断() then
			self.种族选中 = "仙"
			self.选中人物=13
			self.说明框状态 = "人物介绍"
		elseif self.人族未选中:事件判断() then
			self.种族选中 = "人"
			self.选中人物=1
			self.说明框状态 = "人物介绍"
		elseif self.魔族未选中:事件判断() then
			self.种族选中 = "魔"
			self.选中人物=7
			self.说明框状态 = "人物介绍"
		end
	end

	local 头像个数 = 0
	for i=1,#self.角色图片组 do
		if self.种族选中 == self.角色图片组[i].种族 then
                                    if self.种族选中 == "魔" then
                                            self.角色图片组[i].头像:显示(210+头像个数*85,30)
                                    else
                                        self.角色图片组[i].头像:显示(260+头像个数*85,30)
                                    end
			--self.角色图片组[i].头像:显示(260+头像个数*85,30)
			头像个数 = 头像个数 + 1
			if self.角色图片组[i].头像:是否选中(x,y) and mousea(0) then
				self.选中人物=i
				self.说明框状态 = "人物介绍"
			end
		end
	end

	if self.选中人物 ~= 0 then
		self.创建选中光环:显示(self.角色图片组[self.选中人物].头像.按钮.x,self.角色图片组[self.选中人物].头像.按钮.y)
		if self.角色图片组[self.选中人物].模型=="杀破狼" then
		self.角色图片组[self.选中人物].图片:显示(180,80)
		elseif self.角色图片组[self.选中人物].模型=="虎头怪" then
		self.角色图片组[self.选中人物].图片:显示(80,80)
		elseif self.角色图片组[self.选中人物].模型=="巨魔王" then
		self.角色图片组[self.选中人物].图片:显示(100,130)
		elseif self.角色图片组[self.选中人物].模型=="鬼潇潇" then
		self.角色图片组[self.选中人物].图片:显示(160,80)
		elseif self.角色图片组[self.选中人物].模型=="狐美人" then
		self.角色图片组[self.选中人物].图片:显示(160,160)
		elseif self.角色图片组[self.选中人物].模型=="骨精灵" then
		self.角色图片组[self.选中人物].图片:显示(170,180)
		elseif self.角色图片组[self.选中人物].模型=="英女侠" then
		self.角色图片组[self.选中人物].图片:显示(120,110)
		elseif self.角色图片组[self.选中人物].模型=="飞燕女" then
		self.角色图片组[self.选中人物].图片:显示(110,130)
		elseif self.角色图片组[self.选中人物].模型=="剑侠客" then
		self.角色图片组[self.选中人物].图片:显示(5,140)
                          elseif self.角色图片组[self.选中人物].模型=="影精灵" then
                           local 偏移x,偏移y = 等比例缩放公式(480,420,self.角色图片组[self.选中人物].图片.宽度,self.角色图片组[self.选中人物].图片.高度)
                          self.角色图片组[self.选中人物].图片:显示(120,80,偏移x,偏移y)
		elseif self.角色图片组[self.选中人物].模型=="巫蛮儿" then
		self.角色图片组[self.选中人物].图片:显示(90,120)
		elseif self.角色图片组[self.选中人物].模型=="逍遥生" then
		self.角色图片组[self.选中人物].图片:显示(70,150)
		elseif self.角色图片组[self.选中人物].模型=="偃无师" then
		self.角色图片组[self.选中人物].图片:显示(110,130)
		elseif self.角色图片组[self.选中人物].模型=="龙太子" then
		self.角色图片组[self.选中人物].图片:显示(40,160)
		elseif self.角色图片组[self.选中人物].模型=="神天兵" then
		self.角色图片组[self.选中人物].图片:显示(80,130)
		elseif self.角色图片组[self.选中人物].模型=="桃夭夭" then
		self.角色图片组[self.选中人物].图片:显示(150,130)
		elseif self.角色图片组[self.选中人物].模型=="舞天姬" then
		self.角色图片组[self.选中人物].图片:显示(80,120)
		elseif self.角色图片组[self.选中人物].模型=="玄彩娥" then
		self.角色图片组[self.选中人物].图片:显示(170,110)
		elseif self.角色图片组[self.选中人物].模型=="羽灵神" then
		self.角色图片组[self.选中人物].图片:显示(100,110)
		end
		self.角色图片组[self.选中人物].图片介绍:显示(10,180)
	end
	self.上一步:更新(x,y)
	self.创建:更新(x,y)
	self.名字骰子:更新(x,y)
	self.上一步:显示(800-270,600-70)
	self.创建:显示(800-130,600-70)
	self.创建名字:显示(130,600-70)
	self.名字骰子:显示(365,600-70)
	self.创建人物背景:显示(485,130)
	self.站台:显示(623,280)
	self.左边旋转按钮:显示(575,280)
	self.右边旋转按钮:显示(730,280)
	self.站立按钮:显示(555,150)
	self.奔跑按钮:显示(615,150)
	self.攻击按钮:显示(675,150)
	self.施法按钮:显示(735,150)
	self.人物介绍按钮:显示(565,370)
	self.可用兵器按钮:显示(645,370)
	self.可选门派按钮:显示(725,370)
	if self.选中人物 ~= 0 then
		self.染色ID = 0
		if self.角色图片组[self.选中人物].染色方案 ~= nil  then
			self:置染色(self.选中人物,self.角色图片组[self.选中人物].染色方案,self.染色ID,self.方向)
		end
		self:置方向(self.方向,self.选中人物)
		if self.说明框状态 == "人物介绍" then
			self.介绍文本:清空()
			self.介绍文本:添加文本(self.角色图片组[self.选中人物].介绍)
			self.说明框状态 = ""
		end
		self.控件类:显示(x,y)
		self.名称输入框:置可视(true,true)
		if self.名称输入框._输入焦点 == false then
			self.名称输入框._输入焦点=true
		end
		self.影子:显示(678,290)
		self.创建人物精灵[self.选中人物][self.动作]:显示(678,280)
		self.介绍文本:显示(560,410)
	else
		self.名称输入框:置可视(false,false)
	end
	if self.选中人物 ~= 0 then
		self.控件类:更新(dt,x,y)
		if self.左边旋转按钮:事件判断() then
			self:后退方向()
		elseif self.右边旋转按钮:事件判断() then
			self:前进方向()
		elseif self.站立按钮:事件判断() then
			self.动作 = "静立"
		elseif self.奔跑按钮:事件判断() then
			self.动作 = "行走"
		elseif self.攻击按钮:事件判断() then
			self.动作 = "攻击"
		elseif self.施法按钮:事件判断() then
			self.动作 = "施法"
		elseif self.人物介绍按钮:事件判断() then
			self.介绍文本:清空()
			self.介绍文本:添加文本(self.角色图片组[self.选中人物].介绍)
		elseif self.可用兵器按钮:事件判断() then
			self.介绍文本:清空()
			self.介绍文本:添加文本(self.角色图片组[self.选中人物].兵器)
		elseif self.可选门派按钮:事件判断() then
			self.介绍文本:清空()
			self.介绍文本:添加文本(self.角色图片组[self.选中人物].门派)
		elseif self.名字骰子:事件判断() then
			-- local 姓 = {"久违旳骄傲","一纸休妻","叽里呱啦","孤单的烟","寂寞的雪","陪你看日出","第三人称","毒瘾","触动一丝情绪","永不言弃","至死不渝","往日不可追","芷梦","服霸","暗香浮动","红妆满面","含笑半步颠","情场浪子","沫妍","久昧","愿你拥我","激萌","你为谁隐身","泪别","独守空城","旧人心","三分痴迷","惊艳众生","各自安好","请君入菊","末路狂澜","伊人妆","动刀不动情","最后的等待","花落只剩孤寂","爽歪歪","无解","沉鱼落雁","半夏微凉","你在我心里","睡衣男孩","日月星辰","暮雨咋歇","那伤","彻夜难眠","痞子时代","刹那芳华","与情无染","乖乖","花开未央"}
			-- local 组合 = 姓[random(1,#姓)]
      local 组合 = 取随机中文姓名()
			self.名称输入框:置文本(组合)
			self.名称输入框:置可视(true,true)
		end
	end
	if self.上一步:事件判断() then
		self.角色选中号码=0
		self.选中人物 = 1
		self.动作 = "静立"
		self.方向 = 4
		self.染色ID = 0
		self.说明框状态 = "人物介绍"
		self.名称输入框._输入焦点=false
		self.名称输入框._光标可视=false
		self.介绍文本:清空()
		self.名称输入框:置可视(false,false)
		tp.进程 = 2
	elseif self.创建:事件判断() then
		if self.选中人物 == 0 then
			tp.提示:写入("#Y/请选择一个人物进入游戏")
		else
			if self.名称输入框:取文本() == "" then
				tp.提示:写入("#Y/请为角色取一个名字")
			else
				if #self.名称输入框:取文本()<13 then

					if 判断游戏名字(self.名称输入框:取文本())==1 then
						tp.提示:写入("#Y/名字不能带有空格或者特殊符号或者敏感词语")
					else
						客户端:发送数据(3,self.角色图片组[self.选中人物].模型..fgf..self.名称输入框:取文本()..fgf..self.染色ID)
						self.名称输入框:置文本("")
					end
				else
					tp.提示:写入("#Y/角色的名字太长了。")
				end
			end
		end
	end
    -- self.控件类:显示(x,y)
    -- self.名称输入框:置可视(true,true)
    -- self.名称输入框:置文本("士大夫方法方法烦烦烦")
    -- table.print(self.名称输入框)

end

function 取随机中文姓名()
  local 名称 = ''
  local 姓 = 姓库[引擎.取随机整数(1,#姓库)]
  if math.random(100) <= 55 then
      名称 = 姓 .. 双字[引擎.取随机整数(1,#双字)]
  elseif math.random(100) <= 70 then
      名称 = 姓 .. 单字[引擎.取随机整数(1,#单字)]
  else
      名称 = 姓 .. 单字[引擎.取随机整数(1,#单字)] .. 单字[引擎.取随机整数(1,#单字)]
  end
  return 名称
end


return 场景类_创建