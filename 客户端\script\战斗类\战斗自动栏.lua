--======================================================================--
-- @作者: GGE研究群: 342119466
-- @创建时间:   2018-03-03 02:34:19
-- @Last Modified time: 2025-01-10 16:51:16
--======================================================================--
local 场景类_战斗自动栏 = class()
local 状态 = "取消"
local 控制状态 = "关闭"
local 开启自动 = false
local format = string.format
local tp,zts

function 场景类_战斗自动栏:初始化(根)
	self.ID = 59
	self.x = 3
	self.y = 100
	self.xx = 0
	self.yy = 0
	self.注释 = "战斗"
	self.可视 = false
	self.可视化 = false
	self.鼠标 = false
	self.焦点 = false
	self.可移动 = true
	self.窗口时间 = 0
	tp = 根
	local 资源 = 根.资源
	local 按钮 = 根._按钮
	local 自适应 = 根._自适应
	self.资源组 = {
		[1] = 自适应.创建(0,1,255,180,3,9),
		[13] = 按钮.创建(自适应.创建(12,4,60,22,1,3),0,0,4,true,true," 自动"),
		[14] = 按钮.创建(自适应.创建(12,4,60,22,1,3),0,0,4,true,true," 攻击"),
		[15] = 按钮.创建(自适应.创建(12,4,60,22,1,3),0,0,4,true,true," 防御"),
		[16] = 按钮.创建(自适应.创建(12,4,60,22,1,3),0,0,4,true,true," 自动"),
		[17] = 按钮.创建(自适应.创建(12,4,60,22,1,3),0,0,4,true,true," 攻击"),
		[18] = 按钮.创建(自适应.创建(12,4,60,22,1,3),0,0,4,true,true," 防御"),
		[19] = 按钮.创建(自适应.创建(12,4,60,22,1,3),0,0,4,true,true," 自动"),
		[20] = 按钮.创建(自适应.创建(12,4,60,22,1,3),0,0,4,true,true," 攻击"),
		[21] = 按钮.创建(自适应.创建(12,4,60,22,1,3),0,0,4,true,true," 防御"),
		[22] = 按钮.创建(自适应.创建(12,4,60,22,1,3),0,0,4,true,true," 自动"),
		[23] = 按钮.创建(自适应.创建(12,4,60,22,1,3),0,0,4,true,true," 攻击"),
		[24] = 按钮.创建(自适应.创建(12,4,60,22,1,3),0,0,4,true,true," 防御"),
		[6] = 资源:载入('wzife.wdf',"网易WDF动画",0x363AAF1B),
		[7] = tp.技能格子背景_,
		[50] = 资源:载入('wzife.wdf',"网易WDF动画",0X2E8758EE),--条框
	}
	self.人物技能={}
	self.宝宝技能={}
	for n=1,5 do
                   self.人物技能[n]=根._技能格子(0,0,i,"辅助技能")
                   self.人物技能[n]:置技能()
                   self.宝宝技能[n]=根._技能格子(0,0,i,"辅助技能")
                   self.宝宝技能[n]:置技能()
	end
	for i=13,24 do
		self.资源组[i]:绑定窗口_(self.ID)
	end
	tp = 根
	zts = tp.字体表.猫猫字体1
	self.第一次 = false
	self.加入=0
	self.自动数据=nil
end

function 场景类_战斗自动栏:打开()
	if self.可视化 then
	   self.可视化 = false
	else
	    self.加入=0
	    self.可视化 = true
	    tp.运行时间 = tp.运行时间 + 1
	    self.窗口时间 = tp.运行时间

	end
end



function 场景类_战斗自动栏:显示(dt,x,y)

	if not self.可视化 then
	   return
	end
	if not self.自动数据 then
                 return
	end
	--self:取自动状态()
	self.焦点 = false
	self.资源组[1]:显示(self.x,self.y)
	zts:置颜色(白色)
	if 引擎.取鼠标滚轮()>0 then
                 self.加入=(self.加入-1)<0 and 0 or self.加入-1
	elseif 引擎.取鼠标滚轮()<0 then
	    self.加入=(self.加入+1)>(#self.自动数据-2) and #self.自动数据-2 or self.加入+1
	    if #self.自动数据==1 then
                     self.加入=0
	    end
	end
             for n=1,#self.自动数据>=2 and 2 or #self.自动数据 do
                 local 序列=self.加入+n
            	    local 差距y=n*160-160
	     tp.人物头像背景_:显示(self.x+10,self.y+25+差距y)
	     self.自动数据[序列][1].头像:显示(self.x+13,self.y+28+差距y)
	     for n=1,3 do
       	           self.资源组[50]:显示(self.x+64,self.y+28+n*15-15+差距y)
       	           self.自动数据[序列][1][50+n]:显示(self.x+64+12,self.y+31+n*15-15+差距y)
       	           if self.自动数据[序列][1][51]:是否选中(x,y) then
		 tp.提示:自定义(x-42,y+27,format("#Y/气血：%d/%d",self.自动数据[序列][1].气血,self.自动数据[序列][1].最大气血))
	           elseif self.自动数据[序列][1][52]:是否选中(x,y) then
		 tp.提示:自定义(x-42,y+27,format("#Y/魔法：%d/%d",self.自动数据[序列][1].魔法,self.自动数据[序列][1].最大魔法))
	           elseif self.自动数据[序列][1][53]:是否选中(x,y) then
		 tp.提示:自定义(x-42,y+27,format("#Y/愤怒：%d/150",self.自动数据[序列][1].愤怒))
       	           end
                  end
       	     self.自动数据[序列][1][51]:置区域(0,0,math.min(math.floor(self.自动数据[序列][1].气血 / self.自动数据[序列][1].最大气血 * 50),50),8)
       	     self.自动数据[序列][1][52]:置区域(0,0,math.min(math.floor(self.自动数据[序列][1].魔法 / self.自动数据[序列][1].最大魔法 * 50),50),8)
       	     self.自动数据[序列][1][53]:置区域(0,0,math.min(math.floor(self.自动数据[序列][1].愤怒 / 150 * 50),50),8)
	     self.资源组[6]:显示(self.x+135,self.y+37+差距y)
	     for n=1,2 do
       	           self.资源组[50]:显示(self.x+177,self.y+45+n*15-15+差距y)
   	           if self.自动数据[序列][2] then
       	               self.自动数据[序列][2][50+n]:显示(self.x+177+12,self.y+48+n*15-15+差距y)
       	               if self.自动数据[序列][2][51]:是否选中(x,y) then
		     tp.提示:自定义(x-42,y+27,format("#Y/气血：%d/%d",self.自动数据[序列][1].气血,self.自动数据[序列][1].最大气血))
	               elseif self.自动数据[序列][2][52]:是否选中(x,y) then
		     tp.提示:自定义(x-42,y+27,format("#Y/魔法：%d/%d",self.自动数据[序列][1].魔法,self.自动数据[序列][1].最大魔法))
       	               end
       	           end
                  end
	     if self.自动数据[序列][2] then
	        self.自动数据[序列][2].头像:显示(self.x+138,self.y+40+差距y)
       	        self.自动数据[序列][2][51]:置区域(0,0,math.min(math.floor(self.自动数据[序列][2].气血 / self.自动数据[序列][2].最大气血 * 50),50),8)
       	        self.自动数据[序列][2][52]:置区域(0,0,math.min(math.floor(self.自动数据[序列][2].魔法 / self.自动数据[序列][2].最大魔法 * 50),50),8)
	     end
	     zts:置颜色(黄色)

	     if self.自动数据[序列][1].指令 then
		     if  type(self.自动数据[序列][1].指令.参数) == "table"   then
	            			if self.自动数据[序列][1].指令.参数[1] and self.自动数据[序列][1].指令.参数[1][1] then
	            				--print(self.自动数据[序列][1].指令.参数[1][1])
	            				--self.自动数据[序列][1].指令.参数=nil
	            				self.自动数据[序列][1].指令.参数=self.自动数据[序列][1].指令.参数[1][1]
	            			end
	            	    else

	            	    end
            	   end

	     zts:显示(self.x + 15,self.y + 80+差距y,self.自动数据[序列][1].名称.."："..(self.自动数据[序列][1].指令 and ((self.自动数据[序列][1].指令.类型=="法术" and self.自动数据[序列][1].指令.参数) or (self.自动数据[序列][1].指令.类型=="攻击" and "攻击") or (self.自动数据[序列][1].指令.类型=="防御" and "防御") or "未设置") or "未设置"))
	     zts:显示(self.x + 140,self.y + 80+差距y,self.自动数据[序列][2] and self.自动数据[序列][2].名称.."："..(self.自动数据[序列][2].指令 and ((self.自动数据[序列][2].指令.类型=="法术" and self.自动数据[序列][2].指令.参数) or (self.自动数据[序列][2].指令.类型=="攻击" and "攻击") or (self.自动数据[序列][2].指令.类型=="防御" and "防御") or "未设置") or "未设置") or "暂未参战")
                  self.资源组[7]:显示(self.x+10,self.y+100+差距y)
	     self.人物技能[序列]:置坐标(self.x+11,self.y+101+差距y)
	     self.人物技能[序列]:显示(x,y,self.鼠标)
	     if self.人物技能[序列] and (self.人物技能[序列].事件 or (self.资源组[7]:是否选中(x,y) and 引擎.鼠标弹起(0))) then
                      战斗类.战斗指令.法术界面:打开(self.自动数据[序列][1].技能,"法术","人物",nil,{1,self.自动数据[序列][1].id,self.自动数据[序列][1].位置})
	         tp.鼠标.置鼠标("普通")
	     end
	     self.资源组[14+n*6-6]:更新1(x,y)
	     self.资源组[15+n*6-6]:更新1(x,y)
	     self.资源组[13+n*6-6]:更新1(x,y)
	     self.资源组[14+n*6-6]:显示(self.x+65,self.y+98+差距y)
	     self.资源组[15+n*6-6]:显示(self.x+65,self.y+123+差距y)
	     self.资源组[13+n*6-6]:显示(self.x+100,self.y+150+差距y)
	     self.资源组[13+n*6-6]:置文字(self.自动数据[序列][1].自动战斗 and " 手动" or " 自动")
	     if self.资源组[14+n*6-6]:事件判断() then
                      发送数据(5601,{类型="攻击",目标=self.自动数据[序列][1].位置,敌我=0,参数="",附加="",玩家id=self.自动数据[序列][1].id,操作类型=1})
                  elseif self.资源组[15+n*6-6]:事件判断() then
                      发送数据(5601,{类型="防御",目标=self.自动数据[序列][1].位置,敌我=0,参数="",附加="",玩家id=self.自动数据[序列][1].id,操作类型=1})
                  elseif self.资源组[13+n*6-6]:事件判断() then
                  	if self.自动数据[序列][2] ~=nil  then
                  		发送数据(5602,{位置=self.自动数据[序列][1].位置,宠物位置=self.自动数据[序列][2].位置,玩家id=self.自动数据[序列][1].id,操作类型=1})
                  	else
                      		发送数据(5602,{位置=self.自动数据[序列][1].位置,玩家id=self.自动数据[序列][1].id,操作类型=1})
                  	end
	     end

                  self.资源组[7]:显示(self.x+135,self.y+100+差距y)
	     self.宝宝技能[序列]:置坐标(self.x+136,self.y+101+差距y)
	     self.宝宝技能[序列]:显示(x,y,self.鼠标)
	     if self.自动数据[序列][2] and self.宝宝技能[序列] and (self.宝宝技能[序列].事件 or (self.资源组[7]:是否选中(x,y) and 引擎.鼠标弹起(0))) then
                      战斗类.战斗指令.法术界面:打开(self.自动数据[序列][2].技能,"法术","宠物",nil,{2,self.自动数据[序列][2].id,self.自动数据[序列][2].位置})
	         tp.鼠标.置鼠标("普通")
	     end
	     self.资源组[17+n*6-6]:更新1(x,y)
	     self.资源组[18+n*6-6]:更新1(x,y)
	     self.资源组[16+n*6-6]:更新1(x,y)
	     self.资源组[17+n*6-6]:显示(self.x+190,self.y+98+差距y)
	     self.资源组[18+n*6-6]:显示(self.x+190,self.y+123+差距y)
	     if self.资源组[17+n*6-6]:事件判断() and self.自动数据[序列][2] then
                      发送数据(5601,{类型="攻击",目标=self.自动数据[序列][2].位置,敌我=0,参数="",附加="",玩家id=self.自动数据[序列][2].id,操作类型=2})
                  elseif self.资源组[18+n*6-6]:事件判断() and self.自动数据[序列][2] then
                      发送数据(5601,{类型="防御",目标=self.自动数据[序列][2].位置,敌我=0,参数="",附加="",玩家id=self.自动数据[序列][2].id,操作类型=2})
	     end
             end

	-- zts:显示(self.x + 66,self.y + 33,"剩余   回合")
	-- zts:显示(self.x + 11,self.y + 54,"人物：         召唤兽：")

	-- zts:置颜色(黄色)
	-- zts:显示(self.x + 92,self.y + 33,"∞")
	-- zts:显示(self.x + 46,self.y + 54,self:取自动语句(1))
	-- zts:显示(self.x + 147,self.y + 54,self:取自动语句(2))
	引擎.场景.字体表.标题字体:显示(self.x+self.资源组[1].宽度/2,self.y+3,"自动战斗")
	-- zts:显示(self.x + 16,self.y + 92,"自动战斗：")
	-- zts:显示(self.x + 16,self.y + 119,"队长控制：")
             if #self.自动数据>1 then
                zts:显示(self.x + 15,self.y + 340,"操作总数:"..(#self.自动数据).."个  当前操作的为:第"..(self.加入+1).."个、第"..(self.加入+2).."个")
   	   zts:置颜色(白色)
                zts:显示(self.x + 37,self.y + 355,"滑动鼠标滚轮,可操作其他操作对象")
             end
	--if 战斗类.战斗指令.法术开关 and not 战斗类.战斗指令.命令版面 then
	战斗类.战斗指令.法术界面:显示(dt,x,y)
	--end
	if self.鼠标 then
		-- if self.资源组[5]:事件判断() then
		-- 	发送数据(5507)
		-- -- elseif self.资源组[4]:事件判断() then --队长控制
		-- -- 	发送数据(5521)
		-- end
	end
	-- for i=3,5 do
	-- 	self.资源组[i]:更新1(x,y)
	-- 	self.资源组[i]:显示(self.x+6+65*(i-3),self.y+78)
	-- end

	if tp.按钮焦点 then
		self.焦点 = true
	end
	if 引擎.鼠标弹起(1) and not tp.禁止关闭 and self.鼠标 then
	 	if self.状态==" 取消" then --有自动战斗时候，直接取消自动战斗
	 		发送数据(5507)
	 		战斗类.自动开关=false
	 		return
	 	end
		战斗类.自动开关=false --没有的时候直接关闭
		return
	end
end


function 场景类_战斗自动栏:刷新数据()
      self.自动数据={}
      for n=1,#战斗类.自动数据 do
            local 临时数据=战斗类.自动数据
            self.自动数据[n]={}
            self.自动数据[n][1]=战斗类.自动数据[n][1]
            local x=引擎.取头像(临时数据[n][1].模型)
            self.自动数据[n][1].头像=tp.资源:载入(x[7],"网易WDF动画",x[2])
            self.自动数据[n][1][51] = tp.资源:载入('wzife.wdf',"网易WDF动画",0xAAD44583)
            self.自动数据[n][1][52] = tp.资源:载入('wzife.wdf',"网易WDF动画",0xCE4D3C2D)
            self.自动数据[n][1][53] = tp.资源:载入('wzife.wdf',"网易WDF动画",0xBAF8009F)
            self.人物技能[n]:置技能()
            self.宝宝技能[n]:置技能()
            if 临时数据[n][1].指令 and 临时数据[n][1].指令.类型=="法术" then
            		if  type(临时数据[n][1].指令.参数) == "table"   then
            			if 临时数据[n][1].指令.参数[1] and 临时数据[n][1].指令.参数[1][1] then
            				self.人物技能[n]:置技能({名称=临时数据[n][1].指令.参数[1][1]})
            			end
            		else
               	self.人物技能[n]:置技能({名称=临时数据[n][1].指令.参数})
           		end
            end
            if 战斗类.自动数据[n][2] then
               self.自动数据[n][2]=战斗类.自动数据[n][2]
               local x=引擎.取头像(临时数据[n][2].模型)
               self.自动数据[n][2].头像=tp.资源:载入(x[7],"网易WDF动画",x[1])
               self.自动数据[n][2][51] = tp.资源:载入('wzife.wdf',"网易WDF动画",0xAAD44583)
               self.自动数据[n][2][52] = tp.资源:载入('wzife.wdf',"网易WDF动画",0xCE4D3C2D)
               if 临时数据[n][2].指令 and 临时数据[n][2].指令.类型=="法术" then
                  self.宝宝技能[n]:置技能({名称=临时数据[n][2].指令.参数})
               end
            end
      end
            --print(#self.自动数据)
      local 高度=180
      高度=(#self.自动数据>1) and 高度+35 or 高度
      for n=2,#self.自动数据 do
            高度=高度+160
            if 高度>=375 then
               高度=375
            end
      end
      self.资源组[1] = tp._自适应(0,1,255,高度,3,9)
end

function 场景类_战斗自动栏:检查点(x,y)
	if self.资源组[1]:是否选中(x,y)  then
	 --local xy={x=x-self.x,y=y-self.y}
	--self.x,self.y=x,y
		return true
	end
	return false
end

function 场景类_战斗自动栏:初始移动(x,y)
	tp.运行时间 = tp.运行时间 + 1
	if not self.第一次 then
		tp.运行时间 = tp.运行时间 + 2
		self.第一次 = true
	end
	if not 引擎.场景.消息栏焦点 then
		self.窗口时间 = tp.运行时间
	end
	if not self.焦点 then
		tp.场景.战斗.移动窗口 = true
	end
	if self.可视化 and self.鼠标 and not self.焦点 then
		self.xx = x - self.x
		self.yy = y - self.y
	end
end

function 场景类_战斗自动栏:开始移动(x,y)
	if self.可视化 and self.鼠标 then
		self.x = x - self.xx
		self.y = y - self.yy
	end
end




function 场景类_战斗自动栏:取自动语句(类型)
 	if 类型==1 then --人物
	 	local 编号=0
	 	for n=1,#战斗类.战斗单位[ljcs] do
		 	if 战斗类.战斗单位[ljcs][n].数据.类型=="角色" and 战斗类.战斗单位[ljcs][n].数据.id==tp.队伍[1].数字id then
			 	编号=n
			end
		end
	  	local 语句="攻击"
	  	if 编号==0 then return 语句 end
 		if 战斗类.战斗单位[ljcs][编号].数据.自动指令~=nil then
	 		if 战斗类.战斗单位[ljcs][编号].数据.自动指令.类型=="法术" then
		 		语句=战斗类.战斗单位[ljcs][编号].数据.自动指令.参数
			else
				语句=战斗类.战斗单位[ljcs][编号].数据.自动指令.类型
		 	end
	 	end
	   	return 语句
   	elseif 类型==2 then --人物
	 	local 编号=0
	 	for n=1,#战斗类.战斗单位[ljcs] do
		 	if 战斗类.战斗单位[ljcs][n].数据.类型=="bb" and 战斗类.战斗单位[ljcs][n].数据.id==tp.队伍[1].数字id then
			 	编号=n
			end
		end
	  	if 编号==0 then return "无" end
	  	local 语句="攻击"
	 	if 战斗类.战斗单位[ljcs][编号].数据.自动指令~=nil then
		 	if 战斗类.战斗单位[ljcs][编号].数据.自动指令.类型=="法术" then
			 	语句=战斗类.战斗单位[ljcs][编号].数据.自动指令.参数
			else
			 	语句=战斗类.战斗单位[ljcs][编号].数据.自动指令.类型
			 end
		end
	   	return 语句
	end
end

function 场景类_战斗自动栏:取自动状态()
	状态=" 开启"
	 -- 控制状态 = "开启"
  	for n=1,#战斗类.战斗单位[ljcs] do
		if 战斗类.战斗单位[ljcs][n].数据.类型=="角色" and 战斗类.战斗单位[ljcs][n].数据.id==tp.队伍[1].数字id then
			if 战斗类.战斗单位[ljcs][n].数据.自动战斗 then
				状态=" 取消"
			end
			-- if 战斗类.战斗单位[ljcs][n].数据.队长控制 then
			-- 	控制状态 = "关闭"
			-- end
		end
	end
	-- if self.资源组[5].按钮文字~=状态 then
	-- 	self.资源组[5]:置文字(状态)
	-- end
	self.状态=状态
	-- if self.资源组[4].按钮文字~=控制状态 then
	-- 	  self.资源组[4]:置文字(控制状态)
	-- end
 end
return 场景类_战斗自动栏