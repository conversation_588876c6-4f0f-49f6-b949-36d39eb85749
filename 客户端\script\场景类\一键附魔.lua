--======================================================================--
-- @作者: www.wnfj.com，联系QQ：79550111
-- @创建时间:   2018-03-03 02:34:19
-- @Last Modified time: 2024-12-05 17:26:06
--======================================================================--
local 场景类_一键附魔 = class()
local sd =0
local sd1 =0
local tp,zts,zts1
local floor = math.floor
local tonumber = tonumber
local tostring = tostring
local insert = table.insert
local 图像类 = require("gge图像类1")
local 矩阵 = require("gge包围盒")(0,0,71,22)

function 场景类_一键附魔:初始化(根)
	self.ID = 168
	self.x = 280    --+(全局游戏宽度-800)/2
	self.y = 120
	self.xx = 0
	self.yy = 0
	self.注释 = "一键附魔"
	self.可视 = false
	self.鼠标 = false
	self.焦点 = false
	self.可移动 = true
	tp = 根
	local 资源 = tp.资源
	local 按钮 = 根._按钮
	local 自适应 = 根._自适应
	local 资源 = 根.资源
	self.资源组 = {
		[1] = 自适应.创建(99,1,480,381,3,9),
		[2] = 按钮.创建(自适应.创建(12,4,100,20,1,3),0,0,4,true,true,"仙玉打符"),
		[3] = 按钮.创建(自适应.创建(12,4,100,20,1,3),0,0,4,true,true,"银子打符"),
		[4] = 自适应.创建(93,1,85,22,1,3),

	}
	self.附魔状态 = {}
	self.附魔选中 = {}
	for n=1,18 do
	--self.附魔选中[n] = 按钮.创建(自适应.创建(30,4,26,26,4,1),0,0,5,true,true)
	self.附魔选中[n] = 按钮.创建(资源:载入('aaa.wdf',"网易WDF动画",0x1343E14),0,0,4,true,true)
	self.附魔选中[n]:绑定窗口_(self.ID)
	self.附魔选中[n].允许再次点击 = true
	self.附魔状态[n] = false
	end


	for n=2,3 do
	   self.资源组[n]:绑定窗口_(self.ID)
	   self.资源组[n]:置偏移(13,-2)
	end

	self.窗口时间 = 0
	tp = 根
	zts = tp.字体表.普通字体__
	zts1 = tp.字体表.描边字体
	self.进程 = 1
	self.胜利 = 3
	self.点击 = false
	self.附魔数 = 0
	self.颜色 = -16466190
	self.偏移 = 0


end



function 场景类_一键附魔:打开(数据)
	if self.可视 then
		self.可视 = false
		self.进程 = 1
		self.胜利 = 3
		self.点击 = false
		self.颜色 = -16466190
	    self.偏移 = 0


	else

		self.数据=数据
		insert(tp.窗口_,self)
	    tp.运行时间 = tp.运行时间 + 1
	  	self.窗口时间 = tp.运行时间
	    self.可视 = true
		self.点击 = false
		self.颜色 = -16466190
	    self.偏移 = 0
		self.属性 = {"伤害","力量","命中","体质","速度","耐力","愤怒","防御","魔力","气血","法术防御","法术伤害","魔法","气血回复效果","法伤结果","物伤结果"}--"固定伤害","气血回复效果","封印命中等级","抵抗封印等级"               --魔法替换了固伤           法伤结果替换封印命中等级    物伤结果替换抵抗封印等级
		self.属性值 = {}

		self.附魔状态 = self.数据.锁定附魔
		self.起始时间 = os.time()
		self.上限表=self.数据.上限表
		self.附魔基数=self.数据.上限表.附魔基数
		self.仙玉消耗=self.数据.上限表.仙玉消耗
		self.银子消耗=数额尾数转换(self.数据.上限表.银子消耗)
		self.提示文字 = "每项属性附魔费用为【银子："..self.银子消耗.."】或【仙玉："..self.仙玉消耗.."】"
		self.提示文字1 = "效果达到最大值时将自动取消选中"

	end
end
function 场景类_一键附魔:刷新(数据)

self.数据=数据
self.附魔状态 = self.数据.锁定附魔


end

function 场景类_一键附魔:是否最大(类型,数值)

if 数值 == nil then
 数值 = 0
end

local 上限表 = {
伤害 = math.floor(self.附魔基数 *self.上限表.伤害大) ,
力量 = math.floor(self.附魔基数 *self.上限表.力量大),
命中 = math.floor(self.附魔基数 *self.上限表.命中大),
体质 = math.floor(self.附魔基数 *self.上限表.体质大),
速度 = math.floor(self.附魔基数 *self.上限表.速度大),
耐力 = math.floor(self.附魔基数 *self.上限表.耐力大),
愤怒 = math.floor(self.附魔基数 *self.上限表.愤怒大),
防御 = math.floor(self.附魔基数 *self.上限表.防御大),
魔力 = math.floor(self.附魔基数 *self.上限表.魔力大),
气血 = math.floor(self.附魔基数 *self.上限表.气血大),
法术防御 = math.floor(self.附魔基数 *self.上限表.法术防御大),
法术伤害 =math.floor(self.附魔基数 *self.上限表.法术伤害大),
--固定伤害 =math.floor(180 *0.5),
魔法 = math.floor(self.附魔基数 *self.上限表.魔法大),
气血回复效果 =math.floor(self.附魔基数 *self.上限表.气血回复效果大),
法伤结果 = math.floor(self.附魔基数 *self.上限表.法伤结果大),
--封印命中等级 =math.floor(180 *0.5),
物伤结果 = math.floor(self.附魔基数 *self.上限表.物伤结果大)
--抵抗封印等级 =math.floor(180 *0.5),

}


if 数值 == 上限表[类型] then
return true
end

return false
end




function 场景类_一键附魔:显示(dt,x,y)
	self.焦点 = false

	--self.资源组[2]:更新(x,y)
	self.资源组[2]:更新(x,y)
	self.资源组[3]:更新(x,y)
	self.资源组[1]:显示(self.x,self.y)
	--self.资源组[2]:显示(self.x+445,self.y+3)
	self.资源组[2]:显示(self.x+75+50,self.y+285)
	self.资源组[3]:显示(self.x+205+50,self.y+285)
	tp.字体表.普通字体:置颜色(0xFFFFFFFF):显示(self.x+210,self.y+3,self.注释)
	tp.字体表.普通字体:置颜色(-256):显示(self.x+127,self.y+315,"附魔加成效果与门派强化符效果一致")
	tp.字体表.普通字体:置颜色(-256):显示(self.x+80,self.y+335,self.提示文字)
	tp.字体表.普通字体:置颜色(-256):显示(self.x+135,self.y+355,self.提示文字1)

	  for i=1,6 do
	    if self.数据.物品数据[i]~=nil  then
	    	for k,v in pairs(self.数据.物品数据[i]) do
	    		if v.数值~=0 then
		    		self.属性值[k]=v.数值
		    	end
	    	end
		 end
	   end

	local xx = 0
	local yy = 0
	for n=1,#self.属性 do

        self.资源组[4]:显示(self.x+150+xx*220,self.y+35+30*yy)
        tp.字体表.普通字体:置颜色(0xFFFFFFFF):显示(self.x+50+xx*220,self.y+40+30*yy ,self.属性[n])
	    tp.字体表.普通字体:置颜色(0xFF0000ff):显示(self.x+160+xx*220,self.y+40+30*yy ,"+"..(self.属性值[self.属性[n]] or 0))

		if self:是否最大(self.属性[n],self.属性值[self.属性[n]] or 0) then
		 tp.字体表.普通字体:置颜色(0xFFFFFF00):显示(self.x+190+xx*220,self.y+40+30*yy ,"(最大)")
		 self.附魔状态[n] = false
		end

		self.附魔选中[n]:更新(x,y,self.附魔状态[n] ~= true)
		self.附魔选中[n]:显示(self.x+18+xx*220,self.y+33+30*yy,true,nil,nil,self.附魔状态[n],2)
	           if self.附魔选中[n]:事件判断() then
		          self.附魔状态[n] = not self.附魔状态[n]
		            if self:是否最大(self.属性[n],self.属性值[self.属性[n]] or 0) then
		               self.附魔状态[n] = false
					    tp.提示:写入("#Y/已经是最大值无需附魔")
		            end

				  if self.附魔状态[n] then
				      发送数据(114,{文本=n})
					elseif self.附魔状态[n]==false then
				      	发送数据(112,{文本=n})

		           end
				 end

	    xx = xx + 1
		if xx == 2 then
			xx = 0
			yy = yy + 1
		end

    end

	if self.资源组[2]:事件判断() then

	    if os.time() -  self.起始时间 < 1 then
		     tp.提示:写入("#Y/请不要频繁点击,间隔1秒再试")
		  return
		end

	             for n=1,#self.属性 do
		            if self:是否最大(self.属性[n],self.属性值[self.属性[n]] or 0) then
	        发送数据(112,{文本=n})
		              -- 客户端:发送数据(2, 265, 13, n, 1)
		            end
				  end
	       self.起始时间 = os.time()
	        发送数据(113,{货币="仙玉"})
	elseif self.资源组[3]:事件判断() then

	    if os.time() -  self.起始时间 < 1 then
		     tp.提示:写入("#Y/请不要频繁点击,间隔1秒再试")
		  return
		end

	             for n=1,#self.属性 do
		            if self:是否最大(self.属性[n],self.属性值[self.属性[n]] or 0) then
	        发送数据(112,{文本=n})
		              -- 客户端:发送数据(2, 265, 13, n, 1)
		            end
				  end
	       self.起始时间 = os.time()
	        发送数据(113,{货币="银子"})

    end
end

function 场景类_一键附魔:检查点(x,y)
	if self.资源组[1]:是否选中(x,y)  then
		return true
	end
end

function 场景类_一键附魔:初始移动(x,y)
	tp.运行时间 = tp.运行时间 + 1
	if not 引擎.消息栏焦点 then
  		self.窗口时间 = tp.运行时间
 	end
	if not self.焦点 then
		tp.移动窗口 = true
	end
	if self.鼠标 and  not self.焦点 then
		self.xx = x - self.x
		self.yy = y - self.y
	end
end

function 场景类_一键附魔:开始移动(x,y)
	if self.鼠标 then
		self.x = x - self.xx
		self.y = y - self.yy
	end
end
return 场景类_一键附魔