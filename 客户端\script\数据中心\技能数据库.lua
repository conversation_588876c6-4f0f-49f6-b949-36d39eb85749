local 技能数据库 = class()
local qz=math.floor
--1.前置，2加速，3，偏移坐标，4，是否透明，5，全屏，6，地址，7，文件，8，音效，
--1.地址，2文件，3前置，4加速
--  self.数据["龙卷雨击"]={
--      大图标=
--     小图标=
--     是否全屏=
--     特效=
--   音效=
-- 偏移=
-- }
function 技能数据库:初始化()
	self["龙卷雨击"]={
	    大图标=201672841,
		小图标=842454497,
		图标文件="wzife.wdf",
		偏移


	}
	self["龙卷雨击21"]={
		大图标={201672841,"wzife.wdf"},
		小图标={842454497,"wzife.wdf"},
		是否全屏=1,
		特效=1,
		音效=1,
		偏移=1,
	}
-- self["龙卷雨击1"]={
--      大图标=1,
--     小图标=1,
--     是否全屏=1,
--     特效=1,
--   音效=1,
-- 偏移=1,
-- }
	-- self.数据={
	-- ----------------------------状态----------------------------
	-- --法宝====================================================
	-- 	苍白纸人 = {
	-- 	    地址=0x899DFF11,
	-- 	    资源="magic.wdf",
	-- 	},
	-- 	状态_苍白纸人_我方 = {
	-- 	    地址=0x592ebd89,
	-- 	    资源="waddon.wdf",
	-- 	    前置=true,
	-- 	},
	-- 	状态_苍白纸人_敌方 = {
	-- 	    地址=0x2f45b9f0,
	-- 	    资源="waddon.wdf",
	-- 	    前置=true,
	-- 	},
	-- 	乾坤玄火塔 = {
	-- 	    地址=0xE4A2B66E,
	-- 	    资源="magic.wdf",
	-- 	},
	-- 	状态_乾坤玄火塔_我方 = {
	-- 	    地址=0xc236bf21,
	-- 	    资源="waddon.wdf",
	-- 	},
	-- 	状态_乾坤玄火塔_敌方 = {
	-- 	    地址=0x325a1f6a,
	-- 	    资源="waddon.wdf",

	-- 	},
	-- 	干将莫邪_敌方 = {
	-- 	    地址=0x47F867FF,
	-- 	    资源="magic.wdf",
	-- 	},
	-- 	干将莫邪_我方 = {
	-- 	    地址=0x7838CE56,
	-- 	    资源="magic.wdf",
	-- 	},
	-- 	状态_干将莫邪_我方 = {
	-- 	    地址=0x7838ce56,
	-- 	    资源="waddon.wdf",
	-- 	},
	-- 	状态_干将莫邪_敌方 = {
	-- 	    地址=0x47f867ff,
	-- 	    资源="waddon.wdf",
	-- 	},
-- }
end

return 技能数据库
