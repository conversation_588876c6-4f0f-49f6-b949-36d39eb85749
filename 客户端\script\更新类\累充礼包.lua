--======================================================================--

--======================================================================--
local 累充礼包 = class()
local tp,zts2
local insert = table.insert


function 累充礼包:初始化(根)
	self.ID = 134
	self.x = 40+(全局游戏宽度-800)/2
	self.y = 160--全局游戏高度/2-160
	self.xx = 0
	self.yy = 0
	self.注释 = "福利中心"
	self.可视 = false
	self.鼠标 = false
	self.焦点 = false
	self.可移动 = true
	tp = 根
	self.窗口时间 = 0
	zts = tp.字体表.普通字体
	zts1 = tp.字体表.汉仪字体4
	zts2 = tp.字体表.描边自定
	self.进程=1
	self.选中 = 0
	self.加入 = 0
	self.选中状态 = 0
	self.VIP经验=0
end

function 累充礼包:打开(内容)
	--table.print(内容)
	if self.可视 then
		self.可视 = false
		self.资源组=nil
		self.物品组={}
		self.物品组1={}
		self.签到物品组 = {}
		self.加入 = 0
		return
	else
		insert(tp.窗口_,self)
		local 资源 = tp.资源
		local 按钮 = tp._按钮
		local 自适应 = tp._自适应
		self.资源组 = {
			--[1] = 自适应.创建(0,1,497,440,3,9),
			--资源:载入('pic/躲避.png',"图片")
			[1] = 自适应.创建(0,1,700,355,3,9),
			[2] = 按钮.创建(自适应.创建(18,4,16,16,4,3),0,0,4,true,true),--关闭
			[3] = 按钮.创建(tp.资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0x10000021),0,0,4,true,true,"领取奖励"),
			[4] = 按钮.创建(tp.资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0x10000021),0,0,4,true,true,"领取奖励"),
			[5] = 按钮.创建(tp.资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0x10000021),0,0,4,true,true,"领取奖励"),
			[6] = 按钮.创建(tp.资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0x10000021),0,0,4,true,true,"领取奖励"),
			[7] = 按钮.创建(tp.资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0x10000021),0,0,4,true,true,"领取奖励"),
			[8] = 资源:载入('nice.wdf',"网易WDF动画",0x0683C414), --已领取
			[9] = 资源:载入('wdf/vvxxzcom/定制背景.png',"图片"),
			[10] = 按钮.创建(tp.资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0xAABB0141),0,0,1,true,true,"自动抓鬼"),
			[11] = 按钮.创建(tp.资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0xAABB0141),0,0,1,true,true,"自动鬼王"),
			[12] = 按钮.创建(tp.资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0xAABB0141),0,0,1,true,true,"七日签到"),
			--[13] = 按钮.创建(自适应.创建(12,4,60,22,1,3),0,0,4,true,true,"购买赞助"),
			[13] = 按钮.创建(tp.资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0xAABB0141),0,0,1,true,true,"购买赞助"),
			[14] = 资源:载入('wdf/vvxxzcom/定制背景.png',"图片"),
			[15] =  按钮.创建(资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0x10000010),0,0,4,true,true),   --VIP1
			[16] =  按钮.创建(资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0x10000011),0,0,4,true,true),   --VIP2
			[17] =  按钮.创建(资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0x10000012),0,0,4,true,true),   --VIP3
			[18] =  按钮.创建(资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0x10000013),0,0,4,true,true),   --VIP4
			[19] =  按钮.创建(资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0x10000014),0,0,4,true,true),   --VIP5
			[20] =  按钮.创建(资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0x10000015),0,0,4,true,true),   --VIP6
			[21] =  按钮.创建(资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0x10000016),0,0,4,true,true),   --VIP7
			[22] = 资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0xAABBCC87),     --VIP图标
			[23] = 资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0xAABBCC88),
			[24] = 资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0xAABBCC89),
			[25] = 资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0xAABBCC90),
			[26] = 资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0xAABBCC91),
			[27] = 资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0xAABBCC92),
			[28] = 资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0xAABBCC93),
			[29] = 按钮.创建(tp.资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0x10000021),0,0,4,true,true,"领取奖励"),
			[30] = 按钮.创建(tp.资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0x10000021),0,0,4,true,true,"领取奖励"),
			[31] = 按钮.创建(tp.资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0xAABB0141),0,0,1,true,true,"累充奖励"),
			[32] = 按钮.创建(tp.资源:载入('wzife.wdf',"网易WDF动画",0x48D2590A),0,0,4,true,true,"开启抓鬼"),
			[33] = 按钮.创建(tp.资源:载入('wzife.wdf',"网易WDF动画",0x48D2590A),0,0,4,true,true,"开启鬼王"),
			[34] = 按钮.创建(tp.资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0xAABB0141),0,0,1,true,true,"会员特权"),
			[35] = 按钮.创建(tp.资源:载入('wzife.wdf',"网易WDF动画",0x48D2590A),0,0,4,true,true,"每日领奖"),
			[36] = 资源:载入('wdf/vvxxzcom/VIP底板.png',"图片"),
			[37] = 资源:载入('wdf/vvxxzcom/小标题3.png',"图片"),
			[38] = 资源:载入('wdf/vvxxzcom/vip小图标.png',"图片"),
			[39] = 资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0x00000167),
			[40] = 资源:载入('wdf/vvxxzcom/进度条1.png',"图片"),
			[41] = 资源:载入('wdf/vvxxzcom/VIP底板1.png',"图片"),
			[42] = 资源:载入('wdf/vvxxzcom/VIP底板2.png',"图片"),
			[43] = 按钮.创建(tp.资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0x10000021),0,0,4,true,true,"提升等级"),
			[44] = 资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0xAABB0143),
			[45] = 资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0xAABB0145),
			[46] =  按钮.创建(资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0xAABB0142),0,0,1,true,true),
			[47] =  按钮.创建(资源:载入('nice.wdf',"网易WDF动画",1238408331),0,0,4,true,true),
			[48] =  按钮.创建(资源:载入('nice.wdf',"网易WDF动画",1238408331),0,0,4,true,true),
			[49] =  按钮.创建(资源:载入('nice.wdf',"网易WDF动画",1238408331),0,0,4,true,true),
			[50] =  按钮.创建(资源:载入('nice.wdf',"网易WDF动画",1238408331),0,0,4,true,true),
			[51] =  按钮.创建(资源:载入('nice.wdf',"网易WDF动画",1238408331),0,0,4,true,true),
			[52] =  按钮.创建(资源:载入('nice.wdf',"网易WDF动画",1238408331),0,0,4,true,true),
			[53] =  按钮.创建(资源:载入('nice.wdf',"网易WDF动画",1238408331),0,0,4,true,true),
			[54] = 按钮.创建(tp.资源:载入('wzife.wdf',"网易WDF动画",0x48D2590A),0,0,4,true,true,"开启移速"),
			[55] = 按钮.创建(tp.资源:载入('wzife.wdf',"网易WDF动画",0x48D2590A),0,0,4,true,true,"关闭移速"),
			[56] = 按钮.创建(tp.资源:载入('wzife.wdf',"网易WDF动画",0x48D2590A),0,0,4,true,true,"开启攻速"),
			[57] = 按钮.创建(tp.资源:载入('wzife.wdf',"网易WDF动画",0x48D2590A),0,0,4,true,true,"关闭攻速"),
			[58] = 按钮.创建(tp.资源:载入('wzife.wdf',"网易WDF动画",0x48D2590A),0,0,4,true,true,"开启法速"),
			[59] = 按钮.创建(tp.资源:载入('wzife.wdf',"网易WDF动画",0x48D2590A),0,0,4,true,true,"关闭法速"),

			[60] = 按钮.创建(tp.资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0x10000021),0,0,4,true,true,"领取奖励"), --累充VIP8领奖
			[61] = 按钮.创建(tp.资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0x10000021),0,0,4,true,true,"领取奖励"),
			[62] = 按钮.创建(tp.资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0x10000021),0,0,4,true,true,"领取奖励"),

			[63] = 资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0xAABBCC94),     --VIP图标  VIP8
			[64] = 资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0xAABBCC95),     --VIP图标  VIP9
			[65] = 资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0xAABBCC96),     --VIP图标  VIP10





		}
		self.VIP资源组 = {
			[1] =  按钮.创建(资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0x10000010),0,0,4,true,true),   --VIP1
			[2] =  按钮.创建(资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0x10000011),0,0,4,true,true),   --VIP2
			[3] =  按钮.创建(资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0x10000012),0,0,4,true,true),   --VIP3
			[4] =  按钮.创建(资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0x10000013),0,0,4,true,true),   --VIP4
			[5] =  按钮.创建(资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0x10000014),0,0,4,true,true),   --VIP5
			[6] =  按钮.创建(资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0x10000015),0,0,4,true,true),   --VIP6
			[7] =  按钮.创建(资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0x10000016),0,0,4,true,true),   --VIP7
			[8] =  按钮.创建(资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0xE73FB013),0,0,4,true,true),   --VIP8
			[9] =  按钮.创建(资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0xE73FB014),0,0,4,true,true),   --VIP9
			[10] =  按钮.创建(资源:载入('vvxxzcom/carditem.wdf',"网易WDF动画",0xE73FB015),0,0,4,true,true),   --VIP10
		}
	    --self.资源组[31]:绑定窗口_(315)
         	    self.资源组[31]:置偏移(11,8)
         	    self.资源组[34]:置偏移(11,8)
         	    self.资源组[10]:置偏移(11,8)
         	    self.资源组[11]:置偏移(11,8)
         	    self.资源组[12]:置偏移(11,8)
         	    self.资源组[13]:置偏移(11,8)

         	    self.资源组[3]:置偏移(1,5)
         	    self.资源组[4]:置偏移(1,5)
         	    self.资源组[5]:置偏移(1,5)
         	    self.资源组[6]:置偏移(1,5)
         	    self.资源组[7]:置偏移(1,5)
         	    self.资源组[29]:置偏移(1,5)
         	    self.资源组[30]:置偏移(1,5)
         	    self.资源组[43]:置偏移(1,5)
         	    self.资源组[60]:置偏移(1,5)
         	    self.资源组[61]:置偏移(1,5)
         	    self.资源组[62]:置偏移(1,5)

	    self.线 = tp.资源:载入("wzife.wd1","网易WDF动画",999600305)
	    self.线:置区域(0,0,467,2)
	    self.加入 = 0
	    self.选中 = 0
	    self.选中状态 = 0
	    self.物品组={}
	    self.物品组1={}
	    self.签到物品组 = {}
	    self:加载物品(内容)
	    self:加载物品1(内容)
	    tp.运行时间 = tp.运行时间 + 1
	    self.窗口时间 = tp.运行时间
	    self.可视 = true
	    self.进程=1
	    self.分页=5
	    self.VIP经验=0
	    self.内容=内容
	    self.七日签到数据=内容.七日签到数据
	    self.第一天wp=内容.七日签到数据.第一天.物品
	    self.第二天wp=内容.七日签到数据.第二天.物品
	    self.第三天wp=内容.七日签到数据.第三天.物品
	    self.第四天wp=内容.七日签到数据.第四天.物品
	    self.第五天wp=内容.七日签到数据.第五天.物品
	    self.第六天wp=内容.七日签到数据.第六天.物品
	    self.第七天wp=内容.七日签到数据.第七天.物品
	    self.第一天说明=内容.七日签到数据.第一天.说明
	    self.第二天说明=内容.七日签到数据.第二天.说明
	    self.第三天说明=内容.七日签到数据.第三天.说明
	    self.第四天说明=内容.七日签到数据.第四天.说明
	    self.第五天说明=内容.七日签到数据.第五天.说明
	    self.第六天说明=内容.七日签到数据.第六天.说明
	    self.第七天说明=内容.七日签到数据.第七天.说明


	end
end


function 累充礼包:七日签到(内容)
    self.七日签到数据=内容.七日签到数据
    self.第一天wp=内容.七日签到数据.第一天.物品
    self.第二天wp=内容.七日签到数据.第二天.物品
    self.第三天wp=内容.七日签到数据.第三天.物品
    self.第四天wp=内容.七日签到数据.第四天.物品
    self.第五天wp=内容.七日签到数据.第五天.物品
    self.第六天wp=内容.七日签到数据.第六天.物品
    self.第七天wp=内容.七日签到数据.第七天.物品
    self.第一天说明=内容.第一天说明
	    self.第一天说明=内容.七日签到数据.第一天.说明
	    self.第二天说明=内容.七日签到数据.第二天.说明
	    self.第三天说明=内容.七日签到数据.第三天.说明
	    self.第四天说明=内容.七日签到数据.第四天.说明
	    self.第五天说明=内容.七日签到数据.第五天.说明
	    self.第六天说明=内容.七日签到数据.第六天.说明
	    self.第七天说明=内容.七日签到数据.第七天.说明
end

function 累充礼包:签到物品刷新(内容)
    self.第一天wp=内容.七日签到数据.第一天.物品
    self.第二天wp=内容.七日签到数据.第二天.物品
    self.第三天wp=内容.七日签到数据.第三天.物品
    self.第四天wp=内容.七日签到数据.第四天.物品
    self.第五天wp=内容.七日签到数据.第五天.物品
    self.第六天wp=内容.七日签到数据.第六天.物品
    self.第七天wp=内容.七日签到数据.第七天.物品
    self.第一天说明=内容.第一天说明
	    self.第一天说明=内容.七日签到数据.第一天.说明
	    self.第二天说明=内容.七日签到数据.第二天.说明
	    self.第三天说明=内容.七日签到数据.第三天.说明
	    self.第四天说明=内容.七日签到数据.第四天.说明
	    self.第五天说明=内容.七日签到数据.第五天.说明
	    self.第六天说明=内容.七日签到数据.第六天.说明
	    self.第七天说明=内容.七日签到数据.第七天.说明
end


function 累充礼包:显示(dt,x,y)
	local qdwp = {
	{名称=self.第一天wp,类型="消耗道具"},
	{名称=self.第二天wp,类型="消耗道具"},
	{名称=self.第三天wp,类型="消耗道具"},
	{名称=self.第四天wp,类型="消耗道具"},
	{名称=self.第五天wp,类型="消耗道具"},
	{名称=self.第六天wp,类型="消耗道具"},
	{名称=self.第七天wp,类型="消耗道具"}
	}
	local qdsm = {
	self.第一天说明,
	self.第二天说明,
	self.第三天说明,
	self.第四天说明,
	self.第五天说明,
	self.第六天说明,
	self.第七天说明
	}

	--local 偏移x,偏移y = 等比例缩放公式(410,310,self.资源组[15].宽度,self.资源组[15].高度)
	--cz = self.数据
	self.焦点 = false
	self.资源组[1]:显示(self.x,self.y)
	self.资源组[36]:显示(self.x+8,self.y+25)
	zts:置颜色(0xFFFFFFFF):显示(self.x+32,self.y+51,"欢迎少侠来到福利中心,\n开通特权或提升vip等级\n可获得丰厚的奖励哦！")

	--self.资源组[9]:显示(self.x+200,self.y+90)
	self.资源组[2]:更新(x,y)
	self.资源组[10]:更新(x,y)
	self.资源组[11]:更新(x,y)
	self.资源组[12]:更新(x,y)
	--self.资源组[13]:更新(x,y)

	self.资源组[31]:更新(x,y)
	self.资源组[34]:更新(x,y)


	tp.窗口标题背景_:显示(self.x-86+self.资源组[1].宽度/2,self.y)
	引擎.场景.字体表.标题字体:显示(self.x+self.资源组[1].宽度/2,self.y+3,"福利中心")
	-- self.资源组[2]:显示(self.x-18+self.资源组[1].宽度,self.y+3)
	if 授权码 == "ugzaDy7b4wSHsNY"  or 授权码 =="9xRVCmGmz33nf5dRwnhfYregfrrBz5"  then
	self.资源组[12]:显示(self.x+200,self.y+35,true,1)
	self.资源组[31]:显示(self.x+298,self.y+35,true,1)
	self.资源组[34]:显示(self.x+298,self.y+35,true,1)
	self.资源组[10]:显示(self.x+396,self.y+35,true,1)
	self.资源组[11]:显示(self.x+494,self.y+35,true,1)
	else
	self.资源组[12]:显示(self.x+200,self.y+35,true,1)
	self.资源组[31]:显示(self.x+298,self.y+35,true,1)
	self.资源组[34]:显示(self.x+396,self.y+35,true,1)
	self.资源组[10]:显示(self.x+494,self.y+35,true,1)
	self.资源组[11]:显示(self.x+592,self.y+35,true,1)
	end


	--self.资源组[13]:显示(self.x+560-10,self.y+58)



	zts:置颜色(白色)


	if self.资源组[2]:事件判断() then
	    self:打开()
		return
	elseif self.资源组[12]:事件判断() then
		self.分页=5
			--发送数据(110)
	elseif self.资源组[43]:事件判断() then
			发送数据(94.8)
	elseif self.资源组[13]:事件判断() then
		引擎.运行(充值网址)
	elseif self.资源组[10]:事件判断() then
		self.分页=2
		--self.进程=8
		--发送数据(119)
	elseif self.资源组[11]:事件判断() then
		self.分页=3
		--self.进程=9
		--发送数据(119)
	-- elseif self.资源组[15]:事件判断() then
	-- 	self.分页=1
	-- 	self.进程=1
	-- elseif self.资源组[16]:事件判断() then
	-- 	self.分页=1
	-- 	self.进程=2
	-- elseif self.资源组[17]:事件判断() then
	-- 	self.分页=1
	-- 	self.进程=3
	-- elseif self.资源组[18]:事件判断() then
	-- 	self.分页=1
	-- 	self.进程=4
	-- elseif self.资源组[19]:事件判断() then
	-- 	self.进程=5
	-- elseif self.资源组[20]:事件判断() then
	-- 	self.分页=1
	-- 	self.进程=6
	-- elseif self.资源组[21]:事件判断() then
	-- 	self.分页=1
	-- 	self.进程=7
	elseif self.资源组[31]:事件判断() then
		self.分页=1
		self.进程=1
	elseif self.资源组[34]:事件判断() then
		self.分页=4
		--self.进程=10

	elseif self.VIP资源组[1]:事件判断() then
		self.分页=1
		self.进程=1
	elseif self.VIP资源组[2]:事件判断() then
		self.分页=1
		self.进程=2
	elseif self.VIP资源组[3]:事件判断() then
		self.分页=1
		self.进程=3
	elseif self.VIP资源组[4]:事件判断() then
		self.分页=1
		self.进程=4
	elseif self.VIP资源组[5]:事件判断() then
		self.分页=1
		self.进程=5
	elseif self.VIP资源组[6]:事件判断() then
		self.分页=1
		self.进程=6
	elseif self.VIP资源组[7]:事件判断() then
		self.分页=1
		self.进程=7
	elseif self.VIP资源组[8]:事件判断() then
		self.分页=1
		self.进程=8
	elseif self.VIP资源组[9]:事件判断() then
		self.分页=1
		self.进程=9
	elseif self.VIP资源组[10]:事件判断() then
		self.分页=1
		self.进程=10

	elseif self.资源组[3]:事件判断() then --20
		if self.内容.累充>=self.内容.VIP额度.VIP1充值额度 then --测试模式
		    发送数据(94.6,{礼包=1})
	    else
			tp.常规提示:打开("#Y/累计充值未达到要求！")
		end
	elseif self.资源组[4]:事件判断() then --30
		if self.内容.累充>=self.内容.VIP额度.VIP2充值额度 then
		    发送数据(94.6,{礼包=2})
	    else
			tp.常规提示:打开("#Y/累计充值未达到要求！")
		end
	elseif self.资源组[5]:事件判断() then --40
		if self.内容.累充>=self.内容.VIP额度.VIP3充值额度 then
		    发送数据(94.6,{礼包=3})
	    else
			tp.常规提示:打开("#Y/累计充值未达到要求！")
		end
	elseif self.资源组[6]:事件判断() then --50
		if self.内容.累充>=self.内容.VIP额度.VIP4充值额度 then
		    发送数据(94.6,{礼包=4})
	    else
			tp.常规提示:打开("#Y/累计充值未达到要求！")
		end
	elseif self.资源组[7]:事件判断() then --60
		if self.内容.累充>=self.内容.VIP额度.VIP5充值额度 then
		    发送数据(94.6,{礼包=5})
	    else
			tp.常规提示:打开("#Y/累计充值未达到要求！")
		end
	elseif self.资源组[29]:事件判断() then --60
		if self.内容.累充>=self.内容.VIP额度.VIP6充值额度 then
		    发送数据(94.6,{礼包=6})
	    else
			tp.常规提示:打开("#Y/累计充值未达到要求！")
		end
	elseif self.资源组[30]:事件判断() then --60
		if self.内容.累充>=self.内容.VIP额度.VIP7充值额度 then
		    发送数据(94.6,{礼包=7})
	    else
			tp.常规提示:打开("#Y/累计充值未达到要求！")
		end
	elseif self.资源组[60]:事件判断() then --60
		if self.内容.累充>=self.内容.VIP额度.VIP8充值额度 then
		    发送数据(94.6,{礼包=8})
	    else
			tp.常规提示:打开("#Y/累计充值未达到要求！")
		end
	elseif self.资源组[61]:事件判断() then --60
		if self.内容.累充>=self.内容.VIP额度.VIP9充值额度 then
		    发送数据(94.6,{礼包=9})
	    else
			tp.常规提示:打开("#Y/累计充值未达到要求！")
		end
	elseif self.资源组[62]:事件判断() then --60
		if self.内容.累充>=self.内容.VIP额度.VIP10充值额度 then
		    发送数据(94.6,{礼包=10})
	    else
			tp.常规提示:打开("#Y/累计充值未达到要求！")
		end
	end


	if self.分页==1 then
		self.资源组[41]:显示(self.x+280,self.y+95)
		self.资源组[37]:显示(self.x+200,self.y+73)
		self.资源组[42]:显示(self.x+283,self.y+255)

		self.VIP资源组[1]:更新(x,y,self.进程 ~= 1)
		self.VIP资源组[2]:更新(x,y,self.进程 ~= 2)
		self.VIP资源组[3]:更新(x,y,self.进程 ~= 3)
		self.VIP资源组[4]:更新(x,y,self.进程 ~= 4)
		self.VIP资源组[5]:更新(x,y,self.进程 ~= 5)
		self.VIP资源组[6]:更新(x,y,self.进程 ~= 6)
		self.VIP资源组[7]:更新(x,y,self.进程 ~= 7)
		self.VIP资源组[8]:更新(x,y,self.进程 ~= 8)
		self.VIP资源组[9]:更新(x,y,self.进程 ~= 9)
		self.VIP资源组[10]:更新(x,y,self.进程 ~= 10)
		self.资源组[43]:更新(x,y)

		self.资源组[40]:显示(self.x+459,self.y+85)
		self.资源组[43]:显示(self.x+350,self.y+295)
		--self.资源组[38]:显示(self.x+210,self.y+80)

		if self.内容.累充< self.内容.VIP额度.VIP1充值额度 then
			zts1:显示(self.x+250,self.y+90,"当前等级:	   无")
			self.VIP经验=self.内容.VIP额度.VIP1充值额度
		elseif 	self.内容.累充 >= self.内容.VIP额度.VIP1充值额度 and self.内容.累充 < self.内容.VIP额度.VIP2充值额度 then
			zts1:显示(self.x+250,self.y+90,"当前等级:")
			self.资源组[22]:显示(self.x+325,self.y+88)
			self.VIP经验=self.内容.VIP额度.VIP2充值额度
		elseif 	self.内容.累充 >= self.内容.VIP额度.VIP2充值额度 and self.内容.累充 < self.内容.VIP额度.VIP3充值额度 then
			zts1:显示(self.x+250,self.y+90,"当前等级:")
			self.资源组[23]:显示(self.x+325,self.y+88)
			self.VIP经验=self.内容.VIP额度.VIP3充值额度
		elseif 	self.内容.累充 >= self.内容.VIP额度.VIP3充值额度 and self.内容.累充 < self.内容.VIP额度.VIP4充值额度 then
			zts1:显示(self.x+250,self.y+90,"当前等级:")
			self.资源组[24]:显示(self.x+325,self.y+88)
			self.VIP经验=self.内容.VIP额度.VIP4充值额度
		elseif 	self.内容.累充 >= self.内容.VIP额度.VIP4充值额度 and self.内容.累充 < self.内容.VIP额度.VIP5充值额度 then
			zts1:显示(self.x+250,self.y+90,"当前等级:")
			self.资源组[25]:显示(self.x+325,self.y+88)
			self.VIP经验=self.内容.VIP额度.VIP5充值额度
		elseif 	self.内容.累充 >= self.内容.VIP额度.VIP5充值额度 and self.内容.累充 < self.内容.VIP额度.VIP6充值额度 then
			zts1:显示(self.x+250,self.y+90,"当前等级:")
			self.资源组[26]:显示(self.x+325,self.y+88)
			self.VIP经验=self.内容.VIP额度.VIP6充值额度
		elseif 	self.内容.累充 >= self.内容.VIP额度.VIP6充值额度 and self.内容.累充 < self.内容.VIP额度.VIP7充值额度 then
			zts1:显示(self.x+250,self.y+90,"当前等级:")
			self.资源组[27]:显示(self.x+325,self.y+88)
			self.VIP经验=self.内容.VIP额度.VIP7充值额度
		elseif 	self.内容.累充 >= self.内容.VIP额度.VIP7充值额度 and self.内容.累充 < self.内容.VIP额度.VIP8充值额度 then
			zts1:显示(self.x+250,self.y+90,"当前等级:")
			self.资源组[28]:显示(self.x+325,self.y+88)
			self.VIP经验=self.内容.VIP额度.VIP8充值额度
		elseif 	self.内容.累充 >= self.内容.VIP额度.VIP8充值额度 and self.内容.累充 < self.内容.VIP额度.VIP9充值额度 then
			zts1:显示(self.x+250,self.y+90,"当前等级:")
			self.资源组[63]:显示(self.x+325,self.y+88)
			self.VIP经验=self.内容.VIP额度.VIP9充值额度
		elseif 	self.内容.累充 >= self.内容.VIP额度.VIP9充值额度 and self.内容.累充 < self.内容.VIP额度.VIP10充值额度 then
			zts1:显示(self.x+250,self.y+90,"当前等级:")
			self.资源组[64]:显示(self.x+325,self.y+88)
			self.VIP经验=self.内容.VIP额度.VIP10充值额度
		elseif 	self.内容.累充 >= self.内容.VIP额度.VIP10充值额度 then
			zts1:显示(self.x+250,self.y+90,"当前等级:")
			self.资源组[65]:显示(self.x+325,self.y+88)
			self.VIP经验=self.内容.VIP额度.VIP10充值额度
		end

		self.资源组[39]:置区域(0,0,math.min(math.floor(self.内容.累充 / self.VIP经验 *  self.资源组[39].宽度), self.资源组[39].宽度), self.资源组[39].高度)
		self.资源组[39]:显示(self.x+470,self.y+95)
		zts1:置颜色(0xFFFFFFFF):显示(self.x+470+ self.资源组[39].宽度/2-25,self.y+92-2,""..self.内容.累充.."/"..self.VIP经验)
		zts1:显示(self.x+400,self.y+90,"当前充值:	   ")--..self.内容.累充)
		tp.字体表.描边自定:置颜色(0xFF8B0000):显示(self.x+355,self.y+140,"①CDK充值即可获得相应vip经验值")
       		tp.字体表.描边自定:置颜色(0xFF8B0000):显示(self.x+315,self.y+170,"②提升vip等级可免费享有相关特权及礼包奖励")
       		tp.字体表.描边自定:置颜色(0xFFFF0000):显示(self.x+666,self.y+160,"鼠\n标\n滚\n轮\n向\n下\n滑\n动\n↓")


		if 引擎.取鼠标滚轮()<0 then
		       self.加入=(self.加入+1>10-5 and self.加入) or self.加入+1
		    elseif 引擎.取鼠标滚轮()>0 then
		       self.加入=(self.加入-1>=0 and self.加入-1) or self.加入
		    end

		  local yyy = 0
		    for n=1,5 do
		        yyy=n
		        n=n+self.加入
		        self.VIP资源组[n]:显示(self.x + 220,self.y +  yyy* 43 +82)
		        if self.VIP资源组[n]:事件判断() then
		           self.进程=yyy+self.加入
		        end
		    end

	if self.进程 == 1 then
		self.资源组[3]:更新(x,y,self.礼包序列.礼包一==false)
		self.资源组[3]:显示(self.x+550,self.y+295)

		local xx = 0
		local yy = 0
		for i=1,5 do
			tp.物品格子背景_:显示(self.x+150+(xx-1)*70+230,self.y+35+yy*84+180)
			xx = xx + 1
			if xx==5 then
			    xx=0
			    yy=yy+1
			end
		end

	    for k,v in pairs(self.物品组) do
	    	if k<=5 then
	            local wx = k*70
				v.名称.小动画:显示(self.x+61+wx-50+230,self.y+37+180)
				if self.礼包序列.礼包一 then
					v.名称.小动画:灰度级()
					self.资源组[8]:显示(self.x+61+wx-50-146+230,self.y+37-380+180)
				end
				if v.名称.名称=="九转金丹" then
				   v.名称.品质=300
				end
				if v.名称.小动画:是否选中(x,y) then
			    	tp.提示:商城提示(self.x-150+wx+230,self.y-90+180,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
			    end
	    	end
	    end


	elseif self.进程 == 2 then
		self.资源组[4]:更新(x,y,self.礼包序列.礼包二==false)

		self.资源组[4]:显示(self.x+550,self.y+295)


		local xx = 0
		local yy = 0
		for i=1,5 do
			tp.物品格子背景_:显示(self.x+150+(xx-1)*70+230,self.y+35+yy*84+180)
			xx = xx + 1
			if xx==5 then
			    xx=0
			    yy=yy+1
			end
		end

	    for k,v in pairs(self.物品组) do
	    	if k<=10 and k>5 then
	            local wx = k*70
				v.名称.小动画:显示(self.x+61+wx-50+230-350,self.y+37+180)
				if self.礼包序列.礼包二 then
					v.名称.小动画:灰度级()
					self.资源组[8]:显示(self.x+61+wx-50-146+230-350,self.y+37-380+180)
				end
				if v.名称.名称=="九转金丹" then
				   v.名称.品质=300
				end
				if v.名称.小动画:是否选中(x,y) then
			    	tp.提示:商城提示(self.x-150+wx+230-350,self.y-90+180,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
			    end
	    	end
	    end

	    elseif self.进程 == 3 then
	    	self.资源组[5]:更新(x,y,self.礼包序列.礼包三==false)

		self.资源组[5]:显示(self.x+550,self.y+295)

		local xx = 0
		local yy = 0
		for i=1,5 do
			tp.物品格子背景_:显示(self.x+150+(xx-1)*70+230,self.y+35+yy*84+180)
			xx = xx + 1
			if xx==5 then
			    xx=0
			    yy=yy+1
			end
		end

	    for k,v in pairs(self.物品组) do
	    	if k<=15 and k>10 then
	            local wx = k*70
				v.名称.小动画:显示(self.x+61+wx-50+230-700,self.y+37+180)
				if self.礼包序列.礼包三 then
					v.名称.小动画:灰度级()
					self.资源组[8]:显示(self.x+61+wx-50-146+230-700,self.y+37-380+180)
				end
				if v.名称.名称=="九转金丹" then
				   v.名称.品质=300
				end
				if v.名称.小动画:是否选中(x,y) then
			    	tp.提示:商城提示(self.x-150+wx+230-700,self.y-90+180,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
			    end
	    	end
	    end



	    elseif self.进程 == 4 then
	    	self.资源组[6]:更新(x,y,self.礼包序列.礼包四==false)

		self.资源组[6]:显示(self.x+550,self.y+295)

		local xx = 0
		local yy = 0
		for i=1,5 do
			tp.物品格子背景_:显示(self.x+150+(xx-1)*70+230,self.y+35+yy*84+180)
			xx = xx + 1
			if xx==5 then
			    xx=0
			    yy=yy+1
			end
		end

	    for k,v in pairs(self.物品组) do
	    	if k<=20 and k>15 then
	            local wx = k*70
				v.名称.小动画:显示(self.x+61+wx-50+230-1050,self.y+37+180)
				if self.礼包序列.礼包四 then
					v.名称.小动画:灰度级()
					self.资源组[8]:显示(self.x+61+wx-50-146+230-1050,self.y+37-380+180)
				end
				if v.名称.名称=="九转金丹" then
				   v.名称.品质=300
				end
				if v.名称.小动画:是否选中(x,y) then
			    	tp.提示:商城提示(self.x-150+wx+230-1050,self.y-90+180,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
			    end
	    	end
	    end


	    elseif self.进程 == 5 then
	    	self.资源组[7]:更新(x,y,self.礼包序列.礼包五==false)
		self.资源组[7]:显示(self.x+550,self.y+295)


		local xx = 0
		local yy = 0
		for i=1,5 do
			tp.物品格子背景_:显示(self.x+150+(xx-1)*70+230,self.y+35+yy*84+180)
			xx = xx + 1
			if xx==5 then
			    xx=0
			    yy=yy+1
			end
		end

	    for k,v in pairs(self.物品组) do
	    	if k<=25 and k>20 then
	            local wx = k*70
				v.名称.小动画:显示(self.x+61+wx-50+230-1400,self.y+37+180)
				if self.礼包序列.礼包五 then
					v.名称.小动画:灰度级()
					self.资源组[8]:显示(self.x+61+wx-50-146+230-1400,self.y+37-380+180)
				end
				if v.名称.名称=="九转金丹" then
				   v.名称.品质=300
				end
				if v.名称.小动画:是否选中(x,y) then
			    	tp.提示:商城提示(self.x-150+wx+230-1400,self.y-90+180,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
			    end
	    	end
	    end


	    elseif self.进程 == 6 then
	    	self.资源组[29]:更新(x,y,self.礼包序列.礼包六==false)

		self.资源组[29]:显示(self.x+550,self.y+295)


		local xx = 0
		local yy = 0
		for i=1,5 do
			tp.物品格子背景_:显示(self.x+150+(xx-1)*70+230,self.y+35+yy*84+180)
			xx = xx + 1
			if xx==5 then
			    xx=0
			    yy=yy+1
			end
		end

	    for k,v in pairs(self.物品组) do
	    	if k<=30 and k>25 then
	            local wx = k*70
				v.名称.小动画:显示(self.x+61+wx-50+230-1750,self.y+37+180)
				if self.礼包序列.礼包六 then
					v.名称.小动画:灰度级()
					self.资源组[8]:显示(self.x+61+wx-50-146+230-1750,self.y+37-380+180)
				end
				if v.名称.名称=="九转金丹" then
				   v.名称.品质=300
				end
				if v.名称.小动画:是否选中(x,y) then
			    	tp.提示:商城提示(self.x-150+wx+230-1750,self.y-90+180,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
			    end
	    	end
	    end


	    elseif self.进程 == 7 then
	    	self.资源组[30]:更新(x,y,self.礼包序列.礼包七==false)

		self.资源组[30]:显示(self.x+550,self.y+295)

		local xx = 0
		local yy = 0
		for i=1,5 do
			tp.物品格子背景_:显示(self.x+150+(xx-1)*70+230,self.y+35+yy*84+180)
			xx = xx + 1
			if xx==5 then
			    xx=0
			    yy=yy+1
			end
		end

	    for k,v in pairs(self.物品组) do
	    	if k<=35 and k>30 then
	            local wx = k*70
				v.名称.小动画:显示(self.x+61+wx-50+230-2100,self.y+37+180)
				if self.礼包序列.礼包七 then
					v.名称.小动画:灰度级()
					self.资源组[8]:显示(self.x+61+wx-50-146+230-2100,self.y+37-380+180)
				end
				if v.名称.名称=="九转金丹" then
				   v.名称.品质=300
				end
				if v.名称.小动画:是否选中(x,y) then
			    	tp.提示:商城提示(self.x-150+wx+230-2100,self.y-90+180,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
			    end
	    	end
	    end




	     elseif self.进程 == 8 then
	    	self.资源组[60]:更新(x,y,self.礼包序列.礼包八==false)

		self.资源组[60]:显示(self.x+550,self.y+295)

		local xx = 0
		local yy = 0
		for i=1,5 do
			tp.物品格子背景_:显示(self.x+150+(xx-1)*70+230,self.y+35+yy*84+180)
			xx = xx + 1
			if xx==5 then
			    xx=0
			    yy=yy+1
			end
		end

	    for k,v in pairs(self.物品组) do
	    	if k<=40 and k>35 then
	            local wx = k*70
				v.名称.小动画:显示(self.x+61+wx-50+230-2450,self.y+37+180)
				if self.礼包序列.礼包八 then
					v.名称.小动画:灰度级()
					self.资源组[8]:显示(self.x+61+wx-50-146+230-2450,self.y+37-380+180)
				end
				if v.名称.名称=="九转金丹" then
				   v.名称.品质=300
				end
				if v.名称.小动画:是否选中(x,y) then
			    	tp.提示:商城提示(self.x-150+wx+230-2450,self.y-90+180,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
			    end
	    	end
	    end



	     elseif self.进程 == 9 then
	    	self.资源组[61]:更新(x,y,self.礼包序列.礼包九==false)

		self.资源组[61]:显示(self.x+550,self.y+295)

		local xx = 0
		local yy = 0
		for i=1,5 do
			tp.物品格子背景_:显示(self.x+150+(xx-1)*70+230,self.y+35+yy*84+180)
			xx = xx + 1
			if xx==5 then
			    xx=0
			    yy=yy+1
			end
		end

	    for k,v in pairs(self.物品组) do
	    	if k<=45 and k>40 then
	            local wx = k*70
				v.名称.小动画:显示(self.x+61+wx-50+230-2800,self.y+37+180)
				if self.礼包序列.礼包九 then
					v.名称.小动画:灰度级()
					self.资源组[8]:显示(self.x+61+wx-50-146+230-2800,self.y+37-380+180)
				end
				if v.名称.名称=="九转金丹" then
				   v.名称.品质=300
				end
				if v.名称.小动画:是否选中(x,y) then
			    	tp.提示:商城提示(self.x-150+wx+230-2800,self.y-90+180,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
			    end
	    	end
	    end




	     elseif self.进程 == 10 then
	    	self.资源组[62]:更新(x,y,self.礼包序列.礼包十==false)

		self.资源组[62]:显示(self.x+550,self.y+295)

		local xx = 0
		local yy = 0
		for i=1,5 do
			tp.物品格子背景_:显示(self.x+150+(xx-1)*70+230,self.y+35+yy*84+180)
			xx = xx + 1
			if xx==5 then
			    xx=0
			    yy=yy+1
			end
		end

	    for k,v in pairs(self.物品组) do
	    	if k<=50 and k>45 then
	            local wx = k*70
				v.名称.小动画:显示(self.x+61+wx-50+230-3150,self.y+37+180)
				if self.礼包序列.礼包十 then
					v.名称.小动画:灰度级()
					self.资源组[8]:显示(self.x+61+wx-50-146+230-3150,self.y+37-380+180)
				end
				if v.名称.名称=="九转金丹" then
				   v.名称.品质=300
				end
				if v.名称.小动画:是否选中(x,y) then
			    	tp.提示:商城提示(self.x-150+wx+230-3150,self.y-90+180,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
			    end
	    	end
	    end





	end




	elseif self.分页==2 then
	    --elseif self.进程 == 8 then

	    	self.资源组[32]:更新(x,y)
	    	--self.资源组[15]:显示(self.x+50,self.y+45,偏移x*1.4,偏移y*1.05)
	    	self.资源组[32]:显示(self.x+395,self.y+300)
	    	if self.内容.月卡.生效==true then
	    		zts:置颜色(黑色):显示(self.x+330,self.y+120,"每日可用次数:   "..self.内容.每日剩余.."    (特权加成↑)")
	    		zts:置颜色(黑色):显示(self.x+330,self.y+150,"抓鬼卡可用次数:   "..self.内容.抓鬼卡次数)
	    		zts:置颜色(黑色):显示(self.x+330,self.y+180,"剩余可用次数:   "..self.内容.次数)
	    	else
	    	zts:置颜色(黑色):显示(self.x+330,self.y+120,"每日可用次数:   "..self.内容.每日剩余)
	    	zts:置颜色(黑色):显示(self.x+330,self.y+150,"抓鬼卡可用次数:   "..self.内容.抓鬼卡次数)
	    	zts:置颜色(黑色):显示(self.x+330,self.y+180,"剩余可用次数:   "..self.内容.次数)
	    	end
	    	zts:置颜色(黑色):显示(self.x+230,self.y+250,"开启自动抓鬼后会持续进行战斗，如需取消请退出战斗后关闭自动！  ")
	    	zts:置颜色(红色):显示(self.x+220,self.y+270,"普通玩家每日次数为"..self.内容.每日免费.."次，特权玩家提升至"..self.内容.月卡次数.."次，优先使用每日次数！  ")
	    	--zts2:置颜色(红色):显示(self.x + 150 + 71-5,self.y +  60 +94,"每日更新自动抓鬼次数")
	    	if self.资源组[32]:事件判断() then
	    		发送数据(107.1,{事件="自动抓鬼"})--自动抓鬼
	    		--发送数据(119)
	    	end

	elseif self.分页==3 then
	    --elseif self.进程 == 9 then
		self.资源组[33]:更新(x,y)
	    	--self.资源组[15]:显示(self.x+50,self.y+45,偏移x*1.4,偏移y*1.05)
	    	self.资源组[33]:显示(self.x+395,self.y+300)
	    	if self.内容.月卡.生效==true then
	    		zts:置颜色(黑色):显示(self.x+330,self.y+120,"每日可用次数:   "..self.内容.每日剩余.."    (特权加成↑)")
	    		zts:置颜色(黑色):显示(self.x+330,self.y+150,"抓鬼卡可用次数:   "..self.内容.抓鬼卡次数)
	    		--zts:置颜色(黑色):显示(self.x+90,self.y+130,"已完成次数:   "..self.内容.月卡次数.."    (月卡加成↑)")
	    		zts:置颜色(黑色):显示(self.x+330,self.y+180,"剩余可用次数:   "..self.内容.次数)
	    	else
	    	zts:置颜色(黑色):显示(self.x+330,self.y+120,"每日可用次数:   "..self.内容.每日剩余)
	    	zts:置颜色(黑色):显示(self.x+330,self.y+150,"抓鬼卡可用次数:   "..self.内容.抓鬼卡次数)
	    	zts:置颜色(黑色):显示(self.x+330,self.y+180,"剩余可用次数:   "..self.内容.次数)
	    	end
	    	zts:置颜色(黑色):显示(self.x+230,self.y+250,"开启自动鬼王后会持续进行战斗，如需取消请退出战斗后关闭自动！  ")
	    	zts:置颜色(红色):显示(self.x+220,self.y+270,"普通玩家每日次数为"..self.内容.每日免费.."次，特权玩家提升至"..self.内容.月卡次数.."次，优先使用每日次数！  ")
	    	--zts2:置颜色(红色):显示(self.x + 150 + 71-5,self.y +  60 +94,"每日更新自动抓鬼次数")
	    	if self.资源组[33]:事件判断() then
	    		发送数据(107.1,{事件="自动鬼王"})--自动抓鬼
	    		--发送数据(119)
	    	end

 	elseif self.分页==4 then
	    --elseif self.进程 == 10 then
	    	self.资源组[35]:更新(x,y)
	    	if self.内容.月卡功能 == 1 then
	    	self.资源组[54]:更新(x,y)
	    	self.资源组[55]:更新(x,y)
	    	self.资源组[56]:更新(x,y)
	    	self.资源组[57]:更新(x,y)
	    	self.资源组[58]:更新(x,y)
	    	self.资源组[59]:更新(x,y)
	    	end
	    	--self.资源组[15]:显示(self.x+50,self.y+45,偏移x*1.4,偏移y*1.05)
	    	--self.资源组[35]:显示(self.x+405,self.y+300)
	    	if self.内容.月卡功能 == 1 then
	    	self.资源组[35]:显示(self.x+405,self.y+280)
	    	--下面是功能按钮  攻速 施法速 移速
	    	self.资源组[54]:显示(self.x+205,self.y+320)
	    	self.资源组[55]:显示(self.x+285,self.y+320)
	    	self.资源组[56]:显示(self.x+365,self.y+320)
	    	self.资源组[57]:显示(self.x+445,self.y+320)
	    	self.资源组[58]:显示(self.x+525,self.y+320)
	    	self.资源组[59]:显示(self.x+605,self.y+320)
	    	else
	    		self.资源组[35]:显示(self.x+405,self.y+300)
	    	end
	    	if self.内容.月卡生效 == false then
		zts2:置颜色(红色):显示(self.x+self.资源组[1].宽度/2-37+90,self.y+190,"[未生效]")
		else
			zts2:置颜色(绿色):显示(self.x+self.资源组[1].宽度/2-37+90,self.y+190,"[生效中]")
		end

	    	--zts:置颜色(黑色):显示(self.x+190,self.y+100+50+20-55,"当前累充："..self.内容.累充.."元！")
		zts:置颜色(黑色):显示(self.x+300,self.y+100,"到期时间:   "..时间转换(self.内容.月卡时间))
		zts:置颜色(黑色):显示(self.x+255	,self.y+130,"     特权：每日礼包+100W银子+150次自动抓鬼！")
		zts:置颜色(红色):显示(self.x+255	,self.y+160,"     额外增加：+10%经验、+10%银子、+10%储备！")


		local xx = 0
		local yy = 0
		for i=1,5 do
			tp.物品格子背景_:显示(self.x+68+57+215+(xx-1)*70,self.y+185+yy*84+30)
			xx = xx + 1
			if xx==5 then
			    xx=0
			    yy=yy+1
			end
		end

		for k,v in pairs(self.物品组1) do
	    	if k<=5 then
	            local wx = k*70
				v.名称.小动画:显示(self.x+251+wx-50,self.y+187+30)
				if v.名称.名称=="九转金丹" then
				   v.名称.品质=300
				end
				if v.名称.小动画:是否选中(x,y) then
			    	tp.提示:商城提示(self.x-150+wx+190,self.y+60+30,v.名称.名称,v.名称.说明,v.名称.大动画,v.名称.备注)
			    end
	    	end
		 end

		if self.资源组[35]:事件判断() then
	    		发送数据(95.1)
	    	end
	    	if self.内容.月卡功能 == 1 then
	    	if self.资源组[54]:事件判断() then
	    		发送数据(204)
	    	elseif self.资源组[55]:事件判断() then
	    		发送数据(204.1)
	    	elseif self.资源组[56]:事件判断() then
	    		发送数据(205)
	    	elseif self.资源组[57]:事件判断() then
	    		发送数据(205.1)
	    	elseif self.资源组[58]:事件判断() then
	    		发送数据(206)
	    	elseif self.资源组[59]:事件判断() then
	    		发送数据(206.1)
	    	end
	    	end


	elseif self.分页==5 then
		 local 格子 = tp._物品格子
		     for i=1,7 do
		        self.签到物品组[i] = 格子(0,0,i,"物品")
		         self.签到物品组[i]:置物品(qdwp[i])
	         	end
		self.资源组[44]:显示(self.x+200,self.y+81)
		self.资源组[46]:更新(x,y)
		self.资源组[47]:更新(x,y)
		self.资源组[48]:更新(x,y)
		self.资源组[49]:更新(x,y)
		self.资源组[50]:更新(x,y)
		self.资源组[51]:更新(x,y)
		self.资源组[52]:更新(x,y)
		self.资源组[53]:更新(x,y)
		 for n=1,7 do
		       local jx,jy = 0,0
		       if  n == 1 then
				   jx = 151 + 12
				   jy = 26 + 105
		       elseif n == 2 then
		           jx = 151 + 10 + (n-1)*70
		           jy = 26 + 125
		       elseif n == 3 then
		           jx = 151 + 10 + (n-1)*70
		           jy = 26 + 105
		       elseif n == 4 then
		           jx = 151 + 8 + (n-1)*70
		           jy = 26 + 125
		       elseif n == 5 then
		           jx = 151 + 6 + (n-1)*70
		           jy = 26 + 105
		       elseif n == 6 then
		           jx = 151 + 4 + (n-1)*70
		           jy = 26 + 125
		       elseif n == 7 then
		           jx = 151 + 1 + (n-1)*70
		           jy = 26 + 105
		       end
		self.签到物品组[n]:置坐标(self.x + jx+50,self.y + jy+50,nil,nil,4,-1)
	           	self.签到物品组[n]:显示(dt,x,y,self.鼠标,nil,3)




	           	self.资源组[47]:显示(self.x + 137 + 1*70,self.y + 26 + 190 )
	           	self.资源组[48]:显示(self.x + 135 + 2*70,self.y + 26 + 190+19 )
	           	self.资源组[49]:显示(self.x + 135 + 3*70,self.y + 26 + 190 )
	           	self.资源组[50]:显示(self.x + 133 + 4*70,self.y + 26 + 190+19 )
	           	self.资源组[51]:显示(self.x + 131 + 5*70,self.y + 26 + 190 )
	           	self.资源组[52]:显示(self.x + 129 + 6*70,self.y + 26 + 190+19 )
	           	self.资源组[53]:显示(self.x + 127 + 7*70,self.y + 26 + 190 )
	           	zts1:显示(self.x+362	,self.y+320,"刷新签到奖励需消耗1000*天数的仙玉，请谨慎操作！")

		           	if  self.七日签到数据.签到数据 >= n then
				if n == 1 then
					      self.资源组[45]:显示(self.x + 140 + 64 ,self.y + 6 + 86 + 150)
			           elseif n == 2 then
					      self.资源组[45]:显示(self.x + 140 + 133 ,self.y + 6 + 106 + 150)
			           elseif n == 3 then
					      self.资源组[45]:显示(self.x + 140 + 201 ,self.y + 6 + 86 + 150)
			           elseif n == 4 then
					      self.资源组[45]:显示(self.x + 140 + 270 ,self.y + 6 + 106 + 150)
			           elseif n == 5 then
					      self.资源组[45]:显示(self.x + 140 + 338 ,self.y + 6 + 86 + 150)
			           elseif n == 6 then
					      self.资源组[45]:显示(self.x + 140 + 407 ,self.y + 6 + 105 + 150)
			           elseif n == 7 then
					      self.资源组[45]:显示(self.x + 140 + 475 ,self.y + 6 + 86 + 150)
				       end
	               	end
	               	 if self.签到物品组[n].物品 ~= nil and self.签到物品组[n].焦点 then
				  tp.提示:自定义(x,y,qdsm[n],true)
		       	end
	           end



	            if self.七日签到数据.签到数据 < 7 then
		      self.资源组[46]:显示(self.x + 160 + 64 + self.七日签到数据.签到数据*70,self.y + 26 + 105 + 150)
		          	if self.资源组[46]:事件判断() then
			         发送数据(110.2,{})
			end
           		end

           		if self.资源组[47]:事件判断() then
	    		发送数据(110.1,{文本="第一天",序列=1})
	    	elseif self.资源组[48]:事件判断()  then
	    		发送数据(110.1,{文本="第二天",序列=2})
	    	elseif self.资源组[49]:事件判断()	then
	    		发送数据(110.1,{文本="第三天",序列=3})
	    	elseif self.资源组[50]:事件判断()	then
	    		发送数据(110.1,{文本="第四天",序列=4})
	    	elseif self.资源组[51]:事件判断()	then
	    		发送数据(110.1,{文本="第五天",序列=5})
	    	elseif self.资源组[52]:事件判断()	then
	    		发送数据(110.1,{文本="第六天",序列=6})
	    	elseif self.资源组[53]:事件判断()	then
	    		发送数据(110.1,{文本="第七天",序列=7})
	    	end



	end

end

function 累充礼包:加载物品(数据)
	local  wp = 数据.物品组
	self.礼包序列 =数据.是否领取

	--self.礼包序列 =tp.队伍[1].VIP礼包
	for k,v in pairs(wp) do
		if self.物品组[k]==nil then
		   self.物品组[k]={}
		end

		for i,n in pairs(v) do
			if self.物品组[k][i]== nil then
				self.物品组[k][i]={}
			end
			local 资源=引擎.取物品(n)
			self.物品组[k][i].小动画=tp.资源:载入(资源[11],"网易WDF动画",资源[12])
			self.物品组[k][i].大动画=tp.资源:载入(资源[11],"网易WDF动画",资源[13])
			self.物品组[k][i].名称=n
			if  wp[k].说明 ~=  nil then
				self.物品组[k][i].说明=wp[k].说明
			else
				self.物品组[k][i].说明=资源[1]
			end
		end
	end
end

function 累充礼包:加载物品1(数据)
	local  wp1 = 数据.物品组1
	self.礼包序列 =数据.是否领取
	--self.礼包序列 =tp.队伍[1].VIP礼包
	for k,v in pairs(wp1) do
		if self.物品组1[k]==nil then
		   self.物品组1[k]={}
		end
		for i,n in pairs(v) do
			-- print(n)
			if self.物品组1[k][i]== nil then
				self.物品组1[k][i]={}
			end
			local 资源=引擎.取物品(n)
			self.物品组1[k][i].小动画=tp.资源:载入(资源[11],"网易WDF动画",资源[12])
			self.物品组1[k][i].大动画=tp.资源:载入(资源[11],"网易WDF动画",资源[13])
			self.物品组1[k][i].名称=n
			--self.物品组1[k][i].说明=资源[1]
			if  wp1[k].说明 ~=  nil then
				self.物品组1[k][i].说明=wp1[k].说明
			else
				self.物品组1[k][i].说明=资源[1]
			end
		end
	end
end

function 累充礼包:更新礼包(sj)
	--print(sj)
    self.礼包序列.礼包一=sj.内容.礼包一
    self.礼包序列.礼包二=sj.内容.礼包二
    self.礼包序列.礼包三=sj.内容.礼包三
    self.礼包序列.礼包四=sj.内容.礼包四
    self.礼包序列.礼包五=sj.内容.礼包五
    self.礼包序列.礼包六=sj.内容.礼包六
    self.礼包序列.礼包七=sj.内容.礼包七
end

function 累充礼包:刷新(内容)

    self.内容=内容
end

function 累充礼包:七日签到刷新(内容)
    self.七日签到数据=内容.七日签到数据
    self.第一天wp=内容.七日签到数据.第一天.物品
    self.第二天wp=内容.七日签到数据.第二天.物品
    self.第三天wp=内容.七日签到数据.第三天.物品
    self.第四天wp=内容.七日签到数据.第四天.物品
    self.第五天wp=内容.七日签到数据.第五天.物品
    self.第六天wp=内容.七日签到数据.第六天.物品
    self.第七天wp=内容.七日签到数据.第七天.物品
end


function 累充礼包:检查点(x,y)
	if self.资源组 ~= nil and self.资源组[1]:是否选中(x,y) then
		return true
	end
end

function 累充礼包:初始移动(x,y)
	tp.运行时间 = tp.运行时间 + 1
	if not tp.消息栏焦点 then
  		self.窗口时间 = tp.运行时间
 	end
	if not self.焦点 then
		tp.移动窗口 = true
	end
	if self.鼠标 and  not self.焦点 then
		self.xx = x - self.x
		self.yy = y - self.y
	end
end

function 累充礼包:开始移动(x,y)
	if self.鼠标 then
		self.x = x - self.xx
		self.y = y - self.yy
	end
end

return 累充礼包